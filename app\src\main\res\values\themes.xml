<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.HeadshotSettingsGameBooster" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <!-- Customize your theme here. -->
    </style>

    <style name="no_bar" parent="Theme.MaterialComponents.NoActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/electric_blue</item>
        <item name="colorPrimaryVariant">@color/neon_blue</item>
        <item name="colorOnPrimary">@color/black</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/neon_blue</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">@color/cyber_dark_start</item>
        <!-- Window background. -->
        <item name="android:windowBackground">@drawable/cyber_neon_gradient_bg</item>
        <!-- Customize your theme here. -->
        <item name="android:navigationBarColor">@color/cyber_dark_start</item>

        <!-- Material TextAppearance attributes with Arabic font -->
        <item name="textAppearanceButton">@style/TextAppearance.App.Button</item>
        <item name="textAppearanceBody1">@style/TextAppearance.App.Body1</item>
        <item name="textAppearanceBody2">@style/TextAppearance.App.Body2</item>
        <item name="textAppearanceSubtitle1">@style/TextAppearance.App.Subtitle1</item>
        <item name="textAppearanceSubtitle2">@style/TextAppearance.App.Subtitle2</item>
        <item name="textAppearanceHeadline6">@style/TextAppearance.App.Headline6</item>
        <item name="textAppearanceHeadline5">@style/TextAppearance.App.Headline5</item>
        <item name="textAppearanceHeadline4">@style/TextAppearance.App.Headline4</item>
        <item name="textAppearanceHeadline3">@style/TextAppearance.App.Headline3</item>
        <item name="textAppearanceHeadline2">@style/TextAppearance.App.Headline2</item>
        <item name="textAppearanceHeadline1">@style/TextAppearance.App.Headline1</item>
        <item name="textAppearanceCaption">@style/TextAppearance.App.Caption</item>
        <item name="textAppearanceOverline">@style/TextAppearance.App.Overline</item>

        <!-- Default font for all TextViews -->
        <item name="android:fontFamily">@font/font_for_ar_en</item>
        <item name="fontFamily">@font/font_for_ar_en</item>
    </style>
</resources>