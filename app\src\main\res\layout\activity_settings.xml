<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/cyber_neon_gradient_bg"
    tools:context=".SettingsActivity">

    <!-- Background Effects -->
    <ImageView
        android:id="@+id/speed_lines_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:src="@drawable/blue_speed_lines"
        android:alpha="0.4"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:contentDescription="@string/app_name" />
        
    <ImageView
        android:id="@+id/energy_grid"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:src="@drawable/energy_grid"
        android:alpha="0.2"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:contentDescription="@string/app_name" />

    <!-- Back Button -->
    <ImageView
        android:id="@+id/btn_back"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_margin="16dp"
        android:padding="12dp"
        android:src="@drawable/ic_back"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:clickable="true"
        android:focusable="true"
        app:tint="@color/cyber_neon_cyan"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Settings Title -->
    <TextView
        android:id="@+id/settings_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/settings"
        android:textColor="@color/cyber_neon_cyan"
        android:textSize="24sp"
        android:fontFamily="@font/font_for_ar_en"
        android:layout_marginTop="16dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Settings Container -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        android:fillViewport="true"
        android:scrollbars="none"
        android:overScrollMode="never"
        app:layout_constraintTop_toBottomOf="@+id/settings_title"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Dark Mode Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="8dp"
                app:cardBackgroundColor="@color/cyber_card_bg">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/dark_mode"
                            android:textColor="@color/cyber_text_primary"
                            android:textSize="18sp"
                            android:fontFamily="@font/cairo_regular" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/enable_dark_theme_throughout_the_app"
                            android:textColor="@color/cyber_text_secondary"
                            android:textSize="14sp"
                            android:fontFamily="@font/cairo_regular" />
                    </LinearLayout>

                    <androidx.appcompat.widget.SwitchCompat
                        android:id="@+id/dark_mode_switch"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="true"
                        app:thumbTint="@color/cyber_neon_cyan"
                        app:trackTint="@color/cyber_slider_inactive" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Language Card -->
            <androidx.cardview.widget.CardView
                android:id="@+id/language_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="8dp"
                app:cardBackgroundColor="@color/cyber_card_bg"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?attr/selectableItemBackground">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/language"
                            android:textColor="@color/cyber_text_primary"
                            android:textSize="18sp"
                            android:fontFamily="@font/font_for_ar_en" />

                        <TextView
                            android:id="@+id/current_language"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/english"
                            android:textColor="@color/cyber_text_secondary"
                            android:textSize="14sp"
                            android:fontFamily="@font/cairo_regular" />
                    </LinearLayout>

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_arrow_right"
                        app:tint="@color/cyber_neon_cyan" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- About App Card -->
            <!-- About App Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="8dp"
                app:cardBackgroundColor="@color/cyber_card_bg">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/about"
                        android:textColor="@color/cyber_text_primary"
                        android:textSize="18sp"
                        android:fontFamily="@font/cairo_regular" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/cyber_card_stroke"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/headshot_game_enhancer_v1_0"
                        android:textColor="@color/white"
                        android:textSize="16sp"
                        android:fontFamily="@font/cairo_regular" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:text="@string/developed_by_mahmoud_ff_yt"
                        android:textColor="@color/cyber_neon_cyan"
                        android:textSize="14sp"
                        android:fontFamily="@font/cairo_regular"
                        android:layout_marginTop="8dp" />

                    <!-- Contact Email -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/for_more_suggestions_and_solutions_contact_us_via_email"
                        android:textColor="@color/white"
                        android:textSize="14sp"
                        android:fontFamily="@font/cairo_regular"
                        android:layout_marginTop="12dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:text="@string/email_contact"
                        android:autoLink="email"

                        android:textColor="@color/cyber_neon_cyan"
                        android:textSize="14sp"
                        android:fontFamily="@font/cairo_regular"
                        android:layout_marginTop="4dp" />

                </LinearLayout>
            </androidx.cardview.widget.CardView>

        </LinearLayout>
    </ScrollView>
</androidx.constraintlayout.widget.ConstraintLayout>
