<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="80dp"
    android:height="80dp"
    android:viewportWidth="80"
    android:viewportHeight="80">

    <!-- Outer Circle -->
    <path
        android:fillColor="#30FFFFFF"
        android:strokeColor="#80FFFFFF"
        android:strokeWidth="1"
        android:pathData="M40,5 A35,35 0 1,1 39.9,5" />

    <!-- Middle Circle -->
    <path
        android:fillColor="#40FFFFFF"
        android:strokeColor="#80FFFFFF"
        android:strokeWidth="1"
        android:pathData="M40,15 A25,25 0 1,1 39.9,15" />

    <!-- Inner Circle -->
    <path
        android:fillColor="#50FFFFFF"
        android:strokeColor="#80FFFFFF"
        android:strokeWidth="1"
        android:pathData="M40,25 A15,15 0 1,1 39.9,25" />

    <!-- Center Circle -->
    <path
        android:fillColor="#80FF5252"
        android:strokeColor="#FFFFFF"
        android:strokeWidth="1"
        android:pathData="M40,35 A5,5 0 1,1 39.9,35" />
</vector>
