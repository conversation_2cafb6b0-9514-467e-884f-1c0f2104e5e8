<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="16dp"
    app:cardBackgroundColor="#1A1A1A"
    app:cardCornerRadius="16dp"
    app:cardElevation="8dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header with icon -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingBottom="16dp">

            <ImageView
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:contentDescription="@string/app_name"
                android:src="@drawable/ic_rocket"
                app:tint="@color/vibrant_blue" />

            <TextView
                android:id="@+id/popup_progress_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:fontFamily="@font/font_for_ar_en"
                android:text="@string/optimizing"
                android:textColor="@color/text_color_primary"
                android:textSize="20sp" />
        </LinearLayout>

        <!-- Divider -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#333333" />

        <!-- Content -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Status text -->
            <TextView
                android:id="@+id/popup_progress_status"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/font_for_ar_en"
                android:text="@string/clearing_cache"
                android:textColor="@color/text_color_secondary"
                android:textSize="16sp" />

            <!-- Speedometer -->
            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="160dp"
                android:layout_marginTop="16dp">

                <!-- Speedometer background -->
                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:contentDescription="@string/app_name"
                    android:scaleType="fitCenter"
                    android:src="@drawable/ic_speedometer_bg" />

                <!-- Speedometer needle -->
                <ImageView
                    android:id="@+id/speedometer_needle"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:contentDescription="@string/app_name"
                    android:rotation="0"
                    android:scaleType="fitCenter"
                    android:src="@drawable/ic_speedometer_needle" />

                <!-- Speed value -->
                <TextView

                    android:id="@+id/speedometer_value"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"

                    android:layout_gravity="center_horizontal|bottom"
                    android:layout_marginBottom="16dp"
                    android:fontFamily="sans-serif-medium"
                    android:text="0%"
                    android:textColor="@color/vibrant_blue"
                    android:textSize="24sp" />
            </FrameLayout>

            <!-- Progress bar -->
            <ProgressBar
                android:id="@+id/popup_progress_bar"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:max="100"
                android:progress="0"
                android:progressDrawable="@drawable/progress_bar_gradient" />

            <!-- Progress percentage -->
            <TextView
                android:id="@+id/popup_progress_percent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:fontFamily="sans-serif-medium"
                android:gravity="end"
                android:text="0%"
                android:textColor="@color/vibrant_blue"
                android:textSize="16sp" />
        </LinearLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView>
