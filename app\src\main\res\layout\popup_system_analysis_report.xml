<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="16dp"
    app:cardBackgroundColor="#0f2027"
    app:cardCornerRadius="20dp"
    app:cardElevation="12dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/gaming_popup_background"
        android:orientation="vertical"
        android:padding="20dp">

        <!-- Header with gaming icon -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingBottom="20dp">

            <ImageView
                android:layout_width="56dp"
                android:layout_height="56dp"
                android:contentDescription="@string/app_name"
                android:src="@drawable/ic_gaming_performance"
                app:tint="@color/cyan_accent" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:orientation="vertical">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/orbitron_bold"
                    android:text="@string/gaming_performance_report"
                    android:textColor="@color/cyan_accent"
                    android:textSize="22sp"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:fontFamily="@font/cairo_regular"
                    android:text="@string/device_gaming_analysis"
                    android:textColor="@color/text_color_secondary"
                    android:textSize="14sp" />
            </LinearLayout>
        </LinearLayout>

        <!-- Gaming Performance Score -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/gaming_score_background"
            android:gravity="center"
            android:orientation="horizontal"
            android:padding="16dp">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/cairo_regular"
                    android:text="@string/gaming_score"
                    android:textColor="@color/text_color_secondary"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/gaming_score_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/orbitron_bold"
                    android:text="85/100"
                    android:textColor="@color/vibrant_green"
                    android:textSize="28sp"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/cairo_regular"
                    android:text="@string/excellent_gaming"
                    android:textColor="@color/vibrant_green"
                    android:textSize="12sp" />
            </LinearLayout>

            <View
                android:layout_width="1dp"
                android:layout_height="60dp"
                android:background="@color/cyan_accent"
                android:alpha="0.3" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:src="@drawable/ic_gaming_trophy"
                    app:tint="@color/vibrant_gold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:fontFamily="@font/cairo_regular"
                    android:text="@string/gaming_ready"
                    android:textColor="@color/vibrant_gold"
                    android:textSize="12sp"
                    android:textStyle="bold" />
            </LinearLayout>
        </LinearLayout>

        <!-- Gaming Recommendations -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:fontFamily="@font/orbitron_bold"
            android:text="@string/gaming_recommendations"
            android:textColor="@color/cyan_accent"
            android:textSize="16sp"
            android:textStyle="bold" />

        <!-- Recommendations List -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:orientation="vertical">

            <!-- Free Fire Recommendation -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:background="@drawable/gaming_recommendation_background"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="12dp">

                <ImageView
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:src="@drawable/ic_gaming_controller"
                    app:tint="@color/vibrant_blue" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/cairo_regular"
                        android:text="@string/free_fire_settings"
                        android:textColor="@color/text_color_primary"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/free_fire_recommendation"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/cairo_regular"
                        android:text="@string/high_graphics_recommended"
                        android:textColor="@color/vibrant_green"
                        android:textSize="12sp" />
                </LinearLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/gaming_tag_background"
                    android:fontFamily="@font/cairo_regular"
                    android:padding="6dp"
                    android:text="@string/optimal"
                    android:textColor="@color/dark_background"
                    android:textSize="10sp" />
            </LinearLayout>

            <!-- PUBG Recommendation -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:background="@drawable/gaming_recommendation_background"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="12dp">

                <ImageView
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:src="@drawable/ic_gaming_fps"
                    app:tint="@color/vibrant_orange" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/cairo_regular"
                        android:text="@string/fps_gaming"
                        android:textColor="@color/text_color_primary"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/fps_recommendation"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/cairo_regular"
                        android:text="@string/smooth_fps_expected"
                        android:textColor="@color/vibrant_green"
                        android:textSize="12sp" />
                </LinearLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/gaming_tag_background"
                    android:fontFamily="@font/cairo_regular"
                    android:padding="6dp"
                    android:text="@string/excellent"
                    android:textColor="@color/dark_background"
                    android:textSize="10sp" />
            </LinearLayout>
        </LinearLayout>

        <!-- Gaming Tips -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:fontFamily="@font/orbitron_bold"
            android:text="@string/gaming_tips"
            android:textColor="@color/cyan_accent"
            android:textSize="16sp"
            android:textStyle="bold" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:background="@drawable/gaming_tips_background"
            android:fontFamily="@font/cairo_regular"
            android:padding="12dp"
            android:text="@string/gaming_tips_content"
            android:textColor="@color/text_color_secondary"
            android:textSize="13sp" />

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_headshot_tool"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:background="@drawable/gaming_button_secondary"
                android:fontFamily="@font/cairo_regular"
                android:text="@string/headshot_tool"
                android:textColor="@color/cyan_accent"
                android:textSize="12sp" />

            <Button
                android:id="@+id/popup_done_button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_weight="1"
                android:background="@drawable/button_cyan_gradient"
                android:fontFamily="@font/cairo_regular"
                android:text="@string/got_it"
                android:textColor="@color/dark_background"
                android:textSize="12sp" />
        </LinearLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView>
