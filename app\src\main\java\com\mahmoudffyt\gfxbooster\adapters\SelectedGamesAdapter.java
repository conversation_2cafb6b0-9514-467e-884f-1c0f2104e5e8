package com.mahmoudffyt.gfxbooster.adapters;

import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.mahmoudffyt.gfxbooster.R;
import com.mahmoudffyt.gfxbooster.models.SelectedGame;

import java.util.List;

/**
 * Adapter for displaying selected games in RecyclerView
 */
public class SelectedGamesAdapter extends RecyclerView.Adapter<SelectedGamesAdapter.GameViewHolder> {

    private Context context;
    private List<SelectedGame> games;
    private OnGameActionListener listener;

    public interface OnGameActionListener {
        void onGameRemoved(SelectedGame game, int position);
        void onGameLaunched(SelectedGame game);
    }

    public SelectedGamesAdapter(Context context, List<SelectedGame> games) {
        this.context = context;
        this.games = games;
    }

    public void setOnGameActionListener(OnGameActionListener listener) {
        this.listener = listener;
    }

    @NonNull
    @Override
    public GameViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_selected_game, parent, false);
        return new GameViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull GameViewHolder holder, int position) {
        SelectedGame game = games.get(position);
        
        holder.gameName.setText(game.getAppName());
        holder.gamePackage.setText(game.getPackageName());
        holder.gameIcon.setImageDrawable(game.getAppIcon());

        // Launch button - show/hide based on game mode status
        if (launchButtonsVisible) {
            holder.launchButton.setVisibility(View.VISIBLE);

            // Launch button click
            holder.launchButton.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onGameLaunched(game);
                } else {
                    launchGame(game);
                }
            });

            // Check if game is still installed
            if (!isGameInstalled(game.getPackageName())) {
                holder.launchButton.setEnabled(false);
                holder.launchButton.setText(context.getString(R.string.game_not_installed));
                holder.launchButton.setAlpha(0.5f);
            } else {
                holder.launchButton.setEnabled(true);
                holder.launchButton.setText(context.getString(R.string.launch_game));
                holder.launchButton.setAlpha(1.0f);
            }
        } else {
            holder.launchButton.setVisibility(View.GONE);
        }

        // Remove button click
        holder.removeButton.setOnClickListener(v -> {
            if (listener != null) {
                listener.onGameRemoved(game, position);
            }
        });
    }

    @Override
    public int getItemCount() {
        return games.size();
    }

    private boolean isGameInstalled(String packageName) {
        try {
            context.getPackageManager().getPackageInfo(packageName, 0);
            return true;
        } catch (PackageManager.NameNotFoundException e) {
            return false;
        }
    }

    private void launchGame(SelectedGame game) {
        try {
            Intent launchIntent = context.getPackageManager().getLaunchIntentForPackage(game.getPackageName());
            if (launchIntent != null) {
                context.startActivity(launchIntent);
                Toast.makeText(context, context.getString(R.string.game_launched), Toast.LENGTH_SHORT).show();
            } else {
                Toast.makeText(context, context.getString(R.string.game_not_installed), Toast.LENGTH_SHORT).show();
            }
        } catch (Exception e) {
            Toast.makeText(context, "Error launching game: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }

    public void updateGames(List<SelectedGame> newGames) {
        this.games = newGames;
        notifyDataSetChanged();
    }

    private boolean launchButtonsVisible = false;

    public void setLaunchButtonsVisible(boolean visible) {
        this.launchButtonsVisible = visible;
    }

    public void removeGame(int position) {
        if (position >= 0 && position < games.size()) {
            games.remove(position);
            notifyItemRemoved(position);
            notifyItemRangeChanged(position, games.size());
        }
    }

    public void addGame(SelectedGame game) {
        games.add(game);
        notifyItemInserted(games.size() - 1);
    }

    static class GameViewHolder extends RecyclerView.ViewHolder {
        ImageView gameIcon;
        TextView gameName;
        TextView gamePackage;
        Button launchButton;
        ImageButton removeButton;

        public GameViewHolder(@NonNull View itemView) {
            super(itemView);
            gameIcon = itemView.findViewById(R.id.game_icon);
            gameName = itemView.findViewById(R.id.game_name);
            gamePackage = itemView.findViewById(R.id.game_package);
            launchButton = itemView.findViewById(R.id.launch_button);
            removeButton = itemView.findViewById(R.id.remove_button);
        }
    }
}
