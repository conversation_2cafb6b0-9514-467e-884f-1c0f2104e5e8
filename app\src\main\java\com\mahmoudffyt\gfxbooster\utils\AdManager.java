package com.mahmoudffyt.gfxbooster.utils;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.net.Uri;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.provider.Settings;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AlertDialog;
import androidx.cardview.widget.CardView;

import java.security.MessageDigest;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

import androidx.annotation.NonNull;

import com.mahmoudffyt.gfxbooster.R;
//import com.game.headshot.R;
import com.google.android.gms.ads.AdError;
import com.google.android.gms.ads.AdListener;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.AdSize;
import com.google.android.gms.ads.AdView;
import com.google.android.gms.ads.FullScreenContentCallback;
import com.google.android.gms.ads.LoadAdError;
import com.google.android.gms.ads.MobileAds;
import com.google.android.gms.ads.OnUserEarnedRewardListener;
import com.google.android.gms.ads.RequestConfiguration;
import com.google.android.gms.ads.initialization.AdapterStatus;
import com.google.android.gms.ads.initialization.InitializationStatus;
import com.google.android.gms.ads.initialization.OnInitializationCompleteListener;
import com.google.android.gms.ads.interstitial.InterstitialAd;
import com.google.android.gms.ads.interstitial.InterstitialAdLoadCallback;
import com.google.android.gms.ads.rewarded.RewardedAd;
import com.google.android.gms.ads.rewarded.RewardedAdLoadCallback;
import com.google.android.gms.ads.rewardedinterstitial.RewardedInterstitialAd;
import com.google.android.gms.ads.rewardedinterstitial.RewardedInterstitialAdLoadCallback;
import com.google.android.gms.ads.appopen.AppOpenAd;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * AdManager - Optimized class for managing all AdMob ads in the application
 */
public class AdManager {
    private static final String TAG = "AdManager";

    // App ID
    public static final String APP_ID = "ca-app-pub-4043804233099568~1048346990";

    // Ad Unit IDs
    public static final String BANNER_AD_UNIT_ID = "ca-app-pub-4043804233099568/1595141908";
    public static final String BANNER_MEDIUM_RECTANGLE_AD_UNIT_ID = "ca-app-pub-4043804233099568/1595141908";
    public static final String BANNER_LARGE_AD_UNIT_ID = "ca-app-pub-4043804233099568/1595141908";
    public static final String INTERSTITIAL_AD_UNIT_ID = "ca-app-pub-4043804233099568/2424991935";
    public static final String REWARDED_INTERSTITIAL_AD_UNIT_ID = "ca-app-pub-4043804233099568/7165957386";
    public static final String REWARDED_AD_UNIT_ID = "ca-app-pub-4043804233099568/6890850558";
    public static final String APP_OPEN_AD_UNIT_ID = "ca-app-pub-4043804233099568/2832201954";

//    // Test Ad Unit IDs for development
//    public static final String TEST_BANNER_AD_UNIT_ID = "ca-app-pub-3940256099942544/6300978111";
//    public static final String TEST_INTERSTITIAL_AD_UNIT_ID = "ca-app-pub-3940256099942544/1033173712";
//    public static final String TEST_REWARDED_INTERSTITIAL_AD_UNIT_ID = "ca-app-pub-3940256099942544/5354046379";
//    public static final String TEST_REWARDED_AD_UNIT_ID = "ca-app-pub-3940256099942544/5224354917";
//    public static final String TEST_APP_OPEN_AD_UNIT_ID = "ca-app-pub-3940256099942544/3419835294";

    // Ad instances
    private static InterstitialAd mInterstitialAd;
    private static RewardedAd mRewardedAd;
    private static RewardedInterstitialAd mRewardedInterstitialAd;

    // Flag to use test ads during development
    private static final boolean USE_TEST_ADS = false;

    // Singleton instance
    private static AdManager instance;

    // Premium manager reference
    private PremiumManager premiumManager;

    // Flag to prevent showing multiple ads at the same time
    private AtomicBoolean isAdShowing = new AtomicBoolean(false);

    // Flag to indicate WebView availability (checked only once)
    private AtomicBoolean webViewAvailable = new AtomicBoolean(true);

    // Counter for interstitial ads shown in a session
    private int interstitialCount = 0;

    // Thread pool for background operations
    private ExecutorService executorService;

    // Handler for main thread operations
    private Handler mainHandler;

    // Shorter timeout for ad requests
    private static final int HTTP_TIMEOUT_MS = 5000; // Reduced from 10000 to 5000

    // Load status tracking
    private AtomicBoolean interstitialLoading = new AtomicBoolean(false);
    private AtomicBoolean rewardedLoading = new AtomicBoolean(false);
    private AtomicBoolean rewardedInterstitialLoading = new AtomicBoolean(false);

    // Callback interfaces
    public interface InterstitialAdCallback {
        void onAdClosed();
        void onAdFailed();
    }
// Add these overloaded methods for banner ads
    /**
     * Load and display a medium rectangle banner ad with specific ad unit ID
     * @param activity Activity where the banner will be displayed
     * @param adContainer ViewGroup container for the banner
     * @param adUnitId Ad unit ID to use
     */
    public void loadMediumRectangleBannerAd(Activity activity, ViewGroup adContainer, String adUnitId) {
        loadBannerAd(activity, adContainer, AdSize.MEDIUM_RECTANGLE, adUnitId);
    }

    /**
     * Load and display a large banner ad with specific ad unit ID
     * @param activity Activity where the banner will be displayed
     * @param adContainer ViewGroup container for the banner
     * @param adUnitId Ad unit ID to use
     */
    public void loadLargeBannerAd(Activity activity, ViewGroup adContainer, String adUnitId) {
        loadBannerAd(activity, adContainer, AdSize.LARGE_BANNER, adUnitId);
    }

    /**
     * Check WebView availability (public method for use by application)
     * @param context Application context
     * @return true if WebView is available
     */
    public boolean checkWebViewAvailability(Context context) {
        boolean isAvailable = isWebViewAvailable(context);
        webViewAvailable.set(isAvailable);
        return isAvailable;
    }

    /**
     * Check if the user is premium
     * @return true if user is premium
     */
    public boolean isPremiumUser() {
        return premiumManager.isPremium();
    }

    /**
     * Check if WebView is unavailable
     * @return true if WebView is unavailable
     */
    public boolean isWebViewUnavailable() {
        return !webViewAvailable.get();
    }

    /**
     * Check if interstitial ad is ready to be shown
     * @return true if interstitial ad is ready
     */
    public boolean isInterstitialAdReady() {
        return mInterstitialAd != null;
    }

    /**
     * Check if rewarded ad is ready to be shown
     * @return true if rewarded ad is ready
     */
    public boolean isRewardedAdReady() {
        return mRewardedAd != null;
    }

    /**
     * Check if rewarded interstitial ad is ready to be shown
     * @return true if rewarded interstitial ad is ready
     */
    public boolean isRewardedInterstitialAdReady() {
        return mRewardedInterstitialAd != null;
    }


    public interface RewardedAdCallback {
        void onRewarded(boolean success);
        void onAdClosed();
        void onAdFailed();
    }

    public interface RewardedInterstitialAdCallback {
        void onRewarded(boolean success);
        void onAdClosed();
        void onAdFailed();
    }

    public interface AppOpenAdCallback {
        void onAdClosed();
        void onAdFailed();
    }

    /**
     * Get the singleton instance of AdManager
     * @param context Application context
     * @return AdManager instance
     */
    public static synchronized AdManager getInstance(Context context) {
        if (instance == null) {
            instance = new AdManager(context);
        }
        return instance;
    }

    /**
     * Private constructor to enforce singleton pattern
     * @param context Application context
     */
    private AdManager(Context context) {
        premiumManager = new PremiumManager(context);
        executorService = Executors.newCachedThreadPool();
        mainHandler = new Handler(Looper.getMainLooper());

        // Check WebView availability once during initialization
        checkWebViewAvailability(context);

        // Initialize AdMob (only once)
        initializeAdMob(context);
    }

    /**
     * Check WebView availability once during initialization
     * @param context Application context
     */
//    private void checkWebViewAvailability(Context context) {
//        executorService.execute(() -> {
//            boolean isAvailable = isWebViewAvailable(context);
//            webViewAvailable.set(isAvailable);
//
//            if (!isAvailable) {
//                Log.e(TAG, "WebView is not available on this device. Ad functionality will be limited.");
//                mainHandler.post(() -> {
//                    Toast.makeText(context, R.string.webview_not_available, Toast.LENGTH_SHORT).show();
//                });
//            }
//        });
//    }

    /**
     * Reset interstitial count
     */
    public void resetInterstitialCount() {
        interstitialCount = 0;
    }

    /**
     * Get current interstitial count
     */
    public int getInterstitialCount() {
        return interstitialCount;
    }

    /**
     * Initialize AdMob SDK with fallback mechanisms
     * @param context Application context
     */
    public void initializeAdMob(Context context) {
        Log.d(TAG, "Initializing AdMob SDK");

        executorService.execute(() -> {
            try {
                // Check WebView availability first
                boolean isWebViewAvailable = checkWebViewAvailability(context);

                if (!isWebViewAvailable) {
                    Log.e(TAG, "WebView is not available. Trying alternative initialization approach.");
                    tryAlternativeAdMobInitialization(context);
                    return;
                }

                // Set up test device IDs
//                List<String> testDeviceIds = new ArrayList<>();
//                testDeviceIds.add("ABCDEF012345"); // Default test device
//                String deviceId = getDeviceId(context);
//                if (deviceId != null && !deviceId.isEmpty()) {
//                    testDeviceIds.add(deviceId);
 //               }

                // Configure AdMob with test devices
                RequestConfiguration configuration = new RequestConfiguration.Builder()
                       // .setTestDeviceIds(testDeviceIds)
                        .build();

                mainHandler.post(() -> {
                    MobileAds.setRequestConfiguration(configuration);

                    // Initialize AdMob
                    MobileAds.initialize(context, new OnInitializationCompleteListener() {
                        @Override
                        public void onInitializationComplete(InitializationStatus initializationStatus) {
                            Log.d(TAG, "AdMob SDK initialized successfully");

                            // Check if initialization was successful
                            boolean anyAdapterFailed = false;
                            Map<String, AdapterStatus> statusMap = initializationStatus.getAdapterStatusMap();

                            for (String adapterClass : statusMap.keySet()) {
                                AdapterStatus status = statusMap.get(adapterClass);
                                if (status != null) {
                                    Log.d(TAG, String.format(
                                            "Adapter: %s, Status: %s, Description: %s",
                                            adapterClass, status.getInitializationState(), status.getDescription()));

                                    // Check if any adapter has errors
                                    if (status.getInitializationState() == AdapterStatus.State.NOT_READY) {
                                        anyAdapterFailed = true;
                                    }
                                }
                            }

                            // If there are errors in initialization, try alternative approach
                            if (anyAdapterFailed) {
                                Log.w(TAG, "Some AdMob adapters failed to initialize. Trying alternative approach.");
                                tryAlternativeAdMobInitialization(context);
                                return;
                            }

                            // Only preload interstitial ads initially (higher priority)
                            if (webViewAvailable.get() && !premiumManager.isPremium()) {
                                preloadInterstitialAd(context);

                                // Schedule rewarded ad preloading with slight delay
                                mainHandler.postDelayed(() -> preloadRewardedAd(context), 1000);

                                // Schedule rewarded interstitial ad preloading with more delay
                                mainHandler.postDelayed(() -> preloadRewardedInterstitialAd(context), 2000);
                            }
                        }
                    });
                });
            } catch (Exception e) {
                Log.e(TAG, "Error initializing AdMob: " + e.getMessage());
                tryAlternativeAdMobInitialization(context);
            }
        });
    }

    /**
     * Try alternative approach to initialize AdMob
     * @param context Application context
     */
    private void tryAlternativeAdMobInitialization(Context context) {
        try {
            Log.d(TAG, "Trying alternative AdMob initialization");

            // Approach 1: Initialize with minimal configuration
            mainHandler.post(() -> {
                try {
                    // Use minimal configuration
                    RequestConfiguration minimalConfig = new RequestConfiguration.Builder().build();
                    MobileAds.setRequestConfiguration(minimalConfig);

                    // Initialize with minimal settings
                    MobileAds.initialize(context, new OnInitializationCompleteListener() {
                        @Override
                        public void onInitializationComplete(@NonNull InitializationStatus initializationStatus) {
                            Log.d(TAG, "Alternative AdMob initialization complete");

                            // Check if initialization was successful
                            boolean anyAdapterReady = false;
                            Map<String, AdapterStatus> statusMap = initializationStatus.getAdapterStatusMap();

                            for (String adapterClass : statusMap.keySet()) {
                                AdapterStatus status = statusMap.get(adapterClass);
                                if (status != null && status.getInitializationState() == AdapterStatus.State.READY) {
                                    anyAdapterReady = true;
                                    break;
                                }
                            }

                            // Update WebView availability based on initialization result
                            webViewAvailable.set(anyAdapterReady);

                            if (!anyAdapterReady) {
                                Log.e(TAG, "Alternative AdMob initialization failed. No adapters ready.");
                                tryFallbackAdLoading(context);
                            } else {
                                // Try to preload some ads with delay
                                if (!premiumManager.isPremium()) {
                                    mainHandler.postDelayed(() -> preloadInterstitialAd(context), 2000);
                                }
                            }
                        }
                    });
                } catch (Exception e) {
                    Log.e(TAG, "Error in alternative AdMob initialization: " + e.getMessage());
                    webViewAvailable.set(false);
                    tryFallbackAdLoading(context);
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "Error in alternative AdMob initialization: " + e.getMessage());
            webViewAvailable.set(false);
        }
    }

    /**
     * Last resort method to enable some ad functionality without WebView
     * @param context Application context
     */
    private void tryFallbackAdLoading(Context context) {
        Log.d(TAG, "Trying fallback ad loading mechanism");

        // Set flag to use placeholders instead of real ads
        webViewAvailable.set(false);

        // Schedule periodic checks for WebView availability
        mainHandler.postDelayed(() -> {
            // Try to check WebView availability again after some time
            boolean webViewNowAvailable = checkWebViewAvailability(context);

            if (webViewNowAvailable) {
                // WebView is now available, reinitialize AdMob
                Log.d(TAG, "WebView is now available. Reinitializing AdMob.");
                initializeAdMob(context);
            } else {
                // Schedule another check later
                mainHandler.postDelayed(() -> tryFallbackAdLoading(context), 60000); // Check every minute
            }
        }, 30000); // First check after 30 seconds
    }

    /**
     * Load and display a banner ad
     * @param activity Activity where the banner will be displayed
     * @param adContainer ViewGroup container for the banner
     */
    public void loadBannerAd(Activity activity, ViewGroup adContainer) {
        loadBannerAd(activity, adContainer, AdSize.BANNER, BANNER_AD_UNIT_ID);
    }

    /**
     * Load and display a banner ad with specific ad unit ID
     * @param activity Activity where the banner will be displayed
     * @param adContainer ViewGroup container for the banner
     * @param adUnitId Ad unit ID to use
     */
    public void loadBannerAd(Activity activity, ViewGroup adContainer, String adUnitId) {
        loadBannerAd(activity, adContainer, AdSize.BANNER, adUnitId);
    }

    /**
     * Load and display a medium rectangle banner ad (300x250)
     * @param activity Activity where the banner will be displayed
     * @param adContainer ViewGroup container for the banner
     */
    public void loadMediumRectangleBannerAd(Activity activity, ViewGroup adContainer) {
        loadBannerAd(activity, adContainer, AdSize.MEDIUM_RECTANGLE, BANNER_MEDIUM_RECTANGLE_AD_UNIT_ID);
    }

    /**
     * Load and display a large banner ad (320x100)
     * @param activity Activity where the banner will be displayed
     * @param adContainer ViewGroup container for the banner
     */
    public void loadLargeBannerAd(Activity activity, ViewGroup adContainer) {
        loadBannerAd(activity, adContainer, AdSize.LARGE_BANNER, BANNER_LARGE_AD_UNIT_ID);
    }

    /**
     * Load and display a banner ad with specific size
     * @param activity Activity where the banner will be displayed
     * @param adContainer ViewGroup container for the banner
     * @param adSize Size of the banner ad
     * @param adUnitId Ad unit ID to use
     */
    private void loadBannerAd(Activity activity, ViewGroup adContainer, AdSize adSize, String adUnitId) {
        // Skip ads for premium users
        if (premiumManager.isPremium()) {
            adContainer.setVisibility(View.GONE);
            return;
        }

        // Check if container is valid
        if (adContainer == null) {
            return;
        }

        // Store ad size in container's tag for potential retry
        adContainer.setTag(adSize);

        // Try alternative ad loading if WebView is not available
        if (!webViewAvailable.get()) {
            // Try to check WebView availability again
            boolean webViewNowAvailable = checkWebViewAvailability(activity);

            if (!webViewNowAvailable) {
                // WebView is still not available, show placeholder
                showAdPlaceholder(activity, adContainer);
                return;
            }

            // WebView is now available, update the flag
            webViewAvailable.set(true);
        }

        try {
            adContainer.setVisibility(View.VISIBLE);
            adContainer.setMinimumHeight(150);

            AdView adView = new AdView(activity);
            String finalAdUnitId = USE_TEST_ADS ? BANNER_AD_UNIT_ID : adUnitId;
            adView.setAdUnitId(finalAdUnitId);
            adView.setAdSize(adSize);

            adContainer.removeAllViews();
            adContainer.addView(adView);

            // Create optimized ad request with shorter timeout
            AdRequest adRequest = new AdRequest.Builder()
                    .setHttpTimeoutMillis(HTTP_TIMEOUT_MS)
                    .build();

            // Set a shorter timeout handler for banner ads
            final Handler timeoutHandler = new Handler(Looper.getMainLooper());
            final Runnable timeoutRunnable = new Runnable() {
                @Override
                public void run() {
                    if (adContainer.getChildCount() > 0 && adContainer.getChildAt(0) instanceof AdView) {
                        // Try alternative loading method before showing placeholder
                        tryAlternativeAdLoading(activity, adContainer, adSize, adUnitId);
                    }
                }
            };

            // Set timeout for 2 seconds
            timeoutHandler.postDelayed(timeoutRunnable, 2000);

            adView.setAdListener(new AdListener() {
                @Override
                public void onAdLoaded() {
                    timeoutHandler.removeCallbacks(timeoutRunnable);
                    adContainer.setVisibility(View.VISIBLE);
                    adContainer.setBackgroundColor(Color.TRANSPARENT);

                    // Ad loaded successfully, update WebView availability flag
                    webViewAvailable.set(true);
                }

                @Override
                public void onAdFailedToLoad(@NonNull LoadAdError loadAdError) {
                    timeoutHandler.removeCallbacks(timeoutRunnable);

                    // Log the error
                    Log.e(TAG, "Banner ad failed to load: " + loadAdError.getMessage() +
                            " (Code: " + loadAdError.getCode() + ")");

                    // Try alternative loading method
                    tryAlternativeAdLoading(activity, adContainer, adSize, adUnitId);
                }
            });

            // Load the ad
            adView.loadAd(adRequest);

        } catch (Exception e) {
            Log.e(TAG, "Error loading banner ad: " + e.getMessage());
            tryAlternativeAdLoading(activity, adContainer, adSize, adUnitId);
        }
    }

    /**
     * Try alternative methods to load ads when the primary method fails
     * @param activity Activity where the ad will be displayed
     * @param adContainer ViewGroup container for the ad
     * @param adSize Size of the ad
     * @param adUnitId Ad unit ID to use
     */
    private void tryAlternativeAdLoading(Activity activity, ViewGroup adContainer, AdSize adSize, String adUnitId) {
        try {
            // Method 1: Try with a different ad request configuration
            if (tryAlternativeAdRequest(activity, adContainer, adSize, adUnitId)) {
                return;
            }

            // Method 2: Try with a different ad unit ID
            if (tryBackupAdUnit(activity, adContainer, adSize)) {
                return;
            }

            // If all alternative methods fail, show placeholder
            showAdPlaceholder(activity, adContainer);

        } catch (Exception e) {
            Log.e(TAG, "Error in alternative ad loading: " + e.getMessage());
            showAdPlaceholder(activity, adContainer);
        }
    }

    /**
     * Try loading ad with alternative request configuration
     * @return true if the attempt was made (not necessarily successful)
     */
    private boolean tryAlternativeAdRequest(Activity activity, ViewGroup adContainer, AdSize adSize, String adUnitId) {
        try {
            Log.d(TAG, "Trying alternative ad request configuration");

            AdView adView = new AdView(activity);
            String finalAdUnitId = USE_TEST_ADS ? BANNER_AD_UNIT_ID : adUnitId;
            adView.setAdUnitId(finalAdUnitId);
            adView.setAdSize(adSize);

            adContainer.removeAllViews();
            adContainer.addView(adView);

            // Create a simpler ad request with different configuration
            AdRequest adRequest = new AdRequest.Builder()
                    .setHttpTimeoutMillis(HTTP_TIMEOUT_MS * 2) // Double timeout
                    .build();

            // Set a longer timeout for this alternative attempt
            final Handler timeoutHandler = new Handler(Looper.getMainLooper());
            final Runnable timeoutRunnable = new Runnable() {
                @Override
                public void run() {
                    if (adContainer.getChildCount() > 0 && adContainer.getChildAt(0) instanceof AdView) {
                        // Try next alternative method
                        tryBackupAdUnit(activity, adContainer, adSize);
                    }
                }
            };

            // Set longer timeout (3 seconds)
            timeoutHandler.postDelayed(timeoutRunnable, 3000);

            adView.setAdListener(new AdListener() {
                @Override
                public void onAdLoaded() {
                    timeoutHandler.removeCallbacks(timeoutRunnable);
                    adContainer.setVisibility(View.VISIBLE);
                    adContainer.setBackgroundColor(Color.TRANSPARENT);
                    Log.d(TAG, "Alternative ad request successful");
                }

                @Override
                public void onAdFailedToLoad(@NonNull LoadAdError loadAdError) {
                    timeoutHandler.removeCallbacks(timeoutRunnable);
                    Log.e(TAG, "Alternative ad request failed: " + loadAdError.getMessage());
                    tryBackupAdUnit(activity, adContainer, adSize);
                }
            });

            // Load the ad with alternative configuration
            adView.loadAd(adRequest);
            return true;

        } catch (Exception e) {
            Log.e(TAG, "Error in alternative ad request: " + e.getMessage());
            return false;
        }
    }

    /**
     * Try loading ad with backup ad unit ID
     * @return true if the attempt was made (not necessarily successful)
     */
    private boolean tryBackupAdUnit(Activity activity, ViewGroup adContainer, AdSize adSize) {
        try {
            Log.d(TAG, "Trying backup ad unit");

            // Always use test ad unit as backup for reliability
            String backupAdUnitId = BANNER_AD_UNIT_ID;

            AdView adView = new AdView(activity);
            adView.setAdUnitId(backupAdUnitId);
            adView.setAdSize(adSize);

            adContainer.removeAllViews();
            adContainer.addView(adView);

            // Create a simple ad request
            AdRequest adRequest = new AdRequest.Builder().build();

            // Set a timeout for this final attempt
            final Handler timeoutHandler = new Handler(Looper.getMainLooper());
            final Runnable timeoutRunnable = new Runnable() {
                @Override
                public void run() {
                    if (adContainer.getChildCount() > 0 && adContainer.getChildAt(0) instanceof AdView) {
                        showAdPlaceholder(activity, adContainer);
                    }
                }
            };

            // Set timeout (3 seconds)
            timeoutHandler.postDelayed(timeoutRunnable, 3000);

            adView.setAdListener(new AdListener() {
                @Override
                public void onAdLoaded() {
                    timeoutHandler.removeCallbacks(timeoutRunnable);
                    adContainer.setVisibility(View.VISIBLE);
                    adContainer.setBackgroundColor(Color.TRANSPARENT);
                    Log.d(TAG, "Backup ad unit successful");
                }

                @Override
                public void onAdFailedToLoad(@NonNull LoadAdError loadAdError) {
                    timeoutHandler.removeCallbacks(timeoutRunnable);
                    Log.e(TAG, "Backup ad unit failed: " + loadAdError.getMessage());
                    showAdPlaceholder(activity, adContainer);
                }
            });

            // Load the ad with backup ad unit
            adView.loadAd(adRequest);
            return true;

        } catch (Exception e) {
            Log.e(TAG, "Error in backup ad unit: " + e.getMessage());
            return false;
        }
    }

    /**
     * Check if WebView is available on the device using multiple methods
     * @param context Application context
     * @return true if WebView is available
     */
    private boolean isWebViewAvailable(Context context) {
        // Method 1: Try creating a WebView instance and check user agent
        boolean method1Result = checkWebViewByCreation(context);

        // Method 2: Check if WebView package is installed
        boolean method2Result = checkWebViewPackage(context);

        // Method 3: Check WebView version
        boolean method3Result = checkWebViewVersion(context);

        // Log results for debugging
        Log.d(TAG, "WebView availability check: Method1=" + method1Result +
                ", Method2=" + method2Result + ", Method3=" + method3Result);

        // If any method returns true, consider WebView available
        boolean isAvailable = method1Result || method2Result || method3Result;

        // If WebView is not available, show a dialog to help user fix it
        if (!isAvailable) {
            Log.e(TAG, "WebView is not available on this device. Ad functionality will be limited.");
            mainHandler.post(() -> {
                showWebViewNotAvailableDialog(context);
            });
        }

        return isAvailable;
    }

    /**
     * Method 1: Check WebView by creating an instance
     * @param context Application context
     * @return true if WebView can be created and initialized
     */
    private boolean checkWebViewByCreation(Context context) {
        try {
            // Try creating a WebView instance
            WebView webView = new WebView(context);
            try {
                // Check user agent
                WebSettings settings = webView.getSettings();
                String userAgent = settings.getUserAgentString();
                boolean hasValidUserAgent = userAgent != null && !userAgent.isEmpty();

                // Try accessing other WebView properties
                settings.setJavaScriptEnabled(true);
                settings.setJavaScriptEnabled(false);

                return hasValidUserAgent;
            } catch (Exception e) {
                Log.e(TAG, "Error checking WebView by creation: " + e.getMessage());
                return false;
            } finally {
                webView = null;
            }
        } catch (Exception e) {
            Log.e(TAG, "Error creating WebView instance: " + e.getMessage());
            return false;
        }
    }

    /**
     * Method 2: Check if WebView package is installed
     * @param context Application context
     * @return true if WebView package is installed
     */
    private boolean checkWebViewPackage(Context context) {
        try {
            // Check for standard WebView package
            PackageManager pm = context.getPackageManager();
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                // Android 8.0+ uses the WebView implementation from Google Play services
                return pm.getPackageInfo("com.google.android.webview", 0) != null;
            } else {
                // Try different WebView providers
                try {
                    pm.getPackageInfo("com.google.android.webview", 0);
                    return true;
                } catch (PackageManager.NameNotFoundException e) {
                    try {
                        pm.getPackageInfo("com.android.webview", 0);
                        return true;
                    } catch (PackageManager.NameNotFoundException e2) {
                        return false;
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error checking WebView package: " + e.getMessage());
            return false;
        }
    }

    /**
     * Method 3: Check WebView version
     * @param context Application context
     * @return true if WebView version can be determined
     */
    private boolean checkWebViewVersion(Context context) {
        try {
            // Try to get WebView version
            PackageManager pm = context.getPackageManager();
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                try {
                    return pm.getPackageInfo("com.google.android.webview", 0).versionName != null;
                } catch (Exception e) {
                    return false;
                }
            } else {
                return false; // Skip this check for older Android versions
            }
        } catch (Exception e) {
            Log.e(TAG, "Error checking WebView version: " + e.getMessage());
            return false;
        }
    }

    /**
     * Show a dialog to help user fix WebView issues
     * @param context Application context
     */
    private void showWebViewNotAvailableDialog(Context context) {
        try {
            if (context instanceof Activity) {
                Activity activity = (Activity) context;
                if (!activity.isFinishing()) {
                    AlertDialog.Builder builder = new AlertDialog.Builder(activity);
                    builder.setTitle(R.string.webview_not_available_title)
                           .setMessage(R.string.webview_not_available_message)
                           .setPositiveButton(R.string.open_settings, (dialog, which) -> {
                               try {
                                   // Open Google Play Store to WebView page
                                   Intent intent = new Intent(Intent.ACTION_VIEW);
                                   intent.setData(Uri.parse("market://details?id=com.google.android.webview"));
                                   activity.startActivity(intent);
                               } catch (Exception e) {
                                   Log.e(TAG, "Error opening settings: " + e.getMessage());
                                   Toast.makeText(activity, R.string.error_opening_settings, Toast.LENGTH_SHORT).show();
                               }
                           })
                           .setNegativeButton(android.R.string.cancel, null)
                           .setCancelable(true)
                           .show();
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error showing WebView not available dialog: " + e.getMessage());
        }
    }

    // Constants for placeholder types
    private static final int PLACEHOLDER_TYPE_SIMPLE = 0;
    private static final int PLACEHOLDER_TYPE_CARD = 1;
    private static final int PLACEHOLDER_TYPE_LOCAL_AD = 2;

    // Current placeholder type (can be changed to try different approaches)
    private int currentPlaceholderType = PLACEHOLDER_TYPE_CARD;

    // Local ad resources for fallback
    private static final int[] LOCAL_AD_RESOURCES = {
        R.drawable.blue_speed_lines, // Using existing app resources as placeholders
        R.drawable.cyber_neon_bg,
        R.drawable.game_booster_bg
    };

    /**
     * Show a placeholder when ads cannot be loaded
     * @param activity Activity where the placeholder will be displayed
     * @param adContainer ViewGroup container for the placeholder
     */
    private void showAdPlaceholder(Activity activity, ViewGroup adContainer) {
        try {
            adContainer.removeAllViews();

            // Choose placeholder type based on current setting
            switch (currentPlaceholderType) {
                case PLACEHOLDER_TYPE_CARD:
                    showCardPlaceholder(activity, adContainer);
                    break;
                case PLACEHOLDER_TYPE_LOCAL_AD:
                    showLocalAdPlaceholder(activity, adContainer);
                    break;
                case PLACEHOLDER_TYPE_SIMPLE:
                default:
                    showSimplePlaceholder(activity, adContainer);
                    break;
            }

            // Rotate placeholder type for next time
            currentPlaceholderType = (currentPlaceholderType + 1) % 3;

        } catch (Exception e) {
            Log.e(TAG, "Error showing ad placeholder: " + e.getMessage());
            showSimplePlaceholder(activity, adContainer); // Fallback to simple placeholder
        }
    }

    /**
     * Show a simple text placeholder
     * @param activity Activity where the placeholder will be displayed
     * @param adContainer ViewGroup container for the placeholder
     */
    private void showSimplePlaceholder(Activity activity, ViewGroup adContainer) {
        try {
            // Simple placeholder implementation
            TextView placeholderText = new TextView(activity);
            placeholderText.setText(R.string.ad_space);
            placeholderText.setGravity(Gravity.CENTER);
            placeholderText.setTextColor(Color.WHITE);
            placeholderText.setBackgroundColor(Color.parseColor("#33000000"));
            placeholderText.setPadding(0, 20, 0, 20);

            adContainer.addView(placeholderText);
            adContainer.setVisibility(View.VISIBLE);

            ViewGroup.LayoutParams params = placeholderText.getLayoutParams();
            params.width = ViewGroup.LayoutParams.MATCH_PARENT;
            params.height = 150;
            placeholderText.setLayoutParams(params);
        } catch (Exception e) {
            Log.e(TAG, "Error showing simple placeholder: " + e.getMessage());
        }
    }

    /**
     * Show a card-style placeholder with retry button
     * @param activity Activity where the placeholder will be displayed
     * @param adContainer ViewGroup container for the placeholder
     */
    private void showCardPlaceholder(Activity activity, ViewGroup adContainer) {
        try {
            // Create card container
            CardView cardView = new CardView(activity);
            cardView.setCardBackgroundColor(activity.getResources().getColor(R.color.cyber_card_bg));
            cardView.setRadius(16);
            cardView.setCardElevation(4);
            cardView.setUseCompatPadding(true);

            // Create content layout
            LinearLayout contentLayout = new LinearLayout(activity);
            contentLayout.setOrientation(LinearLayout.VERTICAL);
            contentLayout.setGravity(Gravity.CENTER);
            contentLayout.setPadding(16, 16, 16, 16);

            // Add message
            TextView messageText = new TextView(activity);
            messageText.setText(R.string.ad_loading_short);
            messageText.setGravity(Gravity.CENTER);
            messageText.setTextColor(Color.WHITE);
            messageText.setPadding(0, 8, 0, 16);

            // Add retry button
            Button retryButton = new Button(activity);
            retryButton.setText(R.string.retry);
            retryButton.setBackgroundResource(R.drawable.cyber_button_bg);
            retryButton.setTextColor(Color.WHITE);

            // Set retry action
            final Context appContext = activity.getApplicationContext();
            retryButton.setOnClickListener(v -> {
                // Try to reinitialize AdMob
                initializeAdMob(appContext);

                // Check WebView again
                boolean webViewAvailable = checkWebViewAvailability(appContext);

                if (webViewAvailable) {
                    Toast.makeText(activity, R.string.retrying_ad_load, Toast.LENGTH_SHORT).show();

                    // Try to reload the ad
                    if (adContainer.getTag() instanceof AdSize) {
                        AdSize adSize = (AdSize) adContainer.getTag();
                        loadBannerAd(activity, adContainer, adSize, BANNER_AD_UNIT_ID);
                    } else {
                        loadBannerAd(activity, adContainer);
                    }
                } else {
                    // WebView still not available
                    showWebViewNotAvailableDialog(activity);
                }
            });

            // Add views to layout
            contentLayout.addView(messageText);
            contentLayout.addView(retryButton);
            cardView.addView(contentLayout);

            // Add card to container
            adContainer.addView(cardView);
            adContainer.setVisibility(View.VISIBLE);

            // Set layout parameters
            ViewGroup.LayoutParams params = cardView.getLayoutParams();
            params.width = ViewGroup.LayoutParams.MATCH_PARENT;
            params.height = ViewGroup.LayoutParams.WRAP_CONTENT;
            cardView.setLayoutParams(params);

        } catch (Exception e) {
            Log.e(TAG, "Error showing card placeholder: " + e.getMessage());
            showSimplePlaceholder(activity, adContainer); // Fallback
        }
    }

    /**
     * Show a local ad image as placeholder
     * @param activity Activity where the placeholder will be displayed
     * @param adContainer ViewGroup container for the placeholder
     */
    private void showLocalAdPlaceholder(Activity activity, ViewGroup adContainer) {
        try {
            // Create card container for better appearance
            CardView cardView = new CardView(activity);
            cardView.setCardBackgroundColor(activity.getResources().getColor(R.color.cyber_card_bg));
            cardView.setRadius(8);
            cardView.setCardElevation(2);
            cardView.setUseCompatPadding(true);

            // Create image view for local ad
            ImageView adImageView = new ImageView(activity);
            adImageView.setScaleType(ImageView.ScaleType.CENTER_CROP);

            // Select random local ad image
            int randomIndex = (int) (Math.random() * LOCAL_AD_RESOURCES.length);
            adImageView.setImageResource(LOCAL_AD_RESOURCES[randomIndex]);

            // Add image to card
            cardView.addView(adImageView);

            // Add card to container
            adContainer.addView(cardView);
            adContainer.setVisibility(View.VISIBLE);

            // Set layout parameters
            ViewGroup.LayoutParams cardParams = cardView.getLayoutParams();
            cardParams.width = ViewGroup.LayoutParams.MATCH_PARENT;
            cardParams.height = 150;
            cardView.setLayoutParams(cardParams);

            ViewGroup.LayoutParams imageParams = adImageView.getLayoutParams();
            imageParams.width = ViewGroup.LayoutParams.MATCH_PARENT;
            imageParams.height = ViewGroup.LayoutParams.MATCH_PARENT;
            adImageView.setLayoutParams(imageParams);

        } catch (Exception e) {
            Log.e(TAG, "Error showing local ad placeholder: " + e.getMessage());
            showSimplePlaceholder(activity, adContainer); // Fallback
        }
    }

    /**
     * Get device ID for test ads
     * @param context Application context
     * @return Device ID or null if not available
     */
    private String getDeviceId(Context context) {
        try {
            String androidId = Settings.Secure.getString(context.getContentResolver(), Settings.Secure.ANDROID_ID);
            if (androidId != null && !androidId.isEmpty()) {
                try {
                    MessageDigest md = MessageDigest.getInstance("MD5");
                    md.update(androidId.getBytes());
                    byte[] digest = md.digest();
                    StringBuilder sb = new StringBuilder();
                    for (byte b : digest) {
                        sb.append(String.format("%02X", b));
                    }
                    return sb.toString();
                } catch (Exception e) {
                    Log.e(TAG, "Error creating device ID hash: " + e.getMessage());
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error getting device ID: " + e.getMessage());
        }
        return null;
    }

    /**
     * Preload an interstitial ad
     * @param context Application context
     */
    public void preloadInterstitialAd(Context context) {
        // Skip if already loading, or premium user, or WebView unavailable
        if (interstitialLoading.get() || premiumManager.isPremium() || !webViewAvailable.get()) {
            return;
        }

        interstitialLoading.set(true);

        try {
            // Create optimized ad request with shorter timeout
            AdRequest adRequest = new AdRequest.Builder()
                    .setHttpTimeoutMillis(HTTP_TIMEOUT_MS)
                    .build();

            String adUnitId = USE_TEST_ADS ? INTERSTITIAL_AD_UNIT_ID : INTERSTITIAL_AD_UNIT_ID;

            InterstitialAdLoadCallback adLoadCallback = new InterstitialAdLoadCallback() {
                @Override
                public void onAdLoaded(@NonNull InterstitialAd interstitialAd) {
                    mInterstitialAd = interstitialAd;
                    interstitialLoading.set(false);

                    mInterstitialAd.setFullScreenContentCallback(new FullScreenContentCallback() {
                        @Override
                        public void onAdDismissedFullScreenContent() {
                            mInterstitialAd = null;
                            // Delay preloading next ad to avoid immediate load
                            mainHandler.postDelayed(() -> preloadInterstitialAd(context), 500);
                        }

                        @Override
                        public void onAdFailedToShowFullScreenContent(AdError adError) {
                            mInterstitialAd = null;
                            // Delay preloading next ad
                            mainHandler.postDelayed(() -> preloadInterstitialAd(context), 1000);
                        }

                        @Override
                        public void onAdShowedFullScreenContent() {
                            mInterstitialAd = null;
                        }
                    });
                }

                @Override
                public void onAdFailedToLoad(@NonNull LoadAdError loadAdError) {
                    Log.e(TAG, "Interstitial ad failed to load: " + loadAdError.getMessage());
                    mInterstitialAd = null;
                    interstitialLoading.set(false);

                    // Only retry once with delay
                    mainHandler.postDelayed(() -> preloadInterstitialAd(context), 3000);
                }
            };

            // Load the interstitial ad
            InterstitialAd.load(context, adUnitId, adRequest, adLoadCallback);

        } catch (Exception e) {
            Log.e(TAG, "Error preloading interstitial ad: " + e.getMessage());
            interstitialLoading.set(false);
        }
    }

    /**
     * Show a preloaded interstitial ad
     * @param activity Activity where the ad will be displayed
     * @param callback Callback to be executed after the ad is shown or fails
     */
    public void showInterstitialAd(Activity activity, InterstitialAdCallback callback) {
        // Skip ads for premium users
        if (premiumManager.isPremium()) {
            if (callback != null) {
                callback.onAdClosed();
            }
            return;
        }

        // Check WebView availability
        if (!webViewAvailable.get()) {
            if (callback != null) {
                callback.onAdFailed();
            }
            return;
        }

        // Skip if another ad is already showing
        if (isAdShowing.get()) {
            if (callback != null) {
                callback.onAdFailed();
            }
            return;
        }

        // Increment interstitial count
        interstitialCount++;

        if (mInterstitialAd != null) {
            // Set the ad as showing
            isAdShowing.set(true);

            mInterstitialAd.setFullScreenContentCallback(new FullScreenContentCallback() {
                @Override
                public void onAdDismissedFullScreenContent() {
                    isAdShowing.set(false);

                    if (callback != null) {
                        callback.onAdClosed();
                    }

                    // Preload next ad with delay
                    mainHandler.postDelayed(() -> preloadInterstitialAd(activity), 500);
                }

                @Override
                public void onAdFailedToShowFullScreenContent(AdError adError) {
                    Log.e(TAG, "Interstitial ad failed to show: " + adError.getMessage());
                    mInterstitialAd = null;
                    isAdShowing.set(false);

                    if (callback != null) {
                        callback.onAdFailed();
                    }

                    // Preload next ad immediately
                    preloadInterstitialAd(activity);
                }

                @Override
                public void onAdShowedFullScreenContent() {
                    mInterstitialAd = null;
                }
            });

            mInterstitialAd.show(activity);

        } else {
            // Try to load a new ad if not already loading
            if (!interstitialLoading.get()) {
                preloadInterstitialAd(activity);
            }

            if (callback != null) {
                callback.onAdFailed();
            }
        }
    }

    /**
     * Preload a rewarded ad
     * @param context Application context
     */
    public void preloadRewardedAd(Context context) {
        // Skip if already loading, or premium user, or WebView unavailable
        if (rewardedLoading.get() || premiumManager.isPremium() || !webViewAvailable.get()) {
            return;
        }

        rewardedLoading.set(true);

        try {
            // Create optimized ad request with shorter timeout
            AdRequest adRequest = new AdRequest.Builder()
                    .setHttpTimeoutMillis(HTTP_TIMEOUT_MS)
                    .build();

            String adUnitId = USE_TEST_ADS ? REWARDED_AD_UNIT_ID : REWARDED_AD_UNIT_ID;

            RewardedAdLoadCallback adLoadCallback = new RewardedAdLoadCallback() {
                @Override
                public void onAdLoaded(@NonNull RewardedAd rewardedAd) {
                    mRewardedAd = rewardedAd;
                    rewardedLoading.set(false);
                }

                @Override
                public void onAdFailedToLoad(@NonNull LoadAdError loadAdError) {
                    Log.e(TAG, "Rewarded ad failed to load: " + loadAdError.getMessage());
                    mRewardedAd = null;
                    rewardedLoading.set(false);

                    // Only retry once with delay
                    mainHandler.postDelayed(() -> preloadRewardedAd(context), 5000);
                }
            };

            // Load the rewarded ad
            RewardedAd.load(context, adUnitId, adRequest, adLoadCallback);

        } catch (Exception e) {
            Log.e(TAG, "Error preloading rewarded ad: " + e.getMessage());
            rewardedLoading.set(false);
        }
    }

    /**
     * Show a preloaded rewarded ad
     * @param activity Activity where the ad will be displayed
     * @param callback Callback to be executed after the ad is shown or fails
     */
    public void showRewardedAd(Activity activity, RewardedAdCallback callback) {
        // Skip ads for premium users
        if (premiumManager.isPremium()) {
            if (callback != null) {
                callback.onRewarded(true);
            }
            return;
        }

        // Check WebView availability
        if (!webViewAvailable.get()) {
            if (callback != null) {
                callback.onAdFailed();
            }
            return;
        }

        // Skip if another ad is already showing
        if (isAdShowing.get()) {
            if (callback != null) {
                callback.onAdFailed();
            }
            return;
        }

        if (mRewardedAd != null) {
            // Set the ad as showing
            isAdShowing.set(true);

            mRewardedAd.setFullScreenContentCallback(new FullScreenContentCallback() {
                @Override
                public void onAdDismissedFullScreenContent() {
                    isAdShowing.set(false);

                    if (callback != null) {
                        callback.onAdClosed();
                    }

                    // Preload next ad with delay
                    mainHandler.postDelayed(() -> preloadRewardedAd(activity), 500);
                }

                @Override
                public void onAdFailedToShowFullScreenContent(AdError adError) {
                    Log.e(TAG, "Rewarded ad failed to show: " + adError.getMessage());
                    mRewardedAd = null;
                    isAdShowing.set(false);

                    if (callback != null) {
                        callback.onAdFailed();
                    }

                    // Preload next ad immediately
                    preloadRewardedAd(activity);
                }

                @Override
                public void onAdShowedFullScreenContent() {
                    mRewardedAd = null;
                }
            });

            mRewardedAd.show(activity, new OnUserEarnedRewardListener() {
                @Override
                public void onUserEarnedReward(@NonNull com.google.android.gms.ads.rewarded.RewardItem rewardItem) {
                    if (callback != null) {
                        callback.onRewarded(true);
                    }
                }
            });

        } else {
            Toast.makeText(activity, R.string.ad_loading_short, Toast.LENGTH_SHORT).show();

            // Try to load a new ad if not already loading
            if (!rewardedLoading.get()) {
                preloadRewardedAd(activity);
            }

            if (callback != null) {
                callback.onAdFailed();
            }
        }
    }

    /**
     * Preload a rewarded interstitial ad
     * @param context Application context
     */
    public void preloadRewardedInterstitialAd(Context context) {
        // Skip if already loading, or premium user, or WebView unavailable
        if (rewardedInterstitialLoading.get() || premiumManager.isPremium() || !webViewAvailable.get()) {
            return;
        }

        rewardedInterstitialLoading.set(true);

        try {
            // Create optimized ad request with shorter timeout
            AdRequest adRequest = new AdRequest.Builder()
                    .setHttpTimeoutMillis(HTTP_TIMEOUT_MS)
                    .build();

            String adUnitId = USE_TEST_ADS ? REWARDED_INTERSTITIAL_AD_UNIT_ID : REWARDED_INTERSTITIAL_AD_UNIT_ID;

            RewardedInterstitialAdLoadCallback adLoadCallback = new RewardedInterstitialAdLoadCallback() {
                @Override
                public void onAdLoaded(@NonNull RewardedInterstitialAd rewardedInterstitialAd) {
                    mRewardedInterstitialAd = rewardedInterstitialAd;
                    rewardedInterstitialLoading.set(false);
                }

                @Override
                public void onAdFailedToLoad(@NonNull LoadAdError loadAdError) {
                    Log.e(TAG, "Rewarded interstitial ad failed to load: " + loadAdError.getMessage());
                    mRewardedInterstitialAd = null;
                    rewardedInterstitialLoading.set(false);

                    // Only retry once with longer delay
                    mainHandler.postDelayed(() -> preloadRewardedInterstitialAd(context), 7000);
                }
            };

            // Load the rewarded interstitial ad
            RewardedInterstitialAd.load(context, adUnitId, adRequest, adLoadCallback);

        } catch (Exception e) {
            Log.e(TAG, "Error preloading rewarded interstitial ad: " + e.getMessage());
            rewardedInterstitialLoading.set(false);
        }
    }

    /**
     * Show a preloaded rewarded interstitial ad
     * @param activity Activity where the ad will be displayed
     * @param callback Callback to be executed after the ad is shown or fails
     */
    public void showRewardedInterstitialAd(Activity activity, RewardedInterstitialAdCallback callback) {
        // Skip ads for premium users
        if (premiumManager.isPremium()) {
            if (callback != null) {
                callback.onRewarded(true);
            }
            return;
        }

        // Check WebView availability
        if (!webViewAvailable.get()) {
            if (callback != null) {
                callback.onAdFailed();
            }
            return;
        }

        // Skip if another ad is already showing
        if (isAdShowing.get()) {
            if (callback != null) {
                callback.onAdFailed();
            }
            return;
        }

        if (mRewardedInterstitialAd != null) {
            // Set the ad as showing
            isAdShowing.set(true);

            mRewardedInterstitialAd.setFullScreenContentCallback(new FullScreenContentCallback() {
                @Override
                public void onAdDismissedFullScreenContent() {
                    isAdShowing.set(false);

                    if (callback != null) {
                        callback.onAdClosed();
                    }

                    // Preload next ad with delay
                    mainHandler.postDelayed(() -> preloadRewardedInterstitialAd(activity), 500);
                }

                @Override
                public void onAdFailedToShowFullScreenContent(AdError adError) {
                    Log.e(TAG, "Rewarded interstitial ad failed to show: " + adError.getMessage());
                    mRewardedInterstitialAd = null;
                    isAdShowing.set(false);

                    if (callback != null) {
                        callback.onAdFailed();
                    }

                    // Preload next ad immediately
                    preloadRewardedInterstitialAd(activity);
                }

                @Override
                public void onAdShowedFullScreenContent() {
                    mRewardedInterstitialAd = null;
                }
            });

            mRewardedInterstitialAd.show(activity, new OnUserEarnedRewardListener() {
                @Override
                public void onUserEarnedReward(@NonNull com.google.android.gms.ads.rewarded.RewardItem rewardItem) {
                    if (callback != null) {
                        callback.onRewarded(true);
                    }
                }
            });

        } else {
            Toast.makeText(activity, R.string.ad_loading_short, Toast.LENGTH_SHORT).show();

            // Try to load a new ad if not already loading
            if (!rewardedInterstitialLoading.get()) {
                preloadRewardedInterstitialAd(activity);
            }

            if (callback != null) {
                callback.onAdFailed();
            }
        }
    }

    /**
     * Clean up resources when application is closing
     */
    public void cleanup() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }

        mInterstitialAd = null;
        mRewardedAd = null;
        mRewardedInterstitialAd = null;
        instance = null;
    }

    /**
     * Check if ads are enabled
     * @return true if ads are enabled (non-premium user)
     */
    public boolean areAdsEnabled() {
        return !premiumManager.isPremium() && webViewAvailable.get();
    }



    /**
     * Check if interstitial ad is loaded
     * @return true if interstitial ad is loaded
     */
    public boolean isInterstitialAdLoaded() {
        return mInterstitialAd != null;
    }

    /**
     * Check if rewarded ad is loaded
     * @return true if rewarded ad is loaded
     */
    public boolean isRewardedAdLoaded() {
        return mRewardedAd != null;
    }

    /**
     * Check if rewarded interstitial ad is loaded
     * @return true if rewarded interstitial ad is loaded
     */
    public boolean isRewardedInterstitialAdLoaded() {
        return mRewardedInterstitialAd != null;
    }

    /**
     * Show App Open Ad for non-premium users
     * @param activity Activity where the ad will be displayed
     * @param callback Callback to be executed after the ad is shown or fails
     */
    public void showAppOpenAd(Activity activity, AppOpenAdCallback callback) {
        // Skip ads for premium users
        if (premiumManager.isPremium()) {
            if (callback != null) {
                callback.onAdClosed();
            }
            return;
        }

        // Check WebView availability
        if (!webViewAvailable.get()) {
            if (callback != null) {
                callback.onAdFailed();
            }
            return;
        }

        // Skip if another ad is already showing
        if (isAdShowing.get()) {
            if (callback != null) {
                callback.onAdFailed();
            }
            return;
        }

        // Load and show App Open Ad
        loadAndShowAppOpenAd(activity, callback);
    }

    /**
     * Load and show App Open Ad
     * @param activity Activity where the ad will be displayed
     * @param callback Callback to be executed after the ad is shown or fails
     */
    private void loadAndShowAppOpenAd(Activity activity, AppOpenAdCallback callback) {
        try {
            // Import App Open Ad classes
            com.google.android.gms.ads.appopen.AppOpenAd.AppOpenAdLoadCallback loadCallback =
                new com.google.android.gms.ads.appopen.AppOpenAd.AppOpenAdLoadCallback() {
                    @Override
                    public void onAdLoaded(@NonNull com.google.android.gms.ads.appopen.AppOpenAd appOpenAd) {
                        // Ad loaded successfully, show it
                        isAdShowing.set(true);

                        appOpenAd.setFullScreenContentCallback(new FullScreenContentCallback() {
                            @Override
                            public void onAdDismissedFullScreenContent() {
                                isAdShowing.set(false);
                                if (callback != null) {
                                    callback.onAdClosed();
                                }
                            }

                            @Override
                            public void onAdFailedToShowFullScreenContent(@NonNull AdError adError) {
                                isAdShowing.set(false);
                                Log.e(TAG, "App Open Ad failed to show: " + adError.getMessage());
                                if (callback != null) {
                                    callback.onAdFailed();
                                }
                            }

                            @Override
                            public void onAdShowedFullScreenContent() {
                                Log.d(TAG, "App Open Ad showed full screen content");
                            }
                        });

                        // Show the ad
                        appOpenAd.show(activity);
                    }

                    @Override
                    public void onAdFailedToLoad(@NonNull LoadAdError loadAdError) {
                        Log.e(TAG, "App Open Ad failed to load: " + loadAdError.getMessage());
                        if (callback != null) {
                            callback.onAdFailed();
                        }
                    }
                };

            // Create ad request
            AdRequest adRequest = new AdRequest.Builder()
                    .setHttpTimeoutMillis(HTTP_TIMEOUT_MS)
                    .build();

            // Load App Open Ad
            com.google.android.gms.ads.appopen.AppOpenAd.load(
                    activity,
                    APP_OPEN_AD_UNIT_ID,
                    adRequest,
                    loadCallback
            );

        } catch (Exception e) {
            Log.e(TAG, "Error loading App Open Ad: " + e.getMessage());
            if (callback != null) {
                callback.onAdFailed();
            }
        }
    }
}