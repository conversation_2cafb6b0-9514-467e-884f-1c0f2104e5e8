document.addEventListener('DOMContentLoaded', function() {
    // Feature Details Functionality
    const learnMoreButtons = document.querySelectorAll('.learn-more');
    const closeDetailsButtons = document.querySelectorAll('.close-details');
    const featureDetails = document.querySelectorAll('.feature-details');

    // Open feature details
    learnMoreButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetId = this.getAttribute('data-target');
            const targetDetails = document.getElementById(targetId);
            
            // Close all other details
            featureDetails.forEach(detail => {
                detail.classList.remove('active');
            });
            
            // Open target details
            targetDetails.classList.add('active');
            
            // Add animation class
            targetDetails.querySelector('.details-content').classList.add('animate-in');
        });
    });

    // Close feature details
    closeDetailsButtons.forEach(button => {
        button.addEventListener('click', function() {
            const detailsSection = this.closest('.feature-details');
            detailsSection.classList.remove('active');
        });
    });

    // Close details when clicking outside content
    featureDetails.forEach(detail => {
        detail.addEventListener('click', function(e) {
            if (e.target === this) {
                this.classList.remove('active');
            }
        });
    });

    // FAQ Accordion
    const faqQuestions = document.querySelectorAll('.faq-question');
    
    faqQuestions.forEach(question => {
        question.addEventListener('click', function() {
            const faqItem = this.parentElement;
            const isActive = faqItem.classList.contains('active');
            
            // Close all FAQ items
            document.querySelectorAll('.faq-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // Open clicked item if it wasn't already open
            if (!isActive) {
                faqItem.classList.add('active');
            }
        });
    });

    // Subscribe Button Animation
    const subscribeButtons = document.querySelectorAll('.subscribe-button');
    
    subscribeButtons.forEach(button => {
        button.addEventListener('click', function() {
            this.classList.add('clicked');
            
            // Show a thank you message or modal here
            alert('شكراً لاهتمامك! سيتم توجيهك إلى صفحة الاشتراك.');
            
            setTimeout(() => {
                this.classList.remove('clicked');
            }, 1000);
        });
    });

    // Add hover effects to screenshots
    const screenshots = document.querySelectorAll('.screenshot');
    
    screenshots.forEach(screenshot => {
        screenshot.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.05)';
            this.style.zIndex = '1';
            this.style.boxShadow = '0 10px 20px rgba(0, 255, 255, 0.3)';
        });
        
        screenshot.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
            this.style.zIndex = '0';
            this.style.boxShadow = 'none';
        });
    });

    // Add neon glow effect to feature cards on hover
    const featureCards = document.querySelectorAll('.feature-card');
    
    featureCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.boxShadow = '0 0 20px rgba(0, 255, 255, 0.3)';
            const icon = this.querySelector('.feature-icon');
            icon.style.boxShadow = '0 0 20px rgba(0, 255, 255, 0.5)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.boxShadow = '0 0 10px rgba(0, 255, 255, 0.1)';
            const icon = this.querySelector('.feature-icon');
            icon.style.boxShadow = '0 0 15px rgba(0, 255, 255, 0.3)';
        });
    });

    // Add special effects for premium features
    const premiumFeatures = document.querySelectorAll('.premium-feature');
    
    premiumFeatures.forEach(feature => {
        feature.addEventListener('mouseenter', function() {
            this.style.boxShadow = '0 0 20px rgba(255, 215, 0, 0.3)';
            const icon = this.querySelector('.premium-icon');
            icon.style.boxShadow = '0 0 20px rgba(255, 215, 0, 0.5)';
        });
        
        feature.addEventListener('mouseleave', function() {
            this.style.boxShadow = 'none';
            const icon = this.querySelector('.premium-icon');
            icon.style.boxShadow = '0 0 15px rgba(255, 215, 0, 0.3)';
        });
    });

    // Add smooth scrolling for navigation
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);
            
            if (targetElement) {
                window.scrollTo({
                    top: targetElement.offsetTop - 100,
                    behavior: 'smooth'
                });
            }
        });
    });

    // Add animation to the main title
    const mainTitle = document.querySelector('.main-title');
    const subtitle = document.querySelector('.subtitle');
    
    if (mainTitle && subtitle) {
        // Add initial animation classes
        mainTitle.classList.add('animate-title');
        subtitle.classList.add('animate-subtitle');
        
        // Add animation to title on scroll
        window.addEventListener('scroll', function() {
            const scrollPosition = window.scrollY;
            
            if (scrollPosition > 100) {
                mainTitle.style.textShadow = '0 0 5px rgba(0, 255, 255, 0.5)';
                subtitle.style.textShadow = '0 0 5px rgba(0, 255, 255, 0.5)';
            } else {
                mainTitle.style.textShadow = '0 0 10px rgba(0, 255, 255, 0.8)';
                subtitle.style.textShadow = '0 0 10px rgba(0, 255, 255, 0.8)';
            }
        });
    }

    // Add animation to feature sections on scroll
    const animateOnScroll = function() {
        const elements = document.querySelectorAll('.feature-card, .premium-feature, .screenshot');
        
        elements.forEach(element => {
            const elementPosition = element.getBoundingClientRect().top;
            const windowHeight = window.innerHeight;
            
            if (elementPosition < windowHeight - 100) {
                element.classList.add('animate-in');
            }
        });
    };
    
    // Run animation check on scroll
    window.addEventListener('scroll', animateOnScroll);
    
    // Run once on page load
    animateOnScroll();

    // Add speed lines animation to background
    const createSpeedLines = function() {
        const container = document.querySelector('.container');
        const speedLinesContainer = document.createElement('div');
        speedLinesContainer.classList.add('speed-lines-container');
        
        // Create multiple speed lines
        for (let i = 0; i < 20; i++) {
            const speedLine = document.createElement('div');
            speedLine.classList.add('speed-line');
            
            // Randomize position, length, and animation delay
            const startPosition = Math.random() * 100;
            const length = 50 + Math.random() * 150;
            const delay = Math.random() * 5;
            
            speedLine.style.top = `${startPosition}%`;
            speedLine.style.right = '-10%';
            speedLine.style.width = `${length}px`;
            speedLine.style.animationDelay = `${delay}s`;
            
            speedLinesContainer.appendChild(speedLine);
        }
        
        container.appendChild(speedLinesContainer);
    };
    
    // Create speed lines
    createSpeedLines();

    // Create energy grid effect
    const createEnergyGrid = function() {
        const container = document.querySelector('.container');
        const energyGrid = document.createElement('div');
        energyGrid.classList.add('energy-grid');
        
        // Create grid lines
        for (let i = 0; i < 10; i++) {
            const horizontalLine = document.createElement('div');
            horizontalLine.classList.add('grid-line', 'horizontal');
            horizontalLine.style.top = `${i * 10}%`;
            
            const verticalLine = document.createElement('div');
            verticalLine.classList.add('grid-line', 'vertical');
            verticalLine.style.right = `${i * 10}%`;
            
            energyGrid.appendChild(horizontalLine);
            energyGrid.appendChild(verticalLine);
        }
        
        container.appendChild(energyGrid);
    };
    
    // Create energy grid
    createEnergyGrid();
});
