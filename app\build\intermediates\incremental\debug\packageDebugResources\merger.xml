<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res"><file name="boost_success_animation" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\anim\boost_success_animation.xml" qualifiers="" type="anim"/><file name="button_press" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\anim\button_press.xml" qualifiers="" type="anim"/><file name="button_pulse" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\anim\button_pulse.xml" qualifiers="" type="anim"/><file name="button_pulse_glow" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\anim\button_pulse_glow.xml" qualifiers="" type="anim"/><file name="button_shake" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\anim\button_shake.xml" qualifiers="" type="anim"/><file name="card_fade_in_slide_up" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\anim\card_fade_in_slide_up.xml" qualifiers="" type="anim"/><file name="energy_grid_animation" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\anim\energy_grid_animation.xml" qualifiers="" type="anim"/><file name="fade_in" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\anim\fade_in.xml" qualifiers="" type="anim"/><file name="flame_animation" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\anim\flame_animation.xml" qualifiers="" type="anim"/><file name="float_animation" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\anim\float_animation.xml" qualifiers="" type="anim"/><file name="glow_pulse" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\anim\glow_pulse.xml" qualifiers="" type="anim"/><file name="glow_pulse_repeat" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\anim\glow_pulse_repeat.xml" qualifiers="" type="anim"/><file name="gold_pulse" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\anim\gold_pulse.xml" qualifiers="" type="anim"/><file name="hover_effect" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\anim\hover_effect.xml" qualifiers="" type="anim"/><file name="icon_pulse" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\anim\icon_pulse.xml" qualifiers="" type="anim"/><file name="lightning_flash" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\anim\lightning_flash.xml" qualifiers="" type="anim"/><file name="lines_animation" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\anim\lines_animation.xml" qualifiers="" type="anim"/><file name="logo_pulse" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\anim\logo_pulse.xml" qualifiers="" type="anim"/><file name="logo_shrink" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\anim\logo_shrink.xml" qualifiers="" type="anim"/><file name="premium_feature_animation" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\anim\premium_feature_animation.xml" qualifiers="" type="anim"/><file name="pulse" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\anim\pulse.xml" qualifiers="" type="anim"/><file name="pulse_animation" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\anim\pulse_animation.xml" qualifiers="" type="anim"/><file name="rocket_animation" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\anim\rocket_animation.xml" qualifiers="" type="anim"/><file name="rotate_animation" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\anim\rotate_animation.xml" qualifiers="" type="anim"/><file name="slide_down_bounce" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\anim\slide_down_bounce.xml" qualifiers="" type="anim"/><file name="slide_in_left" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\anim\slide_in_left.xml" qualifiers="" type="anim"/><file name="slide_in_right" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\anim\slide_in_right.xml" qualifiers="" type="anim"/><file name="slide_up" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\anim\slide_up.xml" qualifiers="" type="anim"/><file name="slide_up_fade_in" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\anim\slide_up_fade_in.xml" qualifiers="" type="anim"/><file name="speed_lines_animation" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\anim\speed_lines_animation.xml" qualifiers="" type="anim"/><file name="splash_logo_anim" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\anim\splash_logo_anim.xml" qualifiers="" type="anim"/><file name="staggered_fade_in" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\anim\staggered_fade_in.xml" qualifiers="" type="anim"/><file name="subtle_scale" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\anim\subtle_scale.xml" qualifiers="" type="anim"/><file name="zoom_in" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\anim\zoom_in.xml" qualifiers="" type="anim"/><file name="button_state_animator" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\animator\button_state_animator.xml" qualifiers="" type="animator"/><file name="aim_preview_background" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\aim_preview_background.xml" qualifiers="" type="drawable"/><file name="aim_target" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\aim_target.xml" qualifiers="" type="drawable"/><file name="animated_gradient_background" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\animated_gradient_background.xml" qualifiers="" type="drawable"/><file name="blue_color_button" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\blue_color_button.xml" qualifiers="" type="drawable"/><file name="blue_speed_lines" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\blue_speed_lines.xml" qualifiers="" type="drawable"/><file name="boost_button_background" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\boost_button_background.xml" qualifiers="" type="drawable"/><file name="button_cyan_gradient" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\button_cyan_gradient.xml" qualifiers="" type="drawable"/><file name="button_glow" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\button_glow.xml" qualifiers="" type="drawable"/><file name="card_background" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\card_background.xml" qualifiers="" type="drawable"/><file name="card_bg_translucent" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\card_bg_translucent.xml" qualifiers="" type="drawable"/><file name="card_dark_bg" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\card_dark_bg.xml" qualifiers="" type="drawable"/><file name="card_shadow" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\card_shadow.xml" qualifiers="" type="drawable"/><file name="circle_button" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\circle_button.xml" qualifiers="" type="drawable"/><file name="circle_shape_blue" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\circle_shape_blue.xml" qualifiers="" type="drawable"/><file name="circle_shape_green" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\circle_shape_green.xml" qualifiers="" type="drawable"/><file name="circle_shape_red" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\circle_shape_red.xml" qualifiers="" type="drawable"/><file name="circle_shape_yellow" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\circle_shape_yellow.xml" qualifiers="" type="drawable"/><file name="circular_progress_bar" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\circular_progress_bar.xml" qualifiers="" type="drawable"/><file name="circular_progress_bar_green" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\circular_progress_bar_green.xml" qualifiers="" type="drawable"/><file name="crosshair_dot" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\crosshair_dot.xml" qualifiers="" type="drawable"/><file name="crosshair_lines" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\crosshair_lines.xml" qualifiers="" type="drawable"/><file name="crosshair_overlay" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\crosshair_overlay.xml" qualifiers="" type="drawable"/><file name="crosshair_precision" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\crosshair_precision.xml" qualifiers="" type="drawable"/><file name="crosshair_simple" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\crosshair_simple.xml" qualifiers="" type="drawable"/><file name="custom_progress" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\custom_progress.xml" qualifiers="" type="drawable"/><file name="cyber_border_gold" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\cyber_border_gold.xml" qualifiers="" type="drawable"/><file name="cyber_button_bg" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\cyber_button_bg.xml" qualifiers="" type="drawable"/><file name="cyber_neon_background" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\cyber_neon_background.xml" qualifiers="" type="drawable"/><file name="cyber_neon_bg" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\cyber_neon_bg.xml" qualifiers="" type="drawable"/><file name="cyber_neon_gradient_bg" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\cyber_neon_gradient_bg.xml" qualifiers="" type="drawable"/><file name="cyber_neon_gradient_bg_gold" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\cyber_neon_gradient_bg_gold.xml" qualifiers="" type="drawable"/><file name="dialog_background" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\dialog_background.xml" qualifiers="" type="drawable"/><file name="disclaimer_background" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\disclaimer_background.xml" qualifiers="" type="drawable"/><file name="edit_text_background" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\edit_text_background.xml" qualifiers="" type="drawable"/><file name="energy_grid" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\energy_grid.xml" qualifiers="" type="drawable"/><file name="energy_lines" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\energy_lines.xml" qualifiers="" type="drawable"/><file name="energy_lines_bg" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\energy_lines_bg.xml" qualifiers="" type="drawable"/><file name="enhanced_button_background" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\enhanced_button_background.xml" qualifiers="" type="drawable"/><file name="enhanced_button_square_blue" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\enhanced_button_square_blue.xml" qualifiers="" type="drawable"/><file name="enhanced_button_with_glow" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\enhanced_button_with_glow.xml" qualifiers="" type="drawable"/><file name="game_booster_bg" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\game_booster_bg.xml" qualifiers="" type="drawable"/><file name="game_mode_button_active" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\game_mode_button_active.xml" qualifiers="" type="drawable"/><file name="game_mode_button_bg" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\game_mode_button_bg.xml" qualifiers="" type="drawable"/><file name="game_mode_button_custom" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\game_mode_button_custom.xml" qualifiers="" type="drawable"/><file name="game_mode_logo" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\game_mode_logo.xml" qualifiers="" type="drawable"/><file name="gaming_button_secondary" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\gaming_button_secondary.xml" qualifiers="" type="drawable"/><file name="gaming_popup_background" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\gaming_popup_background.xml" qualifiers="" type="drawable"/><file name="gaming_recommendation_background" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\gaming_recommendation_background.xml" qualifiers="" type="drawable"/><file name="gaming_score_background" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\gaming_score_background.xml" qualifiers="" type="drawable"/><file name="gaming_tag_background" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\gaming_tag_background.xml" qualifiers="" type="drawable"/><file name="gaming_tips_background" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\gaming_tips_background.xml" qualifiers="" type="drawable"/><file name="gfx_tools_gradient_bg" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\gfx_tools_gradient_bg.xml" qualifiers="" type="drawable"/><file name="gradient_background" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\gradient_background.xml" qualifiers="" type="drawable"/><file name="green_color_button" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\green_color_button.xml" qualifiers="" type="drawable"/><file name="headshot_logo" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\headshot_logo.xml" qualifiers="" type="drawable"/><file name="headshot_logo_blue" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\headshot_logo_blue.xml" qualifiers="" type="drawable"/><file name="headshot_tool_bg" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\headshot_tool_bg.xml" qualifiers="" type="drawable"/><file name="ic_about" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_about.xml" qualifiers="" type="drawable"/><file name="ic_advanced" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_advanced.xml" qualifiers="" type="drawable"/><file name="ic_aim_button" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_aim_button.xml" qualifiers="" type="drawable"/><file name="ic_analytics" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_analytics.xml" qualifiers="" type="drawable"/><file name="ic_android" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_android.xml" qualifiers="" type="drawable"/><file name="ic_apps" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_apps.xml" qualifiers="" type="drawable"/><file name="ic_arrow_right" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_arrow_right.xml" qualifiers="" type="drawable"/><file name="ic_auto_optimization" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_auto_optimization.xml" qualifiers="" type="drawable"/><file name="ic_back" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_back.xml" qualifiers="" type="drawable"/><file name="ic_battery" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_battery.xml" qualifiers="" type="drawable"/><file name="ic_boost" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_boost.xml" qualifiers="" type="drawable"/><file name="ic_check_circle" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_check_circle.xml" qualifiers="" type="drawable"/><file name="ic_check_small" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_check_small.xml" qualifiers="" type="drawable"/><file name="ic_cpu" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_cpu.xml" qualifiers="" type="drawable"/><file name="ic_crosshair" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_crosshair.xml" qualifiers="" type="drawable"/><file name="ic_crown" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_crown.png" qualifiers="" type="drawable"/><file name="ic_delete" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_delete.xml" qualifiers="" type="drawable"/><file name="ic_dpi_tip" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_dpi_tip.xml" qualifiers="" type="drawable"/><file name="ic_exit" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_exit.xml" qualifiers="" type="drawable"/><file name="ic_fps" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_fps.xml" qualifiers="" type="drawable"/><file name="ic_games" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_games.xml" qualifiers="" type="drawable"/><file name="ic_game_booster" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_game_booster.xml" qualifiers="" type="drawable"/><file name="ic_gaming_controller" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_gaming_controller.xml" qualifiers="" type="drawable"/><file name="ic_gaming_fps" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_gaming_fps.xml" qualifiers="" type="drawable"/><file name="ic_gaming_performance" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_gaming_performance.xml" qualifiers="" type="drawable"/><file name="ic_gaming_trophy" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_gaming_trophy.xml" qualifiers="" type="drawable"/><file name="ic_gfx_logo" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_gfx_logo.xml" qualifiers="" type="drawable"/><file name="ic_gfx_tools" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_gfx_tools.xml" qualifiers="" type="drawable"/><file name="ic_graphics" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_graphics.xml" qualifiers="" type="drawable"/><file name="ic_headshot_tool" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_headshot_tool.xml" qualifiers="" type="drawable"/><file name="ic_help" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_help.xml" qualifiers="" type="drawable"/><file name="ic_info" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_info.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_logo" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_logo.png" qualifiers="" type="drawable"/><file name="ic_memory" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_memory.xml" qualifiers="" type="drawable"/><file name="ic_menu" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_menu.xml" qualifiers="" type="drawable"/><file name="ic_mood_game" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_mood_game.xml" qualifiers="" type="drawable"/><file name="ic_network" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_network.xml" qualifiers="" type="drawable"/><file name="ic_play_game" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_play_game.xml" qualifiers="" type="drawable"/><file name="ic_power" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_power.xml" qualifiers="" type="drawable"/><file name="ic_premium" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_premium.xml" qualifiers="" type="drawable"/><file name="ic_premium_crown" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_premium_crown.xml" qualifiers="" type="drawable"/><file name="ic_privacy" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_privacy.xml" qualifiers="" type="drawable"/><file name="ic_ram" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_ram.xml" qualifiers="" type="drawable"/><file name="ic_rate" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_rate.xml" qualifiers="" type="drawable"/><file name="ic_recoil_tip" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_recoil_tip.xml" qualifiers="" type="drawable"/><file name="ic_red_dot_tip" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_red_dot_tip.xml" qualifiers="" type="drawable"/><file name="ic_resolution" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_resolution.xml" qualifiers="" type="drawable"/><file name="ic_rocket" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_rocket.xml" qualifiers="" type="drawable"/><file name="ic_sensitivity_tip" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_sensitivity_tip.xml" qualifiers="" type="drawable"/><file name="ic_settings" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_settings.xml" qualifiers="" type="drawable"/><file name="ic_smart_aim" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_smart_aim.xml" qualifiers="" type="drawable"/><file name="ic_speedometer_bg" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_speedometer_bg.xml" qualifiers="" type="drawable"/><file name="ic_speedometer_needle" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_speedometer_needle.xml" qualifiers="" type="drawable"/><file name="ic_storage" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_storage.xml" qualifiers="" type="drawable"/><file name="ic_swipe" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_swipe.xml" qualifiers="" type="drawable"/><file name="ic_tap" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_tap.xml" qualifiers="" type="drawable"/><file name="ic_temperature" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_temperature.xml" qualifiers="" type="drawable"/><file name="ic_wifi" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_wifi.xml" qualifiers="" type="drawable"/><file name="lightning_effect" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\lightning_effect.xml" qualifiers="" type="drawable"/><file name="logo_background" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\logo_background.xml" qualifiers="" type="drawable"/><file name="logo_drawable_android" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\logo_drawable_android.png" qualifiers="" type="drawable"/><file name="logo_glow" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\logo_glow.xml" qualifiers="" type="drawable"/><file name="neon_button_active_bg" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\neon_button_active_bg.xml" qualifiers="" type="drawable"/><file name="neon_button_bg" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\neon_button_bg.xml" qualifiers="" type="drawable"/><file name="neon_button_bg_cyan" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\neon_button_bg_cyan.xml" qualifiers="" type="drawable"/><file name="neon_button_bg_gold" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\neon_button_bg_gold.xml" qualifiers="" type="drawable"/><file name="neon_button_bg_premium" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\neon_button_bg_premium.xml" qualifiers="" type="drawable"/><file name="neon_button_bg_secondary" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\neon_button_bg_secondary.xml" qualifiers="" type="drawable"/><file name="neon_radio_selector" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\neon_radio_selector.xml" qualifiers="" type="drawable"/><file name="particle_effect" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\particle_effect.xml" qualifiers="" type="drawable"/><file name="premium_button_bg" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\premium_button_bg.xml" qualifiers="" type="drawable"/><file name="premium_glow_bg" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\premium_glow_bg.xml" qualifiers="" type="drawable"/><file name="premium_header_bg" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\premium_header_bg.xml" qualifiers="" type="drawable"/><file name="premium_price_bg" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\premium_price_bg.xml" qualifiers="" type="drawable"/><file name="preview_background" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\preview_background.xml" qualifiers="" type="drawable"/><file name="progress_bar_gradient" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\progress_bar_gradient.xml" qualifiers="" type="drawable"/><file name="recoil_pattern" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\recoil_pattern.xml" qualifiers="" type="drawable"/><file name="red_color_button" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\red_color_button.xml" qualifiers="" type="drawable"/><file name="red_dot_shape" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\red_dot_shape.xml" qualifiers="" type="drawable"/><file name="rounded_button_blue" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\rounded_button_blue.xml" qualifiers="" type="drawable"/><file name="rounded_button_dark" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\rounded_button_dark.xml" qualifiers="" type="drawable"/><file name="rounded_button_green" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\rounded_button_green.xml" qualifiers="" type="drawable"/><file name="smartphone_frame" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\smartphone_frame.xml" qualifiers="" type="drawable"/><file name="smart_aim_button_bg" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\smart_aim_button_bg.xml" qualifiers="" type="drawable"/><file name="smart_aim_button_glow" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\smart_aim_button_glow.xml" qualifiers="" type="drawable"/><file name="speed_lines_bg" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\speed_lines_bg.xml" qualifiers="" type="drawable"/><file name="speed_lines_blue" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\speed_lines_blue.xml" qualifiers="" type="drawable"/><file name="spinner_shape" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\spinner_shape.xml" qualifiers="" type="drawable"/><file name="target_icon" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\target_icon.xml" qualifiers="" type="drawable"/><file name="toast_background" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\toast_background.xml" qualifiers="" type="drawable"/><file name="yellow_color_button" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\yellow_color_button.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable-v24\ic_launcher_foreground.xml" qualifiers="v24" type="drawable"/><file name="cairo_regular" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\font\cairo_regular.ttf" qualifiers="" type="font"/><file name="cyberpunk_font" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\font\cyberpunk_font.ttf" qualifiers="" type="font"/><file name="font" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\font\font.xml" qualifiers="" type="font"/><file name="fonts" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\font\fonts.xml" qualifiers="" type="font"/><file name="font_for_ar_en" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\font\font_for_ar_en.ttf" qualifiers="" type="font"/><file name="orbitron_bold" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\font\orbitron_bold.otf" qualifiers="" type="font"/><file name="premium_font" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\font\premium_font.ttf" qualifiers="" type="font"/><file name="rajdhani_bold" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\font\rajdhani_bold.ttf" qualifiers="" type="font"/><file name="rajdhani_medium" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\font\rajdhani_medium.xml" qualifiers="" type="font"/><file name="tajawal_bold" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\font\tajawal_bold.xml" qualifiers="" type="font"/><file name="tajawal_medium" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\font\tajawal_medium.xml" qualifiers="" type="font"/><file name="tajawal_regular" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\font\tajawal_regular.xml" qualifiers="" type="font"/><file name="activity_aim_overlay_settings" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\layout\activity_aim_overlay_settings.xml" qualifiers="" type="layout"/><file name="activity_app_guide" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\layout\activity_app_guide.xml" qualifiers="" type="layout"/><file name="activity_game_booster" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\layout\activity_game_booster.xml" qualifiers="" type="layout"/><file name="activity_game_mode" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\layout\activity_game_mode.xml" qualifiers="" type="layout"/><file name="activity_gfx_tools" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\layout\activity_gfx_tools.xml" qualifiers="" type="layout"/><file name="activity_headshot_tool" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\layout\activity_headshot_tool.xml" qualifiers="" type="layout"/><file name="activity_main" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_settings" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\layout\activity_settings.xml" qualifiers="" type="layout"/><file name="activity_splashscreen" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\layout\activity_splashscreen.xml" qualifiers="" type="layout"/><file name="activity_webview" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\layout\activity_webview.xml" qualifiers="" type="layout"/><file name="aim_overlay" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\layout\aim_overlay.xml" qualifiers="" type="layout"/><file name="dialog_about" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\layout\dialog_about.xml" qualifiers="" type="layout"/><file name="dialog_aim_test" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\layout\dialog_aim_test.xml" qualifiers="" type="layout"/><file name="dialog_app_disclaimer" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\layout\dialog_app_disclaimer.xml" qualifiers="" type="layout"/><file name="dialog_premium_features" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\layout\dialog_premium_features.xml" qualifiers="" type="layout"/><file name="dialog_premium_trial" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\layout\dialog_premium_trial.xml" qualifiers="" type="layout"/><file name="dialog_progress" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\layout\dialog_progress.xml" qualifiers="" type="layout"/><file name="gfx_selection_layout" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\layout\gfx_selection_layout.xml" qualifiers="" type="layout"/><file name="item_app" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\layout\item_app.xml" qualifiers="" type="layout"/><file name="item_selected_game" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\layout\item_selected_game.xml" qualifiers="" type="layout"/><file name="nav_footer_premium_new" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\layout\nav_footer_premium_new.xml" qualifiers="" type="layout"/><file name="nav_footer_pro_status" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\layout\nav_footer_pro_status.xml" qualifiers="" type="layout"/><file name="nav_header" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\layout\nav_header.xml" qualifiers="" type="layout"/><file name="popup_optimization_needed" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\layout\popup_optimization_needed.xml" qualifiers="" type="layout"/><file name="popup_optimization_progress" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\layout\popup_optimization_progress.xml" qualifiers="" type="layout"/><file name="popup_system_analysis_report" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\layout\popup_system_analysis_report.xml" qualifiers="" type="layout"/><file name="premium_feature_item" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\layout\premium_feature_item.xml" qualifiers="" type="layout"/><file name="drawer_menu" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\menu\drawer_menu.xml" qualifiers="" type="menu"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\mipmap-hdpi\ic_launcher_foreground.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\mipmap-mdpi\ic_launcher_foreground.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\mipmap-xhdpi\ic_launcher_foreground.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\mipmap-xxhdpi\ic_launcher_foreground.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\mipmap-xxxhdpi\ic_launcher_foreground.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="splash_background">#121212</color><color name="neon_blue">#00FFFF</color><color name="neon_blue_dim">#8000FFFF</color><color name="dark_blue">#0D1B2A</color><color name="electric_blue">#0077FF</color><color name="text_color_2primary">#0f2027</color><color name="text_color_2secondary">#203a43</color><color name="main_background">#0f2027</color><color name="main_card_background">#162c36</color><color name="primary_color">#0f2027</color><color name="primary_color_dim">#800f2027</color><color name="secondary_color">#203a43</color><color name="secondary_color_dim">#80203a43</color><color name="accent_color">#00ccff</color><color name="accent_color_dim">#8000ccff</color><color name="button_glow">#3D00ccff</color><color name="button_stroke">#00ccff</color><color name="button_background">#162c36</color><color name="text_color_primary">#FFFFFF</color><color name="text_color_secondary">#CCCCCC</color><color name="light_gray">#999999</color><color name="cyan_accent">#00FFFF</color><color name="dark_background">#0f2027</color><color name="gradient_start">#0f2027</color><color name="gradient_center">#203a43</color><color name="gradient_end">#2c5364</color><color name="shadow_color">#40000000</color><color name="card_shadow">#30000000</color><color name="vibrant_blue">#00ccff</color><color name="vibrant_orange">#ff9500</color><color name="vibrant_green">#00ff9d</color><color name="vibrant_purple">#d900ff</color><color name="vibrant_red">#ff0050</color><color name="neon_pink">#ff0099</color><color name="card_dark_blue">#162b35</color><color name="switch_track_color">#4D00e5ff</color><color name="drawer_background">#0a1a22</color><color name="game_mode_background">#0A0A1A</color><color name="card_background">#121228</color><color name="neon_blue_glow">#8000E5FF</color><color name="neon_green">#00FF9D</color><color name="neon_orange">#FF5722</color><color name="neon_purple">#B026FF</color><color name="neon_red">#FF073A</color><color name="vibrant_gold">#FFD700</color><color name="neon_cyan">#00FFFF</color><color name="neon_yellow">#FFFF00</color></file><file path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\values\cyber_neon_colors.xml" qualifiers=""><color name="cyber_dark_start">#0f2027</color><color name="cyber_dark_center">#203a43</color><color name="cyber_dark_end">#2c5364</color><color name="cyber_neon_cyan">#00FFFF</color><color name="cyber_neon_blue">#0088FF</color><color name="cyber_neon_magenta">#FF00FF</color><color name="cyber_neon_gold">#FFD700</color><color name="cyber_neon_gold_dim">#80FFD700</color><color name="cyber_text_primary">#FFFFFF</color><color name="cyber_text_secondary">#AAAAAA</color><color name="cyber_card_bg">#66000000</color><color name="cyber_card_stroke">#3300FFFF</color><color name="cyber_slider_active">#00FFFF</color><color name="cyber_slider_inactive">#336688</color></file><file path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\values\dimens.xml" qualifiers=""><dimen name="activity_horizontal_margin">16dp</dimen><dimen name="activity_vertical_margin">16dp</dimen><dimen name="text_size_title">28sp</dimen><dimen name="text_size_subtitle">14sp</dimen><dimen name="text_size_button">16sp</dimen><dimen name="text_size_card_title">16sp</dimen><dimen name="text_size_card_content">14sp</dimen><dimen name="text_size_small">12sp</dimen><dimen name="icon_size_large">64dp</dimen><dimen name="icon_size_medium">32dp</dimen><dimen name="icon_size_small">24dp</dimen><dimen name="margin_large">24dp</dimen><dimen name="margin_medium">16dp</dimen><dimen name="margin_small">8dp</dimen><dimen name="margin_tiny">4dp</dimen><dimen name="padding_large">24dp</dimen><dimen name="padding_medium">16dp</dimen><dimen name="padding_small">8dp</dimen><dimen name="padding_tiny">4dp</dimen><dimen name="card_corner_radius">16dp</dimen><dimen name="card_elevation">8dp</dimen><dimen name="card_small_corner_radius">12dp</dimen><dimen name="card_small_elevation">4dp</dimen></file><file path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\values\drawer_styles.xml" qualifiers=""><style name="ThemeOverlay.App.NavigationDrawer" parent="ThemeOverlay.MaterialComponents.Dark">
        
        <item name="textAppearanceListItem">@style/TextAppearance.App.NavigationDrawer.ListItem</item>
        <item name="textAppearanceListItemSmall">@style/TextAppearance.App.NavigationDrawer.ListItemSmall</item>
        <item name="textAppearanceButton">@style/TextAppearance.App.Button</item>

        
        <item name="android:fontFamily">@font/font_for_ar_en</item>
        <item name="fontFamily">@font/font_for_ar_en</item>

        
        <item name="materialButtonStyle">@style/Widget.App.Button.Gold</item>
    </style><style name="TextAppearance.App.NavigationDrawer.ListItem" parent="TextAppearance.MaterialComponents.Body1">
        <item name="fontFamily">@font/font_for_ar_en</item>
        <item name="android:fontFamily">@font/font_for_ar_en</item>
        <item name="android:textSize">16sp</item>
    </style><style name="TextAppearance.App.NavigationDrawer.ListItemSmall" parent="TextAppearance.MaterialComponents.Body2">
        <item name="fontFamily">@font/font_for_ar_en</item>
        <item name="android:fontFamily">@font/font_for_ar_en</item>
        <item name="android:textSize">14sp</item>
    </style><style name="Widget.App.Button.Gold" parent="Widget.MaterialComponents.Button">
        <item name="backgroundTint">@color/cyber_neon_gold</item>
        <item name="android:textColor">@color/black</item>
        <item name="cornerRadius">24dp</item>
        <item name="android:fontFamily">@font/font_for_ar_en</item>
        <item name="fontFamily">@font/font_for_ar_en</item>
    </style><style name="Widget.App.Button.Outlined" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="strokeColor">@color/cyber_neon_gold</item>
        <item name="strokeWidth">1dp</item>
        <item name="android:textColor">@color/cyber_text_primary</item>
        <item name="backgroundTint">@color/cyber_dark_center</item>
        <item name="cornerRadius">24dp</item>
        <item name="android:fontFamily">@font/font_for_ar_en</item>
        <item name="fontFamily">@font/font_for_ar_en</item>
    </style></file><file path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\values\gfx_colors.xml" qualifiers=""><color name="dark_blue_start">#0f2027</color><color name="dark_blue_mid">#203a43</color><color name="dark_blue_end">#2c5364</color></file><file path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\values\gfx_strings.xml" qualifiers=""><string name="gfx_tools_title">GFX TOOLS</string><string name="gfx_tools_description">Optimize your game graphics settings for the best balance of visual quality and performance.</string><string name="back">Back</string><string name="gfx_logo">GFX Logo</string><string name="apply_settings">APPLY SETTINGS</string><string name="resolution">RESOLUTION</string><string name="fps">FPS</string><string name="graphics_quality">GRAPHICS QUALITY</string><string name="advanced_options">ADVANCED OPTIONS</string><string name="device_info_gfx_tools">DEVICE INFO</string><string name="hdr_mode">HDR Mode</string><string name="anti_aliasing">Anti-aliasing</string><string name="gpu_boost">GPU Boost</string><string name="gfx_settings_applied">GFX settings applied successfully!</string><string name="performance_warning_title">Performance Warning</string><string name="performance_warning_message">The selected settings may be too demanding for your device. This could result in lower frame rates, overheating, or increased battery drain. Consider using lower settings for better performance.</string><string name="keep_settings">Keep Settings</string><string name="optimize_settings">Optimize Settings</string><string name="settings_optimized">Settings optimized for your device</string></file><file path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\values\ic_launcher_background.xml" qualifiers=""><color name="ic_launcher_background">#00020A</color></file><file path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Game Booster Fix Lag - Gfx Tool</string><string name="navigation_drawer_open">Open navigation drawer</string><string name="navigation_drawer_close">Close navigation drawer</string><string name="settings">Settings</string><string name="rate_us">Rate Us</string><string name="privacy_policy">Privacy Policy</string><string name="about">About</string><string name="exit">Exit</string><string name="premium">Premium</string><string name="premium_version">Premium Version (Ad-Free)</string><string name="dark_mode">Dark Mode</string><string name="language">Language</string><string name="english">English</string><string name="arabic">العربية</string><string name="select_language">Select Language</string><string name="language_settings">Language Settings</string><string name="language_change_message">The app will restart to apply the language change</string><string name="restart">Restart</string><string name="game_booster_title">GAMING BOOSTER</string><string name="game_booster_description">Monitor your gaming sessions by tracking background apps and system resource usage for better gaming awareness.</string><string name="cpu_status">CPU USAGE</string><string name="memory_status">MEMORY USAGE</string><string name="background_apps">BACKGROUND APPS</string><string name="before">CURRENT</string><string name="after">NOW</string><string name="total_memory">TOTAL MEMORY</string><string name="used_memory">USED MEMORY</string><string name="free_memory">AVAILABLE MEMORY</string><string name="ram_speed">MEMORY INFO</string><string name="high_usage_apps">RESOURCE HEAVY APPS</string><string name="apps_description">These apps are currently using system resources:</string><string name="background_process">Background process</string><string name="no_apps_found">No resource-heavy apps detected</string><string name="tap_boost_button">Tap SCAN NOW to check current system status</string><string name="app_closed">%1$s information viewed</string><string name="app_close_failed">Could not get info for %1$s</string><string name="system_monitor_channel">Gaming Session Monitor</string><string name="system_monitor_channel_description">Notifications about gaming session monitoring</string><string name="optimization_needed">Gaming session check available</string><string name="optimization_needed_description">Check your current gaming session status. Tap to view.</string><string name="usage_access_required">Usage Access Required</string><string name="usage_access_message">To show the most accurate list of running apps, this app needs Usage Access permission. Please enable it in Settings.</string><string name="go_to_settings">Go to Settings</string><string name="cancel">Cancel</string><string name="last_used">Last used: %1$s</string><string name="done">ANALYSIS COMPLETE</string><string name="device_optimized_description">Analysis complete. System status reviewed.</string><string name="optimized_items">Analyzed Items:</string><string name="apps_closed_count">%1$d background apps managed</string><string name="cache_cleared_size">%1$s of cache analyzed</string><string name="ram_optimized">RAM usage: %1$d%% (Current reading)</string><string name="optimizing_progress">Scanning: %1$d%%</string><string name="vibrant_green">#4CAF50</string><string name="optimization_expired">Scan session has expired</string><string name="boost_now">SCAN SYSTEM STATUS</string><string name="optimized">SCAN COMPLETE</string><string name="ready_to_optimize">Ready to scan your device status</string><string name="optimizing">Scanning your device status…</string><string name="clearing_cache">Reading cache information…</string><string name="optimizing_cpu">Reading CPU usage...</string><string name="closing_apps">Scanning background apps…</string><string name="optimizing_ram">Reading memory information…</string><string name="optimization_complete">Scan complete. System status reviewed for gaming awareness.</string><string name="already_optimized">Device already scanned!</string><string name="optimization_success">Gaming Session Monitor scan complete!</string><string name="game_mode_title">GAMING MODE</string><string name="game_mode_description">Monitor your gaming sessions and track system performance for better gaming awareness and insights.</string><string name="activate_game_mode">START</string><string name="deactivate_game_mode">STOP</string><string name="performance_stats">SYSTEM STATUS</string><string name="cpu_boost">CPU Status</string><string name="cpu_boost_value">Monitoring</string><string name="ram_optimization">RAM Status</string><string name="ram_optimization_value">Monitoring</string><string name="network_priority">Network Status</string><string name="network_priority_value">Monitoring</string><string name="battery_optimization">Battery Status</string><string name="battery_optimization_value">Monitoring</string><string name="game_mode_status_active">Gaming Session Dashboard Active – System monitoring enabled</string><string name="game_mode_status_inactive">GAMING SESSION DASHBOARD INACTIVE</string><string name="game_mode_notification_title">Gaming Session Dashboard Active</string><string name="game_mode_notification_text">System monitoring is active for your gaming session</string><string name="device_info_gfx">DEVICE INFO</string><string name="resolution_gfx">RESOLUTION</string><string name="low_720p">Low (720p)</string><string name="medium_1080p">Medium (1080p)</string><string name="high_1440p">High (1440p)</string><string name="ultra_2160p">Ultra (2160p)</string><string name="advanced_options_gfx">ADVANCED OPTIONS</string><string name="apply_settings_gfx">APPLY SETTINGS</string><string name="gpu_boost_gfx">GPU Boost</string><string name="anti_aliasing_gfx">Anti-aliasing</string><string name="hdr_mode_gfx">HDR Mode</string><string name="_4x_scope">4X SCOPE</string><string name="awm_scope">AWM SCOPE</string><string name="awm_scope_sensitivity">AWM Scope Sensitivity</string><string name="game_mode_features">GAMING MODE FEATURES</string><string name="auto_optimization">Auto-Monitoring</string><string name="auto_optimization_desc">Automatically tracks system performance when gaming apps are detected</string><string name="network_priority_title">Network Monitor</string><string name="network_priority_desc">Displays network usage information during gaming sessions</string><string name="cpu_boost_title">CPU Monitor</string><string name="cpu_boost_desc">Shows CPU usage information for better gaming awareness</string><string name="battery_optimization_title">Battery Monitor</string><string name="battery_optimization_desc">Tracks battery usage during gaming sessions</string><string name="supported_games">SUPPORTED GAMES</string><string name="supported_games_desc">Game Mode automatically detects and monitors popular games including:</string><string name="pubg_mobile">PUBG Mobile (detected for monitoring)</string><string name="call_of_duty">Call of Duty (detected for monitoring) </string><string name="minecraft">Minecraft (detected for monitoring) </string><string name="fortnite">Fortnite (detected for monitoring) </string><string name="asphalt_9">Asphalt 9 (detected for monitoring) </string><string name="free_fire">Free Fire (detected for monitoring) </string><string name="more_games_detected">...and many more games are automatically detected!</string><string name="headshot_tool_title">HEADSHOT TOOL</string><string name="free_fire_optimization">GENERAL OPTIMIZATION SETTINGS FOR FREE FIRE</string><string name="device_analysis">DEVICE ANALYSIS</string><string name="analyzing_device">Analyzing device...</string><string name="general">GENERAL</string><string name="general_sensitivity">General Sensitivity</string><string name="graphics_level">Graphics Level</string><string name="standard">STANDARD</string><string name="dpi_setting">DPI Setting</string><string name="red_dot">RED DOT</string><string name="red_dot_sensitivity">Red Dot Sensitivity</string><string name="red_dot_color">Red Dot Color</string><string name="scope_2x">2X SCOPE</string><string name="scope_2x_sensitivity">2x Scope Sensitivity</string><string name="scope_4x">4X SCOPE</string><string name="scope_4x_sensitivity">4x Scope Sensitivity</string><string name="awm_scope_sensitivity_label">AWM Scope Sensitivity</string><string name="breath_hold">Breath Hold</string><string name="free_camera_button">FREE CAMERA BUTTON</string><string name="free_camera_sensitivity">Free Camera Sensitivity</string><string name="smart_aim">SMART AIM</string><string name="enable_smart_aim">Enable Smart Aim</string><string name="smart_aim_description">Helps adjust sensitivity settings based on your preferences.</string><string name="apply_settings_headshot">SUGGEST BEST SETTINGS</string><string name="test_aim">TEST AIM</string><string name="reset">RESET</string><string name="share">SHARE</string><string name="fps_30">30 FPS</string><string name="fps_60">60 FPS</string><string name="fps_90">90 FPS</string><string name="low">Low</string><string name="medium">Medium</string><string name="high">High</string><string name="awm_scope_title">AWM SCOPE</string><string name="game_booster">GAME BOOSTER</string><string name="optimize_your_gaming_experience">Optimize your gaming experience</string><string name="headshot">HEADSHOT</string><string name="game_enhancer">GAME ENHANCER</string><string name="gfx_tools_main">GFX Tools</string><string name="headshot_tool">Headshot Tool</string><string name="aim_button">Aim Settings Pro</string><string name="mood_game">Mood Game</string><string name="aim_sensitivity">AIM SENSITIVITY</string><string name="horizontal_sensitivity">Horizontal Sensitivity</string><string name="vertical_sensitivity">Vertical Sensitivity</string><string name="dpi_settings">DPI SETTINGS</string><string name="select_optimal_dpi">Select optimal DPI for your device</string><string name="red_dot_optimization">RED DOT OPTIMIZATION</string><string name="recoil_control">RECOIL CONTROL</string><string name="recoil_compensation">Recoil Compensation</string><string name="graphics_optimization">GRAPHICS OPTIMIZATION</string><string name="fps_settings">FPS Settings</string><string name="aim_preview">AIM PREVIEW</string><string name="tap_to_test">Tap to test your aim settings</string><string name="test_aim_button">TEST AIM</string><string name="apply_settings_aim">APPLY SETTINGS</string><string name="reset_to_default">RESET TO DEFAULT</string><string name="advanced_aiming_controls">Advanced Aiming Controls</string><string name="compare_settings">COMPARE SETTINGS</string><string name="show_tips">SHOW TIPS</string><string name="hide_tips">HIDE TIPS</string><string name="aim_tips_title">AIMING TIPS</string><string name="sensitivity_tip">Higher sensitivity = faster aim movement but less precision</string><string name="dpi_tip">Higher DPI = more precise mouse/touch movement</string><string name="recoil_tip">Higher recoil compensation = less weapon kick when firing</string><string name="red_dot_tip">Smaller red dot = better precision for distant targets</string><string name="before_optimization">BEFORE</string><string name="after_optimization">AFTER</string><string name="tap_target">Tap on target to simulate shooting</string><string name="swipe_to_aim">Swipe to test aiming movement</string><string name="hold_to_fire">Hold to simulate continuous fire</string><string name="premium_features">PREMIUM FEATURES</string><string name="premium_features_description">Upgrade to Premium to unlock all these amazing features and enhance your gaming experience!</string><string name="premium_message_smart_aim">Unlock Smart Aim to get professional headshot optimization based on your device specifications!</string><string name="premium_message_aim_button">Unlock Aim Button for advanced aiming controls and customization!</string><string name="premium_feature_smart_aim">Smart Aim - Professional headshot optimization</string><string name="premium_feature_aim_button">Aim Button - Advanced aiming controls</string><string name="premium_feature_aim_overlay">Aim Overlay - Customizable crosshair for precise aiming</string><string name="premium_feature_no_ads">No Ads - Enjoy ad-free experience</string><string name="premium_feature_priority_updates">Priority Updates - Get new features first</string><string name="premium_feature_support">Premium Support - Direct support channel</string><string name="premium_feature_exclusive_content">Exclusive Content - Access to pro settings</string><string name="upgrade_now">UPGRADE NOW</string><string name="maybe_later">MAYBE LATER</string><string name="premium_features_unlocked">Premium features unlocked!</string><string name="subscribe_now">SUBSCRIBE NOW</string><string name="premium_version_benefits">Premium Version Benefits:</string><string name="aim_button_premium_message">Aim Button is a premium feature. Please upgrade to access it.</string><string name="smart_aim_premium_message">Smart Aim is a premium feature. Please upgrade to access it.</string><string name="smart_aim_enabled">Smart Aim enabled - Optimized for %1$s devices</string><string name="smart_aim_disabled">Smart Aim disabled</string><string name="recommended_settings_applied">Suggested settings applied based on general device info.</string><string name="testing_with_sensitivity">Testing with: Sensitivity %1$d | DPI %2$d</string><string name="time_seconds">Time: %1$d s</string><string name="score_value">Score: %1$d</string><string name="share_settings_title">My Free Fire Headshot Tool Settings</string><string name="share_settings_via">Share settings via</string><string name="welcome_premium_title">UNLOCK PRO FEATURES</string><string name="welcome_premium_description">Enhance your gaming experience with our Pro features!</string><string name="welcome_premium_monthly">Unlimited Access</string><string name="welcome_premium_yearly">$45/year (25% off)</string><string name="welcome_premium_lifetime">$99 one-time payment</string><string name="welcome_premium_trial">Premium Features</string><string name="welcome_premium_button">UPGRADE TO PRO</string><string name="welcome_premium_later">CONTINUE WITH FREE VERSION</string><string name="red_dot_color_set_to_yellow">Red dot color set to Yellow</string><string name="red_dot_color_set_to_blue_aim">Red dot color set to Blue</string><string name="red_dot_color_set_to_green">Red dot color set to Green</string><string name="red_dot_color_set_to_red">Red dot color set to Red</string><string name="comparing_settings">Comparing settings...</string><string name="showing_aim_tips">Showing aim tips...</string><string name="testing_aim_settings">Testing aim settings...</string><string name="game_mode_service">Game Mode Service</string><string name="monitoring_for_game_launches">Monitoring for game launches</string><string name="game_mode_active">Game Mode Active</string><string name="optimizing_for">"Optimizing for "</string><string name="optimizing_performance_for">"Optimizing performance for "</string><string name="graphics_quality_gfxtool">"Graphics Quality: "</string><string name="enabled">Enabled</string><string name="disabled">Disabled</string><string name="gfx_settings_applied_successfully">GFX settings applied successfully!</string><string name="settings_optimized_for_your_device">Settings optimized for your device</string><string name="dpi_set_to_400">DPI set to 400</string><string name="dpi_set_to_800">DPI set to 800</string><string name="dpi_set_to_1600">DPI set to 1600</string><string name="red_dot_color_set_to_red_2">Red dot color set to Red</string><string name="red_dot_color_set_to_green_2">Red dot color set to Green</string><string name="red_dot_color_set_to_blue_2">Red dot color set to Blue</string><string name="red_dot_color_set_to_yellow_2">Red dot color set to Yellow</string><string name="reset_settings">Reset Settings</string><string name="are_you_sure_you_want_to_reset_all_settings_to_default_values">Are you sure you want to reset all settings to default values?</string><string name="reset_23">Reset</string><string name="cancel_3">Cancel</string><string name="settings_reset_to_recommended_values">Settings reset to recommended values</string><string name="smart_aim_disabled_33">Smart Aim disabled</string><string name="graphics_level_set_to">"Graphics level set to "</string><string name="feature_coming_soon">" feature coming soon!"</string><string name="app_guide">Application guide</string><string name="suggest_best_settings">SUGGEST BEST SETTINGS</string><string name="best_settings_for_your_device">Best Settings For Your Device</string><string name="settings_suggested_successfully">Optimal settings suggested for your device!</string><string name="suggest_best_settings_description">Please wait while we analyze your device and suggest the best settings</string><string name="aim_crosshair">Aim Crosshair</string><string name="aim_overlay_enabled">Aim overlay enabled</string><string name="aim_overlay_disabled">Aim overlay disabled</string><string name="close">Close</string><string name="aim_overlay_permission_required">Permission required for aim overlay</string><string name="aim_overlay_permission_message">To show the aim overlay on screen, this app needs the \"Display over other apps\" permission. Please enable it in Settings.</string><string name="overlay_permission_denied">Permission denied. Crosshair overlay cannot be displayed without this permission.</string><string name="overlay_failed_to_show">Failed to display crosshair. Please try again.</string><string name="overlay_error_occurred">Error occurred while displaying crosshair: %1$s</string><string name="drag_to_move_crosshair">Drag the crosshair to move it anywhere on screen</string><string name="select_game_to_optimize">Select Game to Optimize</string><string name="add_game">Add Game</string><string name="free_version_game_limit">Free version: 1 game only. Upgrade to Pro for unlimited games.</string><string name="pro_version_unlimited_games">Pro version: Unlimited games</string><string name="select_game_from_list">Select a game from your installed apps</string><string name="no_games_selected">No games selected yet</string><string name="game_added_successfully">Game added successfully</string><string name="game_removed">Game removed</string><string name="launch_game">Launch Game</string><string name="remove_game">Remove Game</string><string name="game_limit_reached">Game limit reached. Upgrade to Pro for unlimited games.</string><string name="realtime_monitoring">Real-time Monitoring</string><string name="battery_temperature">Battery Temperature</string><string name="network_ping">Network Ping</string><string name="network_type">Network Type</string><string name="wifi_connected">WiFi</string><string name="mobile_data">Mobile Data</string><string name="no_connection">No Connection</string><string name="ping_excellent">Excellent</string><string name="ping_good">Good</string><string name="ping_fair">Fair</string><string name="ping_poor">Poor</string><string name="game_launched">Game launched successfully</string><string name="game_not_installed">Game not installed</string><string name="position_saved">Position saved</string><string name="ok">OK</string><string name="premium_feature">Premium Feature</string><string name="premium_feature_message">This feature is only available in the premium version. Upgrade now to access all premium features!</string><string name="aim_overlay_premium_title">Premium Feature: Aim Overlay</string><string name="aim_overlay_premium_message">The Aim Overlay feature is only available in the premium version. Upgrade now to access this and other premium features!</string><string name="gb">GB</string><string name="android">Android</string><string name="ram_label">RAM</string><string name="processor_label">Processor</string><string name="screen_label">Screen</string><string name="android_label">Android</string><string name="device_info">Device Info</string><string name="ram_info">RAM</string><string name="cpu_power">CPU Power</string><string name="screen_size">Screen</string><string name="estimated_fps">Est. FPS</string><string name="recommended_settings">Recommended Settings</string><string name="resolution_rec">• Resolution</string><string name="fps_rec">• FPS</string><string name="graphics_rec">• Graphics</string><string name="hdr_rec">• HDR</string><string name="antialiasing_rec">• Anti-aliasing</string><string name="basic">Basic</string><string name="powerful">Powerful</string><string name="screen_small">Small</string><string name="screen_medium">Medium</string><string name="screen_large">Large</string><string name="device_label">Device</string><string name="processor_info">Processor</string><string name="screen_info">Screen</string><string name="dpi_info">DPI</string><string name="estimated_fps_info">Approximate FPS estimation</string><string name="ad_failed_to_load">Ad failed to load</string><string name="ad_closed">Ad closed</string><string name="reward_earned">Reward earned!</string><string name="remove_ads">Remove Ads</string><string name="aim_overlay_settings">Aim Overlay Settings</string><string name="aim_overlay_color">Overlay Color</string><string name="aim_overlay_size">Overlay Size</string><string name="aim_overlay_toggle">Toggle Overlay</string><string name="launch_free_fire">Launch Free Fire</string><string name="clean_device">Clean Device</string><string name="free_fire_not_installed">Free Fire is not installed on this device</string><string name="device_cleaned">Device cleaned and optimized for gaming</string><string name="game_mode_activated">Game Mode activated</string><string name="crosshair_size">Crosshair Size</string><string name="game_optimization">Game Optimization</string><string name="size_small">Small</string><string name="size_medium">Medium</string><string name="size_large">Large</string><string name="overlay_enabled">Overlay Enabled</string><string name="overlay_disabled">Overlay Disabled</string><string name="settings_applied">Settings Applied</string><string name="settings_reset">Settings Reset to Default</string><string name="free_fire_launched">Launching Free Fire</string><string name="cleaning_device">Cleaning device...</string><string name="cpu">CPU</string><string name="ram">RAM</string><string name="select_graphics_level">SELECT GRAPHICS LEVEL</string><string name="choose_the_graphics_level_you_use_in_free_fire">Choose the graphics level you use in Free Fire</string><string name="smooth">SMOOTH</string><string name="standard_gfx_3">STANDARD</string><string name="high_gfx2">HIGH</string><string name="apply">APPLY</string><string name="please_wait_while_we_optimize_your_game_settings">Please wait while we optimize your game settings</string><string name="aim_test">AIM TEST</string><string name="tap_the_targets_as_quickly_as_possible">Tap the targets as quickly as possible</string><string name="testing_with_default_sensitivity">Testing with default sensitivity</string><string name="score_0">Score: 0</string><string name="time_0s">Time: 0s</string><string name="app_name_welcom_screen">GFX TOOLS</string><string name="settings_amp_game_booster_welcome_screen"><![CDATA[ Headshot & Game Booster]]></string><string name="powering_up_welcomescreen">POWERING UP...</string><string name="enable_dark_theme_throughout_the_app">Enable dark theme throughout the app</string><string name="headshot_game_enhancer_v1_0">Game Booster Fix Lag - Gfx Tool v1.0</string><string name="developed_by_mahmoud_ff_yt" translatable="false">Developed by: mahmoud ff yt</string><string name="for_more_suggestions_and_solutions_contact_us_via_email">For more suggestions and solutions, contact us via email</string><string name="email_contact"><EMAIL></string><string name="pro_version">Pro version</string><string name="app_version">Version 1.0.0</string><string name="launch_error">An error occurred while trying to launch the game.</string><string name="cleaning_complete_notification">Analysis complete! System status reviewed.</string><string name="last_optimization_saved">Analysis saved for 3 minutes</string><string name="optimization_expired_notification">Analysis period has expired. Analyze your device again!</string><string name="pro_version_title">Upgrade to Pro Version</string><string name="pro_version_description">Unlock all premium features and enhance your gaming experience!</string><string name="pro_version_price">$5/month</string><string name="pro_feature_aim_overlay">Advanced Aim Overlay - Customizable crosshair for precise aiming</string><string name="pro_feature_game_mode">Enhanced Game Mode - Maximum performance optimization</string><string name="pro_feature_no_ads">Ad-Free Experience - No interruptions during gameplay</string><string name="pro_feature_priority_support">Priority Support - Get help when you need it</string><string name="pro_feature_exclusive_updates">Exclusive Updates - Get new features first</string><string name="try_free">Try Free for 1 Hour</string><string name="watch_ad_for_trial">Try this feature for 1 hour (once per day for 3 days)</string><string name="trial_activated">Trial activated! You can use this feature for 1 hour</string><string name="trial_expired">Trial period has expired. Please upgrade to Pro for unlimited access.</string><string name="days_remaining">Trial days remaining: %d</string><string name="hours_remaining">Hours remaining: %d</string><string name="minutes_remaining">Minutes remaining: %d</string><string name="trial_limit_reached">You\'ve reached your daily trial limit. Come back tomorrow or upgrade to Pro for unlimited access.</string><string name="high_usage_notification">Your device is under heavy load (CPU: %1$d%%, RAM: %2$d%%). Boost now for better gaming!</string><string name="daily_reminder_notification_1">Did you forget to boost your device today? It only takes a second!</string><string name="daily_reminder_notification_2">Don\'t play without boosting your device for the best gaming speed! Boost now!</string><string name="exit_confirmation">Are you sure you want to exit?</string><string name="yes">Yes</string><string name="no">No</string><string name="permission_required">Permission required</string><string name="alarm_permission_title">Alarm Permission Required</string><string name="alarm_permission_message">To schedule notifications and reminders, this app needs permission to set exact alarms. Please enable it in Settings.</string><string name="overlay_permission_title">Overlay Permission Required</string><string name="overlay_permission_message">To display aim overlay and other features on screen, this app needs permission to draw over other apps. Please enable it in Settings.</string><string name="permissions_explanation">This app requires several permissions to function properly. Please grant all requested permissions for the best experience.</string><string name="purchase_failed">Purchase failed. Please try again later.</string><string name="product_not_available">This product is not available at the moment.</string><string name="error_message">Error: %1$s</string><string name="feedback">Feedback</string><string name="ad_loading">Ad is loading. Please try again in a moment.</string><string name="ad_closed_without_reward">Ad closed without reward. Proceeding anyway.</string><string name="ad_space">Ad space</string><string name="webview_not_available">WebView is not available on this device. Some features may not work properly.</string><string name="webview_not_available_title">WebView Not Available</string><string name="webview_not_available_message">Your device is missing WebView, which is required for ads and some app features. Please install or update WebView from the Google Play Store.</string><string name="open_settings">Open Settings</string><string name="error_opening_settings">Error opening settings. Please try manually.</string><string name="retry">Retry</string><string name="retrying_ad_load">Retrying ad load…</string><string name="ad_loading_short">Ad loading…</string><string name="alternative_ad_loading">Trying alternative ad loading method…</string><string name="ad_load_failed">Ad load failed. Showing placeholder.</string><string name="check_webview">Checking WebView availability…</string><string name="webview_available">WebView is now available. Ads should work properly.</string><string name="webview_still_unavailable">WebView is still unavailable. Please install or update WebView.</string><string name="game_mode_activated_for_one_hour">Game Mode activated for 1 hour!</string><string name="ad_reward_failed">Failed to receive reward. Please try again.</string><string name="usage_access_denied_message">Without usage access, some features may not work properly.</string><string name="premium_price_title">MONTHLY SUBSCRIPTION</string><string name="premium_price_description">Monthly subscription for access to all premium features</string><string name="loading_price">5$</string><string name="app_disclaimer_title">Important Notice</string><string name="app_disclaimer_message">This app provides monitoring and analysis tools for gaming performance. It does not modify system settings or guarantee performance improvements. Results may vary by device.</string><string name="monitoring_notice">This app monitors system performance and does not make actual changes to your device\'s core settings.</string><string name="performance_notice">INFORMATION ONLY: This tool provides system monitoring and information display only. No modifications, optimizations, or performance enhancements are made to your device.</string><string name="analysis_disclaimer">Gaming Ready</string><string name="understand">I Understand</string><string name="monitoring_tools_notice">These are monitoring and analysis tools designed to help you understand your device\'s performance during gaming.</string><string name="gaming_performance_report">Gaming Session Report</string><string name="device_gaming_analysis">Your device information for gaming session awareness</string><string name="gaming_score">Device Rating</string><string name="excellent_gaming">Good for Gaming</string><string name="gaming_ready">Gaming Compatible</string><string name="gaming_recommendations">Gaming Information</string><string name="free_fire_settings">Free Fire Information</string><string name="high_graphics_recommended">Device supports high graphics settings</string><string name="fps_gaming">FPS Gaming Info</string><string name="smooth_fps_expected">Device capable of smooth gameplay</string><string name="optimal">COMPATIBLE</string><string name="excellent">SUITABLE</string><string name="gaming_tips">Gaming Tips</string><string name="gaming_tips_content">• Monitor background apps during gaming\n• Use Gaming Focus Mode for awareness\n• Keep device cool during long sessions\n• Check system resources regularly</string><string name="got_it">Got It!</string><string name="pro_status_active">PRO MODE ACTIVE</string><string name="currently_in_pro_mode">You are currently in Pro Mode</string><string name="pro_benefits_summary">Enjoy unlimited access to all premium features</string><string name="subscription_management">Subscription Management</string><string name="cancel_subscription_info">To cancel: Google Play Store > Subscriptions</string><string name="manage_subscription">Manage Subscription</string><string name="google_play_transparency">Managed via Google Play Store. Cancel anytime in account settings.</string><string name="premium_crown">Premium Crown</string><string name="error_opening_subscription_management">Unable to open subscription management</string><string name="system_analysis">Analyzing...</string><string name="needs_optimization">High Usage</string><string name="memory_optimized">✓ Memory optimized</string><string name="cache_cleaned">✓ Cache cleared</string><string name="background_apps_managed">✓ Background apps managed</string><string name="optimization_completed_items">Completed Tasks:</string><string name="device_optimized_success">Your device is now in good condition</string><string name="optimization_success_description">Background apps have been managed and cache has been cleared</string></file><file path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\values\styles.xml" qualifiers=""><style name="AlertDialogTheme" parent="ThemeOverlay.MaterialComponents.Dialog.Alert">
        <item name="colorPrimary">@color/cyber_neon_cyan</item>
        <item name="colorAccent">@color/cyber_neon_cyan</item>
        <item name="android:background">@color/cyber_dark_start</item>
        <item name="android:textColorPrimary">@color/cyber_text_primary</item>
        <item name="android:textColor">@color/cyber_text_primary</item>
        <item name="buttonBarPositiveButtonStyle">@style/AlertDialogButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/AlertDialogButtonStyle</item>
    </style><style name="AlertDialogButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/cyber_neon_cyan</item>
    </style><style name="ArabicTextViewStyle">
        <item name="android:fontFamily">@font/font_for_ar_en</item>
        <item name="android:textColor">@color/cyber_text_primary</item>
    </style><style name="ArabicTextViewBoldStyle">
        <item name="android:fontFamily">@font/font_for_ar_en</item>
        <item name="android:textColor">@color/cyber_text_primary</item>
    </style><style name="ArabicTextViewMediumStyle">
        <item name="android:fontFamily">@font/font_for_ar_en</item>
        <item name="android:textColor">@color/cyber_text_primary</item>
    </style><style name="PremiumFeatureStyle">
        <item name="android:fontFamily">@font/font_for_ar_en</item>
        <item name="android:textColor">@color/cyber_neon_gold</item>
        <item name="android:drawableTint">@color/cyber_neon_gold</item>
        <item name="android:drawablePadding">8dp</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:textSize">14sp</item>
        <item name="android:paddingTop">4dp</item>
        <item name="android:paddingBottom">4dp</item>
    </style><style name="PremiumButtonStyle" parent="Widget.MaterialComponents.Button">
        <item name="android:fontFamily">@font/font_for_ar_en</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:textSize">16sp</item>
        <item name="android:paddingTop">8dp</item>
        <item name="android:paddingBottom">8dp</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="backgroundTint">@color/cyber_neon_gold</item>
        <item name="cornerRadius">24dp</item>
        <item name="android:elevation">4dp</item>
    </style><style name="CyberNeonButtonStyle">
        <item name="android:background">@drawable/neon_button_bg</item>
        <item name="android:fontFamily">@font/rajdhani_bold</item>
        <item name="android:textColor">@color/cyber_neon_cyan</item>
        <item name="android:textSize">16sp</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
        <item name="android:stateListAnimator">@animator/button_state_animator</item>
    </style><style name="CyberNeonHeadingStyle">
        <item name="android:fontFamily">@font/orbitron_bold</item>
        <item name="android:textColor">@color/cyber_neon_cyan</item>
        <item name="android:textSize">22sp</item>
        <item name="android:shadowColor">@color/cyber_neon_cyan</item>
        <item name="android:shadowDx">0</item>
        <item name="android:shadowDy">0</item>
        <item name="android:shadowRadius">8</item>
    </style><style name="CyberNeonSubheadingStyle">
        <item name="android:fontFamily">@font/rajdhani_bold</item>
        <item name="android:textColor">@color/cyber_text_primary</item>
        <item name="android:textSize">16sp</item>
    </style><style name="CyberNeonBodyTextStyle">
        <item name="android:fontFamily">@font/cairo_regular</item>
        <item name="android:textColor">@color/cyber_text_secondary</item>
        <item name="android:textSize">14sp</item>
    </style></file><file path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\values\text_appearances.xml" qualifiers=""><style name="TextAppearance.App.Button" parent="TextAppearance.MaterialComponents.Button">
        <item name="fontFamily">@font/font_for_ar_en</item>
        <item name="android:fontFamily">@font/font_for_ar_en</item>
    </style><style name="TextAppearance.App.Body1" parent="TextAppearance.MaterialComponents.Body1">
        <item name="fontFamily">@font/font_for_ar_en</item>
        <item name="android:fontFamily">@font/font_for_ar_en</item>
    </style><style name="TextAppearance.App.Body2" parent="TextAppearance.MaterialComponents.Body2">
        <item name="fontFamily">@font/font_for_ar_en</item>
        <item name="android:fontFamily">@font/font_for_ar_en</item>
    </style><style name="TextAppearance.App.Subtitle1" parent="TextAppearance.MaterialComponents.Subtitle1">
        <item name="fontFamily">@font/font_for_ar_en</item>
        <item name="android:fontFamily">@font/font_for_ar_en</item>
    </style><style name="TextAppearance.App.Subtitle2" parent="TextAppearance.MaterialComponents.Subtitle2">
        <item name="fontFamily">@font/font_for_ar_en</item>
        <item name="android:fontFamily">@font/font_for_ar_en</item>
    </style><style name="TextAppearance.App.Headline6" parent="TextAppearance.MaterialComponents.Headline6">
        <item name="fontFamily">@font/font_for_ar_en</item>
        <item name="android:fontFamily">@font/font_for_ar_en</item>
    </style><style name="TextAppearance.App.Headline5" parent="TextAppearance.MaterialComponents.Headline5">
        <item name="fontFamily">@font/font_for_ar_en</item>
        <item name="android:fontFamily">@font/font_for_ar_en</item>
    </style><style name="TextAppearance.App.Headline4" parent="TextAppearance.MaterialComponents.Headline4">
        <item name="fontFamily">@font/font_for_ar_en</item>
        <item name="android:fontFamily">@font/font_for_ar_en</item>
    </style><style name="TextAppearance.App.Headline3" parent="TextAppearance.MaterialComponents.Headline3">
        <item name="fontFamily">@font/font_for_ar_en</item>
        <item name="android:fontFamily">@font/font_for_ar_en</item>
    </style><style name="TextAppearance.App.Headline2" parent="TextAppearance.MaterialComponents.Headline2">
        <item name="fontFamily">@font/font_for_ar_en</item>
        <item name="android:fontFamily">@font/font_for_ar_en</item>
    </style><style name="TextAppearance.App.Headline1" parent="TextAppearance.MaterialComponents.Headline1">
        <item name="fontFamily">@font/font_for_ar_en</item>
        <item name="android:fontFamily">@font/font_for_ar_en</item>
    </style><style name="TextAppearance.App.Caption" parent="TextAppearance.MaterialComponents.Caption">
        <item name="fontFamily">@font/font_for_ar_en</item>
        <item name="android:fontFamily">@font/font_for_ar_en</item>
    </style><style name="TextAppearance.App.Overline" parent="TextAppearance.MaterialComponents.Overline">
        <item name="fontFamily">@font/font_for_ar_en</item>
        <item name="android:fontFamily">@font/font_for_ar_en</item>
    </style></file><file path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.HeadshotSettingsGameBooster" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        
    </style><style name="no_bar" parent="Theme.MaterialComponents.NoActionBar">
        
        <item name="colorPrimary">@color/electric_blue</item>
        <item name="colorPrimaryVariant">@color/neon_blue</item>
        <item name="colorOnPrimary">@color/black</item>
        
        <item name="colorSecondary">@color/neon_blue</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">@color/cyber_dark_start</item>
        
        <item name="android:windowBackground">@drawable/cyber_neon_gradient_bg</item>
        
        <item name="android:navigationBarColor">@color/cyber_dark_start</item>

        
        <item name="textAppearanceButton">@style/TextAppearance.App.Button</item>
        <item name="textAppearanceBody1">@style/TextAppearance.App.Body1</item>
        <item name="textAppearanceBody2">@style/TextAppearance.App.Body2</item>
        <item name="textAppearanceSubtitle1">@style/TextAppearance.App.Subtitle1</item>
        <item name="textAppearanceSubtitle2">@style/TextAppearance.App.Subtitle2</item>
        <item name="textAppearanceHeadline6">@style/TextAppearance.App.Headline6</item>
        <item name="textAppearanceHeadline5">@style/TextAppearance.App.Headline5</item>
        <item name="textAppearanceHeadline4">@style/TextAppearance.App.Headline4</item>
        <item name="textAppearanceHeadline3">@style/TextAppearance.App.Headline3</item>
        <item name="textAppearanceHeadline2">@style/TextAppearance.App.Headline2</item>
        <item name="textAppearanceHeadline1">@style/TextAppearance.App.Headline1</item>
        <item name="textAppearanceCaption">@style/TextAppearance.App.Caption</item>
        <item name="textAppearanceOverline">@style/TextAppearance.App.Overline</item>

        
        <item name="android:fontFamily">@font/font_for_ar_en</item>
        <item name="fontFamily">@font/font_for_ar_en</item>
    </style></file><file path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\values-ar\strings.xml" qualifiers="ar"><string name="app_name">أداة تحسين الألعاب لإصلاح التأخر - Gfx Tool</string><string name="navigation_drawer_open">افتح قائمة التنقل</string><string name="navigation_drawer_close">أغلق قائمة التنقل</string><string name="settings">الإعدادات</string><string name="rate_us">قيمنا</string><string name="privacy_policy">سياسة الخصوصية</string><string name="about">عن التطبيق</string><string name="exit">خروج</string><string name="premium">بريميوم</string><string name="premium_version">النسخة المدفوعة (بدون إعلانات)</string><string name="dark_mode">الوضع المظلم</string><string name="language">اللغة</string><string name="english">English</string><string name="arabic">العربية</string><string name="select_language">اختار اللغة</string><string name="language_settings">إعدادات اللغة</string><string name="language_change_message">التطبيق هيعيد التشغيل علشان يطبق تغيير اللغة</string><string name="restart">إعادة تشغيل</string><string name="game_booster_title">معزز الالعاب</string><string name="game_booster_description">راقب جلسات الألعاب من خلال تتبع التطبيقات في الخلفية واستخدام موارد النظام لوعي أفضل أثناء اللعب.</string><string name="cpu_status">استخدام المعالج</string><string name="memory_status">استخدام الذاكرة</string><string name="background_apps">التطبيقات الخلفية</string><string name="before">الحالي</string><string name="after">الان</string><string name="total_memory">إجمالي الذاكرة</string><string name="used_memory">الذاكرة المستخدمة</string><string name="free_memory">الذاكرة المتاحة</string><string name="ram_speed">معلومات الذاكرة</string><string name="high_usage_apps">التطبيقات كثيفة الموارد</string><string name="apps_description">التطبيقات دي بتستهلك معظم موارد الجهاز:</string><string name="background_process">العمليات الخلفية</string><string name="no_apps_found">مفيش تطبيقات عالية الاستهلاك</string><string name="tap_boost_button">دوس على زرار تحليل دلوقتي للفحص</string><string name="app_closed">تم إغلاق %1$s</string><string name="app_close_failed">مقدرناش نقفل %1$s</string><string name="system_monitor_channel">مراقب النظام</string><string name="system_monitor_channel_description">إشعارات عن أداء النظام</string><string name="optimization_needed">يُنصح بتحليل الجهاز</string><string name="optimization_needed_description">يمكن تحليل أداء جهازك. اضغط للفحص.</string><string name="usage_access_required">صلاحية الوصول للاستخدام مطلوبة</string><string name="usage_access_message">علشان نعرض قائمة دقيقة للتطبيقات الشغالة، التطبيق محتاج صلاحية الوصول للاستخدام. من فضلك فعلها في الإعدادات.</string><string name="go_to_settings">روح للإعدادات</string><string name="cancel">إلغاء</string><string name="last_used">آخر استخدام: %1$s</string><string name="done">اكتمل التحليل</string><string name="device_optimized_description">اكتمل التحليل. تم مراجعة حالة النظام.</string><string name="optimized_items">العناصر المحللة:</string><string name="apps_closed_count">تم إدارة %1$d تطبيق خلفي</string><string name="cache_cleared_size">تم تحليل %1$s من الكاش</string><string name="ram_optimized">استخدام الرام: %1$d%% (القراءة الحالية)</string><string name="optimizing_progress">جاري الفحص: %1$d%%</string><string name="vibrant_green">#4CAF50</string><string name="optimization_expired">انتهت جلسة الفحص</string><string name="boost_now">فحص حالة النظام</string><string name="optimized">الفحص مكتمل</string><string name="ready_to_optimize">جاهز لفحص حالة جهازك</string><string name="optimizing">جاري فحص حالة جهازك…</string><string name="clearing_cache">قراءة معلومات التخزين المؤقت…</string><string name="optimizing_cpu">قراءة استخدام المعالج…</string><string name="closing_apps">فحص التطبيقات الخلفية…</string><string name="optimizing_ram">قراءة معلومات الذاكرة…</string><string name="optimization_complete">الفحص مكتمل. تم مراجعة حالة النظام للوعي أثناء الألعاب.</string><string name="already_optimized">الجهاز مفحوص بالفعل!</string><string name="optimization_success">فحص مراقب جلسة الألعاب مكتمل!</string><string name="game_mode_title">وضع الألعاب</string><string name="game_mode_description">راقب جلسات الألعاب وتتبع أداء النظام للحصول على وعي أفضل ورؤى مفيدة أثناء اللعب.</string><string name="activate_game_mode">بدء</string><string name="deactivate_game_mode">إيقاف</string><string name="performance_stats">حالة النظام</string><string name="cpu_boost">حالة المعالج</string><string name="cpu_boost_value">مراقبة</string><string name="ram_optimization">حالة الرام</string><string name="ram_optimization_value">مراقبة</string><string name="network_priority">حالة الشبكة</string><string name="network_priority_value">مراقبة</string><string name="battery_optimization">حالة البطارية</string><string name="battery_optimization_value">مراقبة</string><string name="game_mode_status_active">لوحة مراقبة جلسة الألعاب نشطة - مراقبة النظام مفعلة</string><string name="game_mode_status_inactive">لوحة مراقبة جلسة الألعاب غير نشطة</string><string name="game_mode_notification_title">لوحة مراقبة جلسة الألعاب نشطة</string><string name="game_mode_notification_text">مراقبة النظام نشطة لجلسة الألعاب</string><string name="device_info_gfx">معلومات الجهاز</string><string name="resolution_gfx">الدقة</string><string name="low_720p">منخفضة (720p)</string><string name="medium_1080p">متوسطة (1080p)</string><string name="high_1440p">عالية (1440p)</string><string name="ultra_2160p">فائقة (2160p)</string><string name="advanced_options_gfx">خيارات متقدمة</string><string name="apply_settings_gfx">تطبيق الإعدادات</string><string name="gpu_boost_gfx">تعزيز كارت الشاشة</string><string name="anti_aliasing_gfx">التنعيم</string><string name="hdr_mode_gfx">وضع HDR</string><string name="_4x_scope">سكوب 4X</string><string name="awm_scope">سكوب AWM</string><string name="awm_scope_sensitivity">حساسية سكوب AWM</string><string name="game_mode_features">ميزات وضـع الألعاب</string><string name="auto_optimization">المراقبة التلقائية</string><string name="auto_optimization_desc">يتتبع تلقائياً أداء النظام عند اكتشاف تطبيقات الألعاب</string><string name="network_priority_title">مراقب الشبكة</string><string name="network_priority_desc">يعرض معلومات استخدام الشبكة أثناء جلسات الألعاب</string><string name="cpu_boost_title">مراقب المعالج</string><string name="cpu_boost_desc">يعرض معلومات استخدام المعالج للوعي أثناء الألعاب</string><string name="battery_optimization_title">مراقب البطارية</string><string name="battery_optimization_desc">يتتبع استخدام البطارية أثناء جلسات الألعاب</string><string name="supported_games">الألعاب المدعومة</string><string name="supported_games_desc">وضع الألعاب بيكتشف ويراقب تلقائياً الألعاب الشهيرة ومنها:</string><string name="pubg_mobile">ببجي موبايل (تم التعرف عليها للمراقبة)</string><string name="call_of_duty">كول أوف ديوتي (تم التعرف عليها للمراقبة)</string><string name="minecraft">ماينكرافت (تم التعرف عليها للمراقبة)</string><string name="fortnite">فورتنايت (تم التعرف عليها للمراقبة)</string><string name="asphalt_9">أسفلت 9 (تم التعرف عليها للمراقبة)</string><string name="free_fire">فري فاير (تم التعرف عليها للمراقبة)</string><string name="more_games_detected">...وألعاب تانية كتير بيتم اكتشافها تلقائياً!</string><string name="headshot_tool_title">أداة الهيدشوت</string><string name="free_fire_optimization">تحسين عامة لفري فاير</string><string name="device_analysis">تحليل الجهاز</string><string name="analyzing_device">جاري تحليل الجهاز...</string><string name="general">عام</string><string name="general_sensitivity">الحساسية العامة</string><string name="graphics_level">مستوى الجرافيكس</string><string name="standard">قياسي</string><string name="dpi_setting">إعداد DPI</string><string name="red_dot">النقطة الحمرا</string><string name="red_dot_sensitivity">حساسية النقطة الحمرا</string><string name="red_dot_color">لون النقطة الحمرا</string><string name="scope_2x">سكوب 2X</string><string name="scope_2x_sensitivity">حساسية سكوب 2x</string><string name="scope_4x">سكوب 4X</string><string name="scope_4x_sensitivity">حساسية سكوب 4x</string><string name="awm_scope_sensitivity_label">حساسية سكوب AWM</string><string name="breath_hold">حبس النفس</string><string name="free_camera_button">زرار الكاميرا الحرة</string><string name="free_camera_sensitivity">حساسية الكاميرا الحرة</string><string name="smart_aim">التصويب الذكي</string><string name="enable_smart_aim">تفعيل التصويب الذكي</string><string name="smart_aim_description">يساعد على ضبط إعدادات الحساسية بناءً على تفضيلاتنا.</string><string name="apply_settings_headshot">اقتراح أفضل إعدادات</string><string name="test_aim">اختبار التصويب</string><string name="reset">إعادة ضبط</string><string name="share">مشاركة</string><string name="fps_30">30 FPS</string><string name="fps_60">60 FPS</string><string name="fps_90">90 FPS</string><string name="low">منخفض</string><string name="medium">متوسط</string><string name="high">عالي</string><string name="awm_scope_title">سكوب AWM</string><string name="game_booster">معزز الألعاب</string><string name="optimize_your_gaming_experience">حسن تجربة اللعب بتاعتك</string><string name="headshot">هيدشوت</string><string name="game_enhancer">معزز الألعاب</string><string name="gfx_tools_main">أدوات GFX</string><string name="headshot_tool">أداة الهيدشوت</string><string name="aim_button">إعدادات التصويب برو</string><string name="mood_game">مود اللعبة</string><string name="aim_sensitivity">حساسية التصويب</string><string name="horizontal_sensitivity">الحساسية الأفقية</string><string name="vertical_sensitivity">الحساسية الرأسية</string><string name="dpi_settings">إعدادات DPI</string><string name="select_optimal_dpi">اختار أفضل DPI لجهازك</string><string name="red_dot_optimization">تحسين النقطة الحمرا</string><string name="recoil_control">التحكم في الارتداد</string><string name="recoil_compensation">تعويض الارتداد</string><string name="graphics_optimization">تحسين الجرافيكس</string><string name="fps_settings">إعدادات FPS</string><string name="aim_preview">معاينة التصويب</string><string name="tap_to_test">اضغط لاختبار إعدادات التصويب بتاعتك</string><string name="test_aim_button">اختبار التصويب</string><string name="apply_settings_aim">تطبيق الإعدادات</string><string name="reset_to_default">إعادة للإعدادات الافتراضية</string><string name="advanced_aiming_controls">أدوات تصويب متقدمة</string><string name="compare_settings">مقارنة الإعدادات</string><string name="show_tips">عرض النصائح</string><string name="hide_tips">إخفاء النصائح</string><string name="aim_tips_title">نصائح التصويب</string><string name="sensitivity_tip">الحساسية العالية = حركة تصويب أسرع لكن دقة أقل</string><string name="dpi_tip">DPI الأعلى = حركة ماوس/لمس أكثر دقة</string><string name="recoil_tip">تعويض ارتداد أعلى = ارتداد سلاح أقل عند إطلاق النار</string><string name="red_dot_tip">نقطة حمرا أصغر = دقة أفضل للأهداف البعيدة</string><string name="before_optimization">قبل</string><string name="after_optimization">بعد</string><string name="tap_target">اضغط على الهدف لمحاكاة التصويب</string><string name="swipe_to_aim">اسحب لاختبار حركة التصويب</string><string name="hold_to_fire">اضغط مطولاً لمحاكاة إطلاق النار المستمر</string><string name="premium_features">المميزات المدفوعة</string><string name="premium_features_description">ترقى للنسخة المدفوعة علشان تفتح كل المميزات الرائعة دي وتحسن تجربة اللعب بتاعتك!</string><string name="premium_message_smart_aim">افتح التصويب الذكي للحصول على تحسين هيدشوت احترافي بناءً على مواصفات جهازك!</string><string name="premium_message_aim_button">افتح زرار التصويب لإعدادات تصويب متقدمة وتخصيص!</string><string name="premium_feature_smart_aim">التصويب الذكي - تحسين هيدشوت احترافي</string><string name="premium_feature_aim_button">زرار التصويب - إعدادات تصويب متقدمة</string><string name="premium_feature_aim_overlay">طبقة التصويب - نشان قابل للتخصيص للتصويب الدقيق</string><string name="premium_feature_no_ads">بدون إعلانات - استمتع بتجربة خالية من الإعلانات</string><string name="premium_feature_priority_updates">تحديثات بأولوية - احصل على المميزات الجديدة أولاً</string><string name="premium_feature_support">دعم مميز - قناة دعم مباشرة</string><string name="premium_feature_exclusive_content">محتوى حصري - وصول لإعدادات احترافية</string><string name="upgrade_now">ترقية دلوقتي</string><string name="maybe_later">ربما لاحقاً</string><string name="premium_features_unlocked">تم فتح المميزات المدفوعة!</string><string name="subscribe_now">اشترك دلوقتي</string><string name="premium_version_benefits">مميزات النسخة المدفوعة:</string><string name="aim_button_premium_message">زرار التصويب ميزة مدفوعة. من فضلك ترقى للوصول إليها.</string><string name="smart_aim_premium_message">التصويب الذكي ميزة مدفوعة. من فضلك ترقى للوصول إليها.</string><string name="smart_aim_enabled">تم تفعيل التصويب الذكي - مُحسن لأجهزة %1$s</string><string name="smart_aim_disabled">تم تعطيل التصويب الذكي</string><string name="recommended_settings_applied">تم تطبيق الإعدادات المقترحة بناءً على معلومات الجهاز.</string><string name="testing_with_sensitivity">اختبار بـ: حساسية %1$d | DPI %2$d</string><string name="time_seconds">الوقت: %1$d ثانية</string><string name="score_value">النتيجة: %1$d</string><string name="share_settings_title">إعدادات أداة الهيدشوت بتاعتي للفري فاير</string><string name="share_settings_via">مشاركة الإعدادات عبر</string><string name="welcome_premium_title">افتح مميزات النسخة الاحترافية</string><string name="welcome_premium_description">حسن تجربة اللعب بتاعتك مع مميزاتنا الاحترافية!</string><string name="welcome_premium_monthly">$5/شهرياً</string><string name="welcome_premium_yearly">$45/سنوياً (خصم 25%)</string><string name="welcome_premium_lifetime">$99 دفعة واحدة</string><string name="welcome_premium_trial">جرب مجاناً لمدة 3 أيام</string><string name="welcome_premium_button">الترقية للنسخة الاحترافية</string><string name="welcome_premium_later">الاستمرار بالنسخة المجانية</string><string name="red_dot_color_set_to_yellow">تم ضبط لون النقطة الحمرا على الأصفر</string><string name="red_dot_color_set_to_blue_aim">تم ضبط لون النقطة الحمرا على الأزرق</string><string name="red_dot_color_set_to_green">تم ضبط لون النقطة الحمرا على الأخضر</string><string name="red_dot_color_set_to_red">تم ضبط لون النقطة الحمرا على الأحمر</string><string name="comparing_settings">جاري مقارنة الإعدادات...</string><string name="showing_aim_tips">جاري عرض نصائح التصويب...</string><string name="testing_aim_settings">جاري اختبار إعدادات التصويب...</string><string name="game_mode_service">خدمة وضع الألعاب</string><string name="monitoring_for_game_launches">مراقبة تشغيل الألعاب</string><string name="game_mode_active">وضع الألعاب مفعل</string><string name="optimizing_for">"تحسين لـ "</string><string name="optimizing_performance_for">"تحسين الأداء لـ "</string><string name="graphics_quality_gfxtool">"جودة الجرافيكس: "</string><string name="enabled">مفعل</string><string name="disabled">معطل</string><string name="gfx_settings_applied_successfully">تم تطبيق إعدادات GFX بنجاح!</string><string name="settings_optimized_for_your_device">تم تحسين الإعدادات لجهازك</string><string name="dpi_set_to_400">تم ضبط DPI على 400</string><string name="dpi_set_to_800">تم ضبط DPI على 800</string><string name="dpi_set_to_1600">تم ضبط DPI على 1600</string><string name="red_dot_color_set_to_red_2">تم ضبط لون النقطة الحمرا على الأحمر</string><string name="red_dot_color_set_to_green_2">تم ضبط لون النقطة الحمرا على الأخضر</string><string name="red_dot_color_set_to_blue_2">تم ضبط لون النقطة الحمرا على الأزرق</string><string name="red_dot_color_set_to_yellow_2">تم ضبط لون النقطة الحمرا على الأصفر</string><string name="reset_settings">إعادة ضبط الإعدادات</string><string name="are_you_sure_you_want_to_reset_all_settings_to_default_values">هل أنت متأكد من إعادة ضبط كل الإعدادات للقيم الافتراضية؟</string><string name="reset_23">إعادة ضبط</string><string name="cancel_3">إلغاء</string><string name="settings_reset_to_recommended_values">تم إعادة ضبط الإعدادات للقيم الموصى بها</string><string name="smart_aim_disabled_33">تم تعطيل التصويب الذكي</string><string name="graphics_level_set_to">"تم ضبط مستوى الجرافيكس على "</string><string name="feature_coming_soon">"الميزة قريباً!"</string><string name="app_guide">دليل التطبيق</string><string name="suggest_best_settings">اقتراح أفضل إعدادات</string><string name="best_settings_for_your_device">أفضل إعدادات لجهازك</string><string name="settings_suggested_successfully">تم اقتراح الإعدادات المثالية لجهازك!</string><string name="suggest_best_settings_description">استنى شوية لحد ما نحلل جهازك ونقترح أفضل إعدادات</string><string name="aim_crosshair">نشان التصويب</string><string name="aim_overlay_enabled">تم تفعيل طبقة التصويب</string><string name="aim_overlay_disabled">تم تعطيل طبقة التصويب</string><string name="close">إغلاق</string><string name="aim_overlay_permission_required">مطلوب إذن لطبقة التصويب</string><string name="aim_overlay_permission_message">لعرض طبقة التصويب على الشاشة، التطبيق محتاج إذن \"العرض فوق التطبيقات الأخرى\". من فضلك فعله في الإعدادات.</string><string name="overlay_permission_denied">تم رفض الصلاحية. لا يمكن عرض نشان التصويب بدون هذه الصلاحية.</string><string name="overlay_failed_to_show">فشل في عرض نشان التصويب. حاول مرة أخرى.</string><string name="overlay_error_occurred">حدث خطأ أثناء عرض نشان التصويب: %1$s</string><string name="drag_to_move_crosshair">اسحب النشان لتحريكه في أي مكان على الشاشة</string><string name="select_game_to_optimize">اختر لعبة للتحسين</string><string name="add_game">إضافة لعبة</string><string name="free_version_game_limit">النسخة المجانية: لعبة واحدة فقط. ترقى للبرو للحصول على ألعاب غير محدودة.</string><string name="pro_version_unlimited_games">النسخة البرو: ألعاب غير محدودة</string><string name="select_game_from_list">اختر لعبة من التطبيقات المثبتة</string><string name="no_games_selected">لم يتم اختيار أي ألعاب بعد</string><string name="game_added_successfully">تم إضافة اللعبة بنجاح</string><string name="game_removed">تم حذف اللعبة</string><string name="launch_game">تشغيل اللعبة</string><string name="remove_game">حذف اللعبة</string><string name="game_limit_reached">تم الوصول لحد الألعاب. ترقى للبرو للحصول على ألعاب غير محدودة.</string><string name="realtime_monitoring">المراقبة الحقيقية</string><string name="battery_temperature">حرارة البطارية</string><string name="network_ping">بينج الشبكة</string><string name="network_type">نوع الشبكة</string><string name="wifi_connected">واي فاي</string><string name="mobile_data">بيانات الجوال</string><string name="no_connection">لا يوجد اتصال</string><string name="ping_excellent">ممتاز</string><string name="ping_good">جيد</string><string name="ping_fair">مقبول</string><string name="ping_poor">ضعيف</string><string name="game_launched">تم تشغيل اللعبة بنجاح</string><string name="game_not_installed">اللعبة غير مثبتة</string><string name="position_saved">تم حفظ الموضع</string><string name="ok">موافق</string><string name="premium_feature">ميزة مدفوعة</string><string name="premium_feature_message">الميزة دي متاحة فقط في النسخة المدفوعة. ترقى دلوقتي للوصول لكل المميزات المدفوعة!</string><string name="aim_overlay_premium_title">ميزة مدفوعة: طبقة التصويب</string><string name="aim_overlay_premium_message">ميزة طبقة التصويب متاحة فقط في النسخة المدفوعة. ترقى دلوقتي للوصول لهذه الميزة والمميزات المدفوعة الأخرى!</string><string name="gb">جيجا</string><string name="android">أندرويد</string><string name="ram_label">رام</string><string name="processor_label">معالج</string><string name="screen_label">شاشة</string><string name="android_label">أندرويد</string><string name="device_info">معلومات الجهاز</string><string name="ram_info">رام</string><string name="cpu_power">قوة المعالج</string><string name="screen_size">الشاشة</string><string name="estimated_fps">معدل الإطارات المتوقع</string><string name="recommended_settings">الإعدادات الموصى بها</string><string name="resolution_rec">• الدقة</string><string name="fps_rec">• معدل الإطارات</string><string name="graphics_rec">• الجرافيكس</string><string name="hdr_rec">• HDR</string><string name="antialiasing_rec">• التنعيم</string><string name="basic">أساسي</string><string name="powerful">قوي</string><string name="screen_small">صغيرة</string><string name="screen_medium">متوسطة</string><string name="screen_large">كبيرة</string><string name="device_label">الجهاز</string><string name="processor_info">المعالج</string><string name="screen_info">الشاشة</string><string name="dpi_info">DPI</string><string name="estimated_fps_info">معدل الإطارات التقريبي</string><string name="ad_failed_to_load">فشل تحميل الإعلان</string><string name="ad_closed">تم إغلاق الإعلان</string><string name="reward_earned">حصلت على المكافأة!</string><string name="remove_ads">إزالة الإعلانات</string><string name="aim_overlay_settings">إعدادات طبقة التصويب</string><string name="aim_overlay_color">لون الطبقة</string><string name="aim_overlay_size">حجم الطبقة</string><string name="aim_overlay_toggle">تبديل الطبقة</string><string name="launch_free_fire">تشغيل فري فاير</string><string name="clean_device">تنضيف الجهاز</string><string name="free_fire_not_installed">فري فاير مش متثبت على الجهاز ده</string><string name="device_cleaned">تم تنضيف وتحسين الجهاز للألعاب</string><string name="game_mode_activated">تم تفعيل وضع الألعاب</string><string name="crosshair_size">حجم النشان</string><string name="game_optimization">تحسين اللعبة</string><string name="size_small">صغير</string><string name="size_medium">متوسط</string><string name="size_large">كبير</string><string name="overlay_enabled">الطبقة مفعلة</string><string name="overlay_disabled">الطبقة معطلة</string><string name="settings_applied">تم تطبيق الإعدادات</string><string name="settings_reset">تم إعادة ضبط الإعدادات للوضع الافتراضي</string><string name="free_fire_launched">جاري تشغيل فري فاير</string><string name="cleaning_device">جاري تنضيف الجهاز...</string><string name="cpu">المعالج</string><string name="ram">الرام</string><string name="select_graphics_level">اختار مستوى الجرافيكس</string><string name="choose_the_graphics_level_you_use_in_free_fire">اختار مستوى الجرافيكس اللي بتستخدمه في فري فاير</string><string name="smooth">سلس</string><string name="standard_gfx_3">قياسي</string><string name="high_gfx2">عالي</string><string name="apply">تطبيق</string><string name="please_wait_while_we_optimize_your_game_settings">استنى شوية لحد ما نحسن إعدادات اللعبة بتاعتك</string><string name="aim_test">اختبار التصويب</string><string name="tap_the_targets_as_quickly_as_possible">اضغط على الأهداف بأسرع ما يمكن</string><string name="testing_with_default_sensitivity">اختبار بالحساسية الافتراضية</string><string name="score_0">النتيجة: 0</string><string name="time_0s">الوقت: 0 ثانية</string><string name="app_name_welcom_screen">ادوات GFX</string><string name="settings_amp_game_booster_welcome_screen">هيدشوت ومعزز الألعاب</string><string name="powering_up_welcomescreen">جاري التشغيل...</string><string name="enable_dark_theme_throughout_the_app">تفعيل الوضع المظلم في كل التطبيق</string><string name="headshot_game_enhancer_v1_0">أداة تحسين الألعاب ولإصلاح التأخر - Gfx Tool الإصدار 1.0</string><string name="for_more_suggestions_and_solutions_contact_us_via_email">لمزيد من الاقتراحات والحلول، تواصل معانا عبر الإيميل</string><string name="email_contact"><EMAIL></string><string name="pro_version">النسخة الاحترافية</string><string name="app_version">الإصدار 1.0.0</string><string name="launch_error">حصل خطأ أثناء محاولة تشغيل اللعبة.</string><string name="cleaning_complete_notification">اكتمل التحليل! تم مراجعة حالة النظام.</string><string name="last_optimization_saved">تم حفظ التحليل لمدة 3 دقايق</string><string name="optimization_expired_notification">انتهت فترة التحليل. حلل جهازك مرة تانية!</string><string name="pro_version_title">ترقية للنسخة الاحترافية</string><string name="pro_version_description">افتح كل المميزات المدفوعة وحسن تجربة اللعب بتاعتك!</string><string name="pro_version_price">$5/شهرياً</string><string name="pro_feature_aim_overlay">طبقة تصويب متقدمة - نشان قابل للتخصيص للتصويب الدقيق</string><string name="pro_feature_game_mode">وضع ألعاب محسن - تحسين أداء على أعلى مستوى</string><string name="pro_feature_no_ads">تجربة بدون إعلانات - بدون مقاطعات أثناء اللعب</string><string name="pro_feature_priority_support">دعم بأولوية - احصل على المساعدة وقت ما تحتاجها</string><string name="pro_feature_exclusive_updates">تحديثات حصرية - احصل على المميزات الجديدة أولاً</string><string name="try_free">جرب مجاناً لمدة ساعة</string><string name="watch_ad_for_trial">شاهد إعلان لاستخدام الميزة دي لمدة ساعة</string><string name="trial_activated">تم تفعيل النسخة التجريبية! تقدر تستخدم الميزة دي لمدة ساعة</string><string name="trial_expired">انتهت فترة التجربة</string><string name="days_remaining">الأيام المتبقية: %d</string><string name="hours_remaining">الساعات المتبقية: %d</string><string name="minutes_remaining">الدقائق المتبقية: %d</string><string name="high_usage_notification">جهازك تحت حمل كبير (المعالج: %1$d%%، الرام: %2$d%%). عزز دلوقتي للحصول على لعب أفضل!</string><string name="daily_reminder_notification_1">نسيت تعزز جهازك النهاردة؟ دي هتاخد ثانية بس!</string><string name="daily_reminder_notification_2">متلعبش من غير ما تعزز جهازك لأفضل سرعة لعب! عزز دلوقتي!</string><string name="exit_confirmation">هل أنت متأكد من رغبتك في الخروج؟</string><string name="yes">أيوه</string><string name="no">لأ</string><string name="permission_required">الصلاحية مطلوبة</string><string name="alarm_permission_title">صلاحية المنبه مطلوبة</string><string name="alarm_permission_message">لجدولة الإشعارات والتذكيرات، يحتاج التطبيق إلى صلاحية ضبط المنبهات الدقيقة. يرجى تفعيلها في الإعدادات.</string><string name="overlay_permission_title">صلاحية العرض فوق التطبيقات مطلوبة</string><string name="overlay_permission_message">لعرض طبقة التصويب وميزات أخرى على الشاشة، يحتاج التطبيق إلى صلاحية العرض فوق التطبيقات الأخرى. يرجى تفعيلها في الإعدادات.</string><string name="permissions_explanation">يحتاج هذا التطبيق إلى عدة صلاحيات ليعمل بشكل صحيح. يرجى منح جميع الصلاحيات المطلوبة للحصول على أفضل تجربة.</string><string name="purchase_failed">فشلت عملية الشراء. يرجى المحاولة مرة أخرى لاحقًا.</string><string name="product_not_available">هذا المنتج غير متاح حاليًا.</string><string name="error_message">خطأ: %1$s</string><string name="feedback">تعليقات</string><string name="ad_loading">جاري تحميل الإعلان. يرجى المحاولة مرة أخرى بعد قليل.</string><string name="ad_closed_without_reward">تم إغلاق الإعلان بدون مكافأة. جاري المتابعة على أي حال.</string><string name="ad_space">مساحة إعلانية</string><string name="webview_not_available">خدمة WebView غير متوفرة على هذا الجهاز. قد لا تعمل بعض الميزات بشكل صحيح.</string><string name="webview_not_available_title">خدمة WebView غير متوفرة</string><string name="webview_not_available_message">جهازك يفتقد إلى خدمة WebView، وهي مطلوبة للإعلانات وبعض ميزات التطبيق. يرجى تثبيت أو تحديث WebView من متجر Google Play.</string><string name="open_settings">فتح الإعدادات</string><string name="error_opening_settings">خطأ في فتح الإعدادات. يرجى المحاولة يدويًا.</string><string name="retry">إعادة المحاولة</string><string name="retrying_ad_load">جاري إعادة تحميل الإعلان…</string><string name="ad_loading_short">جاري تحميل الإعلان…</string><string name="alternative_ad_loading">جاري تجربة طريقة بديلة لتحميل الإعلان…</string><string name="ad_load_failed">فشل تحميل الإعلان. جاري عرض بديل.</string><string name="check_webview">جاري التحقق من توفر WebView…</string><string name="webview_available">خدمة WebView متوفرة الآن. الإعلانات يجب أن تعمل بشكل صحيح.</string><string name="webview_still_unavailable">خدمة WebView ما زالت غير متوفرة. يرجى تثبيت أو تحديث WebView.</string><string name="game_mode_activated_for_one_hour">تم تفعيل وضع اللعبة لمدة ساعة واحدة!</string><string name="ad_reward_failed">فشل في استلام المكافأة. يرجى المحاولة مرة أخرى.</string><string name="usage_access_denied_message">بدون الوصول إلى الاستخدام، قد لا تعمل بعض الميزات بشكل صحيح.</string><string name="premium_price_title">اشتراك شهري</string><string name="premium_price_description">اشتراك شهري للوصول إلى جميع الميزات المميزة</string><string name="loading_price">5$</string><string name="app_disclaimer_title">إشعار مهم</string><string name="app_disclaimer_message">يوفر هذا التطبيق أدوات مراقبة وتحليل لأداء الألعاب. لا يقوم بتعديل إعدادات النظام النتائج قد تختلف حسب الجهاز.</string><string name="monitoring_notice">هذا التطبيق يراقب أداء النظام ويحسن الاستخدام فى الالعاب ولا يقوم بتغييرات فعلية على الإعدادات الأساسية لجهازك.</string><string name="performance_notice"> للمعلومات فقط: لا يتم إجراء أي تعديلات على جهازك.</string><string name="analysis_disclaimer">جاهز للألعاب</string><string name="understand">فهمت</string><string name="monitoring_tools_notice">هذه أدوات مراقبة وتحليل مصممة لمساعدتك على فهم أداء جهازك أثناء الألعاب.</string><string name="gaming_performance_report">تقرير جلسة الألعاب</string><string name="device_gaming_analysis">معلومات جهازك للوعي أثناء جلسة الألعاب</string><string name="gaming_score">تقييم الجهاز</string><string name="excellent_gaming">جيد للألعاب</string><string name="gaming_ready">متوافق مع الألعاب</string><string name="gaming_recommendations">معلومات الألعاب</string><string name="free_fire_settings">معلومات فري فاير</string><string name="high_graphics_recommended">الجهاز يدعم إعدادات الجرافيك العالية</string><string name="fps_gaming">معلومات ألعاب FPS</string><string name="smooth_fps_expected">الجهاز قادر على اللعب السلس</string><string name="optimal">متوافق</string><string name="excellent">مناسب</string><string name="gaming_tips">نصائح الألعاب</string><string name="gaming_tips_content">• راقب التطبيقات الخلفية أثناء اللعب\n• استخدم وضع التركيز للألعاب للوعي\n• حافظ على برودة الجهاز أثناء الجلسات الطويلة\n• تحقق من موارد النظام بانتظام</string><string name="got_it">فهمت!</string><string name="pro_status_active">وضع البرو نشط</string><string name="currently_in_pro_mode">أنت حالياً في وضع البرو</string><string name="pro_benefits_summary">استمتع بوصول غير محدود لجميع الميزات المتقدمة</string><string name="subscription_management">إدارة الاشتراك</string><string name="cancel_subscription_info">لإلغاء الاشتراك: متجر جوجل بلاي > الاشتراكات</string><string name="manage_subscription">إدارة الاشتراك</string><string name="google_play_transparency">يُدار الاشتراك عبر متجر جوجل بلاي. يمكن الإلغاء من إعدادات الحساب.</string><string name="premium_crown">تاج البريميوم</string><string name="error_opening_subscription_management">تعذر فتح إدارة الاشتراك</string><string name="system_analysis">جاري التحليل...</string><string name="needs_optimization">استخدام عالي</string><string name="cache_cleaned">✓ تم تنظيف الكاش</string><string name="device_optimized_success">جهازك الآن بحالة جيدة</string><string name="optimization_completed_items">المهام المكتملة:</string><string name="background_apps_managed">✓ تم إدارة التطبيقات الخلفية</string><string name="memory_optimized">✓ تم تحسين الذاكرة</string><string name="optimization_success_description">تم إدارة التطبيقات الخلفية وتنظيف الكاش</string></file><file path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Theme.HeadshotSettingsGameBooster" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_200</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/black</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_200</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        
    </style><style name="no_bar" parent="Theme.AppCompat.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        
    </style></file><file path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\values-sw320dp\dimens.xml" qualifiers="sw320dp-v13"><dimen name="activity_horizontal_margin">12dp</dimen><dimen name="activity_vertical_margin">12dp</dimen><dimen name="text_size_title">24sp</dimen><dimen name="text_size_subtitle">12sp</dimen><dimen name="text_size_button">14sp</dimen><dimen name="text_size_card_title">14sp</dimen><dimen name="text_size_card_content">12sp</dimen><dimen name="text_size_small">10sp</dimen><dimen name="icon_size_large">48dp</dimen><dimen name="icon_size_medium">24dp</dimen><dimen name="icon_size_small">20dp</dimen><dimen name="margin_large">16dp</dimen><dimen name="margin_medium">12dp</dimen><dimen name="margin_small">6dp</dimen><dimen name="margin_tiny">3dp</dimen><dimen name="padding_large">16dp</dimen><dimen name="padding_medium">12dp</dimen><dimen name="padding_small">6dp</dimen><dimen name="padding_tiny">3dp</dimen><dimen name="card_corner_radius">12dp</dimen><dimen name="card_elevation">6dp</dimen><dimen name="card_small_corner_radius">8dp</dimen><dimen name="card_small_elevation">3dp</dimen></file><file path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\values-sw600dp\dimens.xml" qualifiers="sw600dp-v13"><dimen name="activity_horizontal_margin">24dp</dimen><dimen name="activity_vertical_margin">24dp</dimen><dimen name="text_size_title">32sp</dimen><dimen name="text_size_subtitle">18sp</dimen><dimen name="text_size_button">18sp</dimen><dimen name="text_size_card_title">20sp</dimen><dimen name="text_size_card_content">16sp</dimen><dimen name="text_size_small">14sp</dimen><dimen name="icon_size_large">80dp</dimen><dimen name="icon_size_medium">40dp</dimen><dimen name="icon_size_small">32dp</dimen><dimen name="margin_large">32dp</dimen><dimen name="margin_medium">24dp</dimen><dimen name="margin_small">12dp</dimen><dimen name="margin_tiny">6dp</dimen><dimen name="padding_large">32dp</dimen><dimen name="padding_medium">24dp</dimen><dimen name="padding_small">12dp</dimen><dimen name="padding_tiny">6dp</dimen><dimen name="card_corner_radius">20dp</dimen><dimen name="card_elevation">10dp</dimen><dimen name="card_small_corner_radius">16dp</dimen><dimen name="card_small_elevation">6dp</dimen></file><file path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\values-sw720dp\dimens.xml" qualifiers="sw720dp-v13"><dimen name="activity_horizontal_margin">32dp</dimen><dimen name="activity_vertical_margin">32dp</dimen><dimen name="text_size_title">36sp</dimen><dimen name="text_size_subtitle">22sp</dimen><dimen name="text_size_button">22sp</dimen><dimen name="text_size_card_title">24sp</dimen><dimen name="text_size_card_content">18sp</dimen><dimen name="text_size_small">16sp</dimen><dimen name="icon_size_large">96dp</dimen><dimen name="icon_size_medium">48dp</dimen><dimen name="icon_size_small">36dp</dimen><dimen name="margin_large">40dp</dimen><dimen name="margin_medium">32dp</dimen><dimen name="margin_small">16dp</dimen><dimen name="margin_tiny">8dp</dimen><dimen name="padding_large">40dp</dimen><dimen name="padding_medium">32dp</dimen><dimen name="padding_small">16dp</dimen><dimen name="padding_tiny">8dp</dimen><dimen name="card_corner_radius">24dp</dimen><dimen name="card_elevation">12dp</dimen><dimen name="card_small_corner_radius">20dp</dimen><dimen name="card_small_elevation">8dp</dimen></file><file name="backup_rules" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="monitoring_item_bg" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\monitoring_item_bg.xml" qualifiers="" type="drawable"/><file name="ic_arrow_forward" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\drawable\ic_arrow_forward.xml" qualifiers="" type="drawable"/><file name="activity_game_selection" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\layout\activity_game_selection.xml" qualifiers="" type="layout"/><file name="item_game_selection" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\res\layout\item_game_selection.xml" qualifiers="" type="layout"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\build\generated\res\resValues\debug"/><source path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\build\generated\res\processDebugGoogleServices"/><source path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\build\generated\res\injectCrashlyticsMappingFileIdDebug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\build\generated\res\resValues\debug"/><source path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\build\generated\res\processDebugGoogleServices"><file path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\build\generated\res\processDebugGoogleServices\values\values.xml" qualifiers=""><string name="gcm_defaultSenderId" translatable="false">384756602944</string><string name="google_api_key" translatable="false">AIzaSyASO9hE3c2UMYvbiQUGte1KVWz9OibzPJ0</string><string name="google_app_id" translatable="false">1:384756602944:android:44a220dfeeb874d848e7a5</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyASO9hE3c2UMYvbiQUGte1KVWz9OibzPJ0</string><string name="google_storage_bucket" translatable="false">gfx-tool-f60ef.firebasestorage.app</string><string name="project_id" translatable="false">gfx-tool-f60ef</string></file></source><source path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\build\generated\res\injectCrashlyticsMappingFileIdDebug"><file path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\build\generated\res\injectCrashlyticsMappingFileIdDebug\values\com_google_firebase_crashlytics_mappingfileid.xml" qualifiers=""><string name="com.google.firebase.crashlytics.mapping_file_id" ns1:ignore="UnusedResources,TypographyDashes" translatable="false">00000000000000000000000000000000</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices" generated-set="res-processDebugGoogleServices$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-injectCrashlyticsMappingFileIdDebug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-injectCrashlyticsMappingFileIdDebug" generated-set="res-injectCrashlyticsMappingFileIdDebug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>