:root {
    --primary-bg: #0f2027;
    --secondary-bg: #203a43;
    --tertiary-bg: #2c5364;
    --neon-cyan: #00FFFF;
    --neon-cyan-dim: rgba(0, 255, 255, 0.3);
    --neon-gold: #FFD700;
    --neon-gold-dim: rgba(255, 215, 0, 0.3);
    --text-primary: #E0E0E0;
    --text-secondary: #A0A0A0;
    --card-bg: rgba(32, 58, 67, 0.8);
    --card-stroke: rgba(0, 255, 255, 0.2);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Tajawal', 'Cairo', sans-serif;
    background: linear-gradient(135deg, var(--primary-bg), var(--secondary-bg), var(--tertiary-bg));
    color: var(--text-primary);
    line-height: 1.6;
    direction: rtl;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
header {
    padding: 20px 0;
    text-align: center;
    position: relative;
    margin-bottom: 40px;
}

.logo-container {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
}

.logo {
    width: 80px;
    height: 80px;
    margin-left: 20px;
    filter: drop-shadow(0 0 10px var(--neon-cyan));
    animation: pulse 2s infinite alternate;
}

.title-container {
    text-align: right;
}

.main-title {
    font-size: 3rem;
    font-weight: 700;
    color: var(--text-primary);
    text-shadow: 0 0 10px var(--neon-cyan);
    margin: 0;
    letter-spacing: 2px;
}

.subtitle {
    font-size: 1.5rem;
    color: var(--neon-cyan);
    margin: 0;
    letter-spacing: 1px;
}

/* Intro Section */
.intro {
    text-align: center;
    margin-bottom: 50px;
    padding: 30px;
    background: rgba(15, 32, 39, 0.7);
    border-radius: 15px;
    border: 1px solid var(--card-stroke);
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.1);
}

.intro h2 {
    font-size: 2rem;
    margin-bottom: 20px;
    color: var(--neon-cyan);
    text-shadow: 0 0 5px var(--neon-cyan-dim);
}

.intro p {
    font-size: 1.2rem;
    max-width: 800px;
    margin: 0 auto;
}

/* Features Section */
.features {
    margin-bottom: 60px;
}

.features h2 {
    text-align: center;
    font-size: 2rem;
    margin-bottom: 30px;
    color: var(--neon-cyan);
    text-shadow: 0 0 5px var(--neon-cyan-dim);
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.feature-card {
    background: var(--card-bg);
    border-radius: 15px;
    padding: 25px;
    text-align: center;
    transition: transform 0.3s, box-shadow 0.3s;
    border: 1px solid var(--card-stroke);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, transparent, var(--neon-cyan), transparent);
    animation: glow 2s infinite;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 20px rgba(0, 255, 255, 0.2);
}

.feature-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 50%;
    border: 2px solid var(--neon-cyan);
    box-shadow: 0 0 15px var(--neon-cyan-dim);
}

.premium-feature .feature-icon {
    border-color: var(--neon-gold);
    box-shadow: 0 0 15px var(--neon-gold-dim);
}

.feature-icon img {
    width: 50px;
    height: 50px;
    object-fit: contain;
}

.feature-card h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: var(--neon-cyan);
}

.premium-feature h3, .feature-card h3 .premium-badge {
    color: var(--neon-gold);
}

.feature-card p {
    margin-bottom: 20px;
    font-size: 1rem;
}

.learn-more {
    background: transparent;
    color: var(--neon-cyan);
    border: 1px solid var(--neon-cyan);
    padding: 8px 20px;
    border-radius: 30px;
    cursor: pointer;
    font-family: 'Tajawal', sans-serif;
    font-size: 1rem;
    transition: all 0.3s;
}

.learn-more:hover {
    background: var(--neon-cyan-dim);
    box-shadow: 0 0 10px var(--neon-cyan);
}

.premium-badge {
    background: var(--neon-gold);
    color: #000;
    font-size: 0.7rem;
    padding: 3px 8px;
    border-radius: 10px;
    margin-right: 5px;
    vertical-align: middle;
    font-weight: bold;
}

/* Feature Details */
.feature-details {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    z-index: 1000;
    display: none;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.feature-details.active {
    display: flex;
}

.details-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--neon-cyan);
}

.details-header h2 {
    color: var(--neon-cyan);
    font-size: 2rem;
    text-shadow: 0 0 5px var(--neon-cyan-dim);
}

.close-details {
    background: transparent;
    border: none;
    color: var(--text-primary);
    font-size: 2rem;
    cursor: pointer;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s;
}

.close-details:hover {
    background: rgba(255, 255, 255, 0.1);
}

.details-content {
    background: var(--card-bg);
    border-radius: 15px;
    padding: 30px;
    max-width: 900px;
    width: 100%;
    max-height: 80vh;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 30px;
    border: 1px solid var(--card-stroke);
    box-shadow: 0 0 30px rgba(0, 255, 255, 0.2);
}

.details-image {
    text-align: center;
    margin-bottom: 20px;
}

.details-image img {
    max-width: 100%;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
    border: 1px solid var(--neon-cyan-dim);
}

.details-text h3 {
    color: var(--neon-cyan);
    font-size: 1.5rem;
    margin-bottom: 15px;
}

.details-text p {
    margin-bottom: 15px;
}

.details-text ul {
    margin-bottom: 20px;
    padding-right: 20px;
}

.details-text li {
    margin-bottom: 8px;
}

.premium-info {
    background: rgba(255, 215, 0, 0.1);
    border: 1px solid var(--neon-gold);
    padding: 15px;
    border-radius: 10px;
    margin-top: 20px;
}

.premium-info p {
    color: var(--neon-gold);
    margin-bottom: 10px;
}

/* Premium Section */
.premium-section {
    background: linear-gradient(rgba(15, 32, 39, 0.9), rgba(32, 58, 67, 0.9)), url('images/premium_bg.jpg');
    background-size: cover;
    background-position: center;
    padding: 50px 30px;
    border-radius: 15px;
    margin-bottom: 60px;
    text-align: center;
    border: 1px solid var(--neon-gold-dim);
    box-shadow: 0 0 30px rgba(255, 215, 0, 0.2);
}

.premium-section h2 {
    color: var(--neon-gold);
    font-size: 2rem;
    margin-bottom: 30px;
    text-shadow: 0 0 10px var(--neon-gold-dim);
}

.premium-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.premium-feature {
    background: rgba(0, 0, 0, 0.3);
    padding: 25px;
    border-radius: 15px;
    text-align: center;
    border: 1px solid var(--neon-gold-dim);
    transition: transform 0.3s;
}

.premium-feature:hover {
    transform: translateY(-10px);
}

.premium-icon {
    width: 70px;
    height: 70px;
    margin: 0 auto 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 50%;
    border: 2px solid var(--neon-gold);
    box-shadow: 0 0 15px var(--neon-gold-dim);
}

.premium-icon img {
    width: 40px;
    height: 40px;
    object-fit: contain;
}

.premium-feature h3 {
    color: var(--neon-gold);
    font-size: 1.3rem;
    margin-bottom: 10px;
}

.subscribe-container {
    margin-top: 30px;
}

.subscribe-button {
    background: var(--neon-gold);
    color: #000;
    border: none;
    padding: 12px 30px;
    border-radius: 30px;
    font-family: 'Tajawal', sans-serif;
    font-size: 1.2rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s;
    box-shadow: 0 0 15px var(--neon-gold-dim);
    margin-bottom: 15px;
}

.subscribe-button:hover {
    transform: scale(1.05);
    box-shadow: 0 0 20px var(--neon-gold);
}

/* Screenshots Section */
.app-screenshots {
    margin-bottom: 60px;
}

.app-screenshots h2 {
    text-align: center;
    font-size: 2rem;
    margin-bottom: 30px;
    color: var(--neon-cyan);
    text-shadow: 0 0 5px var(--neon-cyan-dim);
}

.screenshots-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.screenshot {
    text-align: center;
    transition: transform 0.3s;
}

.screenshot:hover {
    transform: scale(1.05);
}

.screenshot img {
    width: 100%;
    border-radius: 10px;
    border: 1px solid var(--neon-cyan-dim);
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.1);
}

.screenshot p {
    margin-top: 10px;
    font-size: 1rem;
}

/* FAQ Section */
.faq {
    margin-bottom: 60px;
}

.faq h2 {
    text-align: center;
    font-size: 2rem;
    margin-bottom: 30px;
    color: var(--neon-cyan);
    text-shadow: 0 0 5px var(--neon-cyan-dim);
}

.faq-item {
    background: var(--card-bg);
    border-radius: 10px;
    margin-bottom: 15px;
    border: 1px solid var(--card-stroke);
    overflow: hidden;
}

.faq-question {
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    transition: background 0.3s;
}

.faq-question:hover {
    background: rgba(0, 255, 255, 0.05);
}

.faq-question h3 {
    font-size: 1.2rem;
    color: var(--text-primary);
}

.toggle-icon {
    font-size: 1.5rem;
    color: var(--neon-cyan);
    transition: transform 0.3s;
}

.faq-item.active .toggle-icon {
    transform: rotate(45deg);
}

.faq-answer {
    padding: 0 20px;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out, padding 0.3s;
}

.faq-item.active .faq-answer {
    padding: 0 20px 20px;
    max-height: 500px;
}

/* Footer */
footer {
    background: rgba(15, 32, 39, 0.9);
    padding: 40px 0 20px;
    border-radius: 15px 15px 0 0;
    border-top: 1px solid var(--neon-cyan-dim);
}

.footer-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.footer-logo {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.footer-logo img {
    width: 50px;
    height: 50px;
    margin-left: 15px;
}

.footer-logo h3 {
    font-size: 1.2rem;
    color: var(--neon-cyan);
}

.footer-links {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 20px;
}

.footer-links a {
    color: var(--text-primary);
    text-decoration: none;
    transition: color 0.3s;
}

.footer-links a:hover {
    color: var(--neon-cyan);
}

.social-links {
    display: flex;
    gap: 15px;
}

.social-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 50%;
    transition: transform 0.3s, background 0.3s;
}

.social-icon:hover {
    transform: translateY(-5px);
    background: rgba(0, 255, 255, 0.1);
}

.social-icon img {
    width: 20px;
    height: 20px;
}

.copyright {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Animations */
@keyframes pulse {
    0% {
        filter: drop-shadow(0 0 5px var(--neon-cyan));
    }
    100% {
        filter: drop-shadow(0 0 15px var(--neon-cyan));
    }
}

@keyframes glow {
    0% {
        opacity: 0.3;
    }
    50% {
        opacity: 1;
    }
    100% {
        opacity: 0.3;
    }
}

/* Responsive Styles */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .main-title {
        font-size: 2.5rem;
    }

    .subtitle {
        font-size: 1.2rem;
    }

    .intro h2, .features h2, .premium-section h2, .app-screenshots h2, .faq h2 {
        font-size: 1.8rem;
    }

    .details-content {
        flex-direction: column;
    }

    .footer-content {
        flex-direction: column;
        text-align: center;
    }

    .footer-links, .social-links {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .logo-container {
        flex-direction: column;
    }

    .logo {
        margin-left: 0;
        margin-bottom: 15px;
    }

    .title-container {
        text-align: center;
    }

    .main-title {
        font-size: 2rem;
    }

    .subtitle {
        font-size: 1rem;
    }

    .feature-card, .premium-feature {
        padding: 20px 15px;
    }

    .feature-icon, .premium-icon {
        width: 60px;
        height: 60px;
    }

    .feature-icon img, .premium-icon img {
        width: 35px;
        height: 35px;
    }

    .feature-card h3, .premium-feature h3 {
        font-size: 1.3rem;
    }

    .details-header h2 {
        font-size: 1.5rem;
    }

    .details-content {
        padding: 20px;
    }
}
