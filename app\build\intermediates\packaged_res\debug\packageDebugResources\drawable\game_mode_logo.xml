<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="200dp"
    android:height="200dp"
    android:viewportWidth="200"
    android:viewportHeight="200">

    <!-- Background Circle -->
    <path
        android:fillColor="#0A0A1A"
        android:pathData="M100,100m-100,0a100,100 0,1 1,200 0a100,100 0,1 1,-200 0" />

    <!-- Inner Circle -->
    <path
        android:fillColor="#121228"
        android:pathData="M100,100m-90,0a90,90 0,1 1,180 0a90,90 0,1 1,-180 0" />

    <!-- Outer Ring Glow -->
    <path
        android:strokeColor="@color/neon_blue"
        android:strokeWidth="2"
        android:pathData="M100,100m-95,0a95,95 0,1 1,190 0a95,95 0,1 1,-190 0" />

    <!-- Controller Shape -->
    <path
        android:fillColor="@color/neon_blue"
        android:pathData="M60,80 L60,120 L85,120 L85,140 L115,140 L115,120 L140,120 L140,80 L115,80 L115,60 L85,60 L85,80 Z" />

    <!-- D-Pad -->
    <path
        android:fillColor="#121228"
        android:pathData="M75,100m-10,0a10,10 0,1 1,20 0a10,10 0,1 1,-20 0" />

    <!-- Buttons -->
    <path
        android:fillColor="#121228"
        android:pathData="M125,100m-10,0a10,10 0,1 1,20 0a10,10 0,1 1,-20 0" />

    <!-- D-Pad Cross -->
    <path
        android:strokeColor="@color/neon_blue"
        android:strokeWidth="2"
        android:pathData="M75,90 L75,110 M65,100 L85,100" />

    <!-- Button X -->
    <path
        android:strokeColor="@color/neon_blue"
        android:strokeWidth="2"
        android:pathData="M120,95 L130,105 M120,105 L130,95" />

    <!-- Lightning Effect -->
    <path
        android:fillColor="#80FFFFFF"
        android:pathData="M95,40 L85,70 L100,70 L90,100 L105,70 L90,70 L100,40 Z" />
</vector>
