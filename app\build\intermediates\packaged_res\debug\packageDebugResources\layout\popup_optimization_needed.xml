<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="16dp"
    app:cardBackgroundColor="#1A1A1A"
    app:cardCornerRadius="16dp"
    app:cardElevation="8dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header with icon -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingBottom="16dp">

            <ImageView
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:contentDescription="@string/app_name"
                android:src="@drawable/ic_rocket"
                app:tint="@color/vibrant_blue" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:fontFamily="@font/font_for_ar_en"
                android:text="@string/optimization_needed"
                android:textColor="@color/text_color_primary"
                android:textSize="20sp" />
        </LinearLayout>

        <!-- Divider -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#333333" />

        <!-- Content -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/font_for_ar_en"
                android:text="@string/optimization_needed_description"
                android:textColor="@color/text_color_secondary"
                android:textSize="16sp" />

            <!-- System stats -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:orientation="horizontal">

                <!-- CPU Usage -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="sans-serif-light"
                        android:text="CPU"
                        android:textColor="@color/text_color_secondary"
                        android:textSize="12sp" />

                    <TextView
                        android:id="@+id/popup_cpu_usage"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="sans-serif-medium"
                        android:text="75%"
                        android:textColor="@color/vibrant_orange"
                        android:textSize="20sp" />
                </LinearLayout>

                <!-- Memory Usage -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="sans-serif-light"
                        android:text="RAM"
                        android:textColor="@color/text_color_secondary"
                        android:textSize="12sp" />

                    <TextView
                        android:id="@+id/popup_memory_usage"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="sans-serif-medium"
                        android:text="68%"
                        android:textColor="@color/vibrant_orange"
                        android:textSize="20sp" />
                </LinearLayout>

                <!-- Apps Running -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="sans-serif-light"
                        android:text="APPS"
                        android:textColor="@color/text_color_secondary"
                        android:textSize="12sp" />

                    <TextView
                        android:id="@+id/popup_apps_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="sans-serif-medium"
                        android:text="12"
                        android:textColor="@color/vibrant_orange"
                        android:textSize="20sp" />
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>

        <!-- Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="8dp">

            <Button
                android:id="@+id/popup_cancel_button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:background="@android:color/transparent"
                android:fontFamily="@font/font_for_ar_en"
                android:text="@string/cancel"
                android:textColor="@color/text_color_secondary" />

            <Button
                android:id="@+id/popup_optimize_button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_weight="1"
                android:background="@drawable/boost_button_background"
                android:fontFamily="@font/font_for_ar_en"
                android:text="@string/boost_now"
                android:textColor="@color/text_color_primary" />
        </LinearLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView>
