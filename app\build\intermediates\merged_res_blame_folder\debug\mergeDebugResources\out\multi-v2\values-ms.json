{"logs": [{"outputFile": "com.mahmoudffyt.gfxbooster.app-mergeDebugResources-49:/values-ms/values-ms.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ef3205b01dd1e8d62ca95413fbcfcde0\\transformed\\play-services-base-18.5.0\\res\\values-ms\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,464,590,692,860,988,1104,1207,1388,1493,1664,1795,1962,2133,2196,2256", "endColumns": "101,168,125,101,167,127,115,102,180,104,170,130,166,170,62,59,78", "endOffsets": "294,463,589,691,859,987,1103,1206,1387,1492,1663,1794,1961,2132,2195,2255,2334"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4494,4600,4773,4903,5009,5181,5313,5433,5686,5871,5980,6155,6290,6461,6636,6703,6767", "endColumns": "105,172,129,105,171,131,119,106,184,108,174,134,170,174,66,63,82", "endOffsets": "4595,4768,4898,5004,5176,5308,5428,5535,5866,5975,6150,6285,6456,6631,6698,6762,6845"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8ba171b74415ef9d04b1f3a055c632e8\\transformed\\play-services-ads-24.2.0\\res\\values-ms\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,245,291,347,413,486,592,654,789,915,1048,1098,1151,1279,1373,1412,1490,1524,1557,1604,1670,1709", "endColumns": "45,45,55,65,72,105,61,134,125,132,49,52,127,93,38,77,33,32,46,65,38,55", "endOffsets": "244,290,346,412,485,591,653,788,914,1047,1097,1150,1278,1372,1411,1489,1523,1556,1603,1669,1708,1764"}, "to": {"startLines": "133,134,135,136,137,138,139,140,141,142,143,144,145,146,148,149,150,151,152,153,154,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12204,12254,12304,12364,12434,12511,12621,12687,12826,12956,13093,13147,13204,13336,13520,13563,13645,13683,13720,13771,13841,14306", "endColumns": "49,49,59,69,76,109,65,138,129,136,53,56,131,97,42,81,37,36,50,69,42,59", "endOffsets": "12249,12299,12359,12429,12506,12616,12682,12821,12951,13088,13142,13199,13331,13429,13558,13640,13678,13715,13766,13836,13879,14361"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\464e6a7dcfefd0da870c4050aa381b0c\\transformed\\material-1.12.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,279,359,438,525,617,704,807,923,1006,1068,1133,1226,1291,1350,1437,1499,1561,1621,1687,1749,1803,1911,1968,2029,2084,2155,2275,2366,2443,2540,2625,2711,2859,2945,3031,3159,3247,3325,3378,3429,3495,3566,3644,3715,3794,3867,3943,4016,4087,4194,4286,4359,4449,4542,4616,4687,4778,4830,4910,4978,5062,5147,5209,5273,5336,5408,5512,5620,5716,5822,5879,5934,6020,6105,6183", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,79,78,86,91,86,102,115,82,61,64,92,64,58,86,61,61,59,65,61,53,107,56,60,54,70,119,90,76,96,84,85,147,85,85,127,87,77,52,50,65,70,77,70,78,72,75,72,70,106,91,72,89,92,73,70,90,51,79,67,83,84,61,63,62,71,103,107,95,105,56,54,85,84,77,76", "endOffsets": "274,354,433,520,612,699,802,918,1001,1063,1128,1221,1286,1345,1432,1494,1556,1616,1682,1744,1798,1906,1963,2024,2079,2150,2270,2361,2438,2535,2620,2706,2854,2940,3026,3154,3242,3320,3373,3424,3490,3561,3639,3710,3789,3862,3938,4011,4082,4189,4281,4354,4444,4537,4611,4682,4773,4825,4905,4973,5057,5142,5204,5268,5331,5403,5507,5615,5711,5817,5874,5929,6015,6100,6178,6255"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,67,68,69,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,147,156,157,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3024,3104,3183,3270,3362,4192,4295,4411,6955,7017,7082,7496,7561,7620,7707,7769,7831,7891,7957,8019,8073,8181,8238,8299,8354,8425,8545,8636,8713,8810,8895,8981,9129,9215,9301,9429,9517,9595,9648,9699,9765,9836,9914,9985,10064,10137,10213,10286,10357,10464,10556,10629,10719,10812,10886,10957,11048,11100,11180,11248,11332,11417,11479,11543,11606,11678,11782,11890,11986,12092,12149,13434,13965,14050,14128", "endLines": "5,33,34,35,36,37,45,46,47,67,68,69,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,147,156,157,158", "endColumns": "12,79,78,86,91,86,102,115,82,61,64,92,64,58,86,61,61,59,65,61,53,107,56,60,54,70,119,90,76,96,84,85,147,85,85,127,87,77,52,50,65,70,77,70,78,72,75,72,70,106,91,72,89,92,73,70,90,51,79,67,83,84,61,63,62,71,103,107,95,105,56,54,85,84,77,76", "endOffsets": "324,3099,3178,3265,3357,3444,4290,4406,4489,7012,7077,7170,7556,7615,7702,7764,7826,7886,7952,8014,8068,8176,8233,8294,8349,8420,8540,8631,8708,8805,8890,8976,9124,9210,9296,9424,9512,9590,9643,9694,9760,9831,9909,9980,10059,10132,10208,10281,10352,10459,10551,10624,10714,10807,10881,10952,11043,11095,11175,11243,11327,11412,11474,11538,11601,11673,11777,11885,11981,12087,12144,12199,13515,14045,14123,14200"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\46c7e1c2c774c76fe5e74081b097ef9a\\transformed\\browser-1.8.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,260,379", "endColumns": "104,99,118,101", "endOffsets": "155,255,374,476"}, "to": {"startLines": "66,70,71,72", "startColumns": "4,4,4,4", "startOffsets": "6850,7175,7275,7394", "endColumns": "104,99,118,101", "endOffsets": "6950,7270,7389,7491"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1a44ad6b726c152c4fe16ed54c5a8c02\\transformed\\appcompat-1.7.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,321,429,516,620,731,810,888,979,1072,1167,1261,1359,1452,1547,1641,1732,1823,1903,2015,2123,2220,2329,2433,2540,2699,2800", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "211,316,424,511,615,726,805,883,974,1067,1162,1256,1354,1447,1542,1636,1727,1818,1898,2010,2118,2215,2324,2428,2535,2694,2795,2876"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "329,440,545,653,740,844,955,1034,1112,1203,1296,1391,1485,1583,1676,1771,1865,1956,2047,2127,2239,2347,2444,2553,2657,2764,2923,13884", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "435,540,648,735,839,950,1029,1107,1198,1291,1386,1480,1578,1671,1766,1860,1951,2042,2122,2234,2342,2439,2548,2652,2759,2918,3019,13960"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cf3dd3f063a8dc2168576dc8a6639ef4\\transformed\\core-1.13.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,459,565,683,798", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "145,247,344,454,560,678,793,894"}, "to": {"startLines": "38,39,40,41,42,43,44,159", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3449,3544,3646,3743,3853,3959,4077,14205", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "3539,3641,3738,3848,3954,4072,4187,14301"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\aff62db08cff511cc4373673ada3b6cf\\transformed\\play-services-basement-18.5.0\\res\\values-ms\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5540", "endColumns": "145", "endOffsets": "5681"}}]}]}