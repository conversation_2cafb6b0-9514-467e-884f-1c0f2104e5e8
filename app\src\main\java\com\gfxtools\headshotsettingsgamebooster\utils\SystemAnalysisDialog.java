package com.gfxtools.headshotsettingsgamebooster.utils;

import android.app.ActivityManager;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.TextView;

import com.mahmoudffyt.gfxbooster.R;
import com.mahmoudffyt.gfxbooster.HeadshotToolActivity;

public class SystemAnalysisDialog {

    public static void showGamingPerformanceReport(Context context) {
        Dialog dialog = new Dialog(context);
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);

        View view = LayoutInflater.from(context).inflate(R.layout.popup_system_analysis_report, null);
        dialog.setContentView(view);

        // Make dialog background transparent and full width
        if (dialog.getWindow() != null) {
            dialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            dialog.getWindow().setLayout(WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.WRAP_CONTENT);
        }

        // Calculate gaming performance and populate data
        populateGamingPerformanceData(context, view);

        // Set button listeners
        Button btnDone = view.findViewById(R.id.popup_done_button);
        btnDone.setOnClickListener(v -> dialog.dismiss());

        Button btnHeadshotTool = view.findViewById(R.id.btn_headshot_tool);
        btnHeadshotTool.setOnClickListener(v -> {
            dialog.dismiss();
            // Open Headshot Tool
            Intent intent = new Intent(context, HeadshotToolActivity.class);
            context.startActivity(intent);
        });

        dialog.setCancelable(true);
        dialog.show();
    }

    private static void populateGamingPerformanceData(Context context, View view) {
        try {
            // Calculate gaming score based on device specs
            ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
            ActivityManager.MemoryInfo memoryInfo = new ActivityManager.MemoryInfo();
            activityManager.getMemoryInfo(memoryInfo);

            // Calculate gaming score based on RAM
            double totalRamGB = memoryInfo.totalMem / (1024.0 * 1024.0 * 1024.0);
            int gamingScore = calculateGamingScore(totalRamGB);

            TextView gamingScoreText = view.findViewById(R.id.gaming_score_text);
            gamingScoreText.setText(gamingScore + "/100");

            // Set recommendations based on score
            TextView freeFireRecommendation = view.findViewById(R.id.free_fire_recommendation);
            TextView fpsRecommendation = view.findViewById(R.id.fps_recommendation);

            if (gamingScore >= 80) {
                freeFireRecommendation.setText(context.getString(R.string.high_graphics_recommended));
                fpsRecommendation.setText(context.getString(R.string.smooth_fps_expected));
            } else if (gamingScore >= 60) {
                freeFireRecommendation.setText("Medium Graphics + High Frame Rate");
                fpsRecommendation.setText("45+ FPS Expected");
            } else {
                freeFireRecommendation.setText("Low Graphics + Smooth Frame Rate");
                fpsRecommendation.setText("30+ FPS Expected");
            }

        } catch (Exception e) {
            e.printStackTrace();
            // Set default values if error occurs
            TextView gamingScoreText = view.findViewById(R.id.gaming_score_text);
            gamingScoreText.setText("75/100");
        }
    }

    private static int calculateGamingScore(double ramGB) {
        // Simple gaming score calculation based on RAM
        if (ramGB >= 8.0) {
            return 90 + (int)(Math.random() * 10); // 90-100
        } else if (ramGB >= 6.0) {
            return 80 + (int)(Math.random() * 10); // 80-90
        } else if (ramGB >= 4.0) {
            return 70 + (int)(Math.random() * 10); // 70-80
        } else if (ramGB >= 3.0) {
            return 60 + (int)(Math.random() * 10); // 60-70
        } else {
            return 50 + (int)(Math.random() * 10); // 50-60
        }
    }
}
