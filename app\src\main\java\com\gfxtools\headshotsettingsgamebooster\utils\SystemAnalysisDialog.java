package com.gfxtools.headshotsettingsgamebooster.utils;

import android.app.ActivityManager;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.TextView;

import com.mahmoudffyt.gfxbooster.R;
import com.mahmoudffyt.gfxbooster.HeadshotToolActivity;

public class SystemAnalysisDialog {

    public static void showGamingPerformanceReport(Context context) {
        Dialog dialog = new Dialog(context);
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);

        View view = LayoutInflater.from(context).inflate(R.layout.popup_system_analysis_report, null);
        dialog.setContentView(view);

        // Make dialog background transparent and full width
        if (dialog.getWindow() != null) {
            dialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            dialog.getWindow().setLayout(WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.WRAP_CONTENT);
        }

        // Calculate gaming performance and populate data
        populateGamingPerformanceData(context, view);

        // Set button listeners
        Button btnDone = view.findViewById(R.id.popup_done_button);
        btnDone.setOnClickListener(v -> dialog.dismiss());

        dialog.setCancelable(true);
        dialog.show();
    }

    private static void populateGamingPerformanceData(Context context, View view) {
        // No complex data needed for the simple success message
        // The layout now shows a simple success message with completed tasks
    }
}
