package com.mahmoudffyt.gfxbooster.utils;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Helper class to manage ad loading priorities and background loading
 */
public class AdPriorityManager {
    private static final String TAG = "AdPriorityManager";

    // Executor for background tasks
    private static final Executor backgroundExecutor = Executors.newSingleThreadExecutor();

    // Main thread handler
    private static final Handler mainHandler = new Handler(Looper.getMainLooper());

    // Flags to track loading status
    private static final AtomicBoolean isLoadingInterstitial = new AtomicBoolean(false);
    private static final AtomicBoolean isLoadingRewarded = new AtomicBoolean(false);
    private static final AtomicBoolean isLoadingRewardedInterstitial = new AtomicBoolean(false);

    // Cooldown periods (in milliseconds) - reduced for faster loading
    private static final long INTERSTITIAL_COOLDOWN = 5000; // 5 seconds
    private static final long REWARDED_COOLDOWN = 7000; // 7 seconds
    private static final long REWARDED_INTERSTITIAL_COOLDOWN = 10000; // 10 seconds

    // Last load timestamps
    private static long lastInterstitialLoadTime = 0;
    private static long lastRewardedLoadTime = 0;
    private static long lastRewardedInterstitialLoadTime = 0;

    /**
     * Load ads with priority (interstitial first, then rewarded, then rewarded interstitial)
     * @param context Application context
     * @param adManager AdManager instance
     */
    public static void loadAdsWithPriority(Context context, AdManager adManager) {
        if (adManager == null || context == null) {
            return;
        }

        // Check if premium or WebView unavailable
        if (adManager.isPremiumUser()) {
            return;
        }

        // Start loading in background thread
        backgroundExecutor.execute(() -> {
            try {
                // Load interstitial ad first (highest priority)
                if (!adManager.isInterstitialAdReady() && !isLoadingInterstitial.get() &&
                    System.currentTimeMillis() - lastInterstitialLoadTime > INTERSTITIAL_COOLDOWN) {

                    isLoadingInterstitial.set(true);
                    lastInterstitialLoadTime = System.currentTimeMillis();

                    mainHandler.post(() -> {
                        try {
                            adManager.preloadInterstitialAd(context);
                        } catch (Exception e) {
                            Log.e(TAG, "Error loading interstitial ad: " + e.getMessage());
                        } finally {
                            isLoadingInterstitial.set(false);
                        }
                    });

                    // Add a minimal delay before loading the next ad type
                    Thread.sleep(200);
                }

                // Load rewarded ad second (medium priority)
                if (!adManager.isRewardedAdReady() && !isLoadingRewarded.get() &&
                    System.currentTimeMillis() - lastRewardedLoadTime > REWARDED_COOLDOWN) {

                    isLoadingRewarded.set(true);
                    lastRewardedLoadTime = System.currentTimeMillis();

                    mainHandler.post(() -> {
                        try {
                            adManager.preloadRewardedAd(context);
                        } catch (Exception e) {
                            Log.e(TAG, "Error loading rewarded ad: " + e.getMessage());
                        } finally {
                            isLoadingRewarded.set(false);
                        }
                    });

                    // Add a minimal delay before loading the next ad type
                    Thread.sleep(200);
                }

                // Load rewarded interstitial ad last (lowest priority)
                if (!adManager.isRewardedInterstitialAdReady() && !isLoadingRewardedInterstitial.get() &&
                    System.currentTimeMillis() - lastRewardedInterstitialLoadTime > REWARDED_INTERSTITIAL_COOLDOWN) {

                    isLoadingRewardedInterstitial.set(true);
                    lastRewardedInterstitialLoadTime = System.currentTimeMillis();

                    mainHandler.post(() -> {
                        try {
                            adManager.preloadRewardedInterstitialAd(context);
                        } catch (Exception e) {
                            Log.e(TAG, "Error loading rewarded interstitial ad: " + e.getMessage());
                        } finally {
                            isLoadingRewardedInterstitial.set(false);
                        }
                    });
                }
            } catch (Exception e) {
                Log.e(TAG, "Error in loadAdsWithPriority: " + e.getMessage());
            }
        });
    }

    /**
     * Reset loading flags (call this when the app goes to background)
     */
    public static void resetLoadingFlags() {
        isLoadingInterstitial.set(false);
        isLoadingRewarded.set(false);
        isLoadingRewardedInterstitial.set(false);
    }

    /**
     * Schedule ad preloading during app idle time
     * @param context Application context
     * @param adManager AdManager instance
     */
    public static void scheduleIdleTimePreloading(Context context, AdManager adManager) {
        if (adManager == null || context == null) {
            return;
        }

        // Check if premium or WebView unavailable
        if (adManager.isPremiumUser()) {
            return;
        }

        // Schedule preloading after a shorter delay
        mainHandler.postDelayed(() -> {
            loadAdsWithPriority(context, adManager);

            // Schedule periodic checks for ad status during idle time
            mainHandler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    // Only reload ads if needed
                    if (!adManager.isInterstitialAdReady() ||
                        !adManager.isRewardedAdReady() ||
                        !adManager.isRewardedInterstitialAdReady()) {

                        loadAdsWithPriority(context, adManager);
                    }

                    // Schedule next check after 30 seconds
                    mainHandler.postDelayed(this, 30000);
                }
            }, 30000); // First check after 30 seconds
        }, 3000); // 3 seconds delay
    }
}
