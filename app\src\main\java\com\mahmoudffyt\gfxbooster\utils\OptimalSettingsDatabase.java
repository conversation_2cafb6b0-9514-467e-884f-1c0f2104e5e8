package com.mahmoudffyt.gfxbooster.utils;

import java.util.HashMap;
import java.util.Map;

/**
 * Database of optimal settings for different device profiles
 */
public class OptimalSettingsDatabase {

    // Device profile categories
    public static final String PROFILE_LOW_END = "low_end";
    public static final String PROFILE_MID_RANGE = "mid_range";
    public static final String PROFILE_HIGH_END = "high_end";
    public static final String PROFILE_GAMING = "gaming";
    
    // Settings database
    private static final Map<String, Map<String, Integer>> SETTINGS_DATABASE = new HashMap<>();
    
    static {
        // Initialize database with optimal settings for different device profiles
        
        // Low-end device settings
        Map<String, Integer> lowEndSettings = new HashMap<>();
        lowEndSettings.put("generalSensitivity", 35);
        lowEndSettings.put("redDotSensitivity", 65);
        lowEndSettings.put("scope2xSensitivity", 45);
        lowEndSettings.put("scope4xSensitivity", 30);
        lowEndSettings.put("scopeAwmSensitivity", 25);
        lowEndSettings.put("breathHold", 70);
        lowEndSettings.put("dpiSetting", 400);
        SETTINGS_DATABASE.put(PROFILE_LOW_END, lowEndSettings);
        
        // Mid-range device settings
        Map<String, Integer> midRangeSettings = new HashMap<>();
        midRangeSettings.put("generalSensitivity", 45);
        midRangeSettings.put("redDotSensitivity", 75);
        midRangeSettings.put("scope2xSensitivity", 55);
        midRangeSettings.put("scope4xSensitivity", 40);
        midRangeSettings.put("scopeAwmSensitivity", 32);
        midRangeSettings.put("breathHold", 80);
        midRangeSettings.put("dpiSetting", 800);
        SETTINGS_DATABASE.put(PROFILE_MID_RANGE, midRangeSettings);
        
        // High-end device settings
        Map<String, Integer> highEndSettings = new HashMap<>();
        highEndSettings.put("generalSensitivity", 55);
        highEndSettings.put("redDotSensitivity", 85);
        highEndSettings.put("scope2xSensitivity", 65);
        highEndSettings.put("scope4xSensitivity", 48);
        highEndSettings.put("scopeAwmSensitivity", 38);
        highEndSettings.put("breathHold", 90);
        highEndSettings.put("dpiSetting", 1600);
        SETTINGS_DATABASE.put(PROFILE_HIGH_END, highEndSettings);
        
        // Gaming device settings
        Map<String, Integer> gamingSettings = new HashMap<>();
        gamingSettings.put("generalSensitivity", 65);
        gamingSettings.put("redDotSensitivity", 95);
        gamingSettings.put("scope2xSensitivity", 75);
        gamingSettings.put("scope4xSensitivity", 55);
        gamingSettings.put("scopeAwmSensitivity", 45);
        gamingSettings.put("breathHold", 100);
        gamingSettings.put("dpiSetting", 1600);
        SETTINGS_DATABASE.put(PROFILE_GAMING, gamingSettings);
    }
    
    /**
     * Get optimal settings for a specific device profile
     */
    public static Map<String, Integer> getOptimalSettings(String deviceProfile) {
        if (SETTINGS_DATABASE.containsKey(deviceProfile)) {
            return new HashMap<>(SETTINGS_DATABASE.get(deviceProfile));
        }
        
        // Default to mid-range if profile not found
        return new HashMap<>(SETTINGS_DATABASE.get(PROFILE_MID_RANGE));
    }
    
    /**
     * Determine device profile based on device specs
     */
    public static String determineDeviceProfile(Map<String, Object> deviceSpecs) {
        // Extract relevant specs
        long ram = (long) deviceSpecs.get("ram");
        int estimatedFps = (int) deviceSpecs.get("estimatedFps");
        String model = ((String) deviceSpecs.get("model")).toLowerCase();
        
        // Check for gaming phones
        if (model.contains("rog") || 
            model.contains("black shark") || 
            model.contains("red magic") || 
            model.contains("legion") ||
            (ram >= 12288 && estimatedFps >= 120)) { // 12GB+ RAM and 120+ FPS
            return PROFILE_GAMING;
        }
        
        // Check for high-end phones
        if (ram >= 8192 || estimatedFps >= 90) { // 8GB+ RAM or 90+ FPS
            return PROFILE_HIGH_END;
        }
        
        // Check for mid-range phones
        if (ram >= 4096 || estimatedFps >= 60) { // 4GB+ RAM or 60+ FPS
            return PROFILE_MID_RANGE;
        }
        
        // Default to low-end
        return PROFILE_LOW_END;
    }
    
    /**
     * Fine-tune settings based on specific device characteristics
     */
    public static Map<String, Integer> fineTuneSettings(Map<String, Integer> baseSettings, Map<String, Object> deviceSpecs) {
        Map<String, Integer> tunedSettings = new HashMap<>(baseSettings);
        
        // Extract relevant specs
        int dpi = (int) deviceSpecs.get("dpi");
        int screenWidth = (int) deviceSpecs.get("screenWidth");
        int screenHeight = (int) deviceSpecs.get("screenHeight");
        
        // Adjust for screen size and resolution
        boolean isHighResolution = (screenWidth >= 1080 || screenHeight >= 1920);
        
        if (isHighResolution) {
            // For high-resolution screens, increase sensitivity slightly
            tunedSettings.put("generalSensitivity", Math.min(100, tunedSettings.get("generalSensitivity") + 5));
            tunedSettings.put("redDotSensitivity", Math.min(100, tunedSettings.get("redDotSensitivity") + 3));
        }
        
        // Adjust DPI setting based on device's actual DPI
        if (dpi >= 480) {
            tunedSettings.put("dpiSetting", 1600);
        } else if (dpi >= 320) {
            tunedSettings.put("dpiSetting", 800);
        } else {
            tunedSettings.put("dpiSetting", 400);
        }
        
        return tunedSettings;
    }
}
