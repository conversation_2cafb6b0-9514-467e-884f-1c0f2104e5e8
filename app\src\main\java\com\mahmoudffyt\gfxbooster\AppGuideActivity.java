package com.mahmoudffyt.gfxbooster;

import android.os.Bundle;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;

public class AppGuideActivity extends AppCompatActivity {

    private WebView webView;
    private ImageView backButton;
    private TextView titleText;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_app_guide);

        // Initialize views
        webView = findViewById(R.id.webView);
        backButton = findViewById(R.id.back_button);
        titleText = findViewById(R.id.title_text);

        // Set title
        titleText.setText(R.string.app_guide);

        // Set up back button
        backButton.setOnClickListener(v -> finish());

        // Configure WebView
        WebSettings webSettings = webView.getSettings();
        webSettings.setJavaScriptEnabled(true);
        webSettings.setDomStorageEnabled(true);
        webSettings.setAllowFileAccess(true);
        webSettings.setAllowContentAccess(true);
        webSettings.setAllowFileAccessFromFileURLs(true);
        webSettings.setAllowUniversalAccessFromFileURLs(true);

        // Load HTML file from assets
        webView.loadUrl("file:///android_asset/app_guide_new.html");
    }

    @Override
    public void onBackPressed() {
        if (webView.canGoBack()) {
            webView.goBack();
        } else {
            super.onBackPressed();
        }
    }
}
