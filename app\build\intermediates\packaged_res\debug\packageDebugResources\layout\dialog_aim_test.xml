<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#0A1A22"
    android:padding="16dp">

    <TextView
        android:id="@+id/test_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:text="@string/aim_test"
        android:textSize="24sp"
        android:textColor="#4FC3F7"
        android:textStyle="bold"
        android:fontFamily="@font/font_for_ar_en" />

    <TextView
        android:id="@+id/test_instructions"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/test_title"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="8dp"
        android:text="@string/tap_the_targets_as_quickly_as_possible"
        android:textSize="14sp"
        android:textColor="#FFFFFF"
        android:fontFamily="@font/font_for_ar_en" />

    <!-- Sensitivity Info Display -->
    <TextView
        android:id="@+id/sensitivity_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/test_instructions"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="4dp"
        android:text="@string/testing_with_default_sensitivity"
        android:textSize="12sp"
        android:textColor="#4FC3F7"
        android:fontFamily="@font/font_for_ar_en" />

    <!-- Device Info Display -->
    <TextView
        android:id="@+id/device_info_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/sensitivity_info"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="4dp"
        android:text=""
        android:textSize="10sp"
        android:textColor="#CCCCCC"
        android:fontFamily="sans-serif-light"
        android:visibility="gone" />

    <!-- Stats Container -->
    <LinearLayout
        android:id="@+id/stats_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/device_info_text"
        android:layout_marginTop="12dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/score_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/score_0"
            android:textSize="16sp"
            android:textColor="#4FC3F7"
            android:fontFamily="sans-serif-medium"
            android:gravity="center" />

        <TextView
            android:id="@+id/time_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/time_0s"
            android:textSize="16sp"
            android:textColor="#4FC3F7"
            android:fontFamily="sans-serif-medium"
            android:gravity="center" />
    </LinearLayout>

    <FrameLayout
        android:id="@+id/test_area"
        android:layout_width="match_parent"
        android:layout_height="300dp"
        android:layout_below="@id/stats_container"
        android:layout_above="@+id/close_button"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp"
        android:background="#0D2C3E"
        android:clipChildren="true">

        <ImageView
            android:id="@+id/target_view"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/aim_target"
            android:layout_gravity="center" />

    </FrameLayout>

    <Button
        android:id="@+id/close_button"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:layout_alignParentBottom="true"
        android:fontFamily="@font/font_for_ar_en"
        android:text="@string/close"
        android:textColor="#FFFFFF"
        android:background="@drawable/rounded_button_blue" />

</RelativeLayout>
