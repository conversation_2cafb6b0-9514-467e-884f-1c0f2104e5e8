<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/cyber_neon_gradient_bg"
    tools:context=".AimOverlaySettingsActivity">

    <!-- Background Effects -->
    <ImageView
        android:id="@+id/speed_lines_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:src="@drawable/blue_speed_lines"
        android:alpha="0.4"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:contentDescription="@string/app_name" />

    <ImageView
        android:id="@+id/energy_grid"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:src="@drawable/energy_grid"
        android:alpha="0.2"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:contentDescription="@string/app_name" />

    <!-- Back Button -->
    <ImageView
        android:id="@+id/btn_back"
        android:layout_width="@dimen/icon_size_large"
        android:layout_height="@dimen/icon_size_large"
        android:layout_margin="@dimen/margin_medium"
        android:padding="@dimen/padding_small"
        android:src="@drawable/ic_back"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:clickable="true"
        android:focusable="true"
        app:tint="@color/cyber_neon_cyan"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Title -->
    <TextView
        android:id="@+id/aim_button_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/aim_button"
        android:textColor="@color/cyber_neon_cyan"
        android:textSize="@dimen/text_size_title"
        android:fontFamily="@font/font_for_ar_en"
        android:layout_marginTop="@dimen/margin_medium"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Subtitle -->
    <TextView
        android:id="@+id/aim_button_subtitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/aim_crosshair"
        android:textColor="@color/cyber_text_secondary"
        android:textSize="@dimen/text_size_subtitle"
        android:fontFamily="@font/cairo_regular"
        android:layout_marginTop="@dimen/margin_tiny"
        app:layout_constraintTop_toBottomOf="@id/aim_button_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Main Content ScrollView -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/margin_medium"
        android:fillViewport="true"
        android:scrollbars="none"
        android:overScrollMode="never"
        app:layout_constraintTop_toBottomOf="@+id/aim_button_subtitle"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/padding_medium">

            <!-- Aim Overlay Toggle Card -->
            <androidx.cardview.widget.CardView
                android:id="@+id/aim_overlay_toggle_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/margin_medium"
                app:cardCornerRadius="@dimen/card_corner_radius"
                app:cardElevation="@dimen/card_elevation"
                app:cardBackgroundColor="@color/cyber_card_bg">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/padding_medium">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/aim_overlay_toggle"
                        android:textColor="@color/cyber_neon_cyan"
                        android:textSize="@dimen/text_size_card_title"
                        android:fontFamily="@font/font_for_ar_en" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/cyber_card_stroke"
                        android:layout_marginTop="@dimen/margin_small"
                        android:layout_marginBottom="@dimen/margin_medium" />

                    <Button
                        android:id="@+id/toggle_overlay_button"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/aim_overlay_toggle"
                        android:textStyle="bold"
                        android:layout_margin="@dimen/margin_small"
                        android:textSize="@dimen/text_size_button"
                        android:background="@drawable/neon_button_bg"
                        android:textColor="@color/white"
                        android:fontFamily="@font/font_for_ar_en"
                         />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Crosshair Size Card -->
            <androidx.cardview.widget.CardView
                android:id="@+id/crosshair_size_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/margin_medium"
                app:cardCornerRadius="@dimen/card_corner_radius"
                app:cardElevation="@dimen/card_elevation"
                app:cardBackgroundColor="@color/cyber_card_bg">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/padding_medium">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/crosshair_size"
                        android:textColor="@color/cyber_neon_cyan"
                        android:textSize="@dimen/text_size_card_title"
                        android:fontFamily="@font/font_for_ar_en" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/cyber_card_stroke"
                        android:layout_marginTop="@dimen/margin_small"
                        android:layout_marginBottom="@dimen/margin_medium" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <SeekBar
                            android:id="@+id/crosshair_size_slider"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:max="100"
                            android:progress="50"
                            android:progressTint="@color/cyber_neon_cyan"
                            android:thumbTint="@color/cyber_neon_cyan" />

                        <TextView
                            android:id="@+id/crosshair_size_value"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="50"
                            android:textColor="@color/cyber_neon_cyan"
                            android:textSize="@dimen/text_size_card_content"
                            android:fontFamily="@font/cairo_regular"
                            android:layout_marginStart="@dimen/margin_small"
                            android:minWidth="30dp"
                            android:gravity="end" />
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Crosshair Color Card -->
            <androidx.cardview.widget.CardView
                android:id="@+id/crosshair_color_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/margin_medium"
                app:cardCornerRadius="@dimen/card_corner_radius"
                app:cardElevation="@dimen/card_elevation"
                app:cardBackgroundColor="@color/cyber_card_bg">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/padding_medium">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/aim_overlay_color"
                        android:textColor="@color/cyber_neon_cyan"
                        android:textSize="@dimen/text_size_card_title"
                        android:fontFamily="@font/font_for_ar_en" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/cyber_card_stroke"
                        android:layout_marginTop="@dimen/margin_small"
                        android:layout_marginBottom="@dimen/margin_medium" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center">

                        <View
                            android:id="@+id/red_color"
                            android:layout_width="@dimen/icon_size_medium"
                            android:layout_height="@dimen/icon_size_medium"
                            android:background="@drawable/circle_shape_red"
                            android:layout_marginEnd="@dimen/margin_medium"
                            android:clickable="true"
                            android:focusable="true" />

                        <View
                            android:id="@+id/green_color"
                            android:layout_width="@dimen/icon_size_medium"
                            android:layout_height="@dimen/icon_size_medium"
                            android:background="@drawable/circle_shape_green"
                            android:layout_marginEnd="@dimen/margin_medium"
                            android:clickable="true"
                            android:focusable="true" />

                        <View
                            android:id="@+id/blue_color"
                            android:layout_width="@dimen/icon_size_medium"
                            android:layout_height="@dimen/icon_size_medium"
                            android:background="@drawable/circle_shape_blue"
                            android:layout_marginEnd="16dp"
                            android:clickable="true"
                            android:focusable="true" />

                        <View
                            android:id="@+id/yellow_color"
                            android:layout_width="@dimen/icon_size_medium"
                            android:layout_height="@dimen/icon_size_medium"
                            android:background="@drawable/circle_shape_yellow"
                            android:clickable="true"
                            android:focusable="true" />
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Game Optimization Card -->
            <androidx.cardview.widget.CardView
                android:id="@+id/game_optimization_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="8dp"
                app:cardBackgroundColor="@color/cyber_card_bg">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/game_optimization"
                        android:textColor="@color/cyber_neon_cyan"
                        android:textSize="16sp"
                        android:fontFamily="@font/font_for_ar_en" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/cyber_card_stroke"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="16dp" />

                    <Button
                        android:id="@+id/clean_device_button"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/clean_device"
                        android:background="@drawable/neon_button_bg"
                        android:textColor="@color/white"
                        android:fontFamily="@font/font_for_ar_en"
                        android:layout_margin="10dp"
                        app:drawableLeftCompat="@drawable/ic_rocket"
                        android:textStyle="bold"
                        />

                    <Button
                        android:id="@+id/activate_game_mode_button"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textStyle="bold"
                        app:drawableLeftCompat="@drawable/ic_power"
                        android:text="@string/activate_game_mode"
                        android:background="@drawable/neon_button_bg"
                        android:textColor="@color/white"
                        android:fontFamily="@font/font_for_ar_en"
                        android:layout_margin="10dp"
                        />

                    <Button
                        android:id="@+id/launch_game_button"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/launch_game"
                        android:textStyle="bold"
                        app:drawableLeftCompat="@drawable/ic_smart_aim"
                        android:background="@drawable/neon_button_bg"
                        android:textColor="@color/white"
                        android:fontFamily="@font/font_for_ar_en"
                        android:layout_margin="10dp"
                         />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Aim Preview Card -->
            <androidx.cardview.widget.CardView
                android:id="@+id/aim_preview_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="8dp"
                app:cardBackgroundColor="@color/cyber_card_bg">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/aim_preview"
                        android:textColor="@color/cyber_neon_cyan"
                        android:textSize="16sp"
                        android:fontFamily="@font/font_for_ar_en" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/cyber_card_stroke"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="16dp" />

                    <FrameLayout
                        android:id="@+id/aim_preview_frame"
                        android:layout_width="match_parent"
                        android:layout_height="150dp"
                        android:background="@drawable/preview_background">

                        <ImageView
                            android:id="@+id/target_image"
                            android:layout_width="60dp"
                            android:layout_height="60dp"
                            android:src="@drawable/target_icon"
                            android:layout_gravity="center" />

                        <ImageView
                            android:id="@+id/crosshair_overlay"
                            android:layout_width="80dp"
                            android:layout_height="80dp"
                            android:src="@drawable/crosshair_simple"
                            android:layout_gravity="center" />

                        <View
                            android:id="@+id/red_dot_sight"
                            android:layout_width="8dp"
                            android:layout_height="8dp"
                            android:background="@drawable/circle_shape_red"
                            android:layout_gravity="center" />

                        <ImageView
                            android:id="@+id/recoil_pattern"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:src="@drawable/recoil_pattern"
                            android:alpha="0.3"
                            android:layout_gravity="center" />
                    </FrameLayout>

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/tap_to_test"
                        android:textColor="@color/cyber_text_secondary"
                        android:textSize="12sp"
                        android:fontFamily="@font/cairo_regular"
                        android:gravity="center"
                        android:layout_marginTop="8dp" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Action Buttons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="16dp">

                <Button
                    android:id="@+id/apply_settings_button"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/apply_settings_aim"
                    android:background="@drawable/neon_button_bg"
                    android:textColor="@color/white"
                    android:fontFamily="@font/fonts"
                    android:padding="12dp"
                    android:layout_marginEnd="8dp" />

                <Button
                    android:id="@+id/reset_button"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/reset_to_default"
                    android:background="@drawable/neon_button_bg"
                    android:textColor="@color/white"
                    android:fontFamily="@font/fonts"
                    android:padding="12dp"
                    android:layout_marginStart="8dp" />
            </LinearLayout>
        </LinearLayout>
    </ScrollView>

    <!-- Ad Container Removed -->

</androidx.constraintlayout.widget.ConstraintLayout>
