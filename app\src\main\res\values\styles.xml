<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="AlertDialogTheme" parent="ThemeOverlay.MaterialComponents.Dialog.Alert">
        <item name="colorPrimary">@color/cyber_neon_cyan</item>
        <item name="colorAccent">@color/cyber_neon_cyan</item>
        <item name="android:background">@color/cyber_dark_start</item>
        <item name="android:textColorPrimary">@color/cyber_text_primary</item>
        <item name="android:textColor">@color/cyber_text_primary</item>
        <item name="buttonBarPositiveButtonStyle">@style/AlertDialogButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/AlertDialogButtonStyle</item>
    </style>

    <style name="AlertDialogButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/cyber_neon_cyan</item>
    </style>

    <!-- Arabic Font Styles -->
    <style name="ArabicTextViewStyle">
        <item name="android:fontFamily">@font/font_for_ar_en</item>
        <item name="android:textColor">@color/cyber_text_primary</item>
    </style>

    <style name="ArabicTextViewBoldStyle">
        <item name="android:fontFamily">@font/font_for_ar_en</item>
        <item name="android:textColor">@color/cyber_text_primary</item>
    </style>

    <style name="ArabicTextViewMediumStyle">
        <item name="android:fontFamily">@font/font_for_ar_en</item>
        <item name="android:textColor">@color/cyber_text_primary</item>
    </style>

    <!-- Premium Feature Styles -->
    <style name="PremiumFeatureStyle">
        <item name="android:fontFamily">@font/font_for_ar_en</item>
        <item name="android:textColor">@color/cyber_neon_gold</item>
        <item name="android:drawableTint">@color/cyber_neon_gold</item>
        <item name="android:drawablePadding">8dp</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:textSize">14sp</item>
        <item name="android:paddingTop">4dp</item>
        <item name="android:paddingBottom">4dp</item>
    </style>

    <style name="PremiumButtonStyle" parent="Widget.MaterialComponents.Button">
        <item name="android:fontFamily">@font/font_for_ar_en</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:textSize">16sp</item>
        <item name="android:paddingTop">8dp</item>
        <item name="android:paddingBottom">8dp</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="backgroundTint">@color/cyber_neon_gold</item>
        <item name="cornerRadius">24dp</item>
        <item name="android:elevation">4dp</item>
    </style>

    <!-- Enhanced Button Styles with Animations -->
    <style name="CyberNeonButtonStyle">
        <item name="android:background">@drawable/neon_button_bg</item>
        <item name="android:fontFamily">@font/rajdhani_bold</item>
        <item name="android:textColor">@color/cyber_neon_cyan</item>
        <item name="android:textSize">16sp</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
        <item name="android:stateListAnimator">@animator/button_state_animator</item>
    </style>

    <!-- Unified Text Styles -->
    <style name="CyberNeonHeadingStyle">
        <item name="android:fontFamily">@font/orbitron_bold</item>
        <item name="android:textColor">@color/cyber_neon_cyan</item>
        <item name="android:textSize">22sp</item>
        <item name="android:shadowColor">@color/cyber_neon_cyan</item>
        <item name="android:shadowDx">0</item>
        <item name="android:shadowDy">0</item>
        <item name="android:shadowRadius">8</item>
    </style>

    <style name="CyberNeonSubheadingStyle">
        <item name="android:fontFamily">@font/rajdhani_bold</item>
        <item name="android:textColor">@color/cyber_text_primary</item>
        <item name="android:textSize">16sp</item>
    </style>

    <style name="CyberNeonBodyTextStyle">
        <item name="android:fontFamily">@font/cairo_regular</item>
        <item name="android:textColor">@color/cyber_text_secondary</item>
        <item name="android:textSize">14sp</item>
    </style>
</resources>
