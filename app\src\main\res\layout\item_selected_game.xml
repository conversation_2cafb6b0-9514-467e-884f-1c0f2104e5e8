<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardBackgroundColor="@color/card_background"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="12dp"
        android:gravity="center_vertical">

        <!-- Game Icon -->
        <ImageView
            android:id="@+id/game_icon"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:scaleType="centerCrop"
            android:background="@drawable/neon_button_bg" />

        <!-- Game Info -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="12dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/game_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/cairo_regular"
                android:text="Game Name"
                android:textColor="@color/text_color_primary"
                android:textSize="14sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/game_package"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/cairo_regular"
                android:text="com.example.game"
                android:textColor="@color/text_color_secondary"
                android:textSize="12sp" />
        </LinearLayout>

        <!-- Launch Button -->
        <Button
            android:id="@+id/launch_button"
            android:layout_width="wrap_content"
            android:layout_height="32dp"
            android:layout_marginEnd="8dp"
            android:background="@drawable/neon_button_bg"
            android:fontFamily="@font/cairo_regular"
            android:text="@string/launch_game"
            android:textColor="@color/neon_blue"
            android:textSize="12sp"
            android:minWidth="60dp" />

        <!-- Remove Button -->
        <ImageButton
            android:id="@+id/remove_button"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:background="@drawable/neon_button_bg"
            android:src="@drawable/ic_delete"
            android:contentDescription="@string/remove_game"
            app:tint="@color/neon_orange" />
    </LinearLayout>
</androidx.cardview.widget.CardView>
