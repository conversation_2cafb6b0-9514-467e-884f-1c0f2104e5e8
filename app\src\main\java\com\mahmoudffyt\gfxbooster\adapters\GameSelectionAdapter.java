package com.mahmoudffyt.gfxbooster.adapters;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.mahmoudffyt.gfxbooster.R;
import com.mahmoudffyt.gfxbooster.utils.GameDetector;
import com.mahmoudffyt.gfxbooster.utils.GameDetectorAndroid14;

import java.util.ArrayList;
import java.util.List;

public class GameSelectionAdapter extends RecyclerView.Adapter<GameSelectionAdapter.GameViewHolder> {
    
    private Context context;
    private List<GameDetectorAndroid14.AppInfo> games;
    private OnGameSelectedListener listener;
    
    public interface OnGameSelectedListener {
        void onGameSelected(GameDetectorAndroid14.AppInfo appInfo);
    }
    
    public GameSelectionAdapter(Context context, OnGameSelectedListener listener) {
        this.context = context;
        this.games = new ArrayList<>();
        this.listener = listener;
    }
    
    public void updateGames(List<GameDetectorAndroid14.AppInfo> newGames) {
        this.games.clear();
        this.games.addAll(newGames);
        notifyDataSetChanged();
    }
    
    @NonNull
    @Override
    public GameViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_game_selection, parent, false);
        return new GameViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull GameViewHolder holder, int position) {
        GameDetectorAndroid14.AppInfo game = games.get(position);
        
        holder.gameName.setText(game.appName);
        holder.packageName.setText(game.packageName);
        holder.gameIcon.setImageDrawable(game.icon);
        
        // Show game badge if it's a game
        if (game.isGame) {
            holder.gameBadge.setVisibility(View.VISIBLE);
        } else {
            holder.gameBadge.setVisibility(View.GONE);
        }
        
        holder.itemView.setOnClickListener(v -> {
            if (listener != null) {
                listener.onGameSelected(game);
            }
        });
    }
    
    @Override
    public int getItemCount() {
        return games.size();
    }
    
    static class GameViewHolder extends RecyclerView.ViewHolder {
        ImageView gameIcon;
        TextView gameName;
        TextView packageName;
        ImageView gameBadge;
        
        public GameViewHolder(@NonNull View itemView) {
            super(itemView);
            gameIcon = itemView.findViewById(R.id.game_icon);
            gameName = itemView.findViewById(R.id.game_name);
            packageName = itemView.findViewById(R.id.package_name);
            gameBadge = itemView.findViewById(R.id.game_badge);
        }
    }
}
