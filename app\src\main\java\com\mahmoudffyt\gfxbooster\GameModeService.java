package com.mahmoudffyt.gfxbooster;

import android.app.ActivityManager;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.app.usage.UsageEvents;
import android.app.usage.UsageStatsManager;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.content.pm.ServiceInfo;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

public class GameModeService extends Service {

    private static final String CHANNEL_ID = "game_mode_service_channel";
    private static final int NOTIFICATION_ID = 1003;
    private static final long CHECK_INTERVAL = 5000; // Check every 5 seconds

    private Handler handler;
    private Runnable gameCheckRunnable;
    private SharedPreferences preferences;
    private boolean isGameRunning = false;
    private String currentGamePackage = "";

    // List of known game packages (this would be more comprehensive in a real app)
    private List<String> knownGamePackages = new ArrayList<>();

    @Override
    public void onCreate() {
        super.onCreate();

        try {
            // Initialize preferences
            preferences = getSharedPreferences("GameModePrefs", MODE_PRIVATE);

            // Initialize handler for periodic checks
            handler = new Handler();

            // Create notification channel for Android 8.0+ (important for foreground service)
            createNotificationChannel();

            // Initialize known game packages (this would be more comprehensive in a real app)
            initializeKnownGamePackages();

            // Initialize the game check runnable
            gameCheckRunnable = new Runnable() {
                @Override
                public void run() {
                    checkForRunningGames();
                    handler.postDelayed(this, CHECK_INTERVAL);
                }
            };
        } catch (Exception e) {
            // Log any errors during initialization
            android.util.Log.e("GameModeService", "Error in onCreate: " + e.getMessage());
        }
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        // Start as a foreground service with notification immediately
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                // For Android 10+ (API 29+), use the new startForeground with foregroundServiceType
                int foregroundServiceType = ServiceInfo.FOREGROUND_SERVICE_TYPE_DATA_SYNC;

                // Check if a specific foreground service type was requested
                if (intent != null && intent.hasExtra("foregroundServiceType")) {
                    foregroundServiceType = intent.getIntExtra("foregroundServiceType",
                            ServiceInfo.FOREGROUND_SERVICE_TYPE_DATA_SYNC);
                }

                startForeground(NOTIFICATION_ID, createNotification(), foregroundServiceType);
            } else {
                // For older Android versions
                startForeground(NOTIFICATION_ID, createNotification());
            }
        } catch (Exception e) {
            // Fallback in case of any issues
            startForeground(NOTIFICATION_ID, createNotification());
        }

        // Start periodic checks for running games
        handler.post(gameCheckRunnable);

        // If service is killed, restart it
        return START_STICKY;
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onDestroy() {
        try {
            // Stop periodic checks
            if (handler != null && gameCheckRunnable != null) {
                handler.removeCallbacks(gameCheckRunnable);
            }

            // If a game was optimized, restore normal settings
            if (isGameRunning) {
                restoreNormalSettings();
            }

            // Save the current state
            if (preferences != null) {
                preferences.edit().putBoolean("isGameRunning", false).apply();
            }

            // Remove notification
            NotificationManager notificationManager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
            if (notificationManager != null) {
                notificationManager.cancel(NOTIFICATION_ID);
            }
        } catch (Exception e) {
            // Log any errors during cleanup
            android.util.Log.e("GameModeService", "Error in onDestroy: " + e.getMessage());
        } finally {
            super.onDestroy();
        }
    }

    private void createNotificationChannel() {
        // Create the notification channel for Android 8.0+
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                    CHANNEL_ID,
                    "Game Mode Service",
                    NotificationManager.IMPORTANCE_LOW);
            channel.setDescription("Background service for Game Mode");

            NotificationManager notificationManager = getSystemService(NotificationManager.class);
            notificationManager.createNotificationChannel(channel);
        }
    }

    private Notification createNotification() {
        try {
            // Create an intent to open the GameModeActivity
            Intent intent = new Intent(this, GameModeActivity.class);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);

            // Create pending intent
            int flags = PendingIntent.FLAG_UPDATE_CURRENT;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                flags |= PendingIntent.FLAG_IMMUTABLE;
            }
            PendingIntent pendingIntent = PendingIntent.getActivity(this, 0, intent, flags);

            // Build the notification with ongoing flag to make it persistent
            return new NotificationCompat.Builder(this, CHANNEL_ID)
                    .setSmallIcon(R.drawable.ic_power)
                    .setContentTitle(getString(R.string.game_mode_service))
                    .setContentText(getString(R.string.monitoring_for_game_launches))
                    .setPriority(NotificationCompat.PRIORITY_LOW)
                    .setContentIntent(pendingIntent)
                    .setOngoing(true) // Make it persistent
                    .build();
        } catch (Exception e) {
            // Fallback notification in case of any issues
            return new NotificationCompat.Builder(this, CHANNEL_ID)
                    .setSmallIcon(R.drawable.ic_power)
                    .setContentTitle("Game Mode")
                    .setContentText("Service Running")
                    .setPriority(NotificationCompat.PRIORITY_LOW)
                    .setOngoing(true)
                    .build();
        }
    }
/*
*  edit here pkg
* */
    private void initializeKnownGamePackages() {
        // This would be a more comprehensive list in a real app
       /* knownGamePackages.add("com.tencent.ig"); // PUBG Mobile
        knownGamePackages.add("com.activision.callofduty.shooter"); // Call of Duty Mobile
        knownGamePackages.add("com.supercell.clashofclans"); // Clash of Clans
        knownGamePackages.add("com.mojang.minecraftpe"); // Minecraft
        knownGamePackages.add("com.epicgames.fortnite"); // Fortnite
        knownGamePackages.add("com.gameloft.android.ANMP.GloftA9HM"); // Asphalt 9
        knownGamePackages.add("com.firsttouchgames.dls7"); // Dream League Soccer
        knownGamePackages.add("com.ea.gp.fifamobile"); // FIFA Mobile
        knownGamePackages.add("com.supercell.brawlstars"); // Brawl Stars*/
        knownGamePackages.add("com.dts.freefireth"); // Free Fire
    }

    private void checkForRunningGames() {
        String foregroundApp = getForegroundApp();

        if (foregroundApp != null && isGameApp(foregroundApp)) {
            if (!isGameRunning || !currentGamePackage.equals(foregroundApp)) {
                // New game detected
                currentGamePackage = foregroundApp;
                isGameRunning = true;

                // Optimize device for gaming
                optimizeForGaming(foregroundApp);
            }
        } else if (isGameRunning) {
            // Game no longer in foreground
            isGameRunning = false;
            currentGamePackage = "";

            // Restore normal settings
            restoreNormalSettings();
        }
    }

    private String getForegroundApp() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            UsageStatsManager usageStatsManager = (UsageStatsManager) getSystemService(Context.USAGE_STATS_SERVICE);
            long time = System.currentTimeMillis();
            long startTime = time - TimeUnit.MINUTES.toMillis(1);

            UsageEvents usageEvents = usageStatsManager.queryEvents(startTime, time);
            UsageEvents.Event event = new UsageEvents.Event();
            String packageName = null;

            while (usageEvents.hasNextEvent()) {
                usageEvents.getNextEvent(event);
                if (event.getEventType() == UsageEvents.Event.MOVE_TO_FOREGROUND) {
                    packageName = event.getPackageName();
                }
            }

            return packageName;
        } else {
            ActivityManager activityManager = (ActivityManager) getSystemService(Context.ACTIVITY_SERVICE);
            List<ActivityManager.RunningAppProcessInfo> appProcesses = activityManager.getRunningAppProcesses();

            if (appProcesses != null && !appProcesses.isEmpty()) {
                for (ActivityManager.RunningAppProcessInfo appProcess : appProcesses) {
                    if (appProcess.importance == ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND) {
                        return appProcess.processName;
                    }
                }
            }
        }

        return null;
    }

    private boolean isGameApp(String packageName) {
        // Check if it's in our known game packages list
        if (knownGamePackages.contains(packageName)) {
            return true;
        }

        // Check if it's categorized as a game in the Play Store
        try {
            PackageManager pm = getPackageManager();
            ApplicationInfo appInfo = pm.getApplicationInfo(packageName, 0);

            // Check if it's a game using FLAG_IS_GAME (available on all API levels)
            if ((appInfo.flags & ApplicationInfo.FLAG_IS_GAME) != 0) {
                return true;
            }

            // Check if it's in our known game packages list
            if (knownGamePackages.contains(packageName)) {
                return true;
            }

            // Add to known games for future reference
            if (!knownGamePackages.contains(packageName)) {
                knownGamePackages.add(packageName);
            }

            return true;
        } catch (PackageManager.NameNotFoundException e) {
            return false;
        }
    }

    private void optimizeForGaming(String gamePackage) {
        // In a real app, this would implement actual optimizations:
        // 1. Set CPU governor to performance mode
        // 2. Increase process priority for the game
        // 3. Clear memory
        // 4. Disable unnecessary background services
        // 5. Set network priority for the game

        // For this example, we'll just show a toast and update the notification

        // Get game name
        String gameName = getGameName(gamePackage);

        // Show toast
        Toast.makeText(this, getString(R.string.optimizing_for) + gameName, Toast.LENGTH_SHORT).show();

        // Update notification
        NotificationManager notificationManager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
        NotificationCompat.Builder builder = new NotificationCompat.Builder(this, CHANNEL_ID)
                .setSmallIcon(R.drawable.ic_power)
                .setContentTitle(getString(R.string.game_mode_active))
                .setContentText(getString(R.string.optimizing_performance_for) + gameName)
                .setPriority(NotificationCompat.PRIORITY_DEFAULT);

        notificationManager.notify(NOTIFICATION_ID, builder.build());
    }

    private void restoreNormalSettings() {
        // In a real app, this would restore normal device settings:
        // 1. Reset CPU governor
        // 2. Reset process priorities
        // 3. Reset network priorities

        // For this example, we'll just update the notification

        // Update notification
        NotificationManager notificationManager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
        NotificationCompat.Builder builder = new NotificationCompat.Builder(this, CHANNEL_ID)
                .setSmallIcon(R.drawable.ic_power)
                .setContentTitle(getString(R.string.game_mode_service))
                .setContentText(getString(R.string.monitoring_for_game_launches))
                .setPriority(NotificationCompat.PRIORITY_LOW);

        notificationManager.notify(NOTIFICATION_ID, builder.build());
    }

    private String getGameName(String packageName) {
        try {
            PackageManager pm = getPackageManager();
            ApplicationInfo appInfo = pm.getApplicationInfo(packageName, 0);
            return pm.getApplicationLabel(appInfo).toString();
        } catch (PackageManager.NameNotFoundException e) {
            return packageName;
        }
    }
}
