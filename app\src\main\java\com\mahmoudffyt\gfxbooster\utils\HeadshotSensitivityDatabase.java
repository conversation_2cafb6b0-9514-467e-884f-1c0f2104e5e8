package com.mahmoudffyt.gfxbooster.utils;

import java.util.HashMap;
import java.util.Map;

/**
 * Database of optimal sensitivity settings for different device profiles
 * Based on the provided sensitivity table
 */
public class HeadshotSensitivityDatabase {

    // GFX Level constants
    public static final String GFX_SMOOTH = "smooth";
    public static final String GFX_STANDARD = "standard";
    public static final String GFX_HIGH = "high";

    // Device profile constants
    public static final String PROFILE_2GB_MEDIUM_SCREEN_WEAK_CPU = "2gb_medium_screen_weak_cpu";
    public static final String PROFILE_3GB_MEDIUM_SCREEN_WEAK_CPU = "3gb_medium_screen_weak_cpu";
    public static final String PROFILE_4GB = "4gb";
    public static final String PROFILE_6GB = "6gb";

    // Settings database - Maps GFX level to a map of device profiles to sensitivity settings
    private static final Map<String, Map<String, Map<String, Integer>>> SENSITIVITY_DATABASE = new HashMap<>();

    static {
        // Initialize database with optimal settings for different device profiles and GFX levels

        // Initialize maps for each GFX level
        Map<String, Map<String, Integer>> smoothSettings = new HashMap<>();
        Map<String, Map<String, Integer>> standardSettings = new HashMap<>();
        Map<String, Map<String, Integer>> highSettings = new HashMap<>();

        // SMOOTH GFX LEVEL

        // 2GB RAM - Medium Screen - Weak CPU - Smooth GFX
        Map<String, Integer> smooth2gbSettings = new HashMap<>();
        smooth2gbSettings.put("generalSensitivity", 200);
        smooth2gbSettings.put("redDotSensitivity", 200);
        smooth2gbSettings.put("scope2xSensitivity", 180);
        smooth2gbSettings.put("scope4xSensitivity", 180);
        smooth2gbSettings.put("scopeAwmSensitivity", 140);
        smooth2gbSettings.put("freeCameraButton", 50); // Default value for free camera button
        smooth2gbSettings.put("dpiSetting", 460);
        smoothSettings.put(PROFILE_2GB_MEDIUM_SCREEN_WEAK_CPU, smooth2gbSettings);

        // 3GB RAM - Medium Screen - Weak CPU - Smooth GFX
        Map<String, Integer> smooth3gbSettings = new HashMap<>();
        smooth3gbSettings.put("generalSensitivity", 200);
        smooth3gbSettings.put("redDotSensitivity", 200);
        smooth3gbSettings.put("scope2xSensitivity", 195);
        smooth3gbSettings.put("scope4xSensitivity", 190);
        smooth3gbSettings.put("scopeAwmSensitivity", 140);
        smooth3gbSettings.put("freeCameraButton", 50); // Default value for free camera button
        smooth3gbSettings.put("dpiSetting", 500);
        smoothSettings.put(PROFILE_3GB_MEDIUM_SCREEN_WEAK_CPU, smooth3gbSettings);

        // STANDARD GFX LEVEL

        // 4GB RAM - Standard GFX
        Map<String, Integer> standard4gbSettings = new HashMap<>();
        standard4gbSettings.put("generalSensitivity", 200);
        standard4gbSettings.put("redDotSensitivity", 200);
        standard4gbSettings.put("scope2xSensitivity", 190);
        standard4gbSettings.put("scope4xSensitivity", 180);
        standard4gbSettings.put("scopeAwmSensitivity", 130);
        standard4gbSettings.put("freeCameraButton", 50); // Default value for free camera button
        standard4gbSettings.put("dpiSetting", 550);
        standardSettings.put(PROFILE_4GB, standard4gbSettings);

        // HIGH GFX LEVEL

        // 6GB RAM - High GFX
        Map<String, Integer> high6gbSettings = new HashMap<>();
        high6gbSettings.put("generalSensitivity", 200);
        high6gbSettings.put("redDotSensitivity", 200);
        high6gbSettings.put("scope2xSensitivity", 192);
        high6gbSettings.put("scope4xSensitivity", 188);
        high6gbSettings.put("scopeAwmSensitivity", 140);
        high6gbSettings.put("freeCameraButton", 50); // Default value for free camera button
        high6gbSettings.put("dpiSetting", 600);
        highSettings.put(PROFILE_6GB, high6gbSettings);

        // Add all GFX level maps to the main database
        SENSITIVITY_DATABASE.put(GFX_SMOOTH, smoothSettings);
        SENSITIVITY_DATABASE.put(GFX_STANDARD, standardSettings);
        SENSITIVITY_DATABASE.put(GFX_HIGH, highSettings);
    }

    /**
     * Determine device profile based on device specs
     */
    public static String determineDeviceProfile(Map<String, Object> deviceSpecs) {
        // Extract relevant specs
        long ramMB = (long) deviceSpecs.get("ram");
        double ramGB = ramMB / 1024.0;
        int estimatedFps = (int) deviceSpecs.get("estimatedFps");
        boolean isWeakCpu = isWeakCpu(deviceSpecs);
        String screenSize = determineScreenSize(deviceSpecs);

        // Determine profile based on specs
        if (ramGB <= 2.5) {
            return PROFILE_2GB_MEDIUM_SCREEN_WEAK_CPU;
        } else if (ramGB <= 3.5 && isWeakCpu) {
            return PROFILE_3GB_MEDIUM_SCREEN_WEAK_CPU;
        } else if (ramGB <= 5.0) {
            return PROFILE_4GB;
        } else {
            return PROFILE_6GB;
        }
    }

    /**
     * Determine if the device has a weak CPU
     */
    private static boolean isWeakCpu(Map<String, Object> deviceSpecs) {
        int estimatedFps = (int) deviceSpecs.get("estimatedFps");
        String processor = ((String) deviceSpecs.get("processor")).toLowerCase();

        // Consider CPU weak if estimated FPS is low or processor is known to be weak
        return estimatedFps < 30 ||
               processor.contains("mt6") || // MediaTek low-end
               processor.contains("sc") ||  // Spreadtrum
               processor.contains("msm8") || // Older Qualcomm
               processor.contains("exynos3") || // Older Exynos
               processor.contains("exynos4");
    }

    /**
     * Determine screen size category
     */
    private static String determineScreenSize(Map<String, Object> deviceSpecs) {
        int screenWidth = (int) deviceSpecs.get("screenWidth");
        int screenHeight = (int) deviceSpecs.get("screenHeight");

        // Calculate screen size in pixels (diagonal)
        double screenSize = Math.sqrt(screenWidth * screenWidth + screenHeight * screenHeight);

        if (screenSize < 1500) {
            return "small";
        } else if (screenSize < 2000) {
            return "medium";
        } else {
            return "large";
        }
    }

    /**
     * Get optimal sensitivity settings for a specific device profile and GFX level
     */
    public static Map<String, Integer> getOptimalSettings(String gfxLevel, String deviceProfile) {
        // Check if the GFX level exists in the database
        if (!SENSITIVITY_DATABASE.containsKey(gfxLevel)) {
            // Default to standard if GFX level not found
            gfxLevel = GFX_STANDARD;
        }

        Map<String, Map<String, Integer>> gfxSettings = SENSITIVITY_DATABASE.get(gfxLevel);

        // Check if the device profile exists for this GFX level
        if (gfxSettings.containsKey(deviceProfile)) {
            return new HashMap<>(gfxSettings.get(deviceProfile));
        }

        // If profile not found for this GFX level, try to find a suitable alternative
        if (deviceProfile.equals(PROFILE_2GB_MEDIUM_SCREEN_WEAK_CPU) ||
            deviceProfile.equals(PROFILE_3GB_MEDIUM_SCREEN_WEAK_CPU)) {
            // For low-end devices, prefer smooth settings
            if (SENSITIVITY_DATABASE.get(GFX_SMOOTH).containsKey(deviceProfile)) {
                return new HashMap<>(SENSITIVITY_DATABASE.get(GFX_SMOOTH).get(deviceProfile));
            } else if (SENSITIVITY_DATABASE.get(GFX_SMOOTH).containsKey(PROFILE_2GB_MEDIUM_SCREEN_WEAK_CPU)) {
                return new HashMap<>(SENSITIVITY_DATABASE.get(GFX_SMOOTH).get(PROFILE_2GB_MEDIUM_SCREEN_WEAK_CPU));
            }
        } else if (deviceProfile.equals(PROFILE_4GB)) {
            // For mid-range devices, prefer standard settings
            if (SENSITIVITY_DATABASE.get(GFX_STANDARD).containsKey(PROFILE_4GB)) {
                return new HashMap<>(SENSITIVITY_DATABASE.get(GFX_STANDARD).get(PROFILE_4GB));
            }
        } else if (deviceProfile.equals(PROFILE_6GB)) {
            // For high-end devices, prefer high settings
            if (SENSITIVITY_DATABASE.get(GFX_HIGH).containsKey(PROFILE_6GB)) {
                return new HashMap<>(SENSITIVITY_DATABASE.get(GFX_HIGH).get(PROFILE_6GB));
            }
        }

        // If no suitable alternative found, use 4GB standard settings as default
        if (SENSITIVITY_DATABASE.get(GFX_STANDARD).containsKey(PROFILE_4GB)) {
            return new HashMap<>(SENSITIVITY_DATABASE.get(GFX_STANDARD).get(PROFILE_4GB));
        }

        // Create default settings if nothing else is available
        Map<String, Integer> defaultSettings = new HashMap<>();
        defaultSettings.put("generalSensitivity", 200);
        defaultSettings.put("redDotSensitivity", 200);
        defaultSettings.put("scope2xSensitivity", 180);
        defaultSettings.put("scope4xSensitivity", 180);
        defaultSettings.put("scopeAwmSensitivity", 130);
        defaultSettings.put("dpiSetting", 500);

        return defaultSettings;
    }
}
