<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@drawable/cyber_neon_gradient_bg">

    <!-- Toolbar -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/cyber_dark_center"
        android:elevation="4dp">

        <ImageView
            android:id="@+id/back_button"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_centerVertical="true"
            android:layout_marginStart="8dp"
            android:padding="12dp"
            android:src="@drawable/ic_back"
            app:tint="@color/cyber_neon_cyan" />

        <TextView
            android:id="@+id/title_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="@string/app_guide"
            android:textColor="@color/cyber_neon_cyan"
            android:textSize="18sp"
            android:fontFamily="@font/font_for_ar_en" />
    </RelativeLayout>

    <!-- WebView -->
    <WebView
        android:id="@+id/webView"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

</LinearLayout>
