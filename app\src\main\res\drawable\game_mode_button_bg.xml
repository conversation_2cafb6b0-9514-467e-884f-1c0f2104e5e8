<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Button background with gradient -->
    <item>
        <shape android:shape="oval">
            <gradient
                android:type="radial"
                android:gradientRadius="150dp"
                android:startColor="#1A1A40"
                android:endColor="#0A0A20" />
        </shape>
    </item>
    
    <!-- Inner glow -->
    <item android:left="2dp" android:top="2dp" android:right="2dp" android:bottom="2dp">
        <shape android:shape="oval">
            <stroke android:width="2dp" android:color="@color/neon_blue" />
            <solid android:color="#00000000" />
        </shape>
    </item>
    
    <!-- Outer glow -->
    <item android:left="1dp" android:top="1dp" android:right="1dp" android:bottom="1dp">
        <shape android:shape="oval">
            <stroke android:width="1dp" android:color="@color/neon_blue_glow" />
            <solid android:color="#00000000" />
        </shape>
    </item>
</layer-list>
