package com.mahmoudffyt.gfxbooster.utils;

import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.os.BatteryManager;
import android.os.Handler;
import android.os.Looper;
import android.telephony.TelephonyManager;
import android.util.Log;

import java.io.IOException;
import java.net.InetAddress;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Real-time system monitoring utility
 */
public class RealTimeMonitor {
    private static final String TAG = "RealTimeMonitor";
    
    private Context context;
    private Handler mainHandler;
    private ExecutorService executor;
    private boolean isMonitoring = false;
    
    // Monitoring intervals
    private static final long BATTERY_UPDATE_INTERVAL = 5000; // 5 seconds
    private static final long NETWORK_UPDATE_INTERVAL = 10000; // 10 seconds
    
    // Listeners
    public interface MonitorListener {
        void onBatteryTemperatureUpdate(float temperature);
        void onNetworkPingUpdate(int ping);
        void onNetworkTypeUpdate(String networkType);
    }
    
    private MonitorListener listener;
    
    public RealTimeMonitor(Context context) {
        this.context = context;
        this.mainHandler = new Handler(Looper.getMainLooper());
        this.executor = Executors.newFixedThreadPool(2);
    }
    
    public void setMonitorListener(MonitorListener listener) {
        this.listener = listener;
    }
    
    public void startMonitoring() {
        if (isMonitoring) return;
        
        isMonitoring = true;
        Log.d(TAG, "Starting real-time monitoring");
        
        // Start battery monitoring
        startBatteryMonitoring();
        
        // Start network monitoring
        startNetworkMonitoring();
    }
    
    public void stopMonitoring() {
        isMonitoring = false;
        Log.d(TAG, "Stopping real-time monitoring");
        
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
    }
    
    private void startBatteryMonitoring() {
        Runnable batteryTask = new Runnable() {
            @Override
            public void run() {
                if (!isMonitoring) return;
                
                float temperature = getBatteryTemperature();
                
                mainHandler.post(() -> {
                    if (listener != null) {
                        listener.onBatteryTemperatureUpdate(temperature);
                    }
                });
                
                // Schedule next update
                mainHandler.postDelayed(this, BATTERY_UPDATE_INTERVAL);
            }
        };
        
        mainHandler.post(batteryTask);
    }
    
    private void startNetworkMonitoring() {
        Runnable networkTask = new Runnable() {
            @Override
            public void run() {
                if (!isMonitoring) return;
                
                // Update network type immediately
                String networkType = getNetworkType();
                mainHandler.post(() -> {
                    if (listener != null) {
                        listener.onNetworkTypeUpdate(networkType);
                    }
                });
                
                // Ping test in background thread
                executor.execute(() -> {
                    int ping = measurePing();
                    mainHandler.post(() -> {
                        if (listener != null) {
                            listener.onNetworkPingUpdate(ping);
                        }
                    });
                });
                
                // Schedule next update
                mainHandler.postDelayed(this, NETWORK_UPDATE_INTERVAL);
            }
        };
        
        mainHandler.post(networkTask);
    }
    
    /**
     * Get real battery temperature
     */
    private float getBatteryTemperature() {
        try {
            IntentFilter filter = new IntentFilter(Intent.ACTION_BATTERY_CHANGED);
            Intent batteryStatus = context.registerReceiver(null, filter);
            
            if (batteryStatus != null) {
                int temperature = batteryStatus.getIntExtra(BatteryManager.EXTRA_TEMPERATURE, -1);
                if (temperature != -1) {
                    // Temperature is in tenths of degrees Celsius
                    return temperature / 10.0f;
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error getting battery temperature: " + e.getMessage());
        }
        
        return -1; // Error value
    }
    
    /**
     * Get current network type
     */
    private String getNetworkType() {
        try {
            ConnectivityManager cm = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
            if (cm == null) return "Unknown";
            
            NetworkInfo activeNetwork = cm.getActiveNetworkInfo();
            if (activeNetwork == null || !activeNetwork.isConnected()) {
                return "No Connection";
            }
            
            if (activeNetwork.getType() == ConnectivityManager.TYPE_WIFI) {
                return "WiFi";
            } else if (activeNetwork.getType() == ConnectivityManager.TYPE_MOBILE) {
                TelephonyManager tm = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
                if (tm != null) {
                    int networkType = tm.getNetworkType();
                    switch (networkType) {
                        case TelephonyManager.NETWORK_TYPE_LTE:
                            return "4G LTE";
                        case TelephonyManager.NETWORK_TYPE_HSDPA:
                        case TelephonyManager.NETWORK_TYPE_HSUPA:
                        case TelephonyManager.NETWORK_TYPE_HSPA:
                        case TelephonyManager.NETWORK_TYPE_HSPAP:
                            return "3G";
                        case TelephonyManager.NETWORK_TYPE_EDGE:
                        case TelephonyManager.NETWORK_TYPE_GPRS:
                            return "2G";
                        case TelephonyManager.NETWORK_TYPE_NR:
                            return "5G";
                        default:
                            return "Mobile Data";
                    }
                }
                return "Mobile Data";
            }
            
            return "Unknown";
        } catch (Exception e) {
            Log.e(TAG, "Error getting network type: " + e.getMessage());
            return "Error";
        }
    }
    
    /**
     * Measure network ping to Google DNS
     */
    private int measurePing() {
        try {
            String host = "*******"; // Google DNS
            long startTime = System.currentTimeMillis();
            
            InetAddress address = InetAddress.getByName(host);
            boolean reachable = address.isReachable(5000); // 5 second timeout
            
            if (reachable) {
                long endTime = System.currentTimeMillis();
                return (int) (endTime - startTime);
            } else {
                return -1; // Unreachable
            }
        } catch (IOException e) {
            Log.e(TAG, "Error measuring ping: " + e.getMessage());
            return -1;
        }
    }
    
    /**
     * Get ping quality description
     */
    public static String getPingQuality(int ping) {
        if (ping < 0) return "No Connection";
        if (ping < 50) return "Excellent";
        if (ping < 100) return "Good";
        if (ping < 200) return "Fair";
        return "Poor";
    }
    
    /**
     * Get temperature status color
     */
    public static String getTemperatureStatus(float temperature) {
        if (temperature < 0) return "Unknown";
        if (temperature < 35) return "Cool";
        if (temperature < 40) return "Normal";
        if (temperature < 45) return "Warm";
        return "Hot";
    }
}
