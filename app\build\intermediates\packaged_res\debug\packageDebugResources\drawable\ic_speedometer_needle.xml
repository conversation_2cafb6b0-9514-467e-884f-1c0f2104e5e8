<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="200dp"
    android:height="100dp"
    android:viewportWidth="200"
    android:viewportHeight="100">

    <!-- Needle shadow for depth effect (مظللة) -->
    <path
        android:fillColor="#33000000"
        android:pathData="M102,18 L99,23 L105,23 Z" />
    <path
        android:strokeColor="#33000000"
        android:strokeWidth="3"
        android:strokeLineCap="round"
        android:fillColor="#00000000"
        android:pathData="M102,18 L102,58" />

    <!-- Needle -->
    <path
        android:fillColor="#fff"
        android:pathData="M100,10 L96,26 L104,26 Z" />
    <path
        android:strokeColor="#fff"
        android:strokeWidth="3"
        android:strokeLineCap="round"
        android:fillColor="#00000000"
        android:pathData="M100,20 L100,60" />

    <!-- Needle highlight -->
    <path
        android:strokeColor="#80FFFFFF"
        android:strokeWidth="1"
        android:strokeLineCap="round"
        android:fillColor="#00000000"
        android:pathData="M101,20 L101,58" />

    <!-- Center point shadow -->
    <path
        android:fillColor="#33000000"
        android:pathData="M102,60 A5,5 0 1,1 102,66 A5,5 0 1,1 102,60" />

    <!-- Center point -->
    <path
        android:fillColor="#fff"
        android:pathData="M100,60 A4,4 0 1,1 100,68 A4,4 0 1,1 100,60" />

    <!-- Center point highlight -->
    <path
        android:fillColor="#80FFFFFF"
        android:pathData="M98,62 A1.5,1.5 0 1,1 98,65 A1.5,1.5 0 1,1 98,62" />
</vector>
