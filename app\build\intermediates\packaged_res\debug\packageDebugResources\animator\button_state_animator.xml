<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Pressed state -->
    <item android:state_pressed="true">
        <set>
            <objectAnimator
                android:duration="100"
                android:propertyName="scaleX"
                android:valueTo="0.95"
                android:valueType="floatType" />
            <objectAnimator
                android:duration="100"
                android:propertyName="scaleY"
                android:valueTo="0.95"
                android:valueType="floatType" />
            <objectAnimator
                android:duration="100"
                android:propertyName="alpha"
                android:valueTo="0.9"
                android:valueType="floatType" />
        </set>
    </item>
    <!-- Default state -->
    <item>
        <set>
            <objectAnimator
                android:duration="200"
                android:propertyName="scaleX"
                android:valueTo="1.0"
                android:valueType="floatType" />
            <objectAnimator
                android:duration="200"
                android:propertyName="scaleY"
                android:valueTo="1.0"
                android:valueType="floatType" />
            <objectAnimator
                android:duration="200"
                android:propertyName="alpha"
                android:valueTo="1.0"
                android:valueType="floatType" />
        </set>
    </item>
</selector>
