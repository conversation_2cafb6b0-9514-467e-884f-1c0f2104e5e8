{"logs": [{"outputFile": "com.mahmoudffyt.gfxbooster.app-mergeDebugResources-49:/values-ro/values-ro.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cf3dd3f063a8dc2168576dc8a6639ef4\\transformed\\core-1.13.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,454,556,665,782", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "148,250,350,449,551,660,777,878"}, "to": {"startLines": "39,40,41,42,43,44,45,160", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3559,3657,3759,3859,3958,4060,4169,14300", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "3652,3754,3854,3953,4055,4164,4281,14396"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8ba171b74415ef9d04b1f3a055c632e8\\transformed\\play-services-ads-24.2.0\\res\\values-ro\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,244,290,358,423,491,592,660,788,878,999,1049,1099,1213,1294,1339,1432,1467,1501,1561,1643,1685", "endColumns": "44,45,67,64,67,100,67,127,89,120,49,49,113,80,44,92,34,33,59,81,41,55", "endOffsets": "243,289,357,422,490,591,659,787,877,998,1048,1098,1212,1293,1338,1431,1466,1500,1560,1642,1684,1740"}, "to": {"startLines": "134,135,136,137,138,139,140,141,142,143,144,145,146,147,149,150,151,152,153,154,155,161", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12327,12376,12426,12498,12567,12639,12744,12816,12948,13042,13167,13221,13275,13393,13557,13606,13703,13742,13780,13844,13930,14401", "endColumns": "48,49,71,68,71,104,71,131,93,124,53,53,117,84,48,96,38,37,63,85,45,59", "endOffsets": "12371,12421,12493,12562,12634,12739,12811,12943,13037,13162,13216,13270,13388,13473,13601,13698,13737,13775,13839,13925,13971,14456"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ef3205b01dd1e8d62ca95413fbcfcde0\\transformed\\play-services-base-18.5.0\\res\\values-ro\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,453,579,685,832,958,1077,1182,1340,1447,1602,1731,1873,2035,2100,2164", "endColumns": "103,155,125,105,146,125,118,104,157,106,154,128,141,161,64,63,78", "endOffsets": "296,452,578,684,831,957,1076,1181,1339,1446,1601,1730,1872,2034,2099,2163,2242"}, "to": {"startLines": "49,50,51,52,53,54,55,56,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4592,4700,4860,4990,5100,5251,5381,5504,5757,5919,6030,6189,6322,6468,6634,6703,6771", "endColumns": "107,159,129,109,150,129,122,108,161,110,158,132,145,165,68,67,82", "endOffsets": "4695,4855,4985,5095,5246,5376,5499,5608,5914,6025,6184,6317,6463,6629,6698,6766,6849"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1a44ad6b726c152c4fe16ed54c5a8c02\\transformed\\appcompat-1.7.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,228,334,447,531,636,755,840,920,1011,1104,1199,1293,1393,1486,1581,1675,1766,1858,1939,2049,2157,2255,2367,2473,2577,2739,2840", "endColumns": "122,105,112,83,104,118,84,79,90,92,94,93,99,92,94,93,90,91,80,109,107,97,111,105,103,161,100,81", "endOffsets": "223,329,442,526,631,750,835,915,1006,1099,1194,1288,1388,1481,1576,1670,1761,1853,1934,2044,2152,2250,2362,2468,2572,2734,2835,2917"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,156", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "371,494,600,713,797,902,1021,1106,1186,1277,1370,1465,1559,1659,1752,1847,1941,2032,2124,2205,2315,2423,2521,2633,2739,2843,3005,13976", "endColumns": "122,105,112,83,104,118,84,79,90,92,94,93,99,92,94,93,90,91,80,109,107,97,111,105,103,161,100,81", "endOffsets": "489,595,708,792,897,1016,1101,1181,1272,1365,1460,1554,1654,1747,1842,1936,2027,2119,2200,2310,2418,2516,2628,2734,2838,3000,3101,14053"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\464e6a7dcfefd0da870c4050aa381b0c\\transformed\\material-1.12.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,321,413,501,588,684,774,875,996,1080,1142,1208,1303,1377,1437,1521,1583,1649,1707,1780,1843,1899,2018,2075,2136,2192,2266,2411,2497,2572,2661,2740,2824,2957,3039,3122,3268,3358,3438,3493,3544,3610,3683,3761,3832,3917,3988,4065,4139,4211,4317,4408,4482,4577,4675,4749,4829,4930,4983,5069,5135,5224,5314,5376,5440,5503,5577,5689,5799,5909,6014,6073,6128,6207,6293,6370", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "12,91,87,86,95,89,100,120,83,61,65,94,73,59,83,61,65,57,72,62,55,118,56,60,55,73,144,85,74,88,78,83,132,81,82,145,89,79,54,50,65,72,77,70,84,70,76,73,71,105,90,73,94,97,73,79,100,52,85,65,88,89,61,63,62,73,111,109,109,104,58,54,78,85,76,78", "endOffsets": "316,408,496,583,679,769,870,991,1075,1137,1203,1298,1372,1432,1516,1578,1644,1702,1775,1838,1894,2013,2070,2131,2187,2261,2406,2492,2567,2656,2735,2819,2952,3034,3117,3263,3353,3433,3488,3539,3605,3678,3756,3827,3912,3983,4060,4134,4206,4312,4403,4477,4572,4670,4744,4824,4925,4978,5064,5130,5219,5309,5371,5435,5498,5572,5684,5794,5904,6009,6068,6123,6202,6288,6365,6444"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,68,69,70,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,148,157,158,159", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3106,3198,3286,3373,3469,4286,4387,4508,6961,7023,7089,7502,7576,7636,7720,7782,7848,7906,7979,8042,8098,8217,8274,8335,8391,8465,8610,8696,8771,8860,8939,9023,9156,9238,9321,9467,9557,9637,9692,9743,9809,9882,9960,10031,10116,10187,10264,10338,10410,10516,10607,10681,10776,10874,10948,11028,11129,11182,11268,11334,11423,11513,11575,11639,11702,11776,11888,11998,12108,12213,12272,13478,14058,14144,14221", "endLines": "6,34,35,36,37,38,46,47,48,68,69,70,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,148,157,158,159", "endColumns": "12,91,87,86,95,89,100,120,83,61,65,94,73,59,83,61,65,57,72,62,55,118,56,60,55,73,144,85,74,88,78,83,132,81,82,145,89,79,54,50,65,72,77,70,84,70,76,73,71,105,90,73,94,97,73,79,100,52,85,65,88,89,61,63,62,73,111,109,109,104,58,54,78,85,76,78", "endOffsets": "366,3193,3281,3368,3464,3554,4382,4503,4587,7018,7084,7179,7571,7631,7715,7777,7843,7901,7974,8037,8093,8212,8269,8330,8386,8460,8605,8691,8766,8855,8934,9018,9151,9233,9316,9462,9552,9632,9687,9738,9804,9877,9955,10026,10111,10182,10259,10333,10405,10511,10602,10676,10771,10869,10943,11023,11124,11177,11263,11329,11418,11508,11570,11634,11697,11771,11883,11993,12103,12208,12267,12322,13552,14139,14216,14295"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\46c7e1c2c774c76fe5e74081b097ef9a\\transformed\\browser-1.8.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,377", "endColumns": "106,101,112,102", "endOffsets": "157,259,372,475"}, "to": {"startLines": "67,71,72,73", "startColumns": "4,4,4,4", "startOffsets": "6854,7184,7286,7399", "endColumns": "106,101,112,102", "endOffsets": "6956,7281,7394,7497"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\aff62db08cff511cc4373673ada3b6cf\\transformed\\play-services-basement-18.5.0\\res\\values-ro\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "139", "endOffsets": "334"}, "to": {"startLines": "57", "startColumns": "4", "startOffsets": "5613", "endColumns": "143", "endOffsets": "5752"}}]}]}