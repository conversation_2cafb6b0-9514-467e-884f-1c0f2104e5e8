package com.mahmoudffyt.gfxbooster;

import android.app.AlertDialog;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.VibrationEffect;
import android.os.Vibrator;
import android.provider.Settings;
import android.util.Log;
import android.view.View;
import android.view.ViewParent;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import java.lang.reflect.Field;
import java.util.ArrayList;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.SeekBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.cardview.widget.CardView;

// Using local DeviceAnalyzer and SettingsManager instead of utils versions

import com.mahmoudffyt.gfxbooster.utils.AdManager;
import com.mahmoudffyt.gfxbooster.utils.AimOverlayManager;
import com.mahmoudffyt.gfxbooster.utils.HeadshotSensitivityDatabase;
import java.util.HashMap;
import java.util.Map;

public class HeadshotToolActivity extends AppCompatActivity {

    // UI Elements
    private ImageView btnBack, energyLinesBg, headshotLogo;
    private TextView headshotTitle, headshotSubtitle, deviceInfoText;
    private CardView generalSection, redDotSection, scope2xSection, scope4xSection, scopeAwmSection;
    private Button applySettingsButton, resetButton, smartAimButton, shareButton, testButton, gfxSelectButton, aimOverlayButton;
    private ProgressBar analyzeProgressBar;

    // Sensitivity Sliders and Values
    private SeekBar generalSensitivitySlider, redDotSensitivitySlider, scope2xSensitivitySlider,
                   scope4xSensitivitySlider, scopeAwmSensitivitySlider, breathHoldSlider, freeCameraSlider;
    private TextView generalSensitivityValue, redDotSensitivityValue, scope2xSensitivityValue,
                    scope4xSensitivityValue, scopeAwmSensitivityValue, breathHoldValue, freeCameraValue;

    // Color Views
    private View redColor, greenColor, blueColor, yellowColor;

    // DPI Radio Group
    private RadioGroup dpiRadioGroup;
    private RadioButton dpi400, dpi800, dpi1600;

    // GFX Level Dialog Elements
    private RadioGroup gfxRadioGroup;
    private RadioButton gfxSmooth, gfxStandard, gfxHigh;
    private Button gfxCancelButton, gfxApplyButton;
    private String selectedGfxLevel = "standard"; // Default standard

    // Constants for settings keys
    private static final String KEY_SELECTED_COLOR = "selectedColor";

    // Selected Color
    private String selectedColor = "#FF5252"; // Default red

    // Utility classes
    private com.mahmoudffyt.gfxbooster.utils.DeviceAnalyzer deviceAnalyzer;
    private com.mahmoudffyt.gfxbooster.utils.SettingsManager settingsManager;
    private com.mahmoudffyt.gfxbooster.utils.PremiumManager premiumManager;
    private com.mahmoudffyt.gfxbooster.utils.OptimalSettingsDatabase optimalSettingsDatabase;
    private GfxSettingsHelper gfxSettingsHelper;
    private Handler handler = new Handler();

    // Device specs and settings
    private Map<String, Object> deviceSpecs;
    private Map<String, Integer> recommendedSettings;
    private boolean isSmartAimEnabled = false;

    // Aim Overlay Manager
    private AimOverlayManager aimOverlayManager;
    private static final int OVERLAY_PERMISSION_REQUEST_CODE = 1234;

    // Ad Manager and containers
    private AdManager adManager;
    private FrameLayout adContainerGeneral, adContainerAwm;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_headshot_tool);

        // Initialize utility classes
        deviceAnalyzer = new com.mahmoudffyt.gfxbooster.utils.DeviceAnalyzer(this);
        settingsManager = new com.mahmoudffyt.gfxbooster.utils.SettingsManager(this);
        premiumManager = new com.mahmoudffyt.gfxbooster.utils.PremiumManager(this);
        gfxSettingsHelper = new GfxSettingsHelper(this);
        aimOverlayManager = new AimOverlayManager(this);
        adManager = AdManager.getInstance(this);

        // Initialize UI elements
        initializeViews();

        // Set up listeners
        setupListeners();

        // Start entrance animations
        startEntranceAnimations();

        // Start button pulse animation
        startButtonPulseAnimation();

        // Start device analysis
        analyzeDevice();

        // Load ads
        loadAds();
    }

    @Override
    protected void onResume() {
        super.onResume();

        // Reload ads when activity resumes
        loadAds();

        // Log current locale
        String currentLocale = getResources().getConfiguration().locale.getLanguage();
        Log.d("HeadshotTool", "onResume called, current locale: " + currentLocale);

        // Get saved overlay state
        SharedPreferences prefs = getSharedPreferences("aim_overlay_prefs", Context.MODE_PRIVATE);
        boolean savedOverlayEnabled = prefs.getBoolean("overlay_enabled", false);
        boolean savedOverlayActive = prefs.getBoolean("overlay_active", false);

        // Get current button state
        boolean buttonShowsActive = false;
        try {
            buttonShowsActive = aimOverlayButton != null &&
                aimOverlayButton.getBackground().getConstantState().equals(
                    getResources().getDrawable(R.drawable.neon_button_active_bg, getTheme()).getConstantState());
        } catch (Exception e) {
            Log.e("HeadshotTool", "Error checking button state: " + e.getMessage());
        }

        // Get current overlay state
        boolean overlayIsShowing = aimOverlayManager != null && aimOverlayManager.isShowing();

        Log.d("HeadshotTool", "onResume state check: Button active=" + buttonShowsActive +
              ", Overlay showing=" + overlayIsShowing +
              ", Saved enabled=" + savedOverlayEnabled +
              ", Saved active=" + savedOverlayActive);

        // Handle inconsistent states
        if (overlayIsShowing != buttonShowsActive ||
            overlayIsShowing != savedOverlayEnabled ||
            overlayIsShowing != savedOverlayActive) {

            Log.d("HeadshotTool", "Inconsistent overlay state detected in onResume, fixing");

            // If the overlay is actually showing, make sure everything reflects that
            if (overlayIsShowing) {
                // Update button and saved state
                aimOverlayButton.setBackgroundResource(R.drawable.neon_button_active_bg);
                prefs.edit()
                    .putBoolean("overlay_enabled", true)
                    .putBoolean("overlay_active", true)
                    .apply();

                // For Arabic locale, refresh the overlay to ensure it's visible
                if ("ar".equals(currentLocale)) {
                    Log.d("HeadshotTool", "Arabic locale detected in onResume, refreshing overlay");
                    handler.postDelayed(() -> {
                        try {
                            if (!isFinishing() && aimOverlayManager != null && aimOverlayManager.isShowing()) {
                                aimOverlayManager.refreshOverlay();
                            }
                        } catch (Exception e) {
                            Log.e("HeadshotTool", "Error refreshing overlay: " + e.getMessage());
                        }
                    }, 300); // Short delay to ensure UI is ready
                }
            }
            // If the overlay is not showing but something thinks it is, reset everything
            else {
                Log.d("HeadshotTool", "Overlay not showing but state indicates it should be, resetting");

                // Reset everything
                resetOverlayCompletely();

                // Update button
                aimOverlayButton.setBackgroundResource(R.drawable.neon_button_bg);
            }
        }

        // Check for stale overlay after long periods
        checkForStaleOverlay();

        // For Arabic locale, add a special check to ensure we can close the overlay
        if ("ar".equals(currentLocale)) {
            Log.d("HeadshotTool", "Arabic locale detected, adding special overlay check");

            // Add a delayed check to ensure the overlay can be closed
            handler.postDelayed(() -> {
                // If we're in an inconsistent state, try to fix it
                try {
                    if (isFinishing()) return; // Don't proceed if activity is finishing

                    boolean delayedOverlayShowing = aimOverlayManager != null && aimOverlayManager.isShowing();
                    boolean delayedButtonActive = aimOverlayButton != null &&
                        aimOverlayButton.getBackground() != null &&
                        aimOverlayButton.getBackground().getConstantState() != null &&
                        getResources().getDrawable(R.drawable.neon_button_active_bg, getTheme()) != null &&
                        aimOverlayButton.getBackground().getConstantState().equals(
                            getResources().getDrawable(R.drawable.neon_button_active_bg, getTheme()).getConstantState());

                    if (delayedOverlayShowing != delayedButtonActive) {
                        Log.d("HeadshotTool", "Delayed check found inconsistent state, fixing");

                        if (delayedOverlayShowing) {
                            aimOverlayButton.setBackgroundResource(R.drawable.neon_button_active_bg);
                        } else {
                            aimOverlayButton.setBackgroundResource(R.drawable.neon_button_bg);
                        }
                    }
                } catch (Exception e) {
                    Log.e("HeadshotTool", "Error in delayed overlay check: " + e.getMessage());
                }
            }, 1000); // 1 second delay
        }
    }

    /**
     * Load ads in the activity
     */
    private void loadAds() {
        try {
            // Skip if premium user
            if (premiumManager.isPremium()) {
                if (adContainerGeneral != null) {
                    adContainerGeneral.setVisibility(View.GONE);
                }
                if (adContainerAwm != null) {
                    adContainerAwm.setVisibility(View.GONE);
                }
                return;
            }

            // Load banner ads only if they're not already loaded
            if (adContainerGeneral != null) {
                adContainerGeneral.setVisibility(View.VISIBLE);
                if (adContainerGeneral.getChildCount() == 0) {
                    adManager.loadLargeBannerAd(this, adContainerGeneral, AdManager.BANNER_AD_UNIT_ID);
                }
            }

            if (adContainerAwm != null) {
                adContainerAwm.setVisibility(View.VISIBLE);
                if (adContainerAwm.getChildCount() == 0) {
                    adManager.loadLargeBannerAd(this, adContainerAwm, AdManager.BANNER_AD_UNIT_ID);
                }
            }

            // Preload interstitial ad for Test Aim button if not already loaded
            if (adManager != null && !adManager.isInterstitialAdLoaded()) {
                adManager.preloadInterstitialAd(this);
            }

            // Preload rewarded ad for Suggest Best Settings button if not already loaded
            if (adManager != null && !adManager.isRewardedAdLoaded()) {
                adManager.preloadRewardedAd(this);
            }
        } catch (Exception e) {
            Log.e("HeadshotTool", "Error loading ads: " + e.getMessage());
        }
    }

    private void initializeViews() {
        // Basic UI elements
        btnBack = findViewById(R.id.btn_back);
        energyLinesBg = findViewById(R.id.energy_lines_bg);
        // headshotLogo is not used in the current layout
        // headshotLogo = findViewById(R.id.headshot_logo);
        headshotTitle = findViewById(R.id.headshot_title);
        headshotSubtitle = findViewById(R.id.headshot_subtitle);
        deviceInfoText = findViewById(R.id.device_info_text);
        analyzeProgressBar = findViewById(R.id.analyze_progress_bar);

        // Sections
        generalSection = findViewById(R.id.general_section);
       // redDotSection = findViewById(R.id.red_dot_section);
        scope2xSection = findViewById(R.id.scope_2x_section);
        scope4xSection = findViewById(R.id.scope_4x_section);
        scopeAwmSection = findViewById(R.id.scope_awm_section);

        // Buttons
        applySettingsButton = findViewById(R.id.apply_settings_button);
        // resetButton = findViewById(R.id.reset_button); // Removed
        // smartAimButton = findViewById(R.id.smart_aim_button); // Removed
        shareButton = findViewById(R.id.share_button);
        testButton = findViewById(R.id.test_button);
        gfxSelectButton = findViewById(R.id.gfx_select_button);
        aimOverlayButton = findViewById(R.id.aim_overlay_button);

        // Sensitivity sliders
        generalSensitivitySlider = findViewById(R.id.general_sensitivity_slider);
        // redDotSensitivitySlider = findViewById(R.id.red_dot_sensitivity_slider); // Removed
        scope2xSensitivitySlider = findViewById(R.id.scope_2x_sensitivity_slider);
        scope4xSensitivitySlider = findViewById(R.id.scope_4x_sensitivity_slider);
        scopeAwmSensitivitySlider = findViewById(R.id.scope_awm_sensitivity_slider);
        // breathHoldSlider = findViewById(R.id.breath_hold_slider); // Removed
        freeCameraSlider = findViewById(R.id.free_camera_sensitivity_slider);

        // Sensitivity values
        generalSensitivityValue = findViewById(R.id.general_sensitivity_value);
        // redDotSensitivityValue = findViewById(R.id.red_dot_sensitivity_value); // Removed
        scope2xSensitivityValue = findViewById(R.id.scope_2x_sensitivity_value);
        scope4xSensitivityValue = findViewById(R.id.scope_4x_sensitivity_value);
        scopeAwmSensitivityValue = findViewById(R.id.scope_awm_sensitivity_value);
        // breathHoldValue = findViewById(R.id.breath_hold_value); // Removed
        freeCameraValue = findViewById(R.id.free_camera_sensitivity_value);

        // Color buttons - Removed
        // redColor = findViewById(R.id.red_color);
        // greenColor = findViewById(R.id.green_color);
        // blueColor = findViewById(R.id.blue_color);
        // yellowColor = findViewById(R.id.yellow_color);

        // DPI radio group
        dpiRadioGroup = findViewById(R.id.dpi_radio_group);
        dpi400 = findViewById(R.id.dpi_400);
        dpi800 = findViewById(R.id.dpi_800);
        dpi1600 = findViewById(R.id.dpi_1600);

        // Ad containers
        adContainerGeneral = findViewById(R.id.ad_container_general);
        adContainerAwm = findViewById(R.id.ad_container_awm);

        // Check if we have saved settings and load them
        if (settingsManager.hasSettings()) {
            loadSavedSettings();
        }

        // Check if aim overlay was previously enabled
        SharedPreferences prefs = getSharedPreferences("aim_overlay_prefs", Context.MODE_PRIVATE);
        boolean wasOverlayEnabled = prefs.getBoolean("overlay_enabled", false);
        boolean wasOverlayActive = prefs.getBoolean("overlay_active", false);

        // Use either value (for backward compatibility)
        boolean shouldShowOverlay = wasOverlayEnabled || wasOverlayActive;

        // Update aim overlay button appearance if it was previously enabled
        if (shouldShowOverlay) {
            aimOverlayButton.setBackgroundResource(R.drawable.neon_button_active_bg);

            // If we have overlay permission, restore the overlay
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M &&
                Settings.canDrawOverlays(this) &&
                !aimOverlayManager.isShowing()) {

                // Show the overlay
                aimOverlayManager.showOverlay();
                Log.d("HeadshotTool", "Restored aim overlay on startup");
            }
        }
    }

    private void setupListeners() {
        // Back button
        btnBack.setOnClickListener(v -> {
            vibrateDevice(20);
            finish();
        });

        // Apply settings button (Suggest Best Settings)
        applySettingsButton.setOnClickListener(v -> {
            vibrateDevice(50);
            Animation buttonPress = AnimationUtils.loadAnimation(this, R.anim.button_press);
            v.startAnimation(buttonPress);

            // Show rewarded ad before suggesting best settings
            if (!premiumManager.isPremium() && adManager != null) {
                adManager.showRewardedAd(this, new AdManager.RewardedAdCallback() {
                    @Override
                    public void onRewarded(boolean success) {
                        // Proceed with suggesting best settings
                        applySettings();
                    }

                    @Override
                    public void onAdClosed() {
                        // Ad was closed without reward, still proceed but with a message
                        Toast.makeText(HeadshotToolActivity.this, R.string.ad_closed_without_reward, Toast.LENGTH_SHORT).show();
                        applySettings();
                    }

                    @Override
                    public void onAdFailed() {
                        // Ad failed to load, proceed anyway
                        applySettings();
                    }
                });
            } else {
                // Premium user or ad manager not available, proceed directly
                applySettings();
            }
        });

        // Share button
        shareButton.setOnClickListener(v -> {
            vibrateDevice(20);
            Animation buttonPress = AnimationUtils.loadAnimation(this, R.anim.button_press);
            v.startAnimation(buttonPress);
            shareSettings();
        });

        // Test button
        testButton.setOnClickListener(v -> {
            vibrateDevice(20);
            Animation buttonPress = AnimationUtils.loadAnimation(this, R.anim.button_press);
            v.startAnimation(buttonPress);

            // Show interstitial ad before test aim
            if (!premiumManager.isPremium() && adManager != null) {
                adManager.showInterstitialAd(this, new AdManager.InterstitialAdCallback() {
                    @Override
                    public void onAdClosed() {
                        // Show test dialog after ad is closed
                        try {
                            showTestDialog();
                        } catch (Exception e) {
                            Log.e("HeadshotTool", "Error showing test dialog: " + e.getMessage());
                            Toast.makeText(HeadshotToolActivity.this, "Error showing test dialog: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                        }
                    }

                    @Override
                    public void onAdFailed() {
                        // Show test dialog if ad fails to load
                        try {
                            showTestDialog();
                        } catch (Exception e) {
                            Log.e("HeadshotTool", "Error showing test dialog: " + e.getMessage());
                            Toast.makeText(HeadshotToolActivity.this, "Error showing test dialog: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                        }
                    }
                });
            } else {
                // Premium user or ad manager not available, proceed directly
                try {
                    showTestDialog();
                } catch (Exception e) {
                    Log.e("HeadshotTool", "Error showing test dialog: " + e.getMessage());
                    Toast.makeText(this, "Error showing test dialog: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                }
            }
        });

        // GFX Select button
        gfxSelectButton.setOnClickListener(v -> {
            vibrateDevice(20);
            Animation buttonPress = AnimationUtils.loadAnimation(this, R.anim.button_press);
            v.startAnimation(buttonPress);
            showGfxSelectionDialog();
        });

        // Aim Overlay button
        aimOverlayButton.setOnClickListener(v -> {
            vibrateDevice(30);
            Animation buttonPress = AnimationUtils.loadAnimation(this, R.anim.button_press);
            v.startAnimation(buttonPress);

            // Check if this is a premium feature
            if (!premiumManager.isFeatureAvailable("aim_overlay")) {
                showPremiumPromotionDialog("aim_overlay");
                return;
            }

            toggleAimOverlay();
        });

        // Set up seekbar listeners
        setupSeekBarListeners();

        // Set up DPI radio group listener
        dpiRadioGroup.setOnCheckedChangeListener((group, checkedId) -> {
            vibrateDevice(20);
            int dpiValue = 800; // Default

            if (checkedId == R.id.dpi_400) {
                dpiValue = 400;
                Toast.makeText(this, R.string.dpi_set_to_400 , Toast.LENGTH_SHORT).show();
            } else if (checkedId == R.id.dpi_800) {
                dpiValue = 800;
                Toast.makeText(this, R.string.dpi_set_to_800 , Toast.LENGTH_SHORT).show();
            } else if (checkedId == R.id.dpi_1600) {
                dpiValue = 1600;
                Toast.makeText(this, R.string.dpi_set_to_1600 , Toast.LENGTH_SHORT).show();
            }

            // Save the DPI setting
            // We need to get current settings and update only the DPI
            int generalSensitivity = generalSensitivitySlider.getProgress();
            int redDotSensitivity = 100; // Default value since slider removed
            int scope2xSensitivity = scope2xSensitivitySlider.getProgress();
            int scope4xSensitivity = scope4xSensitivitySlider.getProgress();
            int scopeAwmSensitivity = scopeAwmSensitivitySlider.getProgress();
            int freeCameraSensitivity = freeCameraSlider.getProgress();

            // Create a map for settings
            Map<String, Integer> dpiSettings = new HashMap<>();
            dpiSettings.put("generalSensitivity", generalSensitivity);
            dpiSettings.put("redDotSensitivity", redDotSensitivity);
            dpiSettings.put("scope2xSensitivity", scope2xSensitivity);
            dpiSettings.put("scope4xSensitivity", scope4xSensitivity);
            dpiSettings.put("scopeAwmSensitivity", scopeAwmSensitivity);
            dpiSettings.put("freeCameraButton", freeCameraSensitivity);
            dpiSettings.put("dpiSetting", dpiValue);

            // Save settings
            settingsManager.saveSettings(dpiSettings);

            // Save string settings
            settingsManager.saveStringSetting("gfxLevel", selectedGfxLevel);
        });
    }

    private void setupSeekBarListeners() {
        // General sensitivity
        generalSensitivitySlider.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                generalSensitivityValue.setText(String.valueOf(progress));
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                vibrateDevice(20);
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                vibrateDevice(20);
            }
        });

        // Red dot sensitivity - Removed
        /*
        redDotSensitivitySlider.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                redDotSensitivityValue.setText(String.valueOf(progress));
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                vibrateDevice(20);
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                vibrateDevice(20);
            }
        });
        */

        // 2x scope sensitivity
        scope2xSensitivitySlider.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                scope2xSensitivityValue.setText(String.valueOf(progress));
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                vibrateDevice(20);
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                vibrateDevice(20);
            }
        });

        // 4x scope sensitivity
        scope4xSensitivitySlider.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                scope4xSensitivityValue.setText(String.valueOf(progress));
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                vibrateDevice(20);
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                vibrateDevice(20);
            }
        });

        // AWM scope sensitivity
        scopeAwmSensitivitySlider.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                scopeAwmSensitivityValue.setText(String.valueOf(progress));
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                vibrateDevice(20);
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                vibrateDevice(20);
            }
        });

        // Breath hold - Removed
        /*
        breathHoldSlider.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                breathHoldValue.setText(String.valueOf(progress));
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                vibrateDevice(20);
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                vibrateDevice(20);
            }
        });
        */

        // Free Camera Button
        freeCameraSlider.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                freeCameraValue.setText(String.valueOf(progress));
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                vibrateDevice(20);
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                vibrateDevice(20);
            }
        });
    }

    // Color button listeners removed

    /**
     * Show reset confirmation dialog
     */
    private void showResetConfirmationDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle(R.string.reset_settings );
        builder.setMessage(R.string.are_you_sure_you_want_to_reset_all_settings_to_default_values);
        builder.setPositiveButton(R.string.reset_23, (dialog, which) -> {
            resetSettings();
        });
        builder.setNegativeButton(R.string.cancel_3 , null);
        builder.show();
    }

    /**
     * Reset all settings to default values
     */
    private void resetSettings() {
        // Reset settings in SettingsManager
        settingsManager.resetSettings();

        // Apply recommended settings from device analysis
        applyRecommendedSettings();

        // Disable Smart Aim
        isSmartAimEnabled = false;
        updateSmartAimButton();

        // Show toast
        Toast.makeText(this, R.string.settings_reset_to_recommended_values , Toast.LENGTH_SHORT).show();
    }

    /**
     * Toggle Smart Aim feature
     */
    private void toggleSmartAim() {
        // Check if Smart Aim is a premium feature and user doesn't have premium
        if (!premiumManager.isFeatureAvailable("smart_aim")) {
            showPremiumPromotionDialog("smart_aim");
            return;
        }

        if (isSmartAimEnabled) {
            // Disable Smart Aim
            isSmartAimEnabled = false;
            settingsManager.disableSmartAim();
            Toast.makeText(this, R.string.smart_aim_disabled_33 , Toast.LENGTH_SHORT).show();
        } else {
            // Enable Smart Aim
            isSmartAimEnabled = true;

            // Get device specs
            deviceSpecs = deviceAnalyzer.getDeviceSpecs();

            // Determine device profile based on specs
            String deviceProfile = HeadshotSensitivityDatabase.determineDeviceProfile(deviceSpecs);

            // Get optimal settings based on device profile and GFX level
            Map<String, Integer> optimalSettings = HeadshotSensitivityDatabase.getOptimalSettings(selectedGfxLevel, deviceProfile);

            // Update UI with optimized settings - these settings are fixed based on the table
            // and should not be adjustable by the user
            generalSensitivitySlider.setProgress(optimalSettings.get("generalSensitivity"));
            generalSensitivitySlider.setEnabled(false); // Disable slider

            // Red dot slider removed
            // redDotSensitivitySlider.setProgress(optimalSettings.get("redDotSensitivity"));
            // redDotSensitivitySlider.setEnabled(false); // Disable slider

            scope2xSensitivitySlider.setProgress(optimalSettings.get("scope2xSensitivity"));
            scope2xSensitivitySlider.setEnabled(false); // Disable slider

            scope4xSensitivitySlider.setProgress(optimalSettings.get("scope4xSensitivity"));
            scope4xSensitivitySlider.setEnabled(false); // Disable slider

            scopeAwmSensitivitySlider.setProgress(optimalSettings.get("scopeAwmSensitivity"));
            scopeAwmSensitivitySlider.setEnabled(false); // Disable slider

            // Set Free Camera Button sensitivity if available
            if (optimalSettings.containsKey("freeCameraButton")) {
                freeCameraSlider.setProgress(optimalSettings.get("freeCameraButton"));
                freeCameraSlider.setEnabled(false); // Disable slider
            }

            // Set DPI based on optimal settings
            int recommendedDpi = optimalSettings.get("dpiSetting");
            if (recommendedDpi <= 460) {
                dpi400.setChecked(true);
            } else if (recommendedDpi >= 600) {
                dpi1600.setChecked(true);
            } else {
                dpi800.setChecked(true);
            }

            // Disable DPI radio buttons
            dpi400.setEnabled(false);
            dpi800.setEnabled(false);
            dpi1600.setEnabled(false);

            // Show toast with device profile info
            String message = getString(R.string.smart_aim_enabled, deviceProfile.replace("_", " ").toUpperCase());
            Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
        }

        // Update button appearance
        updateSmartAimButton();
    }

    /**
     * Show premium promotion dialog
     */
    private void showPremiumPromotionDialog(String featureKey) {
        // Use regular AlertDialog.Builder instead of MaterialAlertDialogBuilder
        androidx.appcompat.app.AlertDialog.Builder builder = new androidx.appcompat.app.AlertDialog.Builder(this);
        View premiumView = getLayoutInflater().inflate(R.layout.dialog_premium_features, null);
        builder.setView(premiumView);

        // Setup buttons
        Button subscribeButton = premiumView.findViewById(R.id.btn_subscribe);
        Button laterButton = premiumView.findViewById(R.id.btn_maybe_later);

        // Get price TextView
        TextView priceTextView = premiumView.findViewById(R.id.premium_price);

        // Update price from Google Play Billing
        MainActivity mainActivity = MainActivity.getInstance();
        if (mainActivity != null && mainActivity.getBillingManager() != null) {
            // Get product details and update price
            mainActivity.getBillingManager().getMonthlySubscriptionDetails(new com.mahmoudffyt.gfxbooster.utils.BillingManager.ProductDetailsCallback() {
                @Override
                public void onProductDetailsReceived(com.android.billingclient.api.ProductDetails productDetails) {
                    if (productDetails != null) {
                        // Get the price from product details
                        String formattedPrice = "";

                        // For subscription products
                        if (productDetails.getSubscriptionOfferDetails() != null &&
                            !productDetails.getSubscriptionOfferDetails().isEmpty()) {

                            com.android.billingclient.api.ProductDetails.SubscriptionOfferDetails offerDetails =
                                productDetails.getSubscriptionOfferDetails().get(0);

                            if (offerDetails.getPricingPhases() != null &&
                                offerDetails.getPricingPhases().getPricingPhaseList() != null &&
                                !offerDetails.getPricingPhases().getPricingPhaseList().isEmpty()) {

                                com.android.billingclient.api.ProductDetails.PricingPhase pricingPhase =
                                    offerDetails.getPricingPhases().getPricingPhaseList().get(0);

                                formattedPrice = pricingPhase.getFormattedPrice();
                            }
                        }

                        // Update UI on main thread
                        final String finalPrice = formattedPrice;
                        runOnUiThread(() -> {
                            if (priceTextView != null && !finalPrice.isEmpty()) {
                                priceTextView.setText(finalPrice);
                            }
                        });
                    }
                }
            });
        }

        // Create dialog
        androidx.appcompat.app.AlertDialog dialog = builder.create();

        // Set button listeners
        subscribeButton.setOnClickListener(v -> {
            // Launch Google Play Billing flow
            if (MainActivity.getInstance() != null) {
                MainActivity.getInstance().launchBillingFlow();
            } else {
                // If MainActivity instance is not available, show error
                Toast.makeText(this, getString(R.string.purchase_failed), Toast.LENGTH_SHORT).show();
            }
            dialog.dismiss();

            // Note: We don't re-trigger the feature here because the premium status
            // will be updated by the BillingManager when the purchase is complete
        });

        laterButton.setOnClickListener(v -> {
            dialog.dismiss();
        });

        // Show dialog
        dialog.show();

        // Make sure dialog doesn't get cut off on smaller screens
        dialog.getWindow().setLayout(android.view.ViewGroup.LayoutParams.MATCH_PARENT, android.view.ViewGroup.LayoutParams.WRAP_CONTENT);
    }

    /**
     * Update Smart Aim button appearance based on state - REMOVED
     */
    private void updateSmartAimButton() {
        // Smart Aim feature has been removed
        // This method is kept for backward compatibility
    }

    /**
     * Share current settings
     */
    private void shareSettings() {
        // Build settings string
        StringBuilder sb = new StringBuilder();
        sb.append(getString(R.string.share_settings_title)).append("\n\n");
        sb.append(getString(R.string.general_sensitivity)).append(": ").append(generalSensitivitySlider.getProgress()).append("\n");
        // Red dot section removed
        // sb.append(getString(R.string.red_dot_sensitivity)).append(": ").append(redDotSensitivitySlider.getProgress()).append("\n");
        sb.append(getString(R.string.scope_2x_sensitivity)).append(": ").append(scope2xSensitivitySlider.getProgress()).append("\n");
        sb.append(getString(R.string.scope_4x_sensitivity)).append(": ").append(scope4xSensitivitySlider.getProgress()).append("\n");
        sb.append(getString(R.string.awm_scope_sensitivity_label)).append(": ").append(scopeAwmSensitivitySlider.getProgress()).append("\n");
        // Breath hold section removed
        // sb.append(getString(R.string.breath_hold)).append(": ").append(breathHoldSlider.getProgress()).append("\n");
        sb.append(getString(R.string.free_camera_sensitivity)).append(": ").append(freeCameraSlider.getProgress()).append("\n");
        sb.append(getString(R.string.dpi_setting)).append(": ").append(dpi400.isChecked() ? "400" : dpi1600.isChecked() ? "1600" : "800").append("\n");
        sb.append(getString(R.string.smart_aim)).append(": ").append(isSmartAimEnabled ? getString(R.string.enable_smart_aim) : getString(R.string.smart_aim_disabled)).append("\n\n");
        sb.append(getString(R.string.app_name));

        // Create share intent
        Intent shareIntent = new Intent(Intent.ACTION_SEND);
        shareIntent.setType("text/plain");
        shareIntent.putExtra(Intent.EXTRA_SUBJECT, getString(R.string.share_settings_title));
        shareIntent.putExtra(Intent.EXTRA_TEXT, sb.toString());

        // Start share activity
        startActivity(Intent.createChooser(shareIntent, getString(R.string.share_settings_via)));
    }

    /**
     * Toggle the aim overlay
     */
    private void toggleAimOverlay() {
        Log.d("HeadshotTool", "Toggle Aim Overlay called");

        // Log current locale
        String currentLocale = getResources().getConfiguration().locale.getLanguage();
        Log.d("HeadshotTool", "Current locale: " + currentLocale);

        // Check if we have the overlay permission
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && !Settings.canDrawOverlays(this)) {
            Log.d("HeadshotTool", "Overlay permission not granted");

            // Show permission dialog
            AlertDialog.Builder builder = new AlertDialog.Builder(this);
            builder.setTitle(R.string.aim_overlay_permission_required);
            builder.setMessage(R.string.aim_overlay_permission_message);
            builder.setPositiveButton(R.string.go_to_settings, (dialog, which) -> {
                // Open settings to allow the permission
                Intent intent = new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                        Uri.parse("package:" + getPackageName()));
                startActivityForResult(intent, OVERLAY_PERMISSION_REQUEST_CODE);
            });
            builder.setNegativeButton(R.string.cancel, null);
            builder.show();
            return;
        }

        Log.d("HeadshotTool", "Overlay permission granted, toggling overlay");

        try {
            // Get current button state
            boolean buttonShowsActive = false;
            try {
                buttonShowsActive = aimOverlayButton.getBackground().getConstantState().equals(
                        getResources().getDrawable(R.drawable.neon_button_active_bg, getTheme()).getConstantState());
            } catch (Exception e) {
                Log.e("HeadshotTool", "Error checking button state: " + e.getMessage());
            }

            Log.d("HeadshotTool", "Button shows active: " + buttonShowsActive +
                  ", Overlay is showing: " + (aimOverlayManager != null && aimOverlayManager.isShowing()));

            // If the overlay is showing or the button shows active, we should try to hide it
            if ((aimOverlayManager != null && aimOverlayManager.isShowing()) || buttonShowsActive) {
                Log.d("HeadshotTool", "Attempting to hide overlay");

                // First try to completely reset the overlay state
                resetOverlayCompletely();

                // Update button appearance
                aimOverlayButton.setBackgroundResource(R.drawable.neon_button_bg);

                // Save overlay state
                SharedPreferences prefs = getSharedPreferences("aim_overlay_prefs", Context.MODE_PRIVATE);
                prefs.edit()
                    .putBoolean("overlay_enabled", false)
                    .putBoolean("overlay_active", false)
                    .apply();

                // Show toast message
                Toast.makeText(this, R.string.aim_overlay_disabled, Toast.LENGTH_SHORT).show();

                return;
            }

            // If we get here, we need to show the overlay
            Log.d("HeadshotTool", "Attempting to show overlay");

            // Make sure we have a fresh overlay manager
            if (aimOverlayManager == null) {
                aimOverlayManager = new AimOverlayManager(this);
            }

            // Show the overlay
            aimOverlayManager.showOverlay();
            boolean isShowing = aimOverlayManager.isShowing();

            // Save overlay state in both places for compatibility
            SharedPreferences prefs = getSharedPreferences("aim_overlay_prefs", Context.MODE_PRIVATE);
            prefs.edit()
                .putBoolean("overlay_enabled", isShowing)
                .putBoolean("overlay_active", isShowing)
                .apply();

            // Show toast message
            if (isShowing) {
                Toast.makeText(this, R.string.aim_overlay_enabled, Toast.LENGTH_SHORT).show();

                // Show instructions dialog
                showAimOverlayInstructions();

                // Update button appearance to show active state
                aimOverlayButton.setBackgroundResource(R.drawable.neon_button_active_bg);

                // Add a close button to the overlay
                addCloseButtonToOverlay();

                // For Arabic locale, schedule a refresh after a short delay to ensure visibility
                if ("ar".equals(currentLocale)) {
                    new Handler().postDelayed(() -> {
                        if (aimOverlayManager != null && aimOverlayManager.isShowing()) {
                            Log.d("HeadshotTool", "Performing delayed refresh for Arabic locale");
                            aimOverlayManager.refreshOverlay();
                        }
                    }, 500); // 500ms delay
                }
            } else {
                Toast.makeText(this, R.string.aim_overlay_disabled, Toast.LENGTH_SHORT).show();
                // Reset button appearance
                aimOverlayButton.setBackgroundResource(R.drawable.neon_button_bg);
            }
        } catch (Exception e) {
            Log.e("HeadshotTool", "Error toggling overlay: " + e.getMessage());
            Toast.makeText(this, getString(R.string.error_message, e.getMessage()), Toast.LENGTH_SHORT).show();

            // In case of error, try to reset everything
            try {
                resetOverlayCompletely();
                aimOverlayButton.setBackgroundResource(R.drawable.neon_button_bg);
            } catch (Exception ex) {
                Log.e("HeadshotTool", "Error resetting overlay: " + ex.getMessage());
            }
        }
    }

    /**
     * Completely reset the overlay state
     */
    private void resetOverlayCompletely() {
        Log.d("HeadshotTool", "Completely resetting overlay state");

        try {
            // First try to hide the overlay if it exists
            if (aimOverlayManager != null) {
                try {
                    aimOverlayManager.hideOverlay();
                } catch (Exception e) {
                    Log.e("HeadshotTool", "Error hiding overlay: " + e.getMessage());
                }
            }

            // Kill all overlays forcefully
            killAllOverlays();

            // Create a new overlay manager
            aimOverlayManager = new AimOverlayManager(this);

            // Make sure it's not showing
            if (aimOverlayManager.isShowing()) {
                aimOverlayManager.hideOverlay();
            }

            // Clear all overlay preferences
            SharedPreferences prefs = getSharedPreferences("aim_overlay_prefs", Context.MODE_PRIVATE);
            prefs.edit()
                .putBoolean("overlay_enabled", false)
                .putBoolean("overlay_active", false)
                .apply();

            Log.d("HeadshotTool", "Overlay state completely reset");
        } catch (Exception e) {
            Log.e("HeadshotTool", "Error resetting overlay completely: " + e.getMessage());
        }
    }

    /**
     * Kill all overlays forcefully using reflection
     */
    private void killAllOverlays() {
        Log.d("HeadshotTool", "Forcefully killing all overlays");

        try {
            // Get the WindowManager
            WindowManager windowManager = (WindowManager) getSystemService(Context.WINDOW_SERVICE);

            // Try to use reflection to access the WindowManagerGlobal
            Class<?> wmgClass = Class.forName("android.view.WindowManagerGlobal");
            Object wmgInstance = wmgClass.getMethod("getInstance").invoke(null);

            // Try to get the views field
            Field viewsField = wmgClass.getDeclaredField("mViews");
            viewsField.setAccessible(true);

            // Get the list of views
            ArrayList<View> views = (ArrayList<View>) viewsField.get(wmgInstance);

            // Look for our overlay views
            if (views != null) {
                Log.d("HeadshotTool", "Found " + views.size() + " views in WindowManagerGlobal");

                // Try to find and remove our overlay views
                for (int i = views.size() - 1; i >= 0; i--) {
                    View view = views.get(i);
                    if (view != null) {
                        // Check if this is our overlay view
                        if (view.getTag() != null && "aim_overlay".equals(view.getTag())) {
                            Log.d("HeadshotTool", "Found our overlay view at index " + i);

                            // Try to remove it
                            try {
                                windowManager.removeView(view);
                                Log.d("HeadshotTool", "Successfully removed overlay view with reflection");
                            } catch (Exception e) {
                                Log.e("HeadshotTool", "Error removing view with reflection: " + e.getMessage());
                            }
                        }

                        // Also check by layout ID
                        if (view.getId() == R.id.aim_crosshair) {
                            Log.d("HeadshotTool", "Found view with aim_crosshair ID at index " + i);

                            // Try to remove its parent
                            ViewParent parent = view.getParent();
                            if (parent instanceof View) {
                                try {
                                    windowManager.removeView((View) parent);
                                    Log.d("HeadshotTool", "Successfully removed parent view with reflection");
                                } catch (Exception e) {
                                    Log.e("HeadshotTool", "Error removing parent view with reflection: " + e.getMessage());
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            Log.e("HeadshotTool", "Error killing all overlays: " + e.getMessage());
        }
    }

    /**
     * Add a close button to the overlay
     */
    private void addCloseButtonToOverlay() {
        // This would be implemented if we wanted to add a close button to the overlay
        // For now, we'll just use the app's button to toggle the overlay
    }

    /**
     * Show instructions for using the aim overlay
     */
    private void showAimOverlayInstructions() {
        // Check if we should show instructions (only show once)
        SharedPreferences prefs = getSharedPreferences("aim_overlay_prefs", Context.MODE_PRIVATE);
        if (!prefs.getBoolean("instructions_shown", false)) {
            // Create dialog
            AlertDialog.Builder builder = new AlertDialog.Builder(this);
            builder.setTitle(R.string.aim_crosshair);
            builder.setMessage(R.string.drag_to_move_crosshair);
            builder.setPositiveButton(R.string.ok, null);
            builder.show();

            // Mark instructions as shown
            prefs.edit().putBoolean("instructions_shown", true).apply();
        }
    }

    // onResume method moved to the top of the file

    /**
     * Check for stale overlay after long periods of inactivity
     */
    private void checkForStaleOverlay() {
        // Get the last time we checked the overlay
        SharedPreferences prefs = getSharedPreferences("aim_overlay_prefs", Context.MODE_PRIVATE);
        long lastCheckTime = prefs.getLong("last_check_time", 0);
        long currentTime = System.currentTimeMillis();

        // If it's been more than 30 minutes since the last check
        if (currentTime - lastCheckTime > 30 * 60 * 1000) {
            Log.d("HeadshotTool", "It's been a while since last check, verifying overlay state");

            // Save the current time
            prefs.edit().putLong("last_check_time", currentTime).apply();

            // If the overlay is supposed to be showing
            if (aimOverlayManager != null && aimOverlayManager.isShowing()) {
                // Try to refresh it
                try {
                    aimOverlayManager.refreshOverlay();
                } catch (Exception e) {
                    Log.e("HeadshotTool", "Error refreshing overlay: " + e.getMessage());

                    // If there's an error, recreate the overlay manager
                    try {
                        aimOverlayManager = new AimOverlayManager(this);

                        // Update button state based on saved preferences
                        boolean wasOverlayEnabled = prefs.getBoolean("overlay_enabled", false) ||
                                                   prefs.getBoolean("overlay_active", false);

                        if (wasOverlayEnabled) {
                            // Show the overlay
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M &&
                                Settings.canDrawOverlays(this)) {
                                aimOverlayManager.showOverlay();
                                aimOverlayButton.setBackgroundResource(R.drawable.neon_button_active_bg);
                            }
                        }
                    } catch (Exception ex) {
                        Log.e("HeadshotTool", "Error recreating overlay manager: " + ex.getMessage());
                    }
                }
            }
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == OVERLAY_PERMISSION_REQUEST_CODE) {
            // Check if permission was granted
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && Settings.canDrawOverlays(this)) {
                // Permission granted, toggle the overlay
                toggleAimOverlay();
            } else {
                // Permission denied
                Toast.makeText(this, R.string.aim_overlay_permission_required, Toast.LENGTH_SHORT).show();
            }
        }
    }

    @Override
    protected void onPause() {
        super.onPause();

        // We no longer hide the aim overlay when the activity is paused
        // This allows the overlay to remain visible when switching apps

        // Update button appearance based on overlay state
        if (aimOverlayManager != null) {
            boolean isShowing = aimOverlayManager.isShowing();

            // Update button appearance
            if (aimOverlayButton != null) {
                aimOverlayButton.setBackgroundResource(isShowing ?
                    R.drawable.neon_button_active_bg : R.drawable.neon_button_bg);
            }

            // Save the current state in both places for compatibility
            SharedPreferences prefs = getSharedPreferences("aim_overlay_prefs", Context.MODE_PRIVATE);
            prefs.edit()
                .putBoolean("overlay_enabled", isShowing)
                .putBoolean("overlay_active", isShowing)
                .apply();

            Log.d("HeadshotTool", "Aim overlay state saved on pause: " + isShowing +
                  ", Current locale: " + getResources().getConfiguration().locale.getLanguage());
        }
    }

    @Override
    protected void onDestroy() {
        // We don't hide the overlay when the activity is destroyed
        // This allows the overlay to remain visible when the app is closed
        // The overlay state is saved in the AimOverlayManager

        // Log the current state and save it
        if (aimOverlayManager != null) {
            boolean isShowing = aimOverlayManager.isShowing();

            // Save the current state in both places for compatibility
            SharedPreferences prefs = getSharedPreferences("aim_overlay_prefs", Context.MODE_PRIVATE);
            prefs.edit()
                .putBoolean("overlay_enabled", isShowing)
                .putBoolean("overlay_active", isShowing)
                .apply();

            Log.d("HeadshotTool", "Aim overlay state on destroy: " + isShowing +
                  ", Current locale: " + getResources().getConfiguration().locale.getLanguage());
        }

        // Stop all animations to prevent memory leaks
        if (energyLinesBg != null) {
            energyLinesBg.clearAnimation();
        }

        // Remove all callbacks from handler to prevent memory leaks
        if (handler != null) {
            handler.removeCallbacksAndMessages(null);
        }

        // Dismiss any active dialogs
        dismissAllDialogs();

        super.onDestroy();
    }

    // Helper method to dismiss all active dialogs
    private void dismissAllDialogs() {
        try {
            // Use reflection to get the mManagedDialogs field
            Field field = android.app.Activity.class.getDeclaredField("mManagedDialogs");
            field.setAccessible(true);
            Object dialogsObj = field.get(this);

            if (dialogsObj != null) {
                // Get the actual dialogs
                Map<?, ?> dialogs = (Map<?, ?>) dialogsObj;
                for (Object obj : dialogs.values()) {
                    Field dialogField = obj.getClass().getDeclaredField("mDialog");
                    dialogField.setAccessible(true);
                    android.app.Dialog dialog = (android.app.Dialog) dialogField.get(obj);
                    if (dialog != null && dialog.isShowing()) {
                        dialog.dismiss();
                    }
                }
            }
        } catch (Exception e) {
            // Reflection might fail, but we tried our best
            Log.e("HeadshotTool", "Error dismissing dialogs: " + e.getMessage());
        }
    }

    /**
     * Show test dialog for aim testing
     */
    private void showTestDialog() {
        // Get current sensitivity settings
        final int generalSensitivity = generalSensitivitySlider.getProgress();
        // int redDotSensitivity = redDotSensitivitySlider.getProgress(); // Red Dot Section removed
        final int redDotSensitivity = 100; // Default value since slider removed

        // Get DPI setting with validation
        int dpiValue = 800; // Default value
        try {
            if (dpi400 != null && dpi400.isChecked()) {
                dpiValue = 400;
            } else if (dpi1600 != null && dpi1600.isChecked()) {
                dpiValue = 1600;
            }
        } catch (Exception e) {
            Log.e("HeadshotTool", "Error getting DPI setting: " + e.getMessage());
        }
        final int selectedDpi = dpiValue;

        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        View testView = getLayoutInflater().inflate(R.layout.dialog_aim_test, null);
        builder.setView(testView);

        // Set up test view
        final FrameLayout testArea = testView.findViewById(R.id.test_area);
        final ImageView targetView = testView.findViewById(R.id.target_view);
        final TextView scoreText = testView.findViewById(R.id.score_text);
        final TextView timeText = testView.findViewById(R.id.time_text);
        final TextView sensitivityInfo = testView.findViewById(R.id.sensitivity_info);
        final TextView deviceInfoText = testView.findViewById(R.id.device_info_text);
        final Button closeButton = testView.findViewById(R.id.close_button);

        // Set sensitivity info
        sensitivityInfo.setText(getString(R.string.testing_with_sensitivity, generalSensitivity, selectedDpi));

        // Add device info if available
        if (deviceSpecs != null) {
            String deviceInfo = deviceAnalyzer.getDeviceInfoString();
            deviceInfoText.setText(deviceInfo);
            deviceInfoText.setVisibility(View.VISIBLE);
        }

        // Create dialog
        final AlertDialog dialog = builder.create();

        // Timer variables
        final long[] startTime = {System.currentTimeMillis()};
        final Handler timerHandler = new Handler();
        final Runnable timerRunnable = new Runnable() {
            @Override
            public void run() {
                long millis = System.currentTimeMillis() - startTime[0];
                int seconds = (int) (millis / 1000);
                timeText.setText(getString(R.string.time_seconds, seconds));
                timerHandler.postDelayed(this, 500);
            }
        };

        // Start timer
        timerHandler.postDelayed(timerRunnable, 0);

        // Set up target click listener
        final int[] score = {0};
        targetView.setOnClickListener(v -> {
            try {
                // Increment score
                score[0]++;
                scoreText.setText(getString(R.string.score_value, score[0]));

                // Vibrate
                vibrateDevice(10);

                // Move target to random position with sensitivity applied
                moveTargetWithSensitivity(testArea, targetView, generalSensitivity, selectedDpi);

                // Animate target
                targetView.animate()
                        .scaleX(0.8f)
                        .scaleY(0.8f)
                        .setDuration(100)
                        .withEndAction(() -> {
                            targetView.animate()
                                    .scaleX(1.0f)
                                    .scaleY(1.0f)
                                    .setDuration(100);
                        });
            } catch (Exception e) {
                Log.e("HeadshotTool", "Error in target click: " + e.getMessage());
            }
        });

        // Set up close button
        closeButton.setOnClickListener(v -> {
            // Stop timer
            timerHandler.removeCallbacks(timerRunnable);
            dialog.dismiss();
        });

        // Show dialog
        dialog.show();

        // Wait for layout to be measured, then position target initially
        testArea.post(() -> {
            try {
                // Initial target position
                moveTargetWithSensitivity(testArea, targetView, generalSensitivity, selectedDpi);
            } catch (Exception e) {
                Log.e("HeadshotTool", "Error in initial target positioning: " + e.getMessage());
            }
        });
    }

    /**
     * Move target to a random position within the test area
     * This method is kept for backward compatibility
     */
    private void moveTargetToRandomPosition(FrameLayout testArea, ImageView targetView) {
        // Use the new method with default sensitivity
        moveTargetWithSensitivity(testArea, targetView, 50, 800);
    }

    /**
     * Move target to a random position with sensitivity applied
     *
     * @param testArea The test area frame layout
     * @param targetView The target image view
     * @param sensitivity The sensitivity value (0-100)
     * @param dpi The DPI setting (400, 800, or 1600)
     */
    private void moveTargetWithSensitivity(FrameLayout testArea, ImageView targetView, int sensitivity, int dpi) {
        // Get test area dimensions
        int testAreaWidth = testArea.getWidth();
        int testAreaHeight = testArea.getHeight();
        int targetWidth = targetView.getWidth();
        int targetHeight = targetView.getHeight();

        // Ensure we have valid dimensions
        if (testAreaWidth <= 0 || testAreaHeight <= 0 || targetWidth <= 0 || targetHeight <= 0) {
            return;
        }

        // Calculate maximum position values to keep target within bounds
        int maxX = testAreaWidth - targetWidth;
        int maxY = testAreaHeight - targetHeight;

        // Ensure maxX and maxY are positive
        maxX = Math.max(0, maxX);
        maxY = Math.max(0, maxY);

        // Generate random position within bounds
        float newX = (float) (Math.random() * maxX);
        float newY = (float) (Math.random() * maxY);

        // Calculate movement duration based on sensitivity and DPI
        // Higher sensitivity = faster movement (lower duration)
        // Higher DPI = faster movement (lower duration)

        // Ensure sensitivity is within valid range (0-200)
        int clampedSensitivity = Math.max(0, Math.min(200, sensitivity));

        // Ensure DPI is within valid range (minimum 100)
        int clampedDpi = Math.max(100, dpi);

        // Calculate factors with clamped values
        float sensitivityFactor = (100f - clampedSensitivity) / 100f; // Invert sensitivity to make higher values faster
        sensitivityFactor = Math.max(0.1f, sensitivityFactor); // Ensure minimum factor

        float dpiFactor = 800f / (float)clampedDpi; // Make higher DPI faster
        dpiFactor = Math.max(0.1f, dpiFactor); // Ensure minimum factor

        // Calculate duration with guaranteed minimum (between 50 and 500 milliseconds)
        long duration = Math.max(50, (long) (50 + (sensitivityFactor * dpiFactor * 450)));

        // Move target with animation
        targetView.animate()
                .x(newX)
                .y(newY)
                .setDuration(duration)
                .start();
    }

    private void startButtonPulseAnimation() {
        // Start pulse animation on apply button
        Animation pulseAnimation = AnimationUtils.loadAnimation(this, R.anim.button_pulse);
        applySettingsButton.startAnimation(pulseAnimation);
        // smartAimButton.startAnimation(pulseAnimation); // Smart Aim button removed
    }

    private void startEntranceAnimations() {
        // Load animations
        Animation zoomIn = AnimationUtils.loadAnimation(this, R.anim.zoom_in);
        Animation slideUpFadeIn = AnimationUtils.loadAnimation(this, R.anim.card_fade_in_slide_up);
        Animation pulseAnimation = AnimationUtils.loadAnimation(this, R.anim.pulse_animation);

        // Animate speed lines background
        animateSpeedLines();

        // Apply animations with delays for sequence
        // headshotLogo is not used in the current layout
        // headshotLogo.startAnimation(zoomIn);

        // Delay the title and subtitle animations
        headshotTitle.postDelayed(() -> {
            headshotTitle.setVisibility(View.VISIBLE);
            headshotTitle.startAnimation(slideUpFadeIn);
        }, 200);

        headshotSubtitle.postDelayed(() -> {
            headshotSubtitle.setVisibility(View.VISIBLE);
            headshotSubtitle.startAnimation(slideUpFadeIn);
        }, 300);

        // Delay the sections animations
        generalSection.postDelayed(() -> {
            generalSection.setVisibility(View.VISIBLE);
            generalSection.startAnimation(slideUpFadeIn);
        }, 400);

        // Red Dot Section removed
        // redDotSection.postDelayed(() -> {
        //     redDotSection.setVisibility(View.VISIBLE);
        //     redDotSection.startAnimation(slideUpFadeIn);
        // }, 500);

        scope2xSection.postDelayed(() -> {
            scope2xSection.setVisibility(View.VISIBLE);
            scope2xSection.startAnimation(slideUpFadeIn);
        }, 600);

        scope4xSection.postDelayed(() -> {
            scope4xSection.setVisibility(View.VISIBLE);
            scope4xSection.startAnimation(slideUpFadeIn);
        }, 700);

        scopeAwmSection.postDelayed(() -> {
            scopeAwmSection.setVisibility(View.VISIBLE);
            scopeAwmSection.startAnimation(slideUpFadeIn);
        }, 800);

        // Apply button animation
        applySettingsButton.postDelayed(() -> {
            applySettingsButton.startAnimation(pulseAnimation);
        }, 1000);
    }

    /**
     * Analyze device and generate recommended settings
     */
    private void analyzeDevice() {
        // Check if we already have saved settings
        if (settingsManager.hasSettings()) {
            // Load saved settings directly without analysis
            loadSavedSettings();

            // Update device info text without showing progress bar
            deviceInfoText.setText(deviceAnalyzer.getDeviceInfoString());
            return;
        }

        // Show progress bar only for first-time users
        analyzeProgressBar.setVisibility(View.VISIBLE);

        // Simulate analysis delay
        handler.postDelayed(() -> {
            // Get device specs
            deviceSpecs = deviceAnalyzer.getDeviceSpecs();

            // Get recommended settings
            recommendedSettings = deviceAnalyzer.getRecommendedSettings();

            // Update device info text
            deviceInfoText.setText(deviceAnalyzer.getDeviceInfoString());

            // Apply test settings for debugging
            applyTestSettings();

            // Hide progress bar
            analyzeProgressBar.setVisibility(View.GONE);
        }, 2000); // 2 second delay for visual effect
    }

    /**
     * Apply test settings for debugging
     */
    private void applyTestSettings() {
        Log.d("HeadshotTool", "Applying test settings");

        // These are the settings provided by the user
        generalSensitivitySlider.setProgress(200);
        // redDotSensitivitySlider.setProgress(200); // Red Dot Section removed
        scope2xSensitivitySlider.setProgress(192);
        scope4xSensitivitySlider.setProgress(188);
        scopeAwmSensitivitySlider.setProgress(140);

        // Update text values
        generalSensitivityValue.setText("200");
        // redDotSensitivityValue.setText("200"); // Red Dot Section removed
        scope2xSensitivityValue.setText("192");
        scope4xSensitivityValue.setText("188");
        scopeAwmSensitivityValue.setText("140");

        // Set DPI to 600 (closest is 800)
        dpi800.setChecked(true);

        // Set GFX level to high
        selectedGfxLevel = "high";
        gfxSelectButton.setText("HIGH");

        // Save these settings directly to preferences
        Map<String, Integer> settings = new HashMap<>();
        settings.put("generalSensitivity", 200);
        settings.put("redDotSensitivity", 200); // Default value
        settings.put("scope2xSensitivity", 192);
        settings.put("scope4xSensitivity", 188);
        settings.put("scopeAwmSensitivity", 140);
        settings.put("breathHold", 50); // Default value since slider removed
        settings.put("freeCameraButton", freeCameraSlider.getProgress());
        settings.put("dpiSetting", 800); // Closest to 600

        // Save settings immediately without delay
        settingsManager.saveSettings(settings);
        settingsManager.saveStringSetting("selectedColor", selectedColor);
        settingsManager.saveBooleanSetting("smartAimEnabled", isSmartAimEnabled);
        settingsManager.saveStringSetting("gfxLevel", "high");

        // Save GFX level in our helper
        gfxSettingsHelper.saveGfxLevel("high");

        Log.d("HeadshotTool", "Test settings applied and saved successfully");

        // Show success message
        Toast.makeText(this, "Settings applied successfully!", Toast.LENGTH_SHORT).show();
    }

    /**
     * Apply recommended settings from device analysis
     */
    private void applyRecommendedSettings() {
        // Get device specs
        deviceSpecs = deviceAnalyzer.getDeviceSpecs();

        // Determine device profile based on specs
        String deviceProfile = HeadshotSensitivityDatabase.determineDeviceProfile(deviceSpecs);

        // Get optimal settings based on device profile and GFX level
        Map<String, Integer> optimalSettings = HeadshotSensitivityDatabase.getOptimalSettings(selectedGfxLevel, deviceProfile);

        // Set sliders to recommended values
        generalSensitivitySlider.setProgress(optimalSettings.get("generalSensitivity"));
        // redDotSensitivitySlider.setProgress(optimalSettings.get("redDotSensitivity")); // Red Dot Section removed
        scope2xSensitivitySlider.setProgress(optimalSettings.get("scope2xSensitivity"));
        scope4xSensitivitySlider.setProgress(optimalSettings.get("scope4xSensitivity"));
        scopeAwmSensitivitySlider.setProgress(optimalSettings.get("scopeAwmSensitivity"));

        // Set Free Camera Button sensitivity if available
        if (optimalSettings.containsKey("freeCameraButton")) {
            freeCameraSlider.setProgress(optimalSettings.get("freeCameraButton"));
        }

        // Set DPI radio button
        int recommendedDpi = optimalSettings.get("dpiSetting");
        if (recommendedDpi <= 460) {
            dpi400.setChecked(true);
        } else if (recommendedDpi >= 600) {
            dpi1600.setChecked(true);
        } else {
            dpi800.setChecked(true);
        }

        // Show toast
        Toast.makeText(this, getString(R.string.recommended_settings_applied), Toast.LENGTH_SHORT).show();
    }

    /**
     * Load saved settings from preferences
     */
    private void loadSavedSettings() {
        // Get settings from SettingsManager
        int generalSensitivity = settingsManager.getGeneralSensitivity();
        // int redDotSensitivity = settingsManager.getRedDotSensitivity(); // Removed
        int scope2xSensitivity = settingsManager.getScope2xSensitivity();
        int scope4xSensitivity = settingsManager.getScope4xSensitivity();
        int scopeAwmSensitivity = settingsManager.getScopeAwmSensitivity();
        // int breathHold = settingsManager.getBreathHold(); // Removed
        int freeCameraButton = settingsManager.getFreeCameraButton();
        int dpiSetting = settingsManager.getDpiSetting();

        // Create a map for settings
        Map<String, Integer> settings = new HashMap<>();
        settings.put("generalSensitivity", generalSensitivity);
        // settings.put("redDotSensitivity", redDotSensitivity); // Removed
        settings.put("scope2xSensitivity", scope2xSensitivity);
        settings.put("scope4xSensitivity", scope4xSensitivity);
        settings.put("scopeAwmSensitivity", scopeAwmSensitivity);
        // settings.put("breathHold", breathHold); // Removed
        settings.put("freeCameraButton", freeCameraButton);
        settings.put("dpiSetting", dpiSetting);

        // Set sliders to saved values
        generalSensitivitySlider.setProgress(settings.get("generalSensitivity"));
        // redDotSensitivitySlider.setProgress(settings.get("redDotSensitivity")); // Removed
        scope2xSensitivitySlider.setProgress(settings.get("scope2xSensitivity"));
        scope4xSensitivitySlider.setProgress(settings.get("scope4xSensitivity"));
        scopeAwmSensitivitySlider.setProgress(settings.get("scopeAwmSensitivity"));
        // breathHoldSlider.setProgress(settings.get("breathHold")); // Removed
        freeCameraSlider.setProgress(settings.get("freeCameraButton"));

        // Update text values
        generalSensitivityValue.setText(String.valueOf(settings.get("generalSensitivity")));
        // redDotSensitivityValue.setText(String.valueOf(settings.get("redDotSensitivity"))); // Removed
        scope2xSensitivityValue.setText(String.valueOf(settings.get("scope2xSensitivity")));
        scope4xSensitivityValue.setText(String.valueOf(settings.get("scope4xSensitivity")));
        scopeAwmSensitivityValue.setText(String.valueOf(settings.get("scopeAwmSensitivity")));
        // breathHoldValue.setText(String.valueOf(settings.get("breathHold"))); // Removed
        freeCameraValue.setText(String.valueOf(settings.get("freeCameraButton")));

        // Set DPI radio button
        int dpiSettingValue = settings.get("dpiSetting");
        if (dpiSettingValue == 400) {
            dpi400.setChecked(true);
        } else if (dpiSettingValue == 1600) {
            dpi1600.setChecked(true);
        } else {
            dpi800.setChecked(true);
        }

        // Set selected color
        selectedColor = settingsManager.getSelectedColor();
        // updateSelectedColorButton(); // Removed

        // Set GFX level
        selectedGfxLevel = gfxSettingsHelper.getGfxLevel();
        gfxSelectButton.setText(selectedGfxLevel.toUpperCase());

        // Set Smart Aim state
        isSmartAimEnabled = settingsManager.isSmartAimEnabled();
        updateSmartAimButton();

        // Log settings for debugging
        Log.d("HeadshotTool", "Loaded settings: " + settings.toString());
    }

    /**
     * Update the selected color button highlight - REMOVED
     */
    private void updateSelectedColorButton() {
        // Color buttons have been removed
        // This method is kept for backward compatibility
    }

    /**
     * Suggest best settings based on device analysis
     */
    private void applySettings() {
        // Show a progress dialog
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        View progressView = getLayoutInflater().inflate(R.layout.dialog_progress, null);
        TextView progressText = progressView.findViewById(R.id.progress_text);
        progressText.setText(R.string.suggest_best_settings_description);
        builder.setView(progressView);
        builder.setCancelable(false);
        AlertDialog progressDialog = builder.create();
        progressDialog.show();

        // Add shake animation to apply button
        Animation shakeAnimation = AnimationUtils.loadAnimation(this, R.anim.button_shake);
        applySettingsButton.startAnimation(shakeAnimation);

        // Simulate analysis delay
        handler.postDelayed(() -> {
            try {
                // Get device specs
                deviceSpecs = deviceAnalyzer.getDeviceSpecs();

                // Determine device profile based on specs
                String deviceProfile = HeadshotSensitivityDatabase.determineDeviceProfile(deviceSpecs);

                // Get optimal settings based on device profile and GFX level
                Map<String, Integer> optimalSettings = HeadshotSensitivityDatabase.getOptimalSettings(selectedGfxLevel, deviceProfile);

                // Apply the optimal settings to UI
                generalSensitivitySlider.setProgress(optimalSettings.get("generalSensitivity"));
                scope2xSensitivitySlider.setProgress(optimalSettings.get("scope2xSensitivity"));
                scope4xSensitivitySlider.setProgress(optimalSettings.get("scope4xSensitivity"));
                scopeAwmSensitivitySlider.setProgress(optimalSettings.get("scopeAwmSensitivity"));

                // Set Free Camera Button sensitivity if available
                if (optimalSettings.containsKey("freeCameraButton")) {
                    freeCameraSlider.setProgress(optimalSettings.get("freeCameraButton"));
                }

                // Set DPI based on optimal settings
                int recommendedDpi = optimalSettings.get("dpiSetting");
                if (recommendedDpi <= 460) {
                    dpi400.setChecked(true);
                } else if (recommendedDpi >= 1200) {
                    dpi1600.setChecked(true);
                } else {
                    dpi800.setChecked(true);
                }

                // Create settings map with all values
                Map<String, Integer> allSettings = new HashMap<>();
                allSettings.put("generalSensitivity", optimalSettings.get("generalSensitivity"));
                allSettings.put("redDotSensitivity", optimalSettings.get("redDotSensitivity"));
                allSettings.put("scope2xSensitivity", optimalSettings.get("scope2xSensitivity"));
                allSettings.put("scope4xSensitivity", optimalSettings.get("scope4xSensitivity"));
                allSettings.put("scopeAwmSensitivity", optimalSettings.get("scopeAwmSensitivity"));
                allSettings.put("breathHold", 50); // Default value
                allSettings.put("freeCameraButton", optimalSettings.getOrDefault("freeCameraButton", 50));
                allSettings.put("dpiSetting", recommendedDpi);

                // Save settings immediately
                settingsManager.saveSettings(allSettings);

                // Save string and boolean settings
                settingsManager.saveStringSetting("selectedColor", "#FF5252"); // Default red
                settingsManager.saveBooleanSetting("smartAimEnabled", false);
                settingsManager.saveStringSetting("gfxLevel", selectedGfxLevel);

                // Save GFX level in our helper
                gfxSettingsHelper.saveGfxLevel(selectedGfxLevel);

                Log.d("HeadshotTool", "Optimal settings suggested and saved: " + allSettings.toString());

                // Dismiss progress dialog
                progressDialog.dismiss();

                // Show success message
                Toast.makeText(this, R.string.settings_suggested_successfully, Toast.LENGTH_SHORT).show();

                // Show dialog with device profile info
                showOptimalSettingsDialog(deviceProfile);

            } catch (Exception e) {
                Log.e("HeadshotTool", "Error suggesting settings: " + e.getMessage());
                progressDialog.dismiss();
                Toast.makeText(this, "Error suggesting settings: " + e.getMessage(), Toast.LENGTH_SHORT).show();
            }
        }, 1500); // Delay for visual effect
    }

    /**
     * Show dialog with optimal settings information
     */
    private void showOptimalSettingsDialog(String deviceProfile) {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle(R.string.best_settings_for_your_device);

        // Create message with device profile and settings info
        StringBuilder message = new StringBuilder();
        message.append(getString(R.string.device_analysis)).append(": ")
               .append(deviceProfile.replace("_", " ").toUpperCase()).append("\n\n");

        message.append(getString(R.string.general_sensitivity)).append(": ")
               .append(generalSensitivitySlider.getProgress()).append("\n");

        message.append(getString(R.string.scope_2x_sensitivity)).append(": ")
               .append(scope2xSensitivitySlider.getProgress()).append("\n");

        message.append(getString(R.string.scope_4x_sensitivity)).append(": ")
               .append(scope4xSensitivitySlider.getProgress()).append("\n");

        message.append(getString(R.string.awm_scope_sensitivity_label)).append(": ")
               .append(scopeAwmSensitivitySlider.getProgress()).append("\n");

        message.append(getString(R.string.free_camera_sensitivity)).append(": ")
               .append(freeCameraSlider.getProgress()).append("\n");

        message.append(getString(R.string.dpi_setting)).append(": ")
               .append(dpi400.isChecked() ? "400" : dpi1600.isChecked() ? "1600" : "800").append("\n");

        message.append(getString(R.string.graphics_level)).append(": ")
               .append(selectedGfxLevel.toUpperCase());

        builder.setMessage(message.toString());
        builder.setPositiveButton(R.string.ok, null);
        builder.show();
    }

    /**
     * Show GFX selection dialog
     */
    private void showGfxSelectionDialog() {
        // Create dialog
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        View gfxView = getLayoutInflater().inflate(R.layout.gfx_selection_layout, null);
        builder.setView(gfxView);

        // Get dialog elements
        gfxRadioGroup = gfxView.findViewById(R.id.gfx_radio_group);
        gfxSmooth = gfxView.findViewById(R.id.gfx_smooth);
        gfxStandard = gfxView.findViewById(R.id.gfx_standard);
        gfxHigh = gfxView.findViewById(R.id.gfx_high);
        gfxCancelButton = gfxView.findViewById(R.id.gfx_cancel_button);
        gfxApplyButton = gfxView.findViewById(R.id.gfx_apply_button);

        // Set current selection
        if (selectedGfxLevel.equals("smooth")) {
            gfxSmooth.setChecked(true);
        } else if (selectedGfxLevel.equals("high")) {
            gfxHigh.setChecked(true);
        } else {
            gfxStandard.setChecked(true);
        }

        // Create dialog
        AlertDialog dialog = builder.create();

        // Set up cancel button
        gfxCancelButton.setOnClickListener(v -> {
            vibrateDevice(20);
            dialog.dismiss();
        });

        // Set up apply button
        gfxApplyButton.setOnClickListener(v -> {
            vibrateDevice(30);

            // Get selected GFX level
            if (gfxSmooth.isChecked()) {
                selectedGfxLevel = "smooth";
            } else if (gfxHigh.isChecked()) {
                selectedGfxLevel = "high";
            } else {
                selectedGfxLevel = "standard";
            }

            // Update button text
            gfxSelectButton.setText(selectedGfxLevel.toUpperCase());

            // Update device info
            deviceInfoText.setText(deviceAnalyzer.getDeviceInfoString());

            // If Smart Aim is enabled, update settings based on new GFX level
            if (isSmartAimEnabled) {
                // Get device specs
                deviceSpecs = deviceAnalyzer.getDeviceSpecs();

                // Determine device profile based on specs
                String deviceProfile = HeadshotSensitivityDatabase.determineDeviceProfile(deviceSpecs);

                // Get optimal settings based on device profile and new GFX level
                Map<String, Integer> optimalSettings = HeadshotSensitivityDatabase.getOptimalSettings(selectedGfxLevel, deviceProfile);

                // Update UI with optimized settings
                generalSensitivitySlider.setProgress(optimalSettings.get("generalSensitivity"));
                // redDotSensitivitySlider.setProgress(optimalSettings.get("redDotSensitivity")); // Red Dot Section removed
                scope2xSensitivitySlider.setProgress(optimalSettings.get("scope2xSensitivity"));
                scope4xSensitivitySlider.setProgress(optimalSettings.get("scope4xSensitivity"));
                scopeAwmSensitivitySlider.setProgress(optimalSettings.get("scopeAwmSensitivity"));

                // Set DPI based on optimal settings
                int recommendedDpi = optimalSettings.get("dpiSetting");
                if (recommendedDpi <= 460) {
                    dpi400.setChecked(true);
                } else if (recommendedDpi >= 600) {
                    dpi1600.setChecked(true);
                } else {
                    dpi800.setChecked(true);
                }
            }

            // Show toast
            Toast.makeText(this, getString(R.string.graphics_level_set_to) + selectedGfxLevel.toUpperCase(), Toast.LENGTH_SHORT).show();

            // Dismiss dialog
            dialog.dismiss();
        });

        // Show dialog
        dialog.show();
    }

    private void animateSpeedLines() {
        // Create infinite animation for speed lines
        ObjectAnimator translateX = ObjectAnimator.ofFloat(energyLinesBg, "translationX", -200f, 200f);
        translateX.setDuration(8000);
        translateX.setRepeatCount(ObjectAnimator.INFINITE);
        translateX.setRepeatMode(ObjectAnimator.REVERSE);

        ObjectAnimator translateY = ObjectAnimator.ofFloat(energyLinesBg, "translationY", -100f, 100f);
        translateY.setDuration(6000);
        translateY.setRepeatCount(ObjectAnimator.INFINITE);
        translateY.setRepeatMode(ObjectAnimator.REVERSE);

        AnimatorSet animatorSet = new AnimatorSet();
        animatorSet.playTogether(translateX, translateY);
        animatorSet.start();
    }

    private void vibrateDevice(long milliseconds) {
        try {
            Vibrator vibrator = (Vibrator) getSystemService(Context.VIBRATOR_SERVICE);
            if (vibrator != null && vibrator.hasVibrator()) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    vibrator.vibrate(VibrationEffect.createOneShot(milliseconds, VibrationEffect.DEFAULT_AMPLITUDE));
                } else {
                    // Deprecated in API 26
                    vibrator.vibrate(milliseconds);
                }
            }
        } catch (SecurityException e) {
            // Permission denied or not available
            // Just continue without vibration
            Log.e("HeadshotTool", "Vibration permission denied: " + e.getMessage());
        } catch (Exception e) {
            // Any other exception
            Log.e("HeadshotTool", "Error vibrating device: " + e.getMessage());
        }
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);

        // Save sensitivity values
        if (generalSensitivitySlider != null) {
            outState.putInt("generalSensitivity", generalSensitivitySlider.getProgress());
        }
        if (scope2xSensitivitySlider != null) {
            outState.putInt("scope2xSensitivity", scope2xSensitivitySlider.getProgress());
        }
        if (scope4xSensitivitySlider != null) {
            outState.putInt("scope4xSensitivity", scope4xSensitivitySlider.getProgress());
        }
        if (scopeAwmSensitivitySlider != null) {
            outState.putInt("scopeAwmSensitivity", scopeAwmSensitivitySlider.getProgress());
        }
        if (freeCameraSlider != null) {
            outState.putInt("freeCameraSensitivity", freeCameraSlider.getProgress());
        }

        // Save DPI selection
        if (dpiRadioGroup != null) {
            outState.putInt("selectedDpi", dpiRadioGroup.getCheckedRadioButtonId());
        }

        // Save GFX level
        outState.putString("selectedGfxLevel", selectedGfxLevel);

        // Save overlay state
        if (aimOverlayManager != null) {
            outState.putBoolean("overlayShowing", aimOverlayManager.isShowing());
        }
    }

    @Override
    protected void onRestoreInstanceState(Bundle savedInstanceState) {
        super.onRestoreInstanceState(savedInstanceState);

        if (savedInstanceState != null) {
            // Restore sensitivity values
            if (generalSensitivitySlider != null && savedInstanceState.containsKey("generalSensitivity")) {
                generalSensitivitySlider.setProgress(savedInstanceState.getInt("generalSensitivity"));
                if (generalSensitivityValue != null) {
                    generalSensitivityValue.setText(String.valueOf(generalSensitivitySlider.getProgress()));
                }
            }

            if (scope2xSensitivitySlider != null && savedInstanceState.containsKey("scope2xSensitivity")) {
                scope2xSensitivitySlider.setProgress(savedInstanceState.getInt("scope2xSensitivity"));
                if (scope2xSensitivityValue != null) {
                    scope2xSensitivityValue.setText(String.valueOf(scope2xSensitivitySlider.getProgress()));
                }
            }

            if (scope4xSensitivitySlider != null && savedInstanceState.containsKey("scope4xSensitivity")) {
                scope4xSensitivitySlider.setProgress(savedInstanceState.getInt("scope4xSensitivity"));
                if (scope4xSensitivityValue != null) {
                    scope4xSensitivityValue.setText(String.valueOf(scope4xSensitivitySlider.getProgress()));
                }
            }

            if (scopeAwmSensitivitySlider != null && savedInstanceState.containsKey("scopeAwmSensitivity")) {
                scopeAwmSensitivitySlider.setProgress(savedInstanceState.getInt("scopeAwmSensitivity"));
                if (scopeAwmSensitivityValue != null) {
                    scopeAwmSensitivityValue.setText(String.valueOf(scopeAwmSensitivitySlider.getProgress()));
                }
            }

            if (freeCameraSlider != null && savedInstanceState.containsKey("freeCameraSensitivity")) {
                freeCameraSlider.setProgress(savedInstanceState.getInt("freeCameraSensitivity"));
                if (freeCameraValue != null) {
                    freeCameraValue.setText(String.valueOf(freeCameraSlider.getProgress()));
                }
            }

            // Restore DPI selection
            if (dpiRadioGroup != null && savedInstanceState.containsKey("selectedDpi")) {
                dpiRadioGroup.check(savedInstanceState.getInt("selectedDpi"));
            }

            // Restore GFX level
            if (savedInstanceState.containsKey("selectedGfxLevel")) {
                selectedGfxLevel = savedInstanceState.getString("selectedGfxLevel");
            }
        }
    }
}
