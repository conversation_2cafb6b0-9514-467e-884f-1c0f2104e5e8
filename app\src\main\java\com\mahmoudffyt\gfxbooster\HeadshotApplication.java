package com.mahmoudffyt.gfxbooster;

import android.app.Application;
import android.os.StrictMode;
import android.util.Log;

import com.mahmoudffyt.gfxbooster.utils.AdManager;
import com.google.firebase.FirebaseApp;
import com.google.firebase.crashlytics.FirebaseCrashlytics;

import java.io.File;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Custom Application class for initializing app-wide components
 */
public class HeadshotApplication extends Application {
    private static final String TAG = "HeadshotApplication";
    private ExecutorService executorService;

    @Override
    public void onCreate() {
        super.onCreate();

        // Disable StrictMode for network operations to avoid log spam
        disableStrictModeForNetworkOperations();

        // Handle OPPO device-specific issues
        handleOppoDeviceIssues();

        // Create a single thread executor for background initialization
        executorService = Executors.newSingleThreadExecutor();

        try {
            // Initialize Firebase on main thread (required for proper initialization)
            FirebaseApp.initializeApp(this);

            // Enable Crashlytics on main thread (required for proper initialization)
            FirebaseCrashlytics.getInstance().setCrashlyticsCollectionEnabled(true);

            // Initialize AdMob in background thread
            initializeAdMobInBackground();

            Log.d(TAG, "Firebase, Crashlytics and AdMob initialized successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error initializing app components: " + e.getMessage());
        }
    }

    /**
     * Handle OPPO device-specific issues
     * This prevents errors related to OPPO's debugging functionality
     */
    private void handleOppoDeviceIssues() {
        try {
            // Prevent access to OPPO's log switch file that causes permission errors
            File logSwitchDir = new File("/data/logswitch");
            if (logSwitchDir.exists()) {
                // We can't actually modify this file, but we can prevent crashes
                // by setting system properties that OPPO's InputLog class checks
                System.setProperty("persist.sys.assert.panic", "false");
                System.setProperty("persist.sys.assert.enable", "false");
                System.setProperty("persist.sys.oppo.log.config", "false");
                System.setProperty("persist.sys.disable.inputlog", "true");

                Log.d(TAG, "Applied OPPO device-specific fixes");
            }
        } catch (Exception e) {
            // Just log and continue - this is just a preventative measure
            Log.e(TAG, "Error applying OPPO device-specific fixes: " + e.getMessage());
        }
    }

    /**
     * Initialize AdMob in a background thread to avoid blocking the main thread
     */
    private void initializeAdMobInBackground() {
        executorService.execute(() -> {
            try {
                // Initialize AdMob in background thread
                AdManager.getInstance(getApplicationContext());

                // Pre-check WebView availability in background
                AdManager.getInstance(getApplicationContext()).checkWebViewAvailability(getApplicationContext());
            } catch (Exception e) {
                Log.e(TAG, "Error initializing AdMob: " + e.getMessage());
            }
        });
    }

    /**
     * Disable StrictMode for network operations to avoid log spam
     */
    private void disableStrictModeForNetworkOperations() {
        StrictMode.ThreadPolicy policy = new StrictMode.ThreadPolicy.Builder()
                .permitNetwork()
                .build();
        StrictMode.setThreadPolicy(policy);
    }

    @Override
    public void onTerminate() {
        super.onTerminate();

        // Shutdown executor service
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
    }
}
