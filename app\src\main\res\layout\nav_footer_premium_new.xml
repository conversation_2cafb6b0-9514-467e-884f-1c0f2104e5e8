<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/drawer_background">

    <!-- Premium Header with Glow Effect -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="8dp">

        <!-- Compact Premium Header -->
        <TextView
            android:id="@+id/premium_header_new"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/premium_features"
            android:fontFamily="@font/font_for_ar_en"
            android:textColor="@color/cyber_neon_gold"
            android:textSize="16sp"
            android:textStyle="bold"
            android:gravity="center"
            android:shadowColor="@color/cyber_neon_gold_dim"
            android:shadowDx="0"
            android:shadowDy="0"
            android:shadowRadius="8"
            android:layout_marginBottom="8dp"/>

        <!-- Premium Features Vertical Layout -->
        <LinearLayout
            android:id="@+id/premium_features_container_new"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_margin="15dp"
            android:background="@drawable/premium_glow_bg"
            android:padding="15dp">

            <!-- Each feature in its own row -->
            <!-- Smart Aim -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/premium_feature_smart_aim"
                android:fontFamily="@font/premium_font"
                android:textColor="@color/cyber_neon_gold"
                android:drawableStart="@drawable/ic_check_circle"
                android:drawableTint="@color/cyber_neon_gold"
                android:drawablePadding="4dp"
                android:gravity="center_vertical"
                android:textSize="12sp"
                android:padding="4dp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginVertical="4dp"
                android:background="@color/cyber_neon_gold_dim" />

            <!-- Aim Button -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/premium_feature_aim_button"
                android:fontFamily="@font/premium_font"
                android:textColor="@color/cyber_neon_gold"
                android:drawableStart="@drawable/ic_check_circle"
                android:drawableTint="@color/cyber_neon_gold"
                android:drawablePadding="4dp"
                android:gravity="center_vertical"
                android:textSize="12sp"
                android:padding="4dp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginVertical="4dp"
                android:background="@color/cyber_neon_gold_dim" />

            <!-- No Ads -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/premium_feature_no_ads"
                android:fontFamily="@font/premium_font"
                android:textColor="@color/cyber_neon_gold"
                android:drawableStart="@drawable/ic_check_circle"
                android:drawableTint="@color/cyber_neon_gold"
                android:drawablePadding="4dp"
                android:gravity="center_vertical"
                android:textSize="12sp"
                android:padding="4dp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginVertical="4dp"
                android:background="@color/cyber_neon_gold_dim" />

            <!-- Priority Updates -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/premium_feature_priority_updates"
                android:fontFamily="@font/premium_font"
                android:textColor="@color/cyber_neon_gold"
                android:drawableStart="@drawable/ic_check_circle"
                android:drawableTint="@color/cyber_neon_gold"
                android:drawablePadding="4dp"
                android:gravity="center_vertical"
                android:textSize="12sp"
                android:padding="4dp" />

        </LinearLayout>

        <!-- Compact Subscribe Button -->
        <Button
            android:id="@+id/btn_subscribe_standard"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/subscribe_now"
            android:textSize="14sp"
            android:textStyle="bold"
            android:layout_marginTop="8dp"
            android:layout_margin="15dp"
            android:paddingTop="6dp"
            android:paddingBottom="6dp"
            android:background="@color/cyber_neon_gold"
            android:textColor="@color/black"
            android:fontFamily="@font/font_for_ar_en"/>

        <!-- Hidden Button for Showing Premium Features -->
        <Button
            android:id="@+id/btn_show_premium_features_standard"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/premium_version_benefits"
            android:textSize="12sp"
            android:layout_marginTop="4dp"
            android:paddingTop="4dp"
            android:paddingBottom="4dp"
            android:background="@android:color/transparent"
            android:textColor="@color/cyber_neon_gold"
            android:fontFamily="@font/font_for_ar_en"
            android:visibility="gone"/>
    </LinearLayout>
</FrameLayout>
