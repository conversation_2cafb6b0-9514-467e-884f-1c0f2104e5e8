<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/nav_header_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:theme="@style/ThemeOverlay.App.NavigationDrawer">

    <!-- App Header (Reduced Height) -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="110dp"
        android:background="@drawable/cyber_neon_gradient_bg"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="12dp">

        <ImageView
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:contentDescription="@string/app_name"
            android:src="@drawable/headshot_logo" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginStart="12dp">

            <TextView
                android:id="@+id/nav_app_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/font_for_ar_en"
                android:letterSpacing="0.2"
                android:shadowColor="@color/cyber_neon_cyan"
                android:shadowDx="0"
                android:shadowDy="0"
                android:shadowRadius="10"
                android:text="@string/app_name_welcom_screen"
                android:textColor="@color/cyber_text_primary"
                android:textSize="20sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/game_enhancer"
                android:letterSpacing="0.1"
                android:fontFamily="@font/font_for_ar_en"
                android:textColor="@color/cyber_neon_cyan"
                android:textSize="12sp" />
        </LinearLayout>
    </LinearLayout>

    <!-- Navigation Items will be here -->
</LinearLayout>
