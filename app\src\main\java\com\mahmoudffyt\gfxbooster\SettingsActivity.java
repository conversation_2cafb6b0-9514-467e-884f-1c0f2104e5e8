package com.mahmoudffyt.gfxbooster;

import android.app.AlertDialog;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Bundle;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.app.AppCompatDelegate;
import androidx.appcompat.widget.SwitchCompat;
import androidx.cardview.widget.CardView;

import java.util.Locale;

public class SettingsActivity extends AppCompatActivity {

    private ImageView btnBack;
    private ImageView speedLinesBg, energyGrid;
    private SwitchCompat darkModeSwitch;
    private CardView languageCard;
    private TextView currentLanguage;
    private SharedPreferences preferences;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Set fullscreen
        getWindow().setFlags(
                WindowManager.LayoutParams.FLAG_FULLSCREEN,
                WindowManager.LayoutParams.FLAG_FULLSCREEN
        );

        setContentView(R.layout.activity_settings);

        // Initialize preferences
        preferences = getSharedPreferences("AppSettings", MODE_PRIVATE);

        // Initialize views
        initializeViews();

        // Set up click listeners
        setupClickListeners();

        // Load saved settings
        loadSettings();

        // Start animations
        startAnimations();
    }

    private void initializeViews() {
        btnBack = findViewById(R.id.btn_back);
        speedLinesBg = findViewById(R.id.speed_lines_bg);
        energyGrid = findViewById(R.id.energy_grid);
        darkModeSwitch = findViewById(R.id.dark_mode_switch);
        languageCard = findViewById(R.id.language_card);
        currentLanguage = findViewById(R.id.current_language);
    }

    private void setupClickListeners() {
        btnBack.setOnClickListener(v -> finish());

        darkModeSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
            // Save the dark mode preference
            preferences.edit().putBoolean("dark_mode", isChecked).apply();

            // Apply dark mode
            if (isChecked) {
                AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES);
            } else {
                AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO);
            }
        });

        languageCard.setOnClickListener(v -> showLanguageDialog());
    }

    private void loadSettings() {
        // Load dark mode setting
        boolean isDarkMode = preferences.getBoolean("dark_mode", true);
        darkModeSwitch.setChecked(isDarkMode);

        // Load language setting
        String currentLang = preferences.getString("language", "en");
        if (currentLang.equals("ar")) {
            currentLanguage.setText(R.string.arabic);
        } else {
            currentLanguage.setText(R.string.english);
        }
    }

    private void showLanguageDialog() {
        String[] languages = {getString(R.string.english), getString(R.string.arabic)};

        AlertDialog.Builder builder = new AlertDialog.Builder(this, R.style.AlertDialogTheme);
        builder.setTitle(R.string.select_language);

        // Get current language
        String currentLang = preferences.getString("language", "en");
        int selectedIndex = currentLang.equals("ar") ? 1 : 0;

        builder.setSingleChoiceItems(languages, selectedIndex, (dialog, which) -> {
            dialog.dismiss();

            // Save selected language
            String langCode = (which == 1) ? "ar" : "en";
            if (!langCode.equals(currentLang)) {
                preferences.edit().putString("language", langCode).apply();

                // Show restart dialog
                showRestartDialog(langCode);
            }
        });

        builder.setNegativeButton(R.string.cancel, (dialog, which) -> dialog.dismiss());

        AlertDialog dialog = builder.create();
        dialog.show();
    }

    private void showRestartDialog(String langCode) {
        AlertDialog.Builder builder = new AlertDialog.Builder(this, R.style.AlertDialogTheme);
        builder.setTitle(R.string.language_settings);
        builder.setMessage(R.string.language_change_message);
        builder.setPositiveButton(R.string.restart, (dialog, which) -> {
            setLocale(langCode);
            restartApp();
        });

        builder.setNegativeButton(R.string.cancel, (dialog, which) -> dialog.dismiss());

        AlertDialog dialog = builder.create();
        dialog.show();
    }

    private void setLocale(String langCode) {
        Locale locale = new Locale(langCode);
        Locale.setDefault(locale);

        Resources resources = getResources();
        Configuration config = resources.getConfiguration();
        config.setLocale(locale);

        resources.updateConfiguration(config, resources.getDisplayMetrics());
    }

    private void restartApp() {
        Intent intent = new Intent(this, MainActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
        startActivity(intent);
        finishAffinity();
    }

    private void startAnimations() {
        try {
            // Animate speed lines with floating effect
            if (speedLinesBg != null) {
                Animation floatAnim = AnimationUtils.loadAnimation(this, R.anim.float_animation);
                speedLinesBg.startAnimation(floatAnim);
            }

            // Animate energy grid with subtle scale
            if (energyGrid != null) {
                Animation subtleScale = AnimationUtils.loadAnimation(this, R.anim.subtle_scale);
                energyGrid.startAnimation(subtleScale);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // Helper method to apply the selected language
    public static void applyLanguage(Context context) {
        SharedPreferences preferences = context.getSharedPreferences("AppSettings", MODE_PRIVATE);
        String langCode = preferences.getString("language", "en");

        Locale locale = new Locale(langCode);
        Locale.setDefault(locale);

        Resources resources = context.getResources();
        Configuration config = resources.getConfiguration();
        config.setLocale(locale);

        resources.updateConfiguration(config, resources.getDisplayMetrics());

        // Apply Arabic font for Arabic language
        if (langCode.equals("ar")) {
            // Apply Arabic font styles to the theme
            context.setTheme(R.style.ArabicTextViewStyle);
        }
    }

    // Helper method to apply dark mode
    public static void applyDarkMode(Context context) {
        SharedPreferences preferences = context.getSharedPreferences("AppSettings", MODE_PRIVATE);
        boolean isDarkMode = preferences.getBoolean("dark_mode", true);

        if (isDarkMode) {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES);
        } else {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO);
        }
    }
}
