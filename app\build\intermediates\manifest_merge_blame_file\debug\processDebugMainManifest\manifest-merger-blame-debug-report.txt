1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.mahmoudffyt.gfxbooster"
4    android:versionCode="7"
5    android:versionName="1.1.4" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <!-- Permissions -->
12    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
12-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:6:5-79
12-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
13-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:7:5-77
13-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:7:22-74
14    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
14-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:8:5-84
14-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:8:22-81
15    <uses-permission android:name="android.permission.PACKAGE_USAGE_STATS" />
15-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:9:5-10:47
15-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:9:22-75
16    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
16-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:11:5-77
16-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:11:22-74
17    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
17-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:12:5-87
17-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:12:22-84
18    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
18-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:13:5-81
18-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:13:22-78
19    <uses-permission android:name="android.permission.VIBRATE" />
19-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:14:5-66
19-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:14:22-63
20    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
20-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:15:5-78
20-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:15:22-75
21    <uses-permission android:name="android.permission.INTERNET" />
21-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:16:5-67
21-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:16:22-64
22    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
22-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:17:5-79
22-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:17:22-76
23
24    <!-- Declare foreground service types -->
25    <queries>
25-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:20:5-25:15
26        <intent>
26-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:21:9-24:18
27            <action android:name="android.intent.action.MAIN" />
27-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:22:13-65
27-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:22:21-62
28
29            <category android:name="android.intent.category.GAME" />
29-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:23:13-69
29-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:23:23-66
30        </intent>
31        <intent>
31-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:13:9-15:18
32            <action android:name="com.android.vending.billing.InAppBillingService.BIND" />
32-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:14:13-91
32-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:14:21-88
33        </intent>
34        <intent>
34-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:16:9-18:18
35            <action android:name="com.google.android.apps.play.billingtestcompanion.BillingOverrideService.BIND" />
35-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:17:13-116
35-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:17:21-113
36        </intent> <!-- For browser content -->
37        <intent>
37-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:38:9-44:18
38            <action android:name="android.intent.action.VIEW" />
38-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:39:13-65
38-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:39:21-62
39
40            <category android:name="android.intent.category.BROWSABLE" />
40-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:41:13-74
40-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:41:23-71
41
42            <data android:scheme="https" />
42-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:43:13-44
42-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:43:19-41
43        </intent> <!-- End of browser content -->
44        <!-- For CustomTabsService -->
45        <intent>
45-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:47:9-49:18
46            <action android:name="android.support.customtabs.action.CustomTabsService" />
46-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:48:13-90
46-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:48:21-87
47        </intent> <!-- End of CustomTabsService -->
48        <!-- For MRAID capabilities -->
49        <intent>
49-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:52:9-56:18
50            <action android:name="android.intent.action.INSERT" />
50-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:53:13-67
50-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:53:21-64
51
52            <data android:mimeType="vnd.android.cursor.dir/event" />
52-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:43:13-44
53        </intent>
54        <intent>
54-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:57:9-61:18
55            <action android:name="android.intent.action.VIEW" />
55-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:39:13-65
55-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:39:21-62
56
57            <data android:scheme="sms" />
57-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:43:13-44
57-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:43:19-41
58        </intent>
59        <intent>
59-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:62:9-66:18
60            <action android:name="android.intent.action.DIAL" />
60-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:63:13-65
60-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:63:21-62
61
62            <data android:path="tel:" />
62-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:43:13-44
63        </intent>
64    </queries>
65
66    <uses-permission android:name="com.android.vending.BILLING" />
66-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:10:5-67
66-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:10:22-64
67    <uses-permission android:name="android.permission.WAKE_LOCK" />
67-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:25:5-68
67-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:25:22-65
68    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
68-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:26:5-110
68-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:26:22-107
69    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
69-->[com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5be24e152e5ffa226393bdb33a3aef6\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:27:5-79
69-->[com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5be24e152e5ffa226393bdb33a3aef6\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:27:22-76
70    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
70-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:26:5-88
70-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:26:22-85
71    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
71-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:27:5-82
71-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:27:22-79
72    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" />
72-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:29:5-83
72-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:29:22-80
73
74    <permission
74-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3dd3f063a8dc2168576dc8a6639ef4\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
75        android:name="com.mahmoudffyt.gfxbooster.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
75-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3dd3f063a8dc2168576dc8a6639ef4\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
76        android:protectionLevel="signature" />
76-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3dd3f063a8dc2168576dc8a6639ef4\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
77
78    <uses-permission android:name="com.mahmoudffyt.gfxbooster.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
78-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3dd3f063a8dc2168576dc8a6639ef4\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
78-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3dd3f063a8dc2168576dc8a6639ef4\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
79
80    <application
80-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:27:5-122:19
81        android:name="com.mahmoudffyt.gfxbooster.HeadshotApplication"
81-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:28:9-44
82        android:allowBackup="true"
82-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:29:9-35
83        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
83-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3dd3f063a8dc2168576dc8a6639ef4\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
84        android:dataExtractionRules="@xml/data_extraction_rules"
84-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:30:9-65
85        android:debuggable="true"
86        android:extractNativeLibs="false"
87        android:fullBackupContent="@xml/backup_rules"
87-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:31:9-54
88        android:icon="@mipmap/ic_launcher"
88-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:32:9-43
89        android:label="@string/app_name"
89-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:33:9-41
90        android:roundIcon="@mipmap/ic_launcher_round"
90-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:34:9-54
91        android:supportsRtl="true"
91-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:35:9-35
92        android:testOnly="true"
93        android:theme="@style/Theme.HeadshotSettingsGameBooster" >
93-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:36:9-65
94
95        <!-- AdMob App ID -->
96        <meta-data
96-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:40:9-42:70
97            android:name="com.google.android.gms.ads.APPLICATION_ID"
97-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:41:13-69
98            android:value="ca-app-pub-4043804233099568~1048346990" />
98-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:42:13-67
99
100        <activity
100-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:43:9-46:45
101            android:name="com.mahmoudffyt.gfxbooster.GameBoosterActivity"
101-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:44:13-48
102            android:exported="false"
102-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:45:13-37
103            android:theme="@style/no_bar" />
103-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:46:13-42
104        <activity
104-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:47:9-50:45
105            android:name="com.mahmoudffyt.gfxbooster.MainActivity"
105-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:48:13-41
106            android:exported="false"
106-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:49:13-37
107            android:theme="@style/no_bar" />
107-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:50:13-42
108        <activity
108-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:51:9-60:20
109            android:name="com.mahmoudffyt.gfxbooster.splashscreen"
109-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:52:13-41
110            android:exported="true"
110-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:53:13-36
111            android:theme="@style/no_bar" >
111-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:54:13-42
112            <intent-filter>
112-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:55:13-59:29
113                <action android:name="android.intent.action.MAIN" />
113-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:22:13-65
113-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:22:21-62
114
115                <category android:name="android.intent.category.LAUNCHER" />
115-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:58:17-77
115-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:58:27-74
116            </intent-filter>
117        </activity>
118        <activity
118-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:62:9-65:45
119            android:name="com.mahmoudffyt.gfxbooster.SettingsActivity"
119-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:63:13-45
120            android:exported="false"
120-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:64:13-37
121            android:theme="@style/no_bar" />
121-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:65:13-42
122        <activity
122-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:66:9-69:45
123            android:name="com.mahmoudffyt.gfxbooster.GameModeActivity"
123-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:67:13-45
124            android:exported="false"
124-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:68:13-37
125            android:theme="@style/no_bar" />
125-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:69:13-42
126        <activity
126-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:70:9-73:45
127            android:name="com.mahmoudffyt.gfxbooster.HeadshotToolActivity"
127-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:71:13-49
128            android:exported="false"
128-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:72:13-37
129            android:theme="@style/no_bar" />
129-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:73:13-42
130        <!-- <activity -->
131        <!-- android:name=".AimButtonActivity" -->
132        <!-- android:exported="false" -->
133        <!-- android:theme="@style/no_bar" /> -->
134        <activity
134-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:78:9-81:45
135            android:name="com.mahmoudffyt.gfxbooster.AimOverlaySettingsActivity"
135-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:79:13-55
136            android:exported="false"
136-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:80:13-37
137            android:theme="@style/no_bar" />
137-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:81:13-42
138        <activity
138-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:82:9-85:45
139            android:name="com.mahmoudffyt.gfxbooster.GfxToolsActivity"
139-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:83:13-45
140            android:exported="false"
140-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:84:13-37
141            android:theme="@style/no_bar" />
141-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:85:13-42
142        <activity
142-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:86:9-89:45
143            android:name="com.mahmoudffyt.gfxbooster.AppGuideActivity"
143-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:87:13-45
144            android:exported="false"
144-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:88:13-37
145            android:theme="@style/no_bar" />
145-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:89:13-42
146        <activity
146-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:90:9-93:45
147            android:name="com.mahmoudffyt.gfxbooster.WebViewActivity"
147-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:91:13-44
148            android:exported="false"
148-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:92:13-37
149            android:theme="@style/no_bar" />
149-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:93:13-42
150
151        <service
151-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:96:9-100:56
152            android:name="com.mahmoudffyt.gfxbooster.GameModeService"
152-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:97:13-44
153            android:enabled="true"
153-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:98:13-35
154            android:exported="false"
154-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:99:13-37
155            android:foregroundServiceType="dataSync" />
155-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:100:13-53
156
157        <receiver
157-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:102:9-109:20
158            android:name="com.mahmoudffyt.gfxbooster.GameModeBootReceiver"
158-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:103:13-49
159            android:enabled="true"
159-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:104:13-35
160            android:exported="true" >
160-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:105:13-36
161            <intent-filter>
161-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:106:13-108:29
162                <action android:name="android.intent.action.BOOT_COMPLETED" />
162-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:107:17-79
162-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:107:25-76
163            </intent-filter>
164        </receiver>
165        <receiver
165-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:111:9-121:20
166            android:name="com.mahmoudffyt.gfxbooster.NotificationReceiver"
166-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:112:13-49
167            android:enabled="true"
167-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:113:13-35
168            android:exported="true" >
168-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:114:13-36
169            <intent-filter>
169-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:115:13-120:29
170                <action android:name="com.game.headshot.ACTION_NOON_REMINDER" />
170-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:116:17-81
170-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:116:25-78
171                <action android:name="com.game.headshot.ACTION_EVENING_REMINDER" />
171-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:117:17-84
171-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:117:25-81
172                <action android:name="com.game.headshot.ACTION_HIGH_USAGE" />
172-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:118:17-78
172-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:118:25-75
173                <action android:name="com.game.headshot.ACTION_CHECK_SYSTEM" />
173-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:119:17-80
173-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:119:25-77
174            </intent-filter>
175        </receiver>
176
177        <meta-data
177-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:22:9-24:37
178            android:name="com.google.android.play.billingclient.version"
178-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:23:13-73
179            android:value="7.1.1" />
179-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:24:13-34
180
181        <activity
181-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:26:9-30:75
182            android:name="com.android.billingclient.api.ProxyBillingActivity"
182-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:27:13-78
183            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
183-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:28:13-96
184            android:exported="false"
184-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:29:13-37
185            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
185-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:30:13-72
186        <activity
186-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:31:9-35:75
187            android:name="com.android.billingclient.api.ProxyBillingActivityV2"
187-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:32:13-80
188            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
188-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:33:13-96
189            android:exported="false"
189-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:34:13-37
190            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
190-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:35:13-72
191
192        <service
192-->[com.google.firebase:firebase-analytics-ktx:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ad6a65ba20f74a8e88da6b866e65a53\transformed\firebase-analytics-ktx-22.4.0\AndroidManifest.xml:8:9-14:19
193            android:name="com.google.firebase.components.ComponentDiscoveryService"
193-->[com.google.firebase:firebase-analytics-ktx:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ad6a65ba20f74a8e88da6b866e65a53\transformed\firebase-analytics-ktx-22.4.0\AndroidManifest.xml:9:13-84
194            android:directBootAware="true"
194-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
195            android:exported="false" >
195-->[com.google.firebase:firebase-analytics-ktx:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ad6a65ba20f74a8e88da6b866e65a53\transformed\firebase-analytics-ktx-22.4.0\AndroidManifest.xml:10:13-37
196            <meta-data
196-->[com.google.firebase:firebase-analytics-ktx:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ad6a65ba20f74a8e88da6b866e65a53\transformed\firebase-analytics-ktx-22.4.0\AndroidManifest.xml:11:13-13:85
197                android:name="com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar"
197-->[com.google.firebase:firebase-analytics-ktx:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ad6a65ba20f74a8e88da6b866e65a53\transformed\firebase-analytics-ktx-22.4.0\AndroidManifest.xml:12:17-129
198                android:value="com.google.firebase.components.ComponentRegistrar" />
198-->[com.google.firebase:firebase-analytics-ktx:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ad6a65ba20f74a8e88da6b866e65a53\transformed\firebase-analytics-ktx-22.4.0\AndroidManifest.xml:13:17-82
199            <meta-data
199-->[com.google.firebase:firebase-crashlytics-ktx:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5995498b5b81ac31250b4f1324a21132\transformed\firebase-crashlytics-ktx-19.4.3\AndroidManifest.xml:24:13-26:85
200                android:name="com.google.firebase.components:com.google.firebase.crashlytics.ktx.FirebaseCrashlyticsKtxRegistrar"
200-->[com.google.firebase:firebase-crashlytics-ktx:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5995498b5b81ac31250b4f1324a21132\transformed\firebase-crashlytics-ktx-19.4.3\AndroidManifest.xml:25:17-130
201                android:value="com.google.firebase.components.ComponentRegistrar" />
201-->[com.google.firebase:firebase-crashlytics-ktx:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5995498b5b81ac31250b4f1324a21132\transformed\firebase-crashlytics-ktx-19.4.3\AndroidManifest.xml:26:17-82
202            <meta-data
202-->[com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\046374a346d3cc3e0fb7692fa882950a\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:15:13-17:85
203                android:name="com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar"
203-->[com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\046374a346d3cc3e0fb7692fa882950a\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:16:17-126
204                android:value="com.google.firebase.components.ComponentRegistrar" />
204-->[com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\046374a346d3cc3e0fb7692fa882950a\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:17:17-82
205            <meta-data
205-->[com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\046374a346d3cc3e0fb7692fa882950a\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:18:13-20:85
206                android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar"
206-->[com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\046374a346d3cc3e0fb7692fa882950a\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:19:17-115
207                android:value="com.google.firebase.components.ComponentRegistrar" />
207-->[com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\046374a346d3cc3e0fb7692fa882950a\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:20:17-82
208            <meta-data
208-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:33:13-35:85
209                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
209-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:34:17-139
210                android:value="com.google.firebase.components.ComponentRegistrar" />
210-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:35:17-82
211            <meta-data
211-->[com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a526ba4e24480c7108925ec9a95f4804\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:29:13-31:85
212                android:name="com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar"
212-->[com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a526ba4e24480c7108925ec9a95f4804\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:30:17-117
213                android:value="com.google.firebase.components.ComponentRegistrar" />
213-->[com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a526ba4e24480c7108925ec9a95f4804\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:31:17-82
214            <meta-data
214-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3849af013f032b744f299890f6883a64\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
215                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
215-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3849af013f032b744f299890f6883a64\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
216                android:value="com.google.firebase.components.ComponentRegistrar" />
216-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3849af013f032b744f299890f6883a64\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
217            <meta-data
217-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3849af013f032b744f299890f6883a64\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
218                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
218-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3849af013f032b744f299890f6883a64\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
219                android:value="com.google.firebase.components.ComponentRegistrar" />
219-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3849af013f032b744f299890f6883a64\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
220            <meta-data
220-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9c854e55cb7e484c6bf93dacda33eca\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
221                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
221-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9c854e55cb7e484c6bf93dacda33eca\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
222                android:value="com.google.firebase.components.ComponentRegistrar" />
222-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9c854e55cb7e484c6bf93dacda33eca\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
223            <meta-data
223-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
224                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
224-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
225                android:value="com.google.firebase.components.ComponentRegistrar" />
225-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
226            <meta-data
226-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30632a7c8dd1b017955f6c4482cfc334\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:25:13-27:85
227                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
227-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30632a7c8dd1b017955f6c4482cfc334\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:26:17-115
228                android:value="com.google.firebase.components.ComponentRegistrar" />
228-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30632a7c8dd1b017955f6c4482cfc334\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:27:17-82
229        </service>
230
231        <receiver
231-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:29:9-33:20
232            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
232-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:30:13-85
233            android:enabled="true"
233-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:31:13-35
234            android:exported="false" >
234-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:32:13-37
235        </receiver>
236
237        <service
237-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:35:9-38:40
238            android:name="com.google.android.gms.measurement.AppMeasurementService"
238-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:36:13-84
239            android:enabled="true"
239-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:37:13-35
240            android:exported="false" />
240-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:38:13-37
241        <service
241-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:39:9-43:72
242            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
242-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:40:13-87
243            android:enabled="true"
243-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:41:13-35
244            android:exported="false"
244-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:42:13-37
245            android:permission="android.permission.BIND_JOB_SERVICE" />
245-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:43:13-69
246
247        <activity
247-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef3205b01dd1e8d62ca95413fbcfcde0\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
248            android:name="com.google.android.gms.common.api.GoogleApiActivity"
248-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef3205b01dd1e8d62ca95413fbcfcde0\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
249            android:exported="false"
249-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef3205b01dd1e8d62ca95413fbcfcde0\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
250            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
250-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef3205b01dd1e8d62ca95413fbcfcde0\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
251
252        <service
252-->[com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a526ba4e24480c7108925ec9a95f4804\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:22:9-25:40
253            android:name="com.google.firebase.sessions.SessionLifecycleService"
253-->[com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a526ba4e24480c7108925ec9a95f4804\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:23:13-80
254            android:enabled="true"
254-->[com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a526ba4e24480c7108925ec9a95f4804\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:24:13-35
255            android:exported="false" />
255-->[com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a526ba4e24480c7108925ec9a95f4804\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:25:13-37
256
257        <provider
257-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
258            android:name="com.google.firebase.provider.FirebaseInitProvider"
258-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
259            android:authorities="com.mahmoudffyt.gfxbooster.firebaseinitprovider"
259-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
260            android:directBootAware="true"
260-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
261            android:exported="false"
261-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
262            android:initOrder="100" />
262-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
263
264        <uses-library
264-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6a554b97c6fdafe7f9e475f6f094c65\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
265            android:name="android.ext.adservices"
265-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6a554b97c6fdafe7f9e475f6f094c65\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
266            android:required="false" />
266-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6a554b97c6fdafe7f9e475f6f094c65\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
267
268        <provider
268-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37e33928c78c0b5c37d0e47d6d95412c\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
269            android:name="androidx.startup.InitializationProvider"
269-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37e33928c78c0b5c37d0e47d6d95412c\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
270            android:authorities="com.mahmoudffyt.gfxbooster.androidx-startup"
270-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37e33928c78c0b5c37d0e47d6d95412c\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
271            android:exported="false" >
271-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37e33928c78c0b5c37d0e47d6d95412c\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
272            <meta-data
272-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37e33928c78c0b5c37d0e47d6d95412c\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
273                android:name="androidx.emoji2.text.EmojiCompatInitializer"
273-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37e33928c78c0b5c37d0e47d6d95412c\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
274                android:value="androidx.startup" />
274-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37e33928c78c0b5c37d0e47d6d95412c\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
275            <meta-data
275-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
276                android:name="androidx.work.WorkManagerInitializer"
276-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
277                android:value="androidx.startup" />
277-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
278            <meta-data
278-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\921836e7511c8cb3e70450775705a043\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
279                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
279-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\921836e7511c8cb3e70450775705a043\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
280                android:value="androidx.startup" />
280-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\921836e7511c8cb3e70450775705a043\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
281            <meta-data
281-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
282                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
282-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
283                android:value="androidx.startup" />
283-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
284        </provider> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
285        <activity
285-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:73:9-78:43
286            android:name="com.google.android.gms.ads.AdActivity"
286-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:74:13-65
287            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
287-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:75:13-122
288            android:exported="false"
288-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:76:13-37
289            android:theme="@android:style/Theme.Translucent" />
289-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:77:13-61
290
291        <provider
291-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:80:9-85:43
292            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
292-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:81:13-76
293            android:authorities="com.mahmoudffyt.gfxbooster.mobileadsinitprovider"
293-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:82:13-73
294            android:exported="false"
294-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:83:13-37
295            android:initOrder="100" />
295-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:84:13-36
296
297        <service
297-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:87:9-91:43
298            android:name="com.google.android.gms.ads.AdService"
298-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:88:13-64
299            android:enabled="true"
299-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:89:13-35
300            android:exported="false" />
300-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:90:13-37
301
302        <activity
302-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:93:9-97:43
303            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
303-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:94:13-82
304            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
304-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:95:13-122
305            android:exported="false" />
305-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:96:13-37
306        <activity
306-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:98:9-105:43
307            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
307-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:99:13-82
308            android:excludeFromRecents="true"
308-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:100:13-46
309            android:exported="false"
309-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:101:13-37
310            android:launchMode="singleTask"
310-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:102:13-44
311            android:taskAffinity=""
311-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:103:13-36
312            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
312-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:104:13-72
313
314        <meta-data
314-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:107:9-109:36
315            android:name="com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING"
315-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:108:13-79
316            android:value="true" />
316-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:109:13-33
317        <meta-data
317-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:110:9-112:36
318            android:name="com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION"
318-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:111:13-83
319            android:value="true" />
319-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:112:13-33
320
321        <service
321-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
322            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
322-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
323            android:directBootAware="false"
323-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
324            android:enabled="@bool/enable_system_alarm_service_default"
324-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
325            android:exported="false" />
325-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
326        <service
326-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
327            android:name="androidx.work.impl.background.systemjob.SystemJobService"
327-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
328            android:directBootAware="false"
328-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
329            android:enabled="@bool/enable_system_job_service_default"
329-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
330            android:exported="true"
330-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
331            android:permission="android.permission.BIND_JOB_SERVICE" />
331-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
332        <service
332-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
333            android:name="androidx.work.impl.foreground.SystemForegroundService"
333-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
334            android:directBootAware="false"
334-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
335            android:enabled="@bool/enable_system_foreground_service_default"
335-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
336            android:exported="false" />
336-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
337
338        <receiver
338-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
339            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
339-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
340            android:directBootAware="false"
340-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
341            android:enabled="true"
341-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
342            android:exported="false" />
342-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
343        <receiver
343-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
344            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
344-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
345            android:directBootAware="false"
345-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
346            android:enabled="false"
346-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
347            android:exported="false" >
347-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
348            <intent-filter>
348-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
349                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
349-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
349-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
350                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
350-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
350-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
351            </intent-filter>
352        </receiver>
353        <receiver
353-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
354            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
354-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
355            android:directBootAware="false"
355-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
356            android:enabled="false"
356-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
357            android:exported="false" >
357-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
358            <intent-filter>
358-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
359                <action android:name="android.intent.action.BATTERY_OKAY" />
359-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
359-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
360                <action android:name="android.intent.action.BATTERY_LOW" />
360-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
360-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
361            </intent-filter>
362        </receiver>
363        <receiver
363-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
364            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
364-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
365            android:directBootAware="false"
365-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
366            android:enabled="false"
366-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
367            android:exported="false" >
367-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
368            <intent-filter>
368-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
369                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
369-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
369-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
370                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
370-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
370-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
371            </intent-filter>
372        </receiver>
373        <receiver
373-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
374            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
374-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
375            android:directBootAware="false"
375-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
376            android:enabled="false"
376-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
377            android:exported="false" >
377-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
378            <intent-filter>
378-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
379                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
379-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
379-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
380            </intent-filter>
381        </receiver>
382        <receiver
382-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
383            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
383-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
384            android:directBootAware="false"
384-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
385            android:enabled="false"
385-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
386            android:exported="false" >
386-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
387            <intent-filter>
387-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
388                <action android:name="android.intent.action.BOOT_COMPLETED" />
388-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:107:17-79
388-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:107:25-76
389                <action android:name="android.intent.action.TIME_SET" />
389-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
389-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
390                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
390-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
390-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
391            </intent-filter>
392        </receiver>
393        <receiver
393-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
394            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
394-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
395            android:directBootAware="false"
395-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
396            android:enabled="@bool/enable_system_alarm_service_default"
396-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
397            android:exported="false" >
397-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
398            <intent-filter>
398-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
399                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
399-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
399-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
400            </intent-filter>
401        </receiver>
402        <receiver
402-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
403            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
403-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
404            android:directBootAware="false"
404-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
405            android:enabled="true"
405-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
406            android:exported="true"
406-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
407            android:permission="android.permission.DUMP" >
407-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
408            <intent-filter>
408-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
409                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
409-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
409-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
410            </intent-filter>
411        </receiver>
412
413        <meta-data
413-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aff62db08cff511cc4373673ada3b6cf\transformed\play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
414            android:name="com.google.android.gms.version"
414-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aff62db08cff511cc4373673ada3b6cf\transformed\play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
415            android:value="@integer/google_play_services_version" />
415-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aff62db08cff511cc4373673ada3b6cf\transformed\play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
416
417        <receiver
417-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
418            android:name="androidx.profileinstaller.ProfileInstallReceiver"
418-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
419            android:directBootAware="false"
419-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
420            android:enabled="true"
420-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
421            android:exported="true"
421-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
422            android:permission="android.permission.DUMP" >
422-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
423            <intent-filter>
423-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
424                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
424-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
424-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
425            </intent-filter>
426            <intent-filter>
426-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
427                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
427-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
427-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
428            </intent-filter>
429            <intent-filter>
429-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
430                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
430-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
430-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
431            </intent-filter>
432            <intent-filter>
432-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
433                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
433-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
433-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
434            </intent-filter>
435        </receiver>
436
437        <service
437-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf66c28488d56b085c276c14a4449049\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:26:9-32:19
438            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
438-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf66c28488d56b085c276c14a4449049\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:27:13-103
439            android:exported="false" >
439-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf66c28488d56b085c276c14a4449049\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:28:13-37
440            <meta-data
440-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf66c28488d56b085c276c14a4449049\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:29:13-31:39
441                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
441-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf66c28488d56b085c276c14a4449049\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:30:17-94
442                android:value="cct" />
442-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf66c28488d56b085c276c14a4449049\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:31:17-36
443        </service>
444        <service
444-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18adbb13687610ba2ec263dcbb46b5e9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:24:9-28:19
445            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
445-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18adbb13687610ba2ec263dcbb46b5e9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:25:13-117
446            android:exported="false"
446-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18adbb13687610ba2ec263dcbb46b5e9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:26:13-37
447            android:permission="android.permission.BIND_JOB_SERVICE" >
447-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18adbb13687610ba2ec263dcbb46b5e9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:27:13-69
448        </service>
449
450        <receiver
450-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18adbb13687610ba2ec263dcbb46b5e9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:30:9-32:40
451            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
451-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18adbb13687610ba2ec263dcbb46b5e9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:31:13-132
452            android:exported="false" />
452-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18adbb13687610ba2ec263dcbb46b5e9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:32:13-37
453
454        <service
454-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac9518aeda46c3b802798bcb4651bb33\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
455            android:name="androidx.room.MultiInstanceInvalidationService"
455-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac9518aeda46c3b802798bcb4651bb33\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
456            android:directBootAware="true"
456-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac9518aeda46c3b802798bcb4651bb33\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
457            android:exported="false" />
457-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac9518aeda46c3b802798bcb4651bb33\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
458    </application>
459
460</manifest>
