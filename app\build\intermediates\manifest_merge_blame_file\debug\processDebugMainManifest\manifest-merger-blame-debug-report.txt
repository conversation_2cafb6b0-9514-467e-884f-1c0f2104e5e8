1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.mahmoudffyt.gfxbooster"
4    android:versionCode="7"
5    android:versionName="1.1.4" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <!-- Permissions -->
12    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
12-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:6:5-79
12-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
13-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:7:5-77
13-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:7:22-74
14    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
14-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:8:5-84
14-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:8:22-81
15    <uses-permission android:name="android.permission.PACKAGE_USAGE_STATS" />
15-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:9:5-10:47
15-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:9:22-75
16    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
16-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:11:5-77
16-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:11:22-74
17    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
17-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:12:5-87
17-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:12:22-84
18    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
18-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:13:5-81
18-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:13:22-78
19    <uses-permission android:name="android.permission.VIBRATE" />
19-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:14:5-66
19-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:14:22-63
20    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
20-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:15:5-78
20-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:15:22-75
21    <uses-permission android:name="android.permission.INTERNET" />
21-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:16:5-67
21-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:16:22-64
22    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
22-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:17:5-79
22-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:17:22-76
23
24    <!-- Declare foreground service types -->
25    <queries>
25-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:20:5-48:15
26
27        <!-- Allow querying all launchable apps -->
28        <intent>
28-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:22:9-25:18
29            <action android:name="android.intent.action.MAIN" />
29-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:23:13-65
29-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:23:21-62
30
31            <category android:name="android.intent.category.LAUNCHER" />
31-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:24:13-73
31-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:24:23-70
32        </intent>
33
34        <!-- Popular games packages for Android 14 compatibility -->
35        <package android:name="com.tencent.ig" />
35-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:28:9-50
35-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:28:18-47
36        <package android:name="com.dts.freefireth" />
36-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:29:9-54
36-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:29:18-51
37        <package android:name="com.supercell.clashofclans" />
37-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:30:9-62
37-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:30:18-59
38        <package android:name="com.supercell.clashroyale" />
38-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:31:9-61
38-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:31:18-58
39        <package android:name="com.king.candycrushsaga" />
39-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:32:9-59
39-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:32:18-56
40        <package android:name="com.gameloft.android.ANMP.GloftA8HM" />
40-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:33:9-71
40-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:33:18-68
41        <package android:name="com.mobile.legends" />
41-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:34:9-54
41-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:34:18-51
42        <package android:name="com.garena.game.codm" />
42-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:35:9-56
42-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:35:18-53
43        <package android:name="com.mojang.minecraftpe" />
43-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:36:9-58
43-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:36:18-55
44        <package android:name="com.roblox.client" />
44-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:37:9-53
44-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:37:18-50
45        <package android:name="com.innersloth.spacemafia" />
45-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:38:9-61
45-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:38:18-58
46        <package android:name="com.ea.gp.fifamobile" />
46-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:39:9-56
46-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:39:18-53
47        <package android:name="com.ea.gp.apexlegendsmobilefps" />
47-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:40:9-66
47-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:40:18-63
48        <package android:name="com.miHoYo.GenshinImpact" />
48-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:41:9-60
48-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:41:18-57
49        <package android:name="com.activision.callofduty.shooter" />
49-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:42:9-69
49-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:42:18-66
50        <package android:name="com.epicgames.fortnite" />
50-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:43:9-58
50-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:43:18-55
51        <package android:name="com.netease.lztgglobal" />
51-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:44:9-58
51-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:44:18-55
52        <package android:name="com.netease.mrzhna" />
52-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:45:9-54
52-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:45:18-51
53        <package android:name="com.zynga.words3" />
53-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:46:9-52
53-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:46:18-49
54        <package android:name="com.outfit7.mytalkingtom2" />
54-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:47:9-61
54-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:47:18-58
55
56        <intent>
56-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:13:9-15:18
57            <action android:name="com.android.vending.billing.InAppBillingService.BIND" />
57-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:14:13-91
57-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:14:21-88
58        </intent>
59        <intent>
59-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:16:9-18:18
60            <action android:name="com.google.android.apps.play.billingtestcompanion.BillingOverrideService.BIND" />
60-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:17:13-116
60-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:17:21-113
61        </intent> <!-- For browser content -->
62        <intent>
62-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:38:9-44:18
63            <action android:name="android.intent.action.VIEW" />
63-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:39:13-65
63-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:39:21-62
64
65            <category android:name="android.intent.category.BROWSABLE" />
65-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:41:13-74
65-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:41:23-71
66
67            <data android:scheme="https" />
67-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:43:13-44
67-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:43:19-41
68        </intent> <!-- End of browser content -->
69        <!-- For CustomTabsService -->
70        <intent>
70-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:47:9-49:18
71            <action android:name="android.support.customtabs.action.CustomTabsService" />
71-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:48:13-90
71-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:48:21-87
72        </intent> <!-- End of CustomTabsService -->
73        <!-- For MRAID capabilities -->
74        <intent>
74-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:52:9-56:18
75            <action android:name="android.intent.action.INSERT" />
75-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:53:13-67
75-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:53:21-64
76
77            <data android:mimeType="vnd.android.cursor.dir/event" />
77-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:43:13-44
78        </intent>
79        <intent>
79-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:57:9-61:18
80            <action android:name="android.intent.action.VIEW" />
80-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:39:13-65
80-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:39:21-62
81
82            <data android:scheme="sms" />
82-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:43:13-44
82-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:43:19-41
83        </intent>
84        <intent>
84-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:62:9-66:18
85            <action android:name="android.intent.action.DIAL" />
85-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:63:13-65
85-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:63:21-62
86
87            <data android:path="tel:" />
87-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:43:13-44
88        </intent>
89    </queries>
90
91    <uses-permission android:name="com.android.vending.BILLING" />
91-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:10:5-67
91-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:10:22-64
92    <uses-permission android:name="android.permission.WAKE_LOCK" />
92-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:25:5-68
92-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:25:22-65
93    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
93-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:26:5-110
93-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:26:22-107
94    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
94-->[com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5be24e152e5ffa226393bdb33a3aef6\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:27:5-79
94-->[com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5be24e152e5ffa226393bdb33a3aef6\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:27:22-76
95    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
95-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:26:5-88
95-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:26:22-85
96    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
96-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:27:5-82
96-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:27:22-79
97    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" />
97-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:29:5-83
97-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:29:22-80
98
99    <permission
99-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3dd3f063a8dc2168576dc8a6639ef4\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
100        android:name="com.mahmoudffyt.gfxbooster.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
100-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3dd3f063a8dc2168576dc8a6639ef4\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
101        android:protectionLevel="signature" />
101-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3dd3f063a8dc2168576dc8a6639ef4\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
102
103    <uses-permission android:name="com.mahmoudffyt.gfxbooster.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
103-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3dd3f063a8dc2168576dc8a6639ef4\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
103-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3dd3f063a8dc2168576dc8a6639ef4\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
104
105    <application
105-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:50:5-149:19
106        android:name="com.mahmoudffyt.gfxbooster.HeadshotApplication"
106-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:51:9-44
107        android:allowBackup="true"
107-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:52:9-35
108        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
108-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3dd3f063a8dc2168576dc8a6639ef4\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
109        android:dataExtractionRules="@xml/data_extraction_rules"
109-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:53:9-65
110        android:debuggable="true"
111        android:extractNativeLibs="false"
112        android:fullBackupContent="@xml/backup_rules"
112-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:54:9-54
113        android:icon="@mipmap/ic_launcher"
113-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:55:9-43
114        android:label="@string/app_name"
114-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:56:9-41
115        android:roundIcon="@mipmap/ic_launcher_round"
115-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:57:9-54
116        android:supportsRtl="true"
116-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:58:9-35
117        android:testOnly="true"
118        android:theme="@style/Theme.HeadshotSettingsGameBooster" >
118-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:59:9-65
119
120        <!-- AdMob App ID -->
121        <meta-data
121-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:63:9-65:70
122            android:name="com.google.android.gms.ads.APPLICATION_ID"
122-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:64:13-69
123            android:value="ca-app-pub-4043804233099568~1048346990" />
123-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:65:13-67
124
125        <activity
125-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:66:9-69:45
126            android:name="com.mahmoudffyt.gfxbooster.GameBoosterActivity"
126-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:67:13-48
127            android:exported="false"
127-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:68:13-37
128            android:theme="@style/no_bar" />
128-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:69:13-42
129        <activity
129-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:70:9-73:45
130            android:name="com.mahmoudffyt.gfxbooster.MainActivity"
130-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:71:13-41
131            android:exported="false"
131-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:72:13-37
132            android:theme="@style/no_bar" />
132-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:73:13-42
133        <activity
133-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:74:9-83:20
134            android:name="com.mahmoudffyt.gfxbooster.splashscreen"
134-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:75:13-41
135            android:exported="true"
135-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:76:13-36
136            android:theme="@style/no_bar" >
136-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:77:13-42
137            <intent-filter>
137-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:78:13-82:29
138                <action android:name="android.intent.action.MAIN" />
138-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:23:13-65
138-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:23:21-62
139
140                <category android:name="android.intent.category.LAUNCHER" />
140-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:24:13-73
140-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:24:23-70
141            </intent-filter>
142        </activity>
143        <activity
143-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:85:9-88:45
144            android:name="com.mahmoudffyt.gfxbooster.SettingsActivity"
144-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:86:13-45
145            android:exported="false"
145-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:87:13-37
146            android:theme="@style/no_bar" />
146-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:88:13-42
147        <activity
147-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:89:9-92:45
148            android:name="com.mahmoudffyt.gfxbooster.GameModeActivity"
148-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:90:13-45
149            android:exported="false"
149-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:91:13-37
150            android:theme="@style/no_bar" />
150-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:92:13-42
151        <activity
151-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:93:9-96:45
152            android:name="com.mahmoudffyt.gfxbooster.GameSelectionActivity"
152-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:94:13-50
153            android:exported="false"
153-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:95:13-37
154            android:theme="@style/no_bar" />
154-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:96:13-42
155        <activity
155-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:97:9-100:45
156            android:name="com.mahmoudffyt.gfxbooster.HeadshotToolActivity"
156-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:98:13-49
157            android:exported="false"
157-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:99:13-37
158            android:theme="@style/no_bar" />
158-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:100:13-42
159        <!-- <activity -->
160        <!-- android:name=".AimButtonActivity" -->
161        <!-- android:exported="false" -->
162        <!-- android:theme="@style/no_bar" /> -->
163        <activity
163-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:105:9-108:45
164            android:name="com.mahmoudffyt.gfxbooster.AimOverlaySettingsActivity"
164-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:106:13-55
165            android:exported="false"
165-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:107:13-37
166            android:theme="@style/no_bar" />
166-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:108:13-42
167        <activity
167-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:109:9-112:45
168            android:name="com.mahmoudffyt.gfxbooster.GfxToolsActivity"
168-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:110:13-45
169            android:exported="false"
169-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:111:13-37
170            android:theme="@style/no_bar" />
170-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:112:13-42
171        <activity
171-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:113:9-116:45
172            android:name="com.mahmoudffyt.gfxbooster.AppGuideActivity"
172-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:114:13-45
173            android:exported="false"
173-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:115:13-37
174            android:theme="@style/no_bar" />
174-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:116:13-42
175        <activity
175-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:117:9-120:45
176            android:name="com.mahmoudffyt.gfxbooster.WebViewActivity"
176-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:118:13-44
177            android:exported="false"
177-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:119:13-37
178            android:theme="@style/no_bar" />
178-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:120:13-42
179
180        <service
180-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:123:9-127:56
181            android:name="com.mahmoudffyt.gfxbooster.GameModeService"
181-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:124:13-44
182            android:enabled="true"
182-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:125:13-35
183            android:exported="false"
183-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:126:13-37
184            android:foregroundServiceType="dataSync" />
184-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:127:13-53
185
186        <receiver
186-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:129:9-136:20
187            android:name="com.mahmoudffyt.gfxbooster.GameModeBootReceiver"
187-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:130:13-49
188            android:enabled="true"
188-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:131:13-35
189            android:exported="true" >
189-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:132:13-36
190            <intent-filter>
190-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:133:13-135:29
191                <action android:name="android.intent.action.BOOT_COMPLETED" />
191-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:134:17-79
191-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:134:25-76
192            </intent-filter>
193        </receiver>
194        <receiver
194-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:138:9-148:20
195            android:name="com.mahmoudffyt.gfxbooster.NotificationReceiver"
195-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:139:13-49
196            android:enabled="true"
196-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:140:13-35
197            android:exported="true" >
197-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:141:13-36
198            <intent-filter>
198-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:142:13-147:29
199                <action android:name="com.game.headshot.ACTION_NOON_REMINDER" />
199-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:143:17-81
199-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:143:25-78
200                <action android:name="com.game.headshot.ACTION_EVENING_REMINDER" />
200-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:144:17-84
200-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:144:25-81
201                <action android:name="com.game.headshot.ACTION_HIGH_USAGE" />
201-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:145:17-78
201-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:145:25-75
202                <action android:name="com.game.headshot.ACTION_CHECK_SYSTEM" />
202-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:146:17-80
202-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:146:25-77
203            </intent-filter>
204        </receiver>
205
206        <meta-data
206-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:22:9-24:37
207            android:name="com.google.android.play.billingclient.version"
207-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:23:13-73
208            android:value="7.1.1" />
208-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:24:13-34
209
210        <activity
210-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:26:9-30:75
211            android:name="com.android.billingclient.api.ProxyBillingActivity"
211-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:27:13-78
212            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
212-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:28:13-96
213            android:exported="false"
213-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:29:13-37
214            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
214-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:30:13-72
215        <activity
215-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:31:9-35:75
216            android:name="com.android.billingclient.api.ProxyBillingActivityV2"
216-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:32:13-80
217            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
217-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:33:13-96
218            android:exported="false"
218-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:34:13-37
219            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
219-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:35:13-72
220
221        <service
221-->[com.google.firebase:firebase-analytics-ktx:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ad6a65ba20f74a8e88da6b866e65a53\transformed\firebase-analytics-ktx-22.4.0\AndroidManifest.xml:8:9-14:19
222            android:name="com.google.firebase.components.ComponentDiscoveryService"
222-->[com.google.firebase:firebase-analytics-ktx:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ad6a65ba20f74a8e88da6b866e65a53\transformed\firebase-analytics-ktx-22.4.0\AndroidManifest.xml:9:13-84
223            android:directBootAware="true"
223-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
224            android:exported="false" >
224-->[com.google.firebase:firebase-analytics-ktx:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ad6a65ba20f74a8e88da6b866e65a53\transformed\firebase-analytics-ktx-22.4.0\AndroidManifest.xml:10:13-37
225            <meta-data
225-->[com.google.firebase:firebase-analytics-ktx:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ad6a65ba20f74a8e88da6b866e65a53\transformed\firebase-analytics-ktx-22.4.0\AndroidManifest.xml:11:13-13:85
226                android:name="com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar"
226-->[com.google.firebase:firebase-analytics-ktx:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ad6a65ba20f74a8e88da6b866e65a53\transformed\firebase-analytics-ktx-22.4.0\AndroidManifest.xml:12:17-129
227                android:value="com.google.firebase.components.ComponentRegistrar" />
227-->[com.google.firebase:firebase-analytics-ktx:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ad6a65ba20f74a8e88da6b866e65a53\transformed\firebase-analytics-ktx-22.4.0\AndroidManifest.xml:13:17-82
228            <meta-data
228-->[com.google.firebase:firebase-crashlytics-ktx:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5995498b5b81ac31250b4f1324a21132\transformed\firebase-crashlytics-ktx-19.4.3\AndroidManifest.xml:24:13-26:85
229                android:name="com.google.firebase.components:com.google.firebase.crashlytics.ktx.FirebaseCrashlyticsKtxRegistrar"
229-->[com.google.firebase:firebase-crashlytics-ktx:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5995498b5b81ac31250b4f1324a21132\transformed\firebase-crashlytics-ktx-19.4.3\AndroidManifest.xml:25:17-130
230                android:value="com.google.firebase.components.ComponentRegistrar" />
230-->[com.google.firebase:firebase-crashlytics-ktx:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5995498b5b81ac31250b4f1324a21132\transformed\firebase-crashlytics-ktx-19.4.3\AndroidManifest.xml:26:17-82
231            <meta-data
231-->[com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\046374a346d3cc3e0fb7692fa882950a\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:15:13-17:85
232                android:name="com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar"
232-->[com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\046374a346d3cc3e0fb7692fa882950a\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:16:17-126
233                android:value="com.google.firebase.components.ComponentRegistrar" />
233-->[com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\046374a346d3cc3e0fb7692fa882950a\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:17:17-82
234            <meta-data
234-->[com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\046374a346d3cc3e0fb7692fa882950a\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:18:13-20:85
235                android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar"
235-->[com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\046374a346d3cc3e0fb7692fa882950a\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:19:17-115
236                android:value="com.google.firebase.components.ComponentRegistrar" />
236-->[com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\046374a346d3cc3e0fb7692fa882950a\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:20:17-82
237            <meta-data
237-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:33:13-35:85
238                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
238-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:34:17-139
239                android:value="com.google.firebase.components.ComponentRegistrar" />
239-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:35:17-82
240            <meta-data
240-->[com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a526ba4e24480c7108925ec9a95f4804\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:29:13-31:85
241                android:name="com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar"
241-->[com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a526ba4e24480c7108925ec9a95f4804\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:30:17-117
242                android:value="com.google.firebase.components.ComponentRegistrar" />
242-->[com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a526ba4e24480c7108925ec9a95f4804\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:31:17-82
243            <meta-data
243-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3849af013f032b744f299890f6883a64\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
244                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
244-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3849af013f032b744f299890f6883a64\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
245                android:value="com.google.firebase.components.ComponentRegistrar" />
245-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3849af013f032b744f299890f6883a64\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
246            <meta-data
246-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3849af013f032b744f299890f6883a64\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
247                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
247-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3849af013f032b744f299890f6883a64\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
248                android:value="com.google.firebase.components.ComponentRegistrar" />
248-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3849af013f032b744f299890f6883a64\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
249            <meta-data
249-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9c854e55cb7e484c6bf93dacda33eca\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
250                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
250-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9c854e55cb7e484c6bf93dacda33eca\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
251                android:value="com.google.firebase.components.ComponentRegistrar" />
251-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9c854e55cb7e484c6bf93dacda33eca\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
252            <meta-data
252-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
253                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
253-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
254                android:value="com.google.firebase.components.ComponentRegistrar" />
254-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
255            <meta-data
255-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30632a7c8dd1b017955f6c4482cfc334\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:25:13-27:85
256                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
256-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30632a7c8dd1b017955f6c4482cfc334\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:26:17-115
257                android:value="com.google.firebase.components.ComponentRegistrar" />
257-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30632a7c8dd1b017955f6c4482cfc334\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:27:17-82
258        </service>
259
260        <receiver
260-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:29:9-33:20
261            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
261-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:30:13-85
262            android:enabled="true"
262-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:31:13-35
263            android:exported="false" >
263-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:32:13-37
264        </receiver>
265
266        <service
266-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:35:9-38:40
267            android:name="com.google.android.gms.measurement.AppMeasurementService"
267-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:36:13-84
268            android:enabled="true"
268-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:37:13-35
269            android:exported="false" />
269-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:38:13-37
270        <service
270-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:39:9-43:72
271            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
271-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:40:13-87
272            android:enabled="true"
272-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:41:13-35
273            android:exported="false"
273-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:42:13-37
274            android:permission="android.permission.BIND_JOB_SERVICE" />
274-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:43:13-69
275
276        <activity
276-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef3205b01dd1e8d62ca95413fbcfcde0\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
277            android:name="com.google.android.gms.common.api.GoogleApiActivity"
277-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef3205b01dd1e8d62ca95413fbcfcde0\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
278            android:exported="false"
278-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef3205b01dd1e8d62ca95413fbcfcde0\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
279            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
279-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef3205b01dd1e8d62ca95413fbcfcde0\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
280
281        <service
281-->[com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a526ba4e24480c7108925ec9a95f4804\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:22:9-25:40
282            android:name="com.google.firebase.sessions.SessionLifecycleService"
282-->[com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a526ba4e24480c7108925ec9a95f4804\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:23:13-80
283            android:enabled="true"
283-->[com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a526ba4e24480c7108925ec9a95f4804\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:24:13-35
284            android:exported="false" />
284-->[com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a526ba4e24480c7108925ec9a95f4804\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:25:13-37
285
286        <provider
286-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
287            android:name="com.google.firebase.provider.FirebaseInitProvider"
287-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
288            android:authorities="com.mahmoudffyt.gfxbooster.firebaseinitprovider"
288-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
289            android:directBootAware="true"
289-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
290            android:exported="false"
290-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
291            android:initOrder="100" />
291-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
292
293        <uses-library
293-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6a554b97c6fdafe7f9e475f6f094c65\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
294            android:name="android.ext.adservices"
294-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6a554b97c6fdafe7f9e475f6f094c65\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
295            android:required="false" />
295-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6a554b97c6fdafe7f9e475f6f094c65\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
296
297        <provider
297-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37e33928c78c0b5c37d0e47d6d95412c\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
298            android:name="androidx.startup.InitializationProvider"
298-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37e33928c78c0b5c37d0e47d6d95412c\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
299            android:authorities="com.mahmoudffyt.gfxbooster.androidx-startup"
299-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37e33928c78c0b5c37d0e47d6d95412c\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
300            android:exported="false" >
300-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37e33928c78c0b5c37d0e47d6d95412c\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
301            <meta-data
301-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37e33928c78c0b5c37d0e47d6d95412c\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
302                android:name="androidx.emoji2.text.EmojiCompatInitializer"
302-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37e33928c78c0b5c37d0e47d6d95412c\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
303                android:value="androidx.startup" />
303-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37e33928c78c0b5c37d0e47d6d95412c\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
304            <meta-data
304-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
305                android:name="androidx.work.WorkManagerInitializer"
305-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
306                android:value="androidx.startup" />
306-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
307            <meta-data
307-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\921836e7511c8cb3e70450775705a043\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
308                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
308-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\921836e7511c8cb3e70450775705a043\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
309                android:value="androidx.startup" />
309-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\921836e7511c8cb3e70450775705a043\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
310            <meta-data
310-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
311                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
311-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
312                android:value="androidx.startup" />
312-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
313        </provider> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
314        <activity
314-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:73:9-78:43
315            android:name="com.google.android.gms.ads.AdActivity"
315-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:74:13-65
316            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
316-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:75:13-122
317            android:exported="false"
317-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:76:13-37
318            android:theme="@android:style/Theme.Translucent" />
318-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:77:13-61
319
320        <provider
320-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:80:9-85:43
321            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
321-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:81:13-76
322            android:authorities="com.mahmoudffyt.gfxbooster.mobileadsinitprovider"
322-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:82:13-73
323            android:exported="false"
323-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:83:13-37
324            android:initOrder="100" />
324-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:84:13-36
325
326        <service
326-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:87:9-91:43
327            android:name="com.google.android.gms.ads.AdService"
327-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:88:13-64
328            android:enabled="true"
328-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:89:13-35
329            android:exported="false" />
329-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:90:13-37
330
331        <activity
331-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:93:9-97:43
332            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
332-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:94:13-82
333            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
333-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:95:13-122
334            android:exported="false" />
334-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:96:13-37
335        <activity
335-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:98:9-105:43
336            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
336-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:99:13-82
337            android:excludeFromRecents="true"
337-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:100:13-46
338            android:exported="false"
338-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:101:13-37
339            android:launchMode="singleTask"
339-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:102:13-44
340            android:taskAffinity=""
340-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:103:13-36
341            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
341-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:104:13-72
342
343        <meta-data
343-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:107:9-109:36
344            android:name="com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING"
344-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:108:13-79
345            android:value="true" />
345-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:109:13-33
346        <meta-data
346-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:110:9-112:36
347            android:name="com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION"
347-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:111:13-83
348            android:value="true" />
348-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:112:13-33
349
350        <service
350-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
351            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
351-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
352            android:directBootAware="false"
352-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
353            android:enabled="@bool/enable_system_alarm_service_default"
353-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
354            android:exported="false" />
354-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
355        <service
355-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
356            android:name="androidx.work.impl.background.systemjob.SystemJobService"
356-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
357            android:directBootAware="false"
357-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
358            android:enabled="@bool/enable_system_job_service_default"
358-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
359            android:exported="true"
359-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
360            android:permission="android.permission.BIND_JOB_SERVICE" />
360-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
361        <service
361-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
362            android:name="androidx.work.impl.foreground.SystemForegroundService"
362-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
363            android:directBootAware="false"
363-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
364            android:enabled="@bool/enable_system_foreground_service_default"
364-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
365            android:exported="false" />
365-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
366
367        <receiver
367-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
368            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
368-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
369            android:directBootAware="false"
369-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
370            android:enabled="true"
370-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
371            android:exported="false" />
371-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
372        <receiver
372-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
373            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
373-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
374            android:directBootAware="false"
374-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
375            android:enabled="false"
375-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
376            android:exported="false" >
376-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
377            <intent-filter>
377-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
378                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
378-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
378-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
379                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
379-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
379-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
380            </intent-filter>
381        </receiver>
382        <receiver
382-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
383            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
383-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
384            android:directBootAware="false"
384-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
385            android:enabled="false"
385-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
386            android:exported="false" >
386-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
387            <intent-filter>
387-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
388                <action android:name="android.intent.action.BATTERY_OKAY" />
388-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
388-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
389                <action android:name="android.intent.action.BATTERY_LOW" />
389-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
389-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
390            </intent-filter>
391        </receiver>
392        <receiver
392-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
393            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
393-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
394            android:directBootAware="false"
394-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
395            android:enabled="false"
395-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
396            android:exported="false" >
396-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
397            <intent-filter>
397-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
398                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
398-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
398-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
399                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
399-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
399-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
400            </intent-filter>
401        </receiver>
402        <receiver
402-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
403            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
403-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
404            android:directBootAware="false"
404-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
405            android:enabled="false"
405-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
406            android:exported="false" >
406-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
407            <intent-filter>
407-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
408                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
408-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
408-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
409            </intent-filter>
410        </receiver>
411        <receiver
411-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
412            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
412-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
413            android:directBootAware="false"
413-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
414            android:enabled="false"
414-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
415            android:exported="false" >
415-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
416            <intent-filter>
416-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
417                <action android:name="android.intent.action.BOOT_COMPLETED" />
417-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:134:17-79
417-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:134:25-76
418                <action android:name="android.intent.action.TIME_SET" />
418-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
418-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
419                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
419-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
419-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
420            </intent-filter>
421        </receiver>
422        <receiver
422-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
423            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
423-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
424            android:directBootAware="false"
424-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
425            android:enabled="@bool/enable_system_alarm_service_default"
425-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
426            android:exported="false" >
426-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
427            <intent-filter>
427-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
428                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
428-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
428-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
429            </intent-filter>
430        </receiver>
431        <receiver
431-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
432            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
432-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
433            android:directBootAware="false"
433-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
434            android:enabled="true"
434-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
435            android:exported="true"
435-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
436            android:permission="android.permission.DUMP" >
436-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
437            <intent-filter>
437-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
438                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
438-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
438-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
439            </intent-filter>
440        </receiver>
441
442        <meta-data
442-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aff62db08cff511cc4373673ada3b6cf\transformed\play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
443            android:name="com.google.android.gms.version"
443-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aff62db08cff511cc4373673ada3b6cf\transformed\play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
444            android:value="@integer/google_play_services_version" />
444-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aff62db08cff511cc4373673ada3b6cf\transformed\play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
445
446        <receiver
446-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
447            android:name="androidx.profileinstaller.ProfileInstallReceiver"
447-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
448            android:directBootAware="false"
448-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
449            android:enabled="true"
449-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
450            android:exported="true"
450-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
451            android:permission="android.permission.DUMP" >
451-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
452            <intent-filter>
452-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
453                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
453-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
453-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
454            </intent-filter>
455            <intent-filter>
455-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
456                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
456-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
456-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
457            </intent-filter>
458            <intent-filter>
458-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
459                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
459-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
459-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
460            </intent-filter>
461            <intent-filter>
461-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
462                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
462-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
462-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
463            </intent-filter>
464        </receiver>
465
466        <service
466-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf66c28488d56b085c276c14a4449049\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:26:9-32:19
467            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
467-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf66c28488d56b085c276c14a4449049\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:27:13-103
468            android:exported="false" >
468-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf66c28488d56b085c276c14a4449049\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:28:13-37
469            <meta-data
469-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf66c28488d56b085c276c14a4449049\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:29:13-31:39
470                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
470-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf66c28488d56b085c276c14a4449049\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:30:17-94
471                android:value="cct" />
471-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf66c28488d56b085c276c14a4449049\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:31:17-36
472        </service>
473        <service
473-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18adbb13687610ba2ec263dcbb46b5e9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:24:9-28:19
474            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
474-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18adbb13687610ba2ec263dcbb46b5e9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:25:13-117
475            android:exported="false"
475-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18adbb13687610ba2ec263dcbb46b5e9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:26:13-37
476            android:permission="android.permission.BIND_JOB_SERVICE" >
476-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18adbb13687610ba2ec263dcbb46b5e9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:27:13-69
477        </service>
478
479        <receiver
479-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18adbb13687610ba2ec263dcbb46b5e9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:30:9-32:40
480            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
480-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18adbb13687610ba2ec263dcbb46b5e9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:31:13-132
481            android:exported="false" />
481-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18adbb13687610ba2ec263dcbb46b5e9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:32:13-37
482
483        <service
483-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac9518aeda46c3b802798bcb4651bb33\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
484            android:name="androidx.room.MultiInstanceInvalidationService"
484-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac9518aeda46c3b802798bcb4651bb33\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
485            android:directBootAware="true"
485-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac9518aeda46c3b802798bcb4651bb33\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
486            android:exported="false" />
486-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac9518aeda46c3b802798bcb4651bb33\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
487    </application>
488
489</manifest>
