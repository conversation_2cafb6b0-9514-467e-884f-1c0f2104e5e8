1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.mahmoudffyt.gfxbooster"
4    android:versionCode="7"
5    android:versionName="1.1.4" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <!-- Permissions -->
12    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
12-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:6:5-79
12-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
13-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:7:5-77
13-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:7:22-74
14    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
14-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:8:5-84
14-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:8:22-81
15    <uses-permission android:name="android.permission.PACKAGE_USAGE_STATS" />
15-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:9:5-10:47
15-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:9:22-75
16    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
16-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:11:5-77
16-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:11:22-74
17    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
17-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:12:5-87
17-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:12:22-84
18    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
18-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:13:5-81
18-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:13:22-78
19    <uses-permission android:name="android.permission.VIBRATE" />
19-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:14:5-66
19-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:14:22-63
20    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
20-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:15:5-78
20-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:15:22-75
21    <uses-permission android:name="android.permission.INTERNET" />
21-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:16:5-67
21-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:16:22-64
22    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
22-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:17:5-79
22-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:17:22-76
23
24    <!-- Declare foreground service types -->
25    <queries>
25-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:20:5-25:15
26        <intent>
26-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:21:9-24:18
27            <action android:name="android.intent.action.MAIN" />
27-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:22:13-65
27-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:22:21-62
28
29            <category android:name="android.intent.category.GAME" />
29-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:23:13-69
29-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:23:23-66
30        </intent>
31        <intent>
31-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:13:9-15:18
32            <action android:name="com.android.vending.billing.InAppBillingService.BIND" />
32-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:14:13-91
32-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:14:21-88
33        </intent>
34        <intent>
34-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:16:9-18:18
35            <action android:name="com.google.android.apps.play.billingtestcompanion.BillingOverrideService.BIND" />
35-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:17:13-116
35-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:17:21-113
36        </intent> <!-- For browser content -->
37        <intent>
37-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:38:9-44:18
38            <action android:name="android.intent.action.VIEW" />
38-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:39:13-65
38-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:39:21-62
39
40            <category android:name="android.intent.category.BROWSABLE" />
40-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:41:13-74
40-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:41:23-71
41
42            <data android:scheme="https" />
42-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:43:13-44
42-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:43:19-41
43        </intent> <!-- End of browser content -->
44        <!-- For CustomTabsService -->
45        <intent>
45-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:47:9-49:18
46            <action android:name="android.support.customtabs.action.CustomTabsService" />
46-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:48:13-90
46-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:48:21-87
47        </intent> <!-- End of CustomTabsService -->
48        <!-- For MRAID capabilities -->
49        <intent>
49-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:52:9-56:18
50            <action android:name="android.intent.action.INSERT" />
50-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:53:13-67
50-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:53:21-64
51
52            <data android:mimeType="vnd.android.cursor.dir/event" />
52-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:43:13-44
53        </intent>
54        <intent>
54-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:57:9-61:18
55            <action android:name="android.intent.action.VIEW" />
55-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:39:13-65
55-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:39:21-62
56
57            <data android:scheme="sms" />
57-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:43:13-44
57-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:43:19-41
58        </intent>
59        <intent>
59-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:62:9-66:18
60            <action android:name="android.intent.action.DIAL" />
60-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:63:13-65
60-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:63:21-62
61
62            <data android:path="tel:" />
62-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:43:13-44
63        </intent>
64    </queries>
65
66    <uses-permission android:name="com.android.vending.BILLING" />
66-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:10:5-67
66-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:10:22-64
67    <uses-permission android:name="android.permission.WAKE_LOCK" />
67-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:25:5-68
67-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:25:22-65
68    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
68-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:26:5-110
68-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:26:22-107
69    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
69-->[com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5be24e152e5ffa226393bdb33a3aef6\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:27:5-79
69-->[com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5be24e152e5ffa226393bdb33a3aef6\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:27:22-76
70    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
70-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:26:5-88
70-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:26:22-85
71    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
71-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:27:5-82
71-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:27:22-79
72    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" />
72-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:29:5-83
72-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:29:22-80
73
74    <permission
74-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3dd3f063a8dc2168576dc8a6639ef4\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
75        android:name="com.mahmoudffyt.gfxbooster.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
75-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3dd3f063a8dc2168576dc8a6639ef4\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
76        android:protectionLevel="signature" />
76-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3dd3f063a8dc2168576dc8a6639ef4\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
77
78    <uses-permission android:name="com.mahmoudffyt.gfxbooster.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
78-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3dd3f063a8dc2168576dc8a6639ef4\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
78-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3dd3f063a8dc2168576dc8a6639ef4\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
79
80    <application
80-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:27:5-126:19
81        android:name="com.mahmoudffyt.gfxbooster.HeadshotApplication"
81-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:28:9-44
82        android:allowBackup="true"
82-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:29:9-35
83        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
83-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3dd3f063a8dc2168576dc8a6639ef4\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
84        android:dataExtractionRules="@xml/data_extraction_rules"
84-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:30:9-65
85        android:debuggable="true"
86        android:extractNativeLibs="false"
87        android:fullBackupContent="@xml/backup_rules"
87-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:31:9-54
88        android:icon="@mipmap/ic_launcher"
88-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:32:9-43
89        android:label="@string/app_name"
89-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:33:9-41
90        android:roundIcon="@mipmap/ic_launcher_round"
90-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:34:9-54
91        android:supportsRtl="true"
91-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:35:9-35
92        android:testOnly="true"
93        android:theme="@style/Theme.HeadshotSettingsGameBooster" >
93-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:36:9-65
94
95        <!-- AdMob App ID -->
96        <meta-data
96-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:40:9-42:70
97            android:name="com.google.android.gms.ads.APPLICATION_ID"
97-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:41:13-69
98            android:value="ca-app-pub-4043804233099568~1048346990" />
98-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:42:13-67
99
100        <activity
100-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:43:9-46:45
101            android:name="com.mahmoudffyt.gfxbooster.GameBoosterActivity"
101-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:44:13-48
102            android:exported="false"
102-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:45:13-37
103            android:theme="@style/no_bar" />
103-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:46:13-42
104        <activity
104-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:47:9-50:45
105            android:name="com.mahmoudffyt.gfxbooster.MainActivity"
105-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:48:13-41
106            android:exported="false"
106-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:49:13-37
107            android:theme="@style/no_bar" />
107-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:50:13-42
108        <activity
108-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:51:9-60:20
109            android:name="com.mahmoudffyt.gfxbooster.splashscreen"
109-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:52:13-41
110            android:exported="true"
110-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:53:13-36
111            android:theme="@style/no_bar" >
111-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:54:13-42
112            <intent-filter>
112-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:55:13-59:29
113                <action android:name="android.intent.action.MAIN" />
113-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:22:13-65
113-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:22:21-62
114
115                <category android:name="android.intent.category.LAUNCHER" />
115-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:58:17-77
115-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:58:27-74
116            </intent-filter>
117        </activity>
118        <activity
118-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:62:9-65:45
119            android:name="com.mahmoudffyt.gfxbooster.SettingsActivity"
119-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:63:13-45
120            android:exported="false"
120-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:64:13-37
121            android:theme="@style/no_bar" />
121-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:65:13-42
122        <activity
122-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:66:9-69:45
123            android:name="com.mahmoudffyt.gfxbooster.GameModeActivity"
123-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:67:13-45
124            android:exported="false"
124-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:68:13-37
125            android:theme="@style/no_bar" />
125-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:69:13-42
126        <activity
126-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:70:9-73:45
127            android:name="com.mahmoudffyt.gfxbooster.GameSelectionActivity"
127-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:71:13-50
128            android:exported="false"
128-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:72:13-37
129            android:theme="@style/no_bar" />
129-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:73:13-42
130        <activity
130-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:74:9-77:45
131            android:name="com.mahmoudffyt.gfxbooster.HeadshotToolActivity"
131-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:75:13-49
132            android:exported="false"
132-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:76:13-37
133            android:theme="@style/no_bar" />
133-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:77:13-42
134        <!-- <activity -->
135        <!-- android:name=".AimButtonActivity" -->
136        <!-- android:exported="false" -->
137        <!-- android:theme="@style/no_bar" /> -->
138        <activity
138-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:82:9-85:45
139            android:name="com.mahmoudffyt.gfxbooster.AimOverlaySettingsActivity"
139-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:83:13-55
140            android:exported="false"
140-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:84:13-37
141            android:theme="@style/no_bar" />
141-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:85:13-42
142        <activity
142-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:86:9-89:45
143            android:name="com.mahmoudffyt.gfxbooster.GfxToolsActivity"
143-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:87:13-45
144            android:exported="false"
144-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:88:13-37
145            android:theme="@style/no_bar" />
145-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:89:13-42
146        <activity
146-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:90:9-93:45
147            android:name="com.mahmoudffyt.gfxbooster.AppGuideActivity"
147-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:91:13-45
148            android:exported="false"
148-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:92:13-37
149            android:theme="@style/no_bar" />
149-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:93:13-42
150        <activity
150-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:94:9-97:45
151            android:name="com.mahmoudffyt.gfxbooster.WebViewActivity"
151-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:95:13-44
152            android:exported="false"
152-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:96:13-37
153            android:theme="@style/no_bar" />
153-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:97:13-42
154
155        <service
155-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:100:9-104:56
156            android:name="com.mahmoudffyt.gfxbooster.GameModeService"
156-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:101:13-44
157            android:enabled="true"
157-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:102:13-35
158            android:exported="false"
158-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:103:13-37
159            android:foregroundServiceType="dataSync" />
159-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:104:13-53
160
161        <receiver
161-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:106:9-113:20
162            android:name="com.mahmoudffyt.gfxbooster.GameModeBootReceiver"
162-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:107:13-49
163            android:enabled="true"
163-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:108:13-35
164            android:exported="true" >
164-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:109:13-36
165            <intent-filter>
165-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:110:13-112:29
166                <action android:name="android.intent.action.BOOT_COMPLETED" />
166-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:111:17-79
166-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:111:25-76
167            </intent-filter>
168        </receiver>
169        <receiver
169-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:115:9-125:20
170            android:name="com.mahmoudffyt.gfxbooster.NotificationReceiver"
170-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:116:13-49
171            android:enabled="true"
171-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:117:13-35
172            android:exported="true" >
172-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:118:13-36
173            <intent-filter>
173-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:119:13-124:29
174                <action android:name="com.game.headshot.ACTION_NOON_REMINDER" />
174-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:120:17-81
174-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:120:25-78
175                <action android:name="com.game.headshot.ACTION_EVENING_REMINDER" />
175-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:121:17-84
175-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:121:25-81
176                <action android:name="com.game.headshot.ACTION_HIGH_USAGE" />
176-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:122:17-78
176-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:122:25-75
177                <action android:name="com.game.headshot.ACTION_CHECK_SYSTEM" />
177-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:123:17-80
177-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:123:25-77
178            </intent-filter>
179        </receiver>
180
181        <meta-data
181-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:22:9-24:37
182            android:name="com.google.android.play.billingclient.version"
182-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:23:13-73
183            android:value="7.1.1" />
183-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:24:13-34
184
185        <activity
185-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:26:9-30:75
186            android:name="com.android.billingclient.api.ProxyBillingActivity"
186-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:27:13-78
187            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
187-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:28:13-96
188            android:exported="false"
188-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:29:13-37
189            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
189-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:30:13-72
190        <activity
190-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:31:9-35:75
191            android:name="com.android.billingclient.api.ProxyBillingActivityV2"
191-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:32:13-80
192            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
192-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:33:13-96
193            android:exported="false"
193-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:34:13-37
194            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
194-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:35:13-72
195
196        <service
196-->[com.google.firebase:firebase-analytics-ktx:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ad6a65ba20f74a8e88da6b866e65a53\transformed\firebase-analytics-ktx-22.4.0\AndroidManifest.xml:8:9-14:19
197            android:name="com.google.firebase.components.ComponentDiscoveryService"
197-->[com.google.firebase:firebase-analytics-ktx:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ad6a65ba20f74a8e88da6b866e65a53\transformed\firebase-analytics-ktx-22.4.0\AndroidManifest.xml:9:13-84
198            android:directBootAware="true"
198-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
199            android:exported="false" >
199-->[com.google.firebase:firebase-analytics-ktx:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ad6a65ba20f74a8e88da6b866e65a53\transformed\firebase-analytics-ktx-22.4.0\AndroidManifest.xml:10:13-37
200            <meta-data
200-->[com.google.firebase:firebase-analytics-ktx:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ad6a65ba20f74a8e88da6b866e65a53\transformed\firebase-analytics-ktx-22.4.0\AndroidManifest.xml:11:13-13:85
201                android:name="com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar"
201-->[com.google.firebase:firebase-analytics-ktx:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ad6a65ba20f74a8e88da6b866e65a53\transformed\firebase-analytics-ktx-22.4.0\AndroidManifest.xml:12:17-129
202                android:value="com.google.firebase.components.ComponentRegistrar" />
202-->[com.google.firebase:firebase-analytics-ktx:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ad6a65ba20f74a8e88da6b866e65a53\transformed\firebase-analytics-ktx-22.4.0\AndroidManifest.xml:13:17-82
203            <meta-data
203-->[com.google.firebase:firebase-crashlytics-ktx:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5995498b5b81ac31250b4f1324a21132\transformed\firebase-crashlytics-ktx-19.4.3\AndroidManifest.xml:24:13-26:85
204                android:name="com.google.firebase.components:com.google.firebase.crashlytics.ktx.FirebaseCrashlyticsKtxRegistrar"
204-->[com.google.firebase:firebase-crashlytics-ktx:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5995498b5b81ac31250b4f1324a21132\transformed\firebase-crashlytics-ktx-19.4.3\AndroidManifest.xml:25:17-130
205                android:value="com.google.firebase.components.ComponentRegistrar" />
205-->[com.google.firebase:firebase-crashlytics-ktx:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5995498b5b81ac31250b4f1324a21132\transformed\firebase-crashlytics-ktx-19.4.3\AndroidManifest.xml:26:17-82
206            <meta-data
206-->[com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\046374a346d3cc3e0fb7692fa882950a\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:15:13-17:85
207                android:name="com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar"
207-->[com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\046374a346d3cc3e0fb7692fa882950a\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:16:17-126
208                android:value="com.google.firebase.components.ComponentRegistrar" />
208-->[com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\046374a346d3cc3e0fb7692fa882950a\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:17:17-82
209            <meta-data
209-->[com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\046374a346d3cc3e0fb7692fa882950a\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:18:13-20:85
210                android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar"
210-->[com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\046374a346d3cc3e0fb7692fa882950a\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:19:17-115
211                android:value="com.google.firebase.components.ComponentRegistrar" />
211-->[com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\046374a346d3cc3e0fb7692fa882950a\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:20:17-82
212            <meta-data
212-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:33:13-35:85
213                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
213-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:34:17-139
214                android:value="com.google.firebase.components.ComponentRegistrar" />
214-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:35:17-82
215            <meta-data
215-->[com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a526ba4e24480c7108925ec9a95f4804\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:29:13-31:85
216                android:name="com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar"
216-->[com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a526ba4e24480c7108925ec9a95f4804\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:30:17-117
217                android:value="com.google.firebase.components.ComponentRegistrar" />
217-->[com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a526ba4e24480c7108925ec9a95f4804\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:31:17-82
218            <meta-data
218-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3849af013f032b744f299890f6883a64\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
219                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
219-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3849af013f032b744f299890f6883a64\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
220                android:value="com.google.firebase.components.ComponentRegistrar" />
220-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3849af013f032b744f299890f6883a64\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
221            <meta-data
221-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3849af013f032b744f299890f6883a64\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
222                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
222-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3849af013f032b744f299890f6883a64\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
223                android:value="com.google.firebase.components.ComponentRegistrar" />
223-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3849af013f032b744f299890f6883a64\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
224            <meta-data
224-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9c854e55cb7e484c6bf93dacda33eca\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
225                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
225-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9c854e55cb7e484c6bf93dacda33eca\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
226                android:value="com.google.firebase.components.ComponentRegistrar" />
226-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9c854e55cb7e484c6bf93dacda33eca\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
227            <meta-data
227-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
228                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
228-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
229                android:value="com.google.firebase.components.ComponentRegistrar" />
229-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
230            <meta-data
230-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30632a7c8dd1b017955f6c4482cfc334\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:25:13-27:85
231                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
231-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30632a7c8dd1b017955f6c4482cfc334\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:26:17-115
232                android:value="com.google.firebase.components.ComponentRegistrar" />
232-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30632a7c8dd1b017955f6c4482cfc334\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:27:17-82
233        </service>
234
235        <receiver
235-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:29:9-33:20
236            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
236-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:30:13-85
237            android:enabled="true"
237-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:31:13-35
238            android:exported="false" >
238-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:32:13-37
239        </receiver>
240
241        <service
241-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:35:9-38:40
242            android:name="com.google.android.gms.measurement.AppMeasurementService"
242-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:36:13-84
243            android:enabled="true"
243-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:37:13-35
244            android:exported="false" />
244-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:38:13-37
245        <service
245-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:39:9-43:72
246            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
246-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:40:13-87
247            android:enabled="true"
247-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:41:13-35
248            android:exported="false"
248-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:42:13-37
249            android:permission="android.permission.BIND_JOB_SERVICE" />
249-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:43:13-69
250
251        <activity
251-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef3205b01dd1e8d62ca95413fbcfcde0\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
252            android:name="com.google.android.gms.common.api.GoogleApiActivity"
252-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef3205b01dd1e8d62ca95413fbcfcde0\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
253            android:exported="false"
253-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef3205b01dd1e8d62ca95413fbcfcde0\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
254            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
254-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef3205b01dd1e8d62ca95413fbcfcde0\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
255
256        <service
256-->[com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a526ba4e24480c7108925ec9a95f4804\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:22:9-25:40
257            android:name="com.google.firebase.sessions.SessionLifecycleService"
257-->[com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a526ba4e24480c7108925ec9a95f4804\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:23:13-80
258            android:enabled="true"
258-->[com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a526ba4e24480c7108925ec9a95f4804\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:24:13-35
259            android:exported="false" />
259-->[com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a526ba4e24480c7108925ec9a95f4804\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:25:13-37
260
261        <provider
261-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
262            android:name="com.google.firebase.provider.FirebaseInitProvider"
262-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
263            android:authorities="com.mahmoudffyt.gfxbooster.firebaseinitprovider"
263-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
264            android:directBootAware="true"
264-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
265            android:exported="false"
265-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
266            android:initOrder="100" />
266-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
267
268        <uses-library
268-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6a554b97c6fdafe7f9e475f6f094c65\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
269            android:name="android.ext.adservices"
269-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6a554b97c6fdafe7f9e475f6f094c65\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
270            android:required="false" />
270-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6a554b97c6fdafe7f9e475f6f094c65\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
271
272        <provider
272-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37e33928c78c0b5c37d0e47d6d95412c\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
273            android:name="androidx.startup.InitializationProvider"
273-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37e33928c78c0b5c37d0e47d6d95412c\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
274            android:authorities="com.mahmoudffyt.gfxbooster.androidx-startup"
274-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37e33928c78c0b5c37d0e47d6d95412c\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
275            android:exported="false" >
275-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37e33928c78c0b5c37d0e47d6d95412c\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
276            <meta-data
276-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37e33928c78c0b5c37d0e47d6d95412c\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
277                android:name="androidx.emoji2.text.EmojiCompatInitializer"
277-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37e33928c78c0b5c37d0e47d6d95412c\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
278                android:value="androidx.startup" />
278-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37e33928c78c0b5c37d0e47d6d95412c\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
279            <meta-data
279-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
280                android:name="androidx.work.WorkManagerInitializer"
280-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
281                android:value="androidx.startup" />
281-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
282            <meta-data
282-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\921836e7511c8cb3e70450775705a043\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
283                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
283-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\921836e7511c8cb3e70450775705a043\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
284                android:value="androidx.startup" />
284-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\921836e7511c8cb3e70450775705a043\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
285            <meta-data
285-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
286                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
286-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
287                android:value="androidx.startup" />
287-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
288        </provider> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
289        <activity
289-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:73:9-78:43
290            android:name="com.google.android.gms.ads.AdActivity"
290-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:74:13-65
291            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
291-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:75:13-122
292            android:exported="false"
292-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:76:13-37
293            android:theme="@android:style/Theme.Translucent" />
293-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:77:13-61
294
295        <provider
295-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:80:9-85:43
296            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
296-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:81:13-76
297            android:authorities="com.mahmoudffyt.gfxbooster.mobileadsinitprovider"
297-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:82:13-73
298            android:exported="false"
298-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:83:13-37
299            android:initOrder="100" />
299-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:84:13-36
300
301        <service
301-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:87:9-91:43
302            android:name="com.google.android.gms.ads.AdService"
302-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:88:13-64
303            android:enabled="true"
303-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:89:13-35
304            android:exported="false" />
304-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:90:13-37
305
306        <activity
306-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:93:9-97:43
307            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
307-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:94:13-82
308            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
308-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:95:13-122
309            android:exported="false" />
309-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:96:13-37
310        <activity
310-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:98:9-105:43
311            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
311-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:99:13-82
312            android:excludeFromRecents="true"
312-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:100:13-46
313            android:exported="false"
313-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:101:13-37
314            android:launchMode="singleTask"
314-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:102:13-44
315            android:taskAffinity=""
315-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:103:13-36
316            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
316-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:104:13-72
317
318        <meta-data
318-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:107:9-109:36
319            android:name="com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING"
319-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:108:13-79
320            android:value="true" />
320-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:109:13-33
321        <meta-data
321-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:110:9-112:36
322            android:name="com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION"
322-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:111:13-83
323            android:value="true" />
323-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:112:13-33
324
325        <service
325-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
326            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
326-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
327            android:directBootAware="false"
327-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
328            android:enabled="@bool/enable_system_alarm_service_default"
328-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
329            android:exported="false" />
329-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
330        <service
330-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
331            android:name="androidx.work.impl.background.systemjob.SystemJobService"
331-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
332            android:directBootAware="false"
332-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
333            android:enabled="@bool/enable_system_job_service_default"
333-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
334            android:exported="true"
334-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
335            android:permission="android.permission.BIND_JOB_SERVICE" />
335-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
336        <service
336-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
337            android:name="androidx.work.impl.foreground.SystemForegroundService"
337-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
338            android:directBootAware="false"
338-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
339            android:enabled="@bool/enable_system_foreground_service_default"
339-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
340            android:exported="false" />
340-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
341
342        <receiver
342-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
343            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
343-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
344            android:directBootAware="false"
344-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
345            android:enabled="true"
345-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
346            android:exported="false" />
346-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
347        <receiver
347-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
348            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
348-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
349            android:directBootAware="false"
349-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
350            android:enabled="false"
350-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
351            android:exported="false" >
351-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
352            <intent-filter>
352-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
353                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
353-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
353-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
354                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
354-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
354-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
355            </intent-filter>
356        </receiver>
357        <receiver
357-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
358            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
358-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
359            android:directBootAware="false"
359-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
360            android:enabled="false"
360-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
361            android:exported="false" >
361-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
362            <intent-filter>
362-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
363                <action android:name="android.intent.action.BATTERY_OKAY" />
363-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
363-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
364                <action android:name="android.intent.action.BATTERY_LOW" />
364-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
364-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
365            </intent-filter>
366        </receiver>
367        <receiver
367-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
368            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
368-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
369            android:directBootAware="false"
369-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
370            android:enabled="false"
370-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
371            android:exported="false" >
371-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
372            <intent-filter>
372-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
373                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
373-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
373-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
374                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
374-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
374-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
375            </intent-filter>
376        </receiver>
377        <receiver
377-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
378            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
378-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
379            android:directBootAware="false"
379-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
380            android:enabled="false"
380-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
381            android:exported="false" >
381-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
382            <intent-filter>
382-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
383                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
383-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
383-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
384            </intent-filter>
385        </receiver>
386        <receiver
386-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
387            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
387-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
388            android:directBootAware="false"
388-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
389            android:enabled="false"
389-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
390            android:exported="false" >
390-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
391            <intent-filter>
391-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
392                <action android:name="android.intent.action.BOOT_COMPLETED" />
392-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:111:17-79
392-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:111:25-76
393                <action android:name="android.intent.action.TIME_SET" />
393-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
393-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
394                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
394-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
394-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
395            </intent-filter>
396        </receiver>
397        <receiver
397-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
398            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
398-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
399            android:directBootAware="false"
399-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
400            android:enabled="@bool/enable_system_alarm_service_default"
400-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
401            android:exported="false" >
401-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
402            <intent-filter>
402-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
403                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
403-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
403-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
404            </intent-filter>
405        </receiver>
406        <receiver
406-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
407            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
407-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
408            android:directBootAware="false"
408-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
409            android:enabled="true"
409-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
410            android:exported="true"
410-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
411            android:permission="android.permission.DUMP" >
411-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
412            <intent-filter>
412-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
413                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
413-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
413-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
414            </intent-filter>
415        </receiver>
416
417        <meta-data
417-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aff62db08cff511cc4373673ada3b6cf\transformed\play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
418            android:name="com.google.android.gms.version"
418-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aff62db08cff511cc4373673ada3b6cf\transformed\play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
419            android:value="@integer/google_play_services_version" />
419-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aff62db08cff511cc4373673ada3b6cf\transformed\play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
420
421        <receiver
421-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
422            android:name="androidx.profileinstaller.ProfileInstallReceiver"
422-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
423            android:directBootAware="false"
423-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
424            android:enabled="true"
424-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
425            android:exported="true"
425-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
426            android:permission="android.permission.DUMP" >
426-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
427            <intent-filter>
427-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
428                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
428-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
428-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
429            </intent-filter>
430            <intent-filter>
430-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
431                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
431-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
431-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
432            </intent-filter>
433            <intent-filter>
433-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
434                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
434-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
434-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
435            </intent-filter>
436            <intent-filter>
436-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
437                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
437-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
437-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
438            </intent-filter>
439        </receiver>
440
441        <service
441-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf66c28488d56b085c276c14a4449049\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:26:9-32:19
442            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
442-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf66c28488d56b085c276c14a4449049\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:27:13-103
443            android:exported="false" >
443-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf66c28488d56b085c276c14a4449049\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:28:13-37
444            <meta-data
444-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf66c28488d56b085c276c14a4449049\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:29:13-31:39
445                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
445-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf66c28488d56b085c276c14a4449049\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:30:17-94
446                android:value="cct" />
446-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf66c28488d56b085c276c14a4449049\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:31:17-36
447        </service>
448        <service
448-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18adbb13687610ba2ec263dcbb46b5e9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:24:9-28:19
449            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
449-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18adbb13687610ba2ec263dcbb46b5e9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:25:13-117
450            android:exported="false"
450-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18adbb13687610ba2ec263dcbb46b5e9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:26:13-37
451            android:permission="android.permission.BIND_JOB_SERVICE" >
451-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18adbb13687610ba2ec263dcbb46b5e9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:27:13-69
452        </service>
453
454        <receiver
454-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18adbb13687610ba2ec263dcbb46b5e9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:30:9-32:40
455            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
455-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18adbb13687610ba2ec263dcbb46b5e9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:31:13-132
456            android:exported="false" />
456-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18adbb13687610ba2ec263dcbb46b5e9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:32:13-37
457
458        <service
458-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac9518aeda46c3b802798bcb4651bb33\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
459            android:name="androidx.room.MultiInstanceInvalidationService"
459-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac9518aeda46c3b802798bcb4651bb33\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
460            android:directBootAware="true"
460-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac9518aeda46c3b802798bcb4651bb33\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
461            android:exported="false" />
461-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac9518aeda46c3b802798bcb4651bb33\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
462    </application>
463
464</manifest>
