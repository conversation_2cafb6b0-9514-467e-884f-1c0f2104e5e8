1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.mahmoudffyt.gfxbooster"
4    android:versionCode="7"
5    android:versionName="1.1.4" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <!-- Permissions -->
12    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
12-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:6:5-79
12-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
13-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:7:5-77
13-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:7:22-74
14    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
14-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:8:5-84
14-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:8:22-81
15    <uses-permission android:name="android.permission.PACKAGE_USAGE_STATS" />
15-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:9:5-10:47
15-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:9:22-75
16    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
16-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:11:5-77
16-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:11:22-74
17    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
17-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:12:5-87
17-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:12:22-84
18    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
18-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:13:5-81
18-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:13:22-78
19    <uses-permission android:name="android.permission.VIBRATE" />
19-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:14:5-66
19-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:14:22-63
20    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
20-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:15:5-78
20-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:15:22-75
21    <uses-permission android:name="android.permission.INTERNET" />
21-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:16:5-67
21-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:16:22-64
22    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
22-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:17:5-79
22-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:17:22-76
23
24    <!-- Declare foreground service types -->
25    <queries>
25-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:20:5-48:15
26
27        <!-- Allow querying all launchable apps -->
28        <intent>
28-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:22:9-25:18
29            <action android:name="android.intent.action.MAIN" />
29-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:23:13-65
29-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:23:21-62
30
31            <category android:name="android.intent.category.LAUNCHER" />
31-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:24:13-73
31-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:24:23-70
32        </intent>
33
34        <!-- Popular games packages for Android 14 compatibility -->
35        <package android:name="com.tencent.ig" />
35-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:28:9-50
35-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:28:18-47
36        <package android:name="com.dts.freefireth" />
36-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:29:9-54
36-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:29:18-51
37        <package android:name="com.supercell.clashofclans" />
37-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:30:9-62
37-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:30:18-59
38        <package android:name="com.supercell.clashroyale" />
38-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:31:9-61
38-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:31:18-58
39        <package android:name="com.king.candycrushsaga" />
39-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:32:9-59
39-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:32:18-56
40        <package android:name="com.gameloft.android.ANMP.GloftA8HM" />
40-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:33:9-71
40-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:33:18-68
41        <package android:name="com.mobile.legends" />
41-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:34:9-54
41-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:34:18-51
42        <package android:name="com.garena.game.codm" />
42-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:35:9-56
42-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:35:18-53
43        <package android:name="com.mojang.minecraftpe" />
43-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:36:9-58
43-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:36:18-55
44        <package android:name="com.roblox.client" />
44-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:37:9-53
44-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:37:18-50
45        <package android:name="com.innersloth.spacemafia" />
45-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:38:9-61
45-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:38:18-58
46        <package android:name="com.ea.gp.fifamobile" />
46-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:39:9-56
46-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:39:18-53
47        <package android:name="com.ea.gp.apexlegendsmobilefps" />
47-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:40:9-66
47-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:40:18-63
48        <package android:name="com.miHoYo.GenshinImpact" />
48-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:41:9-60
48-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:41:18-57
49        <package android:name="com.activision.callofduty.shooter" />
49-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:42:9-69
49-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:42:18-66
50        <package android:name="com.epicgames.fortnite" />
50-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:43:9-58
50-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:43:18-55
51        <package android:name="com.netease.lztgglobal" />
51-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:44:9-58
51-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:44:18-55
52        <package android:name="com.netease.mrzhna" />
52-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:45:9-54
52-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:45:18-51
53        <package android:name="com.zynga.words3" />
53-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:46:9-52
53-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:46:18-49
54        <package android:name="com.outfit7.mytalkingtom2" />
54-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:47:9-61
54-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:47:18-58
55
56        <intent>
56-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:13:9-15:18
57            <action android:name="com.android.vending.billing.InAppBillingService.BIND" />
57-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:14:13-91
57-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:14:21-88
58        </intent>
59        <intent>
59-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:16:9-18:18
60            <action android:name="com.google.android.apps.play.billingtestcompanion.BillingOverrideService.BIND" />
60-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:17:13-116
60-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:17:21-113
61        </intent> <!-- For browser content -->
62        <intent>
62-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:38:9-44:18
63            <action android:name="android.intent.action.VIEW" />
63-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:39:13-65
63-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:39:21-62
64
65            <category android:name="android.intent.category.BROWSABLE" />
65-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:41:13-74
65-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:41:23-71
66
67            <data android:scheme="https" />
67-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:43:13-44
67-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:43:19-41
68        </intent> <!-- End of browser content -->
69        <!-- For CustomTabsService -->
70        <intent>
70-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:47:9-49:18
71            <action android:name="android.support.customtabs.action.CustomTabsService" />
71-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:48:13-90
71-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:48:21-87
72        </intent> <!-- End of CustomTabsService -->
73        <!-- For MRAID capabilities -->
74        <intent>
74-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:52:9-56:18
75            <action android:name="android.intent.action.INSERT" />
75-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:53:13-67
75-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:53:21-64
76
77            <data android:mimeType="vnd.android.cursor.dir/event" />
77-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:43:13-44
78        </intent>
79        <intent>
79-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:57:9-61:18
80            <action android:name="android.intent.action.VIEW" />
80-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:39:13-65
80-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:39:21-62
81
82            <data android:scheme="sms" />
82-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:43:13-44
82-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:43:19-41
83        </intent>
84        <intent>
84-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:62:9-66:18
85            <action android:name="android.intent.action.DIAL" />
85-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:63:13-65
85-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:63:21-62
86
87            <data android:path="tel:" />
87-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:43:13-44
88        </intent>
89    </queries>
90
91    <uses-permission android:name="com.android.vending.BILLING" />
91-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:10:5-67
91-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:10:22-64
92    <uses-permission android:name="android.permission.WAKE_LOCK" />
92-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:25:5-68
92-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:25:22-65
93    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
93-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:26:5-110
93-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:26:22-107
94    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
94-->[com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5be24e152e5ffa226393bdb33a3aef6\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:27:5-79
94-->[com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5be24e152e5ffa226393bdb33a3aef6\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:27:22-76
95    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
95-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:26:5-88
95-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:26:22-85
96    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
96-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:27:5-82
96-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:27:22-79
97    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" />
97-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:29:5-83
97-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:29:22-80
98
99    <permission
99-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3dd3f063a8dc2168576dc8a6639ef4\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
100        android:name="com.mahmoudffyt.gfxbooster.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
100-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3dd3f063a8dc2168576dc8a6639ef4\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
101        android:protectionLevel="signature" />
101-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3dd3f063a8dc2168576dc8a6639ef4\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
102
103    <uses-permission android:name="com.mahmoudffyt.gfxbooster.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
103-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3dd3f063a8dc2168576dc8a6639ef4\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
103-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3dd3f063a8dc2168576dc8a6639ef4\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
104
105    <application
105-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:50:5-149:19
106        android:name="com.mahmoudffyt.gfxbooster.HeadshotApplication"
106-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:51:9-44
107        android:allowBackup="true"
107-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:52:9-35
108        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
108-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3dd3f063a8dc2168576dc8a6639ef4\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
109        android:dataExtractionRules="@xml/data_extraction_rules"
109-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:53:9-65
110        android:debuggable="true"
111        android:extractNativeLibs="false"
112        android:fullBackupContent="@xml/backup_rules"
112-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:54:9-54
113        android:icon="@mipmap/ic_launcher"
113-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:55:9-43
114        android:label="@string/app_name"
114-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:56:9-41
115        android:roundIcon="@mipmap/ic_launcher_round"
115-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:57:9-54
116        android:supportsRtl="true"
116-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:58:9-35
117        android:theme="@style/Theme.HeadshotSettingsGameBooster" >
117-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:59:9-65
118
119        <!-- AdMob App ID -->
120        <meta-data
120-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:63:9-65:70
121            android:name="com.google.android.gms.ads.APPLICATION_ID"
121-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:64:13-69
122            android:value="ca-app-pub-4043804233099568~1048346990" />
122-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:65:13-67
123
124        <activity
124-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:66:9-69:45
125            android:name="com.mahmoudffyt.gfxbooster.GameBoosterActivity"
125-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:67:13-48
126            android:exported="false"
126-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:68:13-37
127            android:theme="@style/no_bar" />
127-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:69:13-42
128        <activity
128-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:70:9-73:45
129            android:name="com.mahmoudffyt.gfxbooster.MainActivity"
129-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:71:13-41
130            android:exported="false"
130-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:72:13-37
131            android:theme="@style/no_bar" />
131-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:73:13-42
132        <activity
132-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:74:9-83:20
133            android:name="com.mahmoudffyt.gfxbooster.splashscreen"
133-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:75:13-41
134            android:exported="true"
134-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:76:13-36
135            android:theme="@style/no_bar" >
135-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:77:13-42
136            <intent-filter>
136-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:78:13-82:29
137                <action android:name="android.intent.action.MAIN" />
137-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:23:13-65
137-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:23:21-62
138
139                <category android:name="android.intent.category.LAUNCHER" />
139-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:24:13-73
139-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:24:23-70
140            </intent-filter>
141        </activity>
142        <activity
142-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:85:9-88:45
143            android:name="com.mahmoudffyt.gfxbooster.SettingsActivity"
143-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:86:13-45
144            android:exported="false"
144-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:87:13-37
145            android:theme="@style/no_bar" />
145-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:88:13-42
146        <activity
146-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:89:9-92:45
147            android:name="com.mahmoudffyt.gfxbooster.GameModeActivity"
147-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:90:13-45
148            android:exported="false"
148-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:91:13-37
149            android:theme="@style/no_bar" />
149-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:92:13-42
150        <activity
150-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:93:9-96:45
151            android:name="com.mahmoudffyt.gfxbooster.GameSelectionActivity"
151-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:94:13-50
152            android:exported="false"
152-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:95:13-37
153            android:theme="@style/no_bar" />
153-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:96:13-42
154        <activity
154-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:97:9-100:45
155            android:name="com.mahmoudffyt.gfxbooster.HeadshotToolActivity"
155-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:98:13-49
156            android:exported="false"
156-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:99:13-37
157            android:theme="@style/no_bar" />
157-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:100:13-42
158        <!-- <activity -->
159        <!-- android:name=".AimButtonActivity" -->
160        <!-- android:exported="false" -->
161        <!-- android:theme="@style/no_bar" /> -->
162        <activity
162-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:105:9-108:45
163            android:name="com.mahmoudffyt.gfxbooster.AimOverlaySettingsActivity"
163-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:106:13-55
164            android:exported="false"
164-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:107:13-37
165            android:theme="@style/no_bar" />
165-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:108:13-42
166        <activity
166-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:109:9-112:45
167            android:name="com.mahmoudffyt.gfxbooster.GfxToolsActivity"
167-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:110:13-45
168            android:exported="false"
168-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:111:13-37
169            android:theme="@style/no_bar" />
169-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:112:13-42
170        <activity
170-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:113:9-116:45
171            android:name="com.mahmoudffyt.gfxbooster.AppGuideActivity"
171-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:114:13-45
172            android:exported="false"
172-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:115:13-37
173            android:theme="@style/no_bar" />
173-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:116:13-42
174        <activity
174-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:117:9-120:45
175            android:name="com.mahmoudffyt.gfxbooster.WebViewActivity"
175-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:118:13-44
176            android:exported="false"
176-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:119:13-37
177            android:theme="@style/no_bar" />
177-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:120:13-42
178
179        <service
179-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:123:9-127:56
180            android:name="com.mahmoudffyt.gfxbooster.GameModeService"
180-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:124:13-44
181            android:enabled="true"
181-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:125:13-35
182            android:exported="false"
182-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:126:13-37
183            android:foregroundServiceType="dataSync" />
183-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:127:13-53
184
185        <receiver
185-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:129:9-136:20
186            android:name="com.mahmoudffyt.gfxbooster.GameModeBootReceiver"
186-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:130:13-49
187            android:enabled="true"
187-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:131:13-35
188            android:exported="true" >
188-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:132:13-36
189            <intent-filter>
189-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:133:13-135:29
190                <action android:name="android.intent.action.BOOT_COMPLETED" />
190-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:134:17-79
190-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:134:25-76
191            </intent-filter>
192        </receiver>
193        <receiver
193-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:138:9-148:20
194            android:name="com.mahmoudffyt.gfxbooster.NotificationReceiver"
194-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:139:13-49
195            android:enabled="true"
195-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:140:13-35
196            android:exported="true" >
196-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:141:13-36
197            <intent-filter>
197-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:142:13-147:29
198                <action android:name="com.game.headshot.ACTION_NOON_REMINDER" />
198-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:143:17-81
198-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:143:25-78
199                <action android:name="com.game.headshot.ACTION_EVENING_REMINDER" />
199-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:144:17-84
199-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:144:25-81
200                <action android:name="com.game.headshot.ACTION_HIGH_USAGE" />
200-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:145:17-78
200-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:145:25-75
201                <action android:name="com.game.headshot.ACTION_CHECK_SYSTEM" />
201-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:146:17-80
201-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:146:25-77
202            </intent-filter>
203        </receiver>
204
205        <meta-data
205-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:22:9-24:37
206            android:name="com.google.android.play.billingclient.version"
206-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:23:13-73
207            android:value="7.1.1" />
207-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:24:13-34
208
209        <activity
209-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:26:9-30:75
210            android:name="com.android.billingclient.api.ProxyBillingActivity"
210-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:27:13-78
211            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
211-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:28:13-96
212            android:exported="false"
212-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:29:13-37
213            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
213-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:30:13-72
214        <activity
214-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:31:9-35:75
215            android:name="com.android.billingclient.api.ProxyBillingActivityV2"
215-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:32:13-80
216            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
216-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:33:13-96
217            android:exported="false"
217-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:34:13-37
218            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
218-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:35:13-72
219
220        <service
220-->[com.google.firebase:firebase-analytics-ktx:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ad6a65ba20f74a8e88da6b866e65a53\transformed\firebase-analytics-ktx-22.4.0\AndroidManifest.xml:8:9-14:19
221            android:name="com.google.firebase.components.ComponentDiscoveryService"
221-->[com.google.firebase:firebase-analytics-ktx:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ad6a65ba20f74a8e88da6b866e65a53\transformed\firebase-analytics-ktx-22.4.0\AndroidManifest.xml:9:13-84
222            android:directBootAware="true"
222-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
223            android:exported="false" >
223-->[com.google.firebase:firebase-analytics-ktx:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ad6a65ba20f74a8e88da6b866e65a53\transformed\firebase-analytics-ktx-22.4.0\AndroidManifest.xml:10:13-37
224            <meta-data
224-->[com.google.firebase:firebase-analytics-ktx:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ad6a65ba20f74a8e88da6b866e65a53\transformed\firebase-analytics-ktx-22.4.0\AndroidManifest.xml:11:13-13:85
225                android:name="com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar"
225-->[com.google.firebase:firebase-analytics-ktx:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ad6a65ba20f74a8e88da6b866e65a53\transformed\firebase-analytics-ktx-22.4.0\AndroidManifest.xml:12:17-129
226                android:value="com.google.firebase.components.ComponentRegistrar" />
226-->[com.google.firebase:firebase-analytics-ktx:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ad6a65ba20f74a8e88da6b866e65a53\transformed\firebase-analytics-ktx-22.4.0\AndroidManifest.xml:13:17-82
227            <meta-data
227-->[com.google.firebase:firebase-crashlytics-ktx:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5995498b5b81ac31250b4f1324a21132\transformed\firebase-crashlytics-ktx-19.4.3\AndroidManifest.xml:24:13-26:85
228                android:name="com.google.firebase.components:com.google.firebase.crashlytics.ktx.FirebaseCrashlyticsKtxRegistrar"
228-->[com.google.firebase:firebase-crashlytics-ktx:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5995498b5b81ac31250b4f1324a21132\transformed\firebase-crashlytics-ktx-19.4.3\AndroidManifest.xml:25:17-130
229                android:value="com.google.firebase.components.ComponentRegistrar" />
229-->[com.google.firebase:firebase-crashlytics-ktx:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5995498b5b81ac31250b4f1324a21132\transformed\firebase-crashlytics-ktx-19.4.3\AndroidManifest.xml:26:17-82
230            <meta-data
230-->[com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\046374a346d3cc3e0fb7692fa882950a\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:15:13-17:85
231                android:name="com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar"
231-->[com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\046374a346d3cc3e0fb7692fa882950a\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:16:17-126
232                android:value="com.google.firebase.components.ComponentRegistrar" />
232-->[com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\046374a346d3cc3e0fb7692fa882950a\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:17:17-82
233            <meta-data
233-->[com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\046374a346d3cc3e0fb7692fa882950a\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:18:13-20:85
234                android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar"
234-->[com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\046374a346d3cc3e0fb7692fa882950a\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:19:17-115
235                android:value="com.google.firebase.components.ComponentRegistrar" />
235-->[com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\046374a346d3cc3e0fb7692fa882950a\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:20:17-82
236            <meta-data
236-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:33:13-35:85
237                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
237-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:34:17-139
238                android:value="com.google.firebase.components.ComponentRegistrar" />
238-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:35:17-82
239            <meta-data
239-->[com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a526ba4e24480c7108925ec9a95f4804\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:29:13-31:85
240                android:name="com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar"
240-->[com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a526ba4e24480c7108925ec9a95f4804\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:30:17-117
241                android:value="com.google.firebase.components.ComponentRegistrar" />
241-->[com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a526ba4e24480c7108925ec9a95f4804\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:31:17-82
242            <meta-data
242-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3849af013f032b744f299890f6883a64\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
243                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
243-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3849af013f032b744f299890f6883a64\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
244                android:value="com.google.firebase.components.ComponentRegistrar" />
244-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3849af013f032b744f299890f6883a64\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
245            <meta-data
245-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3849af013f032b744f299890f6883a64\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
246                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
246-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3849af013f032b744f299890f6883a64\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
247                android:value="com.google.firebase.components.ComponentRegistrar" />
247-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3849af013f032b744f299890f6883a64\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
248            <meta-data
248-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9c854e55cb7e484c6bf93dacda33eca\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
249                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
249-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9c854e55cb7e484c6bf93dacda33eca\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
250                android:value="com.google.firebase.components.ComponentRegistrar" />
250-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9c854e55cb7e484c6bf93dacda33eca\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
251            <meta-data
251-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
252                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
252-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
253                android:value="com.google.firebase.components.ComponentRegistrar" />
253-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
254            <meta-data
254-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30632a7c8dd1b017955f6c4482cfc334\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:25:13-27:85
255                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
255-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30632a7c8dd1b017955f6c4482cfc334\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:26:17-115
256                android:value="com.google.firebase.components.ComponentRegistrar" />
256-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30632a7c8dd1b017955f6c4482cfc334\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:27:17-82
257        </service>
258
259        <receiver
259-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:29:9-33:20
260            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
260-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:30:13-85
261            android:enabled="true"
261-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:31:13-35
262            android:exported="false" >
262-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:32:13-37
263        </receiver>
264
265        <service
265-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:35:9-38:40
266            android:name="com.google.android.gms.measurement.AppMeasurementService"
266-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:36:13-84
267            android:enabled="true"
267-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:37:13-35
268            android:exported="false" />
268-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:38:13-37
269        <service
269-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:39:9-43:72
270            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
270-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:40:13-87
271            android:enabled="true"
271-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:41:13-35
272            android:exported="false"
272-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:42:13-37
273            android:permission="android.permission.BIND_JOB_SERVICE" />
273-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:43:13-69
274
275        <activity
275-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef3205b01dd1e8d62ca95413fbcfcde0\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
276            android:name="com.google.android.gms.common.api.GoogleApiActivity"
276-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef3205b01dd1e8d62ca95413fbcfcde0\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
277            android:exported="false"
277-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef3205b01dd1e8d62ca95413fbcfcde0\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
278            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
278-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef3205b01dd1e8d62ca95413fbcfcde0\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
279
280        <service
280-->[com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a526ba4e24480c7108925ec9a95f4804\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:22:9-25:40
281            android:name="com.google.firebase.sessions.SessionLifecycleService"
281-->[com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a526ba4e24480c7108925ec9a95f4804\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:23:13-80
282            android:enabled="true"
282-->[com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a526ba4e24480c7108925ec9a95f4804\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:24:13-35
283            android:exported="false" />
283-->[com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a526ba4e24480c7108925ec9a95f4804\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:25:13-37
284
285        <provider
285-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
286            android:name="com.google.firebase.provider.FirebaseInitProvider"
286-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
287            android:authorities="com.mahmoudffyt.gfxbooster.firebaseinitprovider"
287-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
288            android:directBootAware="true"
288-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
289            android:exported="false"
289-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
290            android:initOrder="100" />
290-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
291
292        <uses-library
292-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6a554b97c6fdafe7f9e475f6f094c65\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
293            android:name="android.ext.adservices"
293-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6a554b97c6fdafe7f9e475f6f094c65\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
294            android:required="false" />
294-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6a554b97c6fdafe7f9e475f6f094c65\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
295
296        <provider
296-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37e33928c78c0b5c37d0e47d6d95412c\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
297            android:name="androidx.startup.InitializationProvider"
297-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37e33928c78c0b5c37d0e47d6d95412c\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
298            android:authorities="com.mahmoudffyt.gfxbooster.androidx-startup"
298-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37e33928c78c0b5c37d0e47d6d95412c\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
299            android:exported="false" >
299-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37e33928c78c0b5c37d0e47d6d95412c\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
300            <meta-data
300-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37e33928c78c0b5c37d0e47d6d95412c\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
301                android:name="androidx.emoji2.text.EmojiCompatInitializer"
301-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37e33928c78c0b5c37d0e47d6d95412c\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
302                android:value="androidx.startup" />
302-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37e33928c78c0b5c37d0e47d6d95412c\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
303            <meta-data
303-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
304                android:name="androidx.work.WorkManagerInitializer"
304-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
305                android:value="androidx.startup" />
305-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
306            <meta-data
306-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\921836e7511c8cb3e70450775705a043\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
307                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
307-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\921836e7511c8cb3e70450775705a043\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
308                android:value="androidx.startup" />
308-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\921836e7511c8cb3e70450775705a043\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
309            <meta-data
309-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
310                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
310-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
311                android:value="androidx.startup" />
311-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
312        </provider> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
313        <activity
313-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:73:9-78:43
314            android:name="com.google.android.gms.ads.AdActivity"
314-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:74:13-65
315            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
315-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:75:13-122
316            android:exported="false"
316-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:76:13-37
317            android:theme="@android:style/Theme.Translucent" />
317-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:77:13-61
318
319        <provider
319-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:80:9-85:43
320            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
320-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:81:13-76
321            android:authorities="com.mahmoudffyt.gfxbooster.mobileadsinitprovider"
321-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:82:13-73
322            android:exported="false"
322-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:83:13-37
323            android:initOrder="100" />
323-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:84:13-36
324
325        <service
325-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:87:9-91:43
326            android:name="com.google.android.gms.ads.AdService"
326-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:88:13-64
327            android:enabled="true"
327-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:89:13-35
328            android:exported="false" />
328-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:90:13-37
329
330        <activity
330-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:93:9-97:43
331            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
331-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:94:13-82
332            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
332-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:95:13-122
333            android:exported="false" />
333-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:96:13-37
334        <activity
334-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:98:9-105:43
335            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
335-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:99:13-82
336            android:excludeFromRecents="true"
336-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:100:13-46
337            android:exported="false"
337-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:101:13-37
338            android:launchMode="singleTask"
338-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:102:13-44
339            android:taskAffinity=""
339-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:103:13-36
340            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
340-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:104:13-72
341
342        <meta-data
342-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:107:9-109:36
343            android:name="com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING"
343-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:108:13-79
344            android:value="true" />
344-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:109:13-33
345        <meta-data
345-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:110:9-112:36
346            android:name="com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION"
346-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:111:13-83
347            android:value="true" />
347-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:112:13-33
348
349        <service
349-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
350            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
350-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
351            android:directBootAware="false"
351-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
352            android:enabled="@bool/enable_system_alarm_service_default"
352-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
353            android:exported="false" />
353-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
354        <service
354-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
355            android:name="androidx.work.impl.background.systemjob.SystemJobService"
355-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
356            android:directBootAware="false"
356-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
357            android:enabled="@bool/enable_system_job_service_default"
357-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
358            android:exported="true"
358-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
359            android:permission="android.permission.BIND_JOB_SERVICE" />
359-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
360        <service
360-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
361            android:name="androidx.work.impl.foreground.SystemForegroundService"
361-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
362            android:directBootAware="false"
362-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
363            android:enabled="@bool/enable_system_foreground_service_default"
363-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
364            android:exported="false" />
364-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
365
366        <receiver
366-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
367            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
367-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
368            android:directBootAware="false"
368-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
369            android:enabled="true"
369-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
370            android:exported="false" />
370-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
371        <receiver
371-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
372            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
372-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
373            android:directBootAware="false"
373-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
374            android:enabled="false"
374-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
375            android:exported="false" >
375-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
376            <intent-filter>
376-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
377                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
377-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
377-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
378                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
378-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
378-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
379            </intent-filter>
380        </receiver>
381        <receiver
381-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
382            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
382-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
383            android:directBootAware="false"
383-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
384            android:enabled="false"
384-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
385            android:exported="false" >
385-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
386            <intent-filter>
386-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
387                <action android:name="android.intent.action.BATTERY_OKAY" />
387-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
387-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
388                <action android:name="android.intent.action.BATTERY_LOW" />
388-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
388-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
389            </intent-filter>
390        </receiver>
391        <receiver
391-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
392            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
392-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
393            android:directBootAware="false"
393-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
394            android:enabled="false"
394-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
395            android:exported="false" >
395-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
396            <intent-filter>
396-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
397                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
397-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
397-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
398                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
398-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
398-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
399            </intent-filter>
400        </receiver>
401        <receiver
401-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
402            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
402-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
403            android:directBootAware="false"
403-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
404            android:enabled="false"
404-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
405            android:exported="false" >
405-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
406            <intent-filter>
406-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
407                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
407-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
407-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
408            </intent-filter>
409        </receiver>
410        <receiver
410-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
411            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
411-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
412            android:directBootAware="false"
412-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
413            android:enabled="false"
413-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
414            android:exported="false" >
414-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
415            <intent-filter>
415-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
416                <action android:name="android.intent.action.BOOT_COMPLETED" />
416-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:134:17-79
416-->C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:134:25-76
417                <action android:name="android.intent.action.TIME_SET" />
417-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
417-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
418                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
418-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
418-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
419            </intent-filter>
420        </receiver>
421        <receiver
421-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
422            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
422-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
423            android:directBootAware="false"
423-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
424            android:enabled="@bool/enable_system_alarm_service_default"
424-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
425            android:exported="false" >
425-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
426            <intent-filter>
426-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
427                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
427-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
427-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
428            </intent-filter>
429        </receiver>
430        <receiver
430-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
431            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
431-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
432            android:directBootAware="false"
432-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
433            android:enabled="true"
433-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
434            android:exported="true"
434-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
435            android:permission="android.permission.DUMP" >
435-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
436            <intent-filter>
436-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
437                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
437-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
437-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
438            </intent-filter>
439        </receiver>
440
441        <meta-data
441-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aff62db08cff511cc4373673ada3b6cf\transformed\play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
442            android:name="com.google.android.gms.version"
442-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aff62db08cff511cc4373673ada3b6cf\transformed\play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
443            android:value="@integer/google_play_services_version" />
443-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aff62db08cff511cc4373673ada3b6cf\transformed\play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
444
445        <receiver
445-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
446            android:name="androidx.profileinstaller.ProfileInstallReceiver"
446-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
447            android:directBootAware="false"
447-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
448            android:enabled="true"
448-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
449            android:exported="true"
449-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
450            android:permission="android.permission.DUMP" >
450-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
451            <intent-filter>
451-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
452                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
452-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
452-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
453            </intent-filter>
454            <intent-filter>
454-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
455                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
455-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
455-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
456            </intent-filter>
457            <intent-filter>
457-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
458                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
458-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
458-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
459            </intent-filter>
460            <intent-filter>
460-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
461                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
461-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
461-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
462            </intent-filter>
463        </receiver>
464
465        <service
465-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf66c28488d56b085c276c14a4449049\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:26:9-32:19
466            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
466-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf66c28488d56b085c276c14a4449049\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:27:13-103
467            android:exported="false" >
467-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf66c28488d56b085c276c14a4449049\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:28:13-37
468            <meta-data
468-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf66c28488d56b085c276c14a4449049\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:29:13-31:39
469                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
469-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf66c28488d56b085c276c14a4449049\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:30:17-94
470                android:value="cct" />
470-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf66c28488d56b085c276c14a4449049\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:31:17-36
471        </service>
472        <service
472-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18adbb13687610ba2ec263dcbb46b5e9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:24:9-28:19
473            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
473-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18adbb13687610ba2ec263dcbb46b5e9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:25:13-117
474            android:exported="false"
474-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18adbb13687610ba2ec263dcbb46b5e9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:26:13-37
475            android:permission="android.permission.BIND_JOB_SERVICE" >
475-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18adbb13687610ba2ec263dcbb46b5e9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:27:13-69
476        </service>
477
478        <receiver
478-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18adbb13687610ba2ec263dcbb46b5e9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:30:9-32:40
479            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
479-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18adbb13687610ba2ec263dcbb46b5e9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:31:13-132
480            android:exported="false" />
480-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18adbb13687610ba2ec263dcbb46b5e9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:32:13-37
481
482        <service
482-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac9518aeda46c3b802798bcb4651bb33\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
483            android:name="androidx.room.MultiInstanceInvalidationService"
483-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac9518aeda46c3b802798bcb4651bb33\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
484            android:directBootAware="true"
484-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac9518aeda46c3b802798bcb4651bb33\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
485            android:exported="false" />
485-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac9518aeda46c3b802798bcb4651bb33\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
486    </application>
487
488</manifest>
