<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/cyber_neon_gradient_bg"
    tools:context=".WebViewActivity">

    <!-- Toolbar -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/cyber_card_bg"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/back_button"
            android:layout_width="@dimen/icon_size_medium"
            android:layout_height="@dimen/icon_size_medium"
            android:layout_marginStart="@dimen/margin_medium"
            android:padding="@dimen/padding_small"
            android:src="@drawable/ic_back"
            android:tint="@color/cyber_neon_cyan"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/title_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_medium"
            android:layout_marginEnd="@dimen/margin_medium"
            android:ellipsize="end"
            android:fontFamily="@font/font_for_ar_en"
            android:maxLines="1"
            android:text="@string/privacy_policy"
            android:textColor="@color/cyber_text_primary"
            android:textSize="@dimen/text_size_title"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/back_button"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- WebView -->
    <WebView
        android:id="@+id/webview"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar" />

    <!-- Progress Bar -->
    <ProgressBar
        android:id="@+id/progress_bar"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:indeterminate="true"
        android:indeterminateTint="@color/cyber_neon_cyan"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/toolbar" />

</androidx.constraintlayout.widget.ConstraintLayout>
