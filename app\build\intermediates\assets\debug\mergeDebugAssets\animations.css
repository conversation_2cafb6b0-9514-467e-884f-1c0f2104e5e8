/* Speed Lines Animation */
.speed-lines-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: -1;
    overflow: hidden;
}

.speed-line {
    position: absolute;
    height: 2px;
    background: linear-gradient(to left, rgba(0, 255, 255, 0), rgba(0, 255, 255, 0.7), rgba(0, 255, 255, 0));
    transform: translateX(0) skewY(-10deg);
    animation: speedLineAnimation 8s linear infinite;
}

@keyframes speedLineAnimation {
    0% {
        transform: translateX(0) skewY(-10deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateX(-120vw) skewY(-10deg);
        opacity: 0;
    }
}

/* Energy Grid Animation */
.energy-grid {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: -2;
}

.grid-line {
    position: absolute;
    background: linear-gradient(90deg, rgba(0, 255, 255, 0), rgba(0, 255, 255, 0.1), rgba(0, 255, 255, 0));
}

.horizontal {
    width: 100%;
    height: 1px;
    animation: horizontalPulse 10s infinite alternate;
}

.vertical {
    height: 100%;
    width: 1px;
    animation: verticalPulse 10s infinite alternate;
}

@keyframes horizontalPulse {
    0% {
        opacity: 0.1;
    }
    50% {
        opacity: 0.3;
    }
    100% {
        opacity: 0.1;
    }
}

@keyframes verticalPulse {
    0% {
        opacity: 0.1;
    }
    50% {
        opacity: 0.3;
    }
    100% {
        opacity: 0.1;
    }
}

/* Feature Card Animations */
.feature-card, .premium-feature, .screenshot {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.8s, transform 0.8s;
}

.feature-card.animate-in, .premium-feature.animate-in, .screenshot.animate-in {
    opacity: 1;
    transform: translateY(0);
}

/* Title Animations */
.animate-title {
    animation: titleAnimation 2s forwards;
}

.animate-subtitle {
    opacity: 0;
    animation: subtitleAnimation 2s 0.5s forwards;
}

@keyframes titleAnimation {
    0% {
        opacity: 0;
        transform: translateY(-20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes subtitleAnimation {
    0% {
        opacity: 0;
        transform: translateX(-20px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Button Animations */
.subscribe-button {
    position: relative;
    overflow: hidden;
}

.subscribe-button::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 215, 0, 0.3) 0%, rgba(255, 215, 0, 0) 70%);
    transform: scale(0);
    transition: transform 0.5s;
}

.subscribe-button:hover::before {
    transform: scale(1);
}

.subscribe-button.clicked {
    animation: buttonClick 0.5s;
}

@keyframes buttonClick {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(0.95);
    }
    100% {
        transform: scale(1);
    }
}

/* Feature Details Animation */
.details-content {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.5s, transform 0.5s;
}

.details-content.animate-in {
    opacity: 1;
    transform: translateY(0);
}

/* Glow Effects */
.neon-glow {
    animation: neonGlow 2s infinite alternate;
}

@keyframes neonGlow {
    0% {
        text-shadow: 0 0 5px rgba(0, 255, 255, 0.5), 0 0 10px rgba(0, 255, 255, 0.3);
    }
    100% {
        text-shadow: 0 0 10px rgba(0, 255, 255, 0.8), 0 0 20px rgba(0, 255, 255, 0.5), 0 0 30px rgba(0, 255, 255, 0.3);
    }
}

.gold-glow {
    animation: goldGlow 2s infinite alternate;
}

@keyframes goldGlow {
    0% {
        text-shadow: 0 0 5px rgba(255, 215, 0, 0.5), 0 0 10px rgba(255, 215, 0, 0.3);
    }
    100% {
        text-shadow: 0 0 10px rgba(255, 215, 0, 0.8), 0 0 20px rgba(255, 215, 0, 0.5), 0 0 30px rgba(255, 215, 0, 0.3);
    }
}

/* Lightning Effect */
.lightning {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    opacity: 0;
}

.lightning.flash {
    animation: lightningFlash 2s;
}

@keyframes lightningFlash {
    0% {
        opacity: 0;
    }
    10% {
        opacity: 0.8;
    }
    20% {
        opacity: 0;
    }
    30% {
        opacity: 0.6;
    }
    40% {
        opacity: 0;
    }
    100% {
        opacity: 0;
    }
}

/* Hover Effects */
.hover-effect {
    transition: all 0.3s;
}

.hover-effect:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 255, 255, 0.2);
}

/* Pulse Animation */
.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* Floating Animation */
.float {
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
    100% {
        transform: translateY(0px);
    }
}

/* Shake Animation */
.shake {
    animation: shake 0.5s;
}

@keyframes shake {
    0% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    50% { transform: translateX(5px); }
    75% { transform: translateX(-5px); }
    100% { transform: translateX(0); }
}

/* Fade In Animation */
.fade-in {
    animation: fadeIn 1s forwards;
}

@keyframes fadeIn {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}

/* Slide Up Animation */
.slide-up {
    animation: slideUp 1s forwards;
}

@keyframes slideUp {
    0% {
        opacity: 0;
        transform: translateY(30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Rotate Animation */
.rotate {
    animation: rotate 10s linear infinite;
}

@keyframes rotate {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* Bounce Animation */
.bounce {
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-20px);
    }
    60% {
        transform: translateY(-10px);
    }
}

/* Scale Animation */
.scale {
    animation: scale 2s infinite alternate;
}

@keyframes scale {
    0% {
        transform: scale(1);
    }
    100% {
        transform: scale(1.1);
    }
}
