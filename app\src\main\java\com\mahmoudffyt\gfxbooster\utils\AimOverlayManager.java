package com.mahmoudffyt.gfxbooster.utils;


import android.content.Context;
import android.content.SharedPreferences;
import android.graphics.PixelFormat;
import android.os.Build;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import java.lang.reflect.Field;
import java.util.ArrayList;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.Toast;

import com.mahmoudffyt.gfxbooster.R;

/**
 * Utility class to manage the aim overlay that appears on screen
 */
public class AimOverlayManager {
    private Context context;
    private WindowManager windowManager;
    private View overlayView;
    private ImageView aimCrosshair;
    private boolean isOverlayShowing = false;
    private String currentCrosshairColor = "#FF5252"; // Default red
    private int currentCrosshairStyle = 0; // Default style
    private WindowManager.LayoutParams params;
    private SharedPreferences preferences;

    // Default position (center of screen)
    private int positionX = 0;
    private int positionY = 0;

    // Touch position tracking
    private float initialTouchX;
    private float initialTouchY;
    private float initialX;
    private float initialY;

    // Crosshair styles
    public static final int STYLE_SIMPLE = 0;
    public static final int STYLE_PRECISION = 1;
    public static final int STYLE_DOT = 2;

    // Preferences keys
    private static final String PREFS_NAME = "aim_overlay_prefs";
    private static final String KEY_POSITION_X = "position_x";
    private static final String KEY_POSITION_Y = "position_y";
    private static final String KEY_OVERLAY_ENABLED = "overlay_enabled";
    private static final String KEY_OVERLAY_ACTIVE = "overlay_active";

    /**
     * Constructor
     * @param context Application context
     */
    public AimOverlayManager(Context context) {
        this.context = context;
        this.windowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        this.preferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);

        // Log current locale
        String currentLocale = context.getResources().getConfiguration().locale.getLanguage();
        Log.d("AimOverlayManager", "Current locale: " + currentLocale);

        // Load saved position
        positionX = preferences.getInt(KEY_POSITION_X, 0);
        positionY = preferences.getInt(KEY_POSITION_Y, 0);
        Log.d("AimOverlayManager", "Loaded position: X=" + positionX + ", Y=" + positionY);

        // Check if overlay was active when app was closed
        boolean wasActive = preferences.getBoolean(KEY_OVERLAY_ACTIVE, false);
        boolean wasEnabled = preferences.getBoolean(KEY_OVERLAY_ENABLED, false);
        Log.d("AimOverlayManager", "Overlay was active: " + wasActive + ", was enabled: " + wasEnabled);

        initOverlay();

        // Restore overlay if it was active (check both flags for compatibility)
        if ((wasActive || wasEnabled) && Build.VERSION.SDK_INT >= Build.VERSION_CODES.M &&
            android.provider.Settings.canDrawOverlays(context)) {
            Log.d("AimOverlayManager", "Attempting to restore overlay on startup");
            showOverlay();
        }
    }

    /**
     * Initialize the overlay view
     */
    private void initOverlay() {
        try {
            // Log current locale
            String currentLocale = context.getResources().getConfiguration().locale.getLanguage();
            Log.d("AimOverlayManager", "Initializing overlay in locale: " + currentLocale);

            // Inflate the overlay layout
            LayoutInflater inflater = LayoutInflater.from(context);
            overlayView = inflater.inflate(R.layout.aim_overlay, null);

            if (overlayView == null) {
                Log.e("AimOverlayManager", "Failed to inflate overlay view");
                return;
            }

            // Set a tag for the overlay view so we can find it later
            overlayView.setTag("aim_overlay");

            Log.d("AimOverlayManager", "Overlay view inflated successfully");

            // Force LTR layout direction for Arabic locale
            if ("ar".equals(currentLocale)) {
                Log.d("AimOverlayManager", "Arabic locale detected, forcing LTR layout direction in initOverlay");
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
                    overlayView.setLayoutDirection(View.LAYOUT_DIRECTION_LTR);
                }
            }

            // Get the crosshair image view
            aimCrosshair = overlayView.findViewById(R.id.aim_crosshair);

            if (aimCrosshair == null) {
                Log.e("AimOverlayManager", "Failed to find aim_crosshair view");
                return;
            }

            Log.d("AimOverlayManager", "Aim crosshair view found successfully");

            // Set initial crosshair style
            updateCrosshairStyle(currentCrosshairStyle);

            // Make sure the crosshair is visible
            aimCrosshair.setVisibility(View.VISIBLE);

            // Set up close button
            ImageButton closeButton = overlayView.findViewById(R.id.btn_close_overlay);
            if (closeButton != null) {
                closeButton.setOnClickListener(v -> {
                    Log.d("AimOverlayManager", "Close button clicked, hiding overlay");
                    hideOverlay();
                    Toast.makeText(context, R.string.aim_overlay_disabled, Toast.LENGTH_SHORT).show();
                });
            } else {
                Log.e("AimOverlayManager", "Close button not found in overlay");
            }

            // Set up touch listener for dragging
            setupTouchListener();

            Log.d("AimOverlayManager", "Overlay initialization completed successfully");
        } catch (Exception e) {
            Log.e("AimOverlayManager", "Error initializing overlay: " + e.getMessage());
            e.printStackTrace();
        }
    }

    // Track last click time for double-click detection
    private long lastClickTime = 0;
    private float lastClickX = 0;
    private float lastClickY = 0;
    private boolean isDragging = false;

    /**
     * Set up touch listener for dragging the crosshair
     */
    private void setupTouchListener() {
        overlayView.setOnTouchListener((view, event) -> {
            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    // Save initial touch position
                    initialTouchX = event.getRawX();
                    initialTouchY = event.getRawY();

                    // Save initial window position
                    initialX = params.x;
                    initialY = params.y;

                    // Reset dragging flag
                    isDragging = false;
                    return true;

                case MotionEvent.ACTION_MOVE:
                    // Calculate new position
                    float dx = event.getRawX() - initialTouchX;
                    float dy = event.getRawY() - initialTouchY;

                    // Only consider it dragging if moved more than a threshold
                    if (Math.abs(dx) > 10 || Math.abs(dy) > 10) {
                        isDragging = true;
                    }

                    params.x = (int) (initialX + dx);
                    params.y = (int) (initialY + dy);

                    // Update window position
                    try {
                        windowManager.updateViewLayout(overlayView, params);
                    } catch (Exception e) {
                        Log.e("AimOverlayManager", "Error updating view position: " + e.getMessage());
                    }
                    return true;

                case MotionEvent.ACTION_UP:
                    // If we didn't drag much, consider it a tap
                    if (!isDragging) {
                        // Check for double-tap
                        long clickTime = System.currentTimeMillis();
                        float clickX = event.getRawX();
                        float clickY = event.getRawY();

                        // Check if this is close to the last click position and within double-click time
                        boolean isDoubleClick = (clickTime - lastClickTime < 500) && // 500ms for double-click
                                               (Math.abs(clickX - lastClickX) < 50) && // Within 50px
                                               (Math.abs(clickY - lastClickY) < 50);

                        // For Arabic locale, add special handling
                        String currentLocale = context.getResources().getConfiguration().locale.getLanguage();
                        if (isDoubleClick && "ar".equals(currentLocale)) {
                            // This is a double-click in Arabic locale, hide the overlay
                            Log.d("AimOverlayManager", "Double-click detected in Arabic locale, hiding overlay");
                            hideOverlay();

                            // Show toast
                            Toast.makeText(context, R.string.aim_overlay_disabled, Toast.LENGTH_SHORT).show();
                        }

                        // Update last click info
                        lastClickTime = clickTime;
                        lastClickX = clickX;
                        lastClickY = clickY;
                    } else {
                        // Save the final position
                        positionX = params.x;
                        positionY = params.y;
                        savePosition();

                        // Show a toast message
                        Toast.makeText(context, R.string.position_saved, Toast.LENGTH_SHORT).show();
                    }
                    return true;
            }
            return false;
        });
    }

    /**
     * Save the current position to preferences
     */
    private void savePosition() {
        preferences.edit()
                .putInt(KEY_POSITION_X, positionX)
                .putInt(KEY_POSITION_Y, positionY)
                .apply();

        Log.d("AimOverlayManager", "Position saved: X=" + positionX + ", Y=" + positionY);
    }

    /**
     * Show the aim overlay
     */
    public void showOverlay() {
        if (isOverlayShowing) {
            Log.d("AimOverlayManager", "Overlay already showing, ignoring showOverlay call");
            return;
        }

        // Log current locale
        String currentLocale = context.getResources().getConfiguration().locale.getLanguage();
        Log.d("AimOverlayManager", "Showing overlay in locale: " + currentLocale);

        try {
            // Check if overlay view is initialized
            if (overlayView == null) {
                Log.e("AimOverlayManager", "Overlay view is null, reinitializing");
                initOverlay();

                if (overlayView == null) {
                    Log.e("AimOverlayManager", "Failed to reinitialize overlay view");
                    return;
                }
            }

            // Check if window manager is available
            if (windowManager == null) {
                Log.e("AimOverlayManager", "Window manager is null");
                windowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);

                if (windowManager == null) {
                    Log.e("AimOverlayManager", "Failed to get window manager service");
                    return;
                }
            }

            // Create window layout parameters
            params = new WindowManager.LayoutParams(
                    WindowManager.LayoutParams.WRAP_CONTENT,
                    WindowManager.LayoutParams.WRAP_CONTENT,
                    getOverlayType(),
                    WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE |
                            WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL |
                            WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN |
                            WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON |
                            WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS,
                    PixelFormat.TRANSLUCENT);

            // Special handling for Arabic locale
            if ("ar".equals(currentLocale)) {
                Log.d("AimOverlayManager", "Arabic locale detected, applying special handling");

                // Force the overlay view to use LTR
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
                    // Set layout direction for the main view
                    overlayView.setLayoutDirection(View.LAYOUT_DIRECTION_LTR);

                    // Set layout direction for all child views
                    if (overlayView instanceof ViewGroup) {
                        ViewGroup viewGroup = (ViewGroup) overlayView;
                        for (int i = 0; i < viewGroup.getChildCount(); i++) {
                            View child = viewGroup.getChildAt(i);
                            child.setLayoutDirection(View.LAYOUT_DIRECTION_LTR);

                            // For RelativeLayout, ensure RTL-specific attributes are handled correctly
                            if (child instanceof RelativeLayout) {
                                child.setLayoutDirection(View.LAYOUT_DIRECTION_LTR);
                            }
                        }
                    }

                    // Ensure the crosshair is visible
                    if (aimCrosshair != null) {
                        aimCrosshair.setVisibility(View.VISIBLE);
                    }
                }
            }

            // Set position (use saved position or center if none)
            params.gravity = Gravity.TOP | Gravity.START;
            params.x = positionX;
            params.y = positionY;

            Log.d("AimOverlayManager", "Adding overlay view to window manager at position X=" + positionX + ", Y=" + positionY);

            // Add the view to window manager
            windowManager.addView(overlayView, params);
            isOverlayShowing = true;

            // Save overlay state in both places for compatibility
            preferences.edit()
                .putBoolean(KEY_OVERLAY_ACTIVE, true)
                .putBoolean(KEY_OVERLAY_ENABLED, true)
                .apply();

            Log.d("AimOverlayManager", "Overlay state saved: active=true, enabled=true");

            // Show a toast with instructions for first-time users
            if (preferences.getBoolean("first_time", true)) {
                Toast.makeText(context, R.string.drag_to_move_crosshair, Toast.LENGTH_LONG).show();
                preferences.edit().putBoolean("first_time", false).apply();
            }

            // Make sure the overlay is visible
            if (overlayView != null) {
                overlayView.setVisibility(View.VISIBLE);
                if (aimCrosshair != null) {
                    aimCrosshair.setVisibility(View.VISIBLE);
                }
            }

            // Verify that the overlay is actually visible
            if (overlayView != null && overlayView.getVisibility() == View.VISIBLE) {
                Log.d("AimOverlayManager", "Overlay shown successfully at position X=" + positionX + ", Y=" + positionY);

                // Double-check that the crosshair is visible
                if (aimCrosshair != null && aimCrosshair.getVisibility() == View.VISIBLE) {
                    Log.d("AimOverlayManager", "Crosshair is visible and ready");
                } else {
                    Log.w("AimOverlayManager", "Crosshair may not be visible");
                    if (aimCrosshair != null) {
                        aimCrosshair.setVisibility(View.VISIBLE);
                    }
                }
            } else {
                Log.w("AimOverlayManager", "Overlay view may not be visible");
                if (overlayView != null) {
                    overlayView.setVisibility(View.VISIBLE);
                }
            }
        } catch (Exception e) {
            Log.e("AimOverlayManager", "Error showing overlay: " + e.getMessage());
            e.printStackTrace();

            // Reset state in case of error
            isOverlayShowing = false;

            // Clear preferences to avoid stuck state
            preferences.edit()
                .putBoolean(KEY_OVERLAY_ACTIVE, false)
                .putBoolean(KEY_OVERLAY_ENABLED, false)
                .apply();
        }
    }

    /**
     * Hide the aim overlay
     */
    public void hideOverlay() {
        if (!isOverlayShowing) {
            Log.d("AimOverlayManager", "Overlay not showing, ignoring hideOverlay call");
            return;
        }

        // Log current locale
        String currentLocale = context.getResources().getConfiguration().locale.getLanguage();
        Log.d("AimOverlayManager", "Hiding overlay in locale: " + currentLocale);

        // First, try the standard way to hide the overlay
        boolean success = tryHideOverlay();

        // If that fails, try a more aggressive approach
        if (!success && "ar".equals(currentLocale)) {
            Log.d("AimOverlayManager", "Standard hide failed in Arabic locale, trying force hide");
            forceHideOverlay();
        }

        // Make sure the state is updated regardless
        isOverlayShowing = false;

        // Save overlay state in both places for compatibility
        preferences.edit()
            .putBoolean(KEY_OVERLAY_ACTIVE, false)
            .putBoolean(KEY_OVERLAY_ENABLED, false)
            .apply();

        Log.d("AimOverlayManager", "Overlay state saved: active=false, enabled=false");
    }

    /**
     * Try to hide the overlay using the standard approach
     * @return true if successful, false otherwise
     */
    private boolean tryHideOverlay() {
        try {
            // Check if window manager and overlay view are available
            if (windowManager == null || overlayView == null) {
                Log.e("AimOverlayManager", "Window manager or overlay view is null");
                return false;
            }

            // Remove the view from window manager
            windowManager.removeView(overlayView);
            Log.d("AimOverlayManager", "Overlay hidden successfully with standard method");
            return true;
        } catch (Exception e) {
            Log.e("AimOverlayManager", "Error hiding overlay with standard method: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * Force hide the overlay by recreating the window manager and overlay view
     */
    private void forceHideOverlay() {
        try {
            Log.d("AimOverlayManager", "Attempting to force hide overlay");

            // First try to make it invisible
            if (overlayView != null) {
                overlayView.setVisibility(View.GONE);
                if (aimCrosshair != null) {
                    aimCrosshair.setVisibility(View.GONE);
                }
            }

            // Try to remove it if possible
            if (windowManager != null && overlayView != null) {
                try {
                    windowManager.removeView(overlayView);
                } catch (Exception e) {
                    Log.e("AimOverlayManager", "Error removing view in force hide: " + e.getMessage());
                }
            }

            // Try a more aggressive approach for Arabic locale
            String currentLocale = context.getResources().getConfiguration().locale.getLanguage();
            if ("ar".equals(currentLocale)) {
                Log.d("AimOverlayManager", "Arabic locale detected, using more aggressive force hide");

                try {
                    // Try to use reflection to access the WindowManagerGlobal
                    Class<?> wmgClass = Class.forName("android.view.WindowManagerGlobal");
                    Object wmgInstance = wmgClass.getMethod("getInstance").invoke(null);

                    // Try to get the views field
                    Field viewsField = wmgClass.getDeclaredField("mViews");
                    viewsField.setAccessible(true);

                    // Get the list of views
                    ArrayList<View> views = (ArrayList<View>) viewsField.get(wmgInstance);

                    // Look for our overlay view
                    if (views != null) {
                        Log.d("AimOverlayManager", "Found " + views.size() + " views in WindowManagerGlobal");

                        // Try to find and remove our overlay view
                        for (int i = views.size() - 1; i >= 0; i--) {
                            View view = views.get(i);
                            if (view != null && view.getTag() != null &&
                                "aim_overlay".equals(view.getTag())) {
                                Log.d("AimOverlayManager", "Found our overlay view at index " + i);

                                // Try to remove it
                                try {
                                    windowManager.removeView(view);
                                    Log.d("AimOverlayManager", "Successfully removed overlay view with reflection");
                                } catch (Exception e) {
                                    Log.e("AimOverlayManager", "Error removing view with reflection: " + e.getMessage());
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    Log.e("AimOverlayManager", "Error using reflection to force hide: " + e.getMessage());
                }
            }

            // Recreate the window manager
            windowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);

            // Recreate the overlay view
            initOverlay();

            // Make sure the overlay view has a tag so we can find it later
            if (overlayView != null) {
                overlayView.setTag("aim_overlay");
            }

            Log.d("AimOverlayManager", "Overlay force hidden successfully");
        } catch (Exception e) {
            Log.e("AimOverlayManager", "Error force hiding overlay: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Toggle the aim overlay
     * @return true if overlay is now showing, false otherwise
     */
    public boolean toggleOverlay() {
        if (isOverlayShowing) {
            hideOverlay();
            return false;
        } else {
            showOverlay();
            return true;
        }
    }

    /**
     * Check if overlay is showing
     * @return true if overlay is showing, false otherwise
     */
    public boolean isShowing() {
        return isOverlayShowing;
    }

    /**
     * Set the crosshair color
     * @param colorHex Color in hex format (e.g. "#FF5252")
     */
    public void setCrosshairColor(String colorHex) {
        this.currentCrosshairColor = colorHex;
        // Apply color to crosshair
        // Note: This would require custom implementation based on your crosshair images
        // For now, we'll just update the style which will reset the image
        updateCrosshairStyle(currentCrosshairStyle);
    }

    /**
     * Set the crosshair style
     * @param style Style constant (STYLE_SIMPLE, STYLE_PRECISION, STYLE_DOT)
     */
    public void setCrosshairStyle(int style) {
        this.currentCrosshairStyle = style;
        updateCrosshairStyle(style);
    }

    /**
     * Update the crosshair style
     * @param style Style constant
     */
    private void updateCrosshairStyle(int style) {
        if (aimCrosshair == null) {
            Log.e("AimOverlayManager", "Cannot update crosshair style: aimCrosshair is null");
            return;
        }

        switch (style) {
            case STYLE_PRECISION:
                aimCrosshair.setImageResource(R.drawable.crosshair_precision);
                break;
            case STYLE_DOT:
                aimCrosshair.setImageResource(R.drawable.crosshair_dot);
                break;
            case STYLE_SIMPLE:
            default:
                aimCrosshair.setImageResource(R.drawable.crosshair_simple);
                break;
        }

        Log.d("AimOverlayManager", "Crosshair style updated to: " + style);
    }

    /**
     * Update the crosshair size
     * @param size Size value (0-100)
     */
    public void updateCrosshairSize(int size) {
        if (aimCrosshair == null) {
            Log.e("AimOverlayManager", "Cannot update crosshair size: aimCrosshair is null");
            return;
        }

        try {
            // Calculate scale based on size (0-100)
            float scale = 0.5f + (size / 100f);

            // Apply scale to crosshair
            aimCrosshair.setScaleX(scale);
            aimCrosshair.setScaleY(scale);

            Log.d("AimOverlayManager", "Crosshair size updated to: " + size + " (scale: " + scale + ")");
        } catch (Exception e) {
            Log.e("AimOverlayManager", "Error updating crosshair size: " + e.getMessage());
        }
    }

    /**
     * Update the crosshair color
     * @param colorHex Color in hex format (e.g., "#00FFFF")
     */
    public void updateCrosshairColor(String colorHex) {
        if (aimCrosshair == null) {
            Log.e("AimOverlayManager", "Cannot update crosshair color: aimCrosshair is null");
            return;
        }

        try {
            // Parse color
            int color = android.graphics.Color.parseColor(colorHex);

            // Apply color filter to crosshair
            aimCrosshair.setColorFilter(color);

            // Save current color
            currentCrosshairColor = colorHex;

            Log.d("AimOverlayManager", "Crosshair color updated to: " + colorHex);
        } catch (Exception e) {
            Log.e("AimOverlayManager", "Error updating crosshair color: " + e.getMessage());
        }
    }

    /**
     * Get the appropriate overlay type based on Android version
     * @return WindowManager.LayoutParams type
     */
    private int getOverlayType() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            return WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;
        } else {
            return WindowManager.LayoutParams.TYPE_PHONE;
        }
    }

    /**
     * Force refresh the overlay if it's showing but not visible
     * This is especially useful for Arabic locale where the overlay might disappear
     */
    public void refreshOverlay() {
        Log.d("AimOverlayManager", "Refreshing overlay");

        if (!isOverlayShowing) {
            Log.d("AimOverlayManager", "Overlay not showing, nothing to refresh");
            return;
        }

        try {
            // Check if window manager and overlay view are available
            if (windowManager == null || overlayView == null) {
                Log.e("AimOverlayManager", "Window manager or overlay view is null, recreating overlay");
                hideOverlay();
                showOverlay();
                return;
            }

            // Special handling for Arabic locale
            String currentLocale = context.getResources().getConfiguration().locale.getLanguage();
            if ("ar".equals(currentLocale)) {
                Log.d("AimOverlayManager", "Arabic locale detected in refreshOverlay, applying special handling");

                // Force LTR for all views
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
                    overlayView.setLayoutDirection(View.LAYOUT_DIRECTION_LTR);

                    // Set layout direction for all child views
                    if (overlayView instanceof ViewGroup) {
                        ViewGroup viewGroup = (ViewGroup) overlayView;
                        for (int i = 0; i < viewGroup.getChildCount(); i++) {
                            View child = viewGroup.getChildAt(i);
                            child.setLayoutDirection(View.LAYOUT_DIRECTION_LTR);
                        }
                    }
                }
            }

            // Make sure the overlay is visible
            overlayView.setVisibility(View.VISIBLE);
            if (aimCrosshair != null) {
                aimCrosshair.setVisibility(View.VISIBLE);
            }

            // Update the layout params to ensure it's displayed correctly
            if (params != null) {
                windowManager.updateViewLayout(overlayView, params);
                Log.d("AimOverlayManager", "Updated overlay layout params");
            }

            Log.d("AimOverlayManager", "Overlay refreshed successfully");
        } catch (Exception e) {
            Log.e("AimOverlayManager", "Error refreshing overlay: " + e.getMessage());
            e.printStackTrace();

            // Try to recreate the overlay
            try {
                hideOverlay();
                showOverlay();
            } catch (Exception ex) {
                Log.e("AimOverlayManager", "Failed to recreate overlay: " + ex.getMessage());
            }
        }
    }
}
