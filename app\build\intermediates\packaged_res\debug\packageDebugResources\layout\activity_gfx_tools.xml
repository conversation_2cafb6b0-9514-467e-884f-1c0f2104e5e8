<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/gfx_tools_gradient_bg"
    tools:context=".GfxToolsActivity">

    <!-- Speed Lines Background Animation -->
    <ImageView
        android:id="@+id/speed_lines_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:src="@drawable/blue_speed_lines"
        android:alpha="0.4" />

    <!-- Main Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:overScrollMode="never">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Header -->
            <RelativeLayout
                android:id="@+id/header_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp">

                <ImageButton
                    android:id="@+id/btn_back"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:contentDescription="@string/back"
                    android:src="@drawable/ic_back"
                    app:tint="@color/neon_blue" />

                <TextView
                    android:id="@+id/title_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:fontFamily="@font/font_for_ar_en"
                    android:text="@string/gfx_tools_title"
                    android:textColor="@color/neon_blue"
                    android:textSize="28sp"
                    android:textStyle="bold" />

<!--                <ImageView-->
<!--                    android:id="@+id/gfx_logo"-->
<!--                    android:layout_width="48dp"-->
<!--                    android:layout_height="48dp"-->
<!--                    android:layout_alignParentEnd="true"-->
<!--                    android:layout_centerVertical="true"-->
<!--                    android:src="@drawable/ic_gfx_logo"-->
<!--                    android:contentDescription="@string/gfx_logo" />-->
            </RelativeLayout>

            <!-- Device Info Card -->
            <androidx.cardview.widget.CardView
                android:id="@+id/device_info_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardBackgroundColor="#99000000"
                app:cardCornerRadius="16dp"
                app:cardElevation="8dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:fontFamily="@font/font_for_ar_en"
                        android:text="@string/device_info_gfx_tools"
                        android:textColor="@color/neon_blue"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/device_info_text"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="@android:color/white"
                        android:fontFamily="@font/cairo_regular"
                        android:textSize="14sp" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Resolution Card -->
            <androidx.cardview.widget.CardView
                android:id="@+id/resolution_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardBackgroundColor="#99000000"
                app:cardCornerRadius="16dp"
                app:cardElevation="8dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:drawableStart="@drawable/ic_resolution"
                        android:drawablePadding="8dp"
                        android:fontFamily="@font/font_for_ar_en"
                        android:text="@string/resolution_gfx"
                        android:textColor="@color/neon_blue"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <RadioGroup
                        android:id="@+id/resolution_radio_group"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <RadioButton
                            android:id="@+id/resolution_low"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:buttonTint="@color/neon_blue"
                            android:text="@string/low_720p"
                            android:textColor="@android:color/white"
                            android:fontFamily="@font/cairo_regular" />

                        <RadioButton
                            android:id="@+id/resolution_medium"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:buttonTint="@color/neon_blue"
                            android:checked="true"
                            android:text="@string/medium_1080p"
                            android:textColor="@android:color/white"
                            android:fontFamily="@font/cairo_regular" />

                        <RadioButton
                            android:id="@+id/resolution_high"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:buttonTint="@color/neon_blue"
                            android:text="@string/high_1440p"
                            android:textColor="@android:color/white"
                            android:fontFamily="@font/cairo_regular" />

                        <RadioButton
                            android:id="@+id/resolution_ultra"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:buttonTint="@color/neon_blue"
                            android:text="@string/ultra_2160p"
                            android:textColor="@android:color/white"
                            android:fontFamily="@font/cairo_regular" />
                    </RadioGroup>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- FPS Card -->
            <androidx.cardview.widget.CardView
                android:id="@+id/fps_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardBackgroundColor="#99000000"
                app:cardCornerRadius="16dp"
                app:cardElevation="8dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:drawableStart="@drawable/ic_fps"
                        android:drawablePadding="8dp"
                        android:fontFamily="@font/font_for_ar_en"
                        android:text="@string/fps"
                        android:textColor="@color/neon_blue"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <RadioGroup
                        android:id="@+id/fps_radio_group"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <RadioButton
                            android:id="@+id/fps_30"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:buttonTint="@color/neon_blue"
                            android:text="@string/fps_30"
                            android:textColor="@android:color/white"
                            android:fontFamily="@font/cairo_regular" />

                        <RadioButton
                            android:id="@+id/fps_60"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:buttonTint="@color/neon_blue"
                            android:checked="true"
                            android:text="@string/fps_60"
                            android:textColor="@android:color/white"
                            android:fontFamily="@font/cairo_regular" />

                        <RadioButton
                            android:id="@+id/fps_90"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:buttonTint="@color/neon_blue"
                            android:text="@string/fps_90"
                            android:textColor="@android:color/white"
                            android:fontFamily="@font/cairo_regular" />
                    </RadioGroup>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Ad Container -->
            <FrameLayout
                android:id="@+id/ad_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:layout_gravity="center_horizontal"
                android:minHeight="250dp" />

            <!-- Graphics Quality Card -->
            <androidx.cardview.widget.CardView
                android:id="@+id/graphics_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardBackgroundColor="#99000000"
                app:cardCornerRadius="16dp"
                app:cardElevation="8dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:drawableStart="@drawable/ic_graphics"
                        android:drawablePadding="8dp"
                        android:fontFamily="@font/font_for_ar_en"
                        android:text="@string/graphics_quality"
                        android:textColor="@color/neon_blue"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <SeekBar
                        android:id="@+id/graphics_seekbar"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="8dp"
                        android:max="2"
                        android:progress="1"
                        android:progressTint="@color/neon_blue"
                        android:thumbTint="@color/neon_blue" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="start"
                            android:text="@string/low"
                            android:textColor="@android:color/white"
                            android:fontFamily="@font/cairo_regular"
                            android:textSize="12sp" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:text="@string/medium"
                            android:textColor="@android:color/white"
                            android:fontFamily="@font/cairo_regular"
                            android:textSize="12sp" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:text="@string/high"
                            android:textColor="@android:color/white"
                            android:fontFamily="@font/cairo_regular"
                            android:textSize="12sp" />
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Advanced Options Card -->
            <androidx.cardview.widget.CardView
                android:id="@+id/advanced_options_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardBackgroundColor="#99000000"
                app:cardCornerRadius="16dp"
                app:cardElevation="8dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:drawableStart="@drawable/ic_advanced"
                        android:drawablePadding="8dp"
                        android:fontFamily="@font/font_for_ar_en"
                        android:text="@string/advanced_options_gfx"
                        android:textColor="@color/neon_blue"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="12dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/hdr_mode_gfx"
                            android:textColor="@android:color/white"
                            android:fontFamily="@font/cairo_regular"
                            android:textSize="16sp" />

                        <com.google.android.material.switchmaterial.SwitchMaterial
                            android:id="@+id/switch_hdr"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="false"
                            app:thumbTint="@color/neon_blue"
                            app:trackTint="@color/switch_track_color" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="12dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/anti_aliasing_gfx"
                            android:textColor="@android:color/white"
                            android:fontFamily="@font/cairo_regular"
                            android:textSize="16sp" />

                        <com.google.android.material.switchmaterial.SwitchMaterial
                            android:id="@+id/switch_antialiasing"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="true"
                            app:thumbTint="@color/neon_blue"
                            app:trackTint="@color/switch_track_color" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/gpu_boost_gfx"
                            android:textColor="@android:color/white"
                            android:fontFamily="@font/cairo_regular"
                            android:textSize="16sp" />

                        <com.google.android.material.switchmaterial.SwitchMaterial
                            android:id="@+id/switch_gpu_boost"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="false"
                            app:thumbTint="@color/neon_blue"
                            app:trackTint="@color/switch_track_color" />
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Suggest Best Settings Button -->
            <Button
                android:id="@+id/suggest_settings_button"
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="8dp"
                android:fontFamily="@font/cairo_regular"
                android:text="@string/suggest_best_settings"
                android:textColor="@android:color/white"
                android:textSize="18sp"
                android:textStyle="bold"
                android:background="@drawable/neon_button_bg_cyan" />

            <!-- Apply Button -->
            <Button
                android:id="@+id/apply_button"
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="16dp"
                android:fontFamily="@font/cairo_regular"
                android:text="@string/apply_settings_gfx"
                android:textColor="@android:color/white"
                android:textSize="18sp"
                android:textStyle="bold"
                android:background="@drawable/neon_button_bg" />

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.coordinatorlayout.widget.CoordinatorLayout>
