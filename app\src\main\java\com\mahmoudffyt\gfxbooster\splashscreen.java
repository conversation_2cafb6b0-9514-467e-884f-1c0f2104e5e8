package com.mahmoudffyt.gfxbooster;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.view.View;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;

import com.airbnb.lottie.LottieAnimationView;

public class splashscreen extends AppCompatActivity {
    private TextView logoMain, logoSub, powerText;
    private FrameLayout logoContainer;
    private LottieAnimationView lightningAnim;
    private ImageView logoBg, crosshairLines;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // Set fullscreen
        getWindow().setFlags(
                WindowManager.LayoutParams.FLAG_FULLSCREEN,
                WindowManager.LayoutParams.FLAG_FULLSCREEN
        );
        setContentView(R.layout.activity_splashscreen);

        // Initialize views
        initializeViews();

        // Start animations
        startAnimations();

        // The background is now a static gradient, no need to animate it
        // We'll add a different animation effect instead
        try {
            View mainView = findViewById(R.id.main);
            Animation fadeIn = AnimationUtils.loadAnimation(this, android.R.anim.fade_in);
            fadeIn.setDuration(1500);
            mainView.startAnimation(fadeIn);
        } catch (Exception e) {
            e.printStackTrace();
        }

        // Navigate to main activity after delay
        new Handler().postDelayed(() -> {
            Intent i = new Intent(splashscreen.this, MainActivity.class);
            startActivity(i);
            overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out);
            finish();
        }, 4000); // 4 seconds delay
    }

    private void initializeViews() {
        logoMain = findViewById(R.id.logo_main);
        logoSub = findViewById(R.id.logo_sub);
        powerText = findViewById(R.id.power_text);
        logoContainer = findViewById(R.id.logo_container);
        lightningAnim = findViewById(R.id.lightning_anim);
        logoBg = findViewById(R.id.logo_bg);
        crosshairLines = findViewById(R.id.crosshair_lines);
    }

    private void startAnimations() {
        // Logo container animation with bounce
        Animation bounceAnimation = AnimationUtils.loadAnimation(this, R.anim.slide_down_bounce);
        logoContainer.startAnimation(bounceAnimation);

        // Logo rotation animation with enhanced speed
       // Animation rotateAnimation = AnimationUtils.loadAnimation(this, R.anim.rotate_animation);
       // rotateAnimation.setDuration(10000); // Slower rotation for more subtle effect
       // logoBg.startAnimation(rotateAnimation);

        // Crosshair lines animation (pulsing in/out)
        Animation linesAnimation = AnimationUtils.loadAnimation(this, R.anim.lines_animation);
        crosshairLines.startAnimation(linesAnimation);

        // Main title animation with glow effect
        Animation slideInLeft = AnimationUtils.loadAnimation(this, R.anim.slide_in_left);
        logoMain.startAnimation(slideInLeft);

        // Add glow pulse after initial animation
        logoMain.postDelayed(() -> {
            Animation glowPulse = AnimationUtils.loadAnimation(this, R.anim.glow_pulse_repeat);
            logoMain.startAnimation(glowPulse);
        }, 1500);

        // Subtitle animation (slide from right)
        Animation slideInRight = AnimationUtils.loadAnimation(this, R.anim.slide_in_right);
        logoSub.startAnimation(slideInRight);

        // Power text fade in with pulse
        Animation fadeIn = AnimationUtils.loadAnimation(this, android.R.anim.fade_in);
        fadeIn.setDuration(1500);
        fadeIn.setStartOffset(1000);
        powerText.startAnimation(fadeIn);

        // Add pulse effect after fade in
        powerText.postDelayed(() -> {
            Animation pulse = AnimationUtils.loadAnimation(this, R.anim.glow_pulse_repeat);
            powerText.startAnimation(pulse);
        }, 2500);
    }
}
