<?xml version="1.0" encoding="utf-8"?>
<set xmlns:android="http://schemas.android.com/apk/res/android"
    android:interpolator="@android:interpolator/accelerate_decelerate">

    <!-- Scale up the rocket -->
    <scale
        android:duration="300"
        android:fromXScale="0.0"
        android:fromYScale="0.0"
        android:pivotX="50%"
        android:pivotY="50%"
        android:toXScale="1.0"
        android:toYScale="1.0" />

    <!-- Circular path animation is handled in code using Path animation -->
    <!-- This is just a placeholder for compatibility -->
    <translate
        android:duration="2500"
        android:fromYDelta="0%"
        android:toYDelta="-100%"
        android:startOffset="2000" />

</set>
