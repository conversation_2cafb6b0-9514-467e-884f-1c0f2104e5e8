<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Shadow -->
    <item>
        <shape android:shape="rectangle">
            <corners android:radius="16dp" />
            <padding
                android:bottom="3dp"
                android:left="1dp"
                android:right="3dp"
                android:top="1dp" />
            <solid android:color="@color/shadow_color" />
        </shape>
    </item>

    <!-- Button background with gradient -->
    <item>
        <shape android:shape="rectangle">
            <corners android:radius="16dp" />
            <gradient
                android:angle="135"
                android:endColor="@color/gradient_end"
                android:startColor="@color/gradient_start"
                android:type="linear" />
        </shape>
    </item>

    <!-- Inner white area -->
    <item android:bottom="2dp" android:left="1dp" android:right="2dp" android:top="1dp">
        <shape android:shape="rectangle">
            <corners android:radius="14dp" />
            <solid android:color="@color/button_background" />
        </shape>
    </item>

    <!-- Highlight at top -->
    <item android:top="2dp" android:left="2dp" android:right="2dp" android:bottom="30dp">
        <shape android:shape="rectangle">
            <corners android:topLeftRadius="12dp" android:topRightRadius="12dp" />
            <gradient
                android:angle="270"
                android:endColor="#00FFFFFF"
                android:startColor="#40FFFFFF"
                android:type="linear" />
        </shape>
    </item>
</layer-list>
