package com.mahmoudffyt.gfxbooster;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.mahmoudffyt.gfxbooster.adapters.GameSelectionAdapter;
import com.mahmoudffyt.gfxbooster.utils.GameDetector;
import com.mahmoudffyt.gfxbooster.utils.GameDetectorAndroid14;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class GameSelectionActivity extends AppCompatActivity {
    
    private RecyclerView recyclerView;
    private GameSelectionAdapter adapter;
    private ProgressBar progressBar;
    private TextView statusText;
    private ImageView btnBack;
    
    private ExecutorService executorService;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_game_selection);
        
        initViews();
        setupRecyclerView();
        loadGames();
    }
    
    private void initViews() {
        recyclerView = findViewById(R.id.games_recycler_view);
        progressBar = findViewById(R.id.progress_bar);
        statusText = findViewById(R.id.status_text);
        btnBack = findViewById(R.id.btn_back);
        
        btnBack.setOnClickListener(v -> finish());
        
        executorService = Executors.newSingleThreadExecutor();
    }
    
    private void setupRecyclerView() {
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        adapter = new GameSelectionAdapter(this, (appInfo) -> {
            // Return selected game to GameModeActivity
            Intent resultIntent = new Intent();
            resultIntent.putExtra("selected_game_name", appInfo.appName);
            resultIntent.putExtra("selected_game_package", appInfo.packageName);
            setResult(RESULT_OK, resultIntent);
            finish();
        });
        recyclerView.setAdapter(adapter);
    }
    
    private void loadGames() {
        progressBar.setVisibility(View.VISIBLE);
        statusText.setText("جاري البحث عن الألعاب والتطبيقات...");
        statusText.setVisibility(View.VISIBLE);
        recyclerView.setVisibility(View.GONE);

        executorService.execute(() -> {
            // Use Android 14 optimized detection
            List<GameDetectorAndroid14.AppInfo> games = GameDetectorAndroid14.getInstalledGames(this);

            // If no games found, get all user apps
            if (games.isEmpty()) {
                runOnUiThread(() -> {
                    statusText.setText("جاري البحث في جميع التطبيقات...");
                });
                games = GameDetectorAndroid14.getAllUserApps(this);
            }

            final List<GameDetectorAndroid14.AppInfo> finalGames = games;
            final int gameCount = countGames(games);
            final int totalCount = games.size();

            runOnUiThread(() -> {
                progressBar.setVisibility(View.GONE);

                if (finalGames.isEmpty()) {
                    statusText.setText("لم يتم العثور على تطبيقات مثبتة");
                    statusText.setVisibility(View.VISIBLE);
                } else {
                    // Show count information
                    String countText = "تم العثور على " + totalCount + " تطبيق";
                    if (gameCount > 0) {
                        countText += " (" + gameCount + " لعبة)";
                    }
                    statusText.setText(countText);
                    statusText.setVisibility(View.VISIBLE);

                    recyclerView.setVisibility(View.VISIBLE);
                    adapter.updateGames(finalGames);
                }
            });
        });
    }

    private int countGames(List<GameDetectorAndroid14.AppInfo> apps) {
        int count = 0;
        for (GameDetectorAndroid14.AppInfo app : apps) {
            if (app.isGame) {
                count++;
            }
        }
        return count;
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
    }
}
