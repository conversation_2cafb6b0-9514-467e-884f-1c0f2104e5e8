package com.mahmoudffyt.gfxbooster;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.mahmoudffyt.gfxbooster.adapters.GameSelectionAdapter;
import com.mahmoudffyt.gfxbooster.utils.GameDetector;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class GameSelectionActivity extends AppCompatActivity {
    
    private RecyclerView recyclerView;
    private GameSelectionAdapter adapter;
    private ProgressBar progressBar;
    private TextView statusText;
    private ImageView btnBack;
    
    private ExecutorService executorService;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_game_selection);
        
        initViews();
        setupRecyclerView();
        loadGames();
    }
    
    private void initViews() {
        recyclerView = findViewById(R.id.games_recycler_view);
        progressBar = findViewById(R.id.progress_bar);
        statusText = findViewById(R.id.status_text);
        btnBack = findViewById(R.id.btn_back);
        
        btnBack.setOnClickListener(v -> finish());
        
        executorService = Executors.newSingleThreadExecutor();
    }
    
    private void setupRecyclerView() {
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        adapter = new GameSelectionAdapter(this, (appInfo) -> {
            // Return selected game to GameModeActivity
            Intent resultIntent = new Intent();
            resultIntent.putExtra("selected_game_name", appInfo.appName);
            resultIntent.putExtra("selected_game_package", appInfo.packageName);
            setResult(RESULT_OK, resultIntent);
            finish();
        });
        recyclerView.setAdapter(adapter);
    }
    
    private void loadGames() {
        progressBar.setVisibility(View.VISIBLE);
        statusText.setText("جاري البحث عن الألعاب...");
        recyclerView.setVisibility(View.GONE);
        
        executorService.execute(() -> {
            // First try to get games using CATEGORY_GAME
            List<GameDetector.AppInfo> games = GameDetector.getInstalledGames(this);
            
            // If no games found, get all user apps
            if (games.isEmpty()) {
                games = GameDetector.getAllUserApps(this);
            }
            
            final List<GameDetector.AppInfo> finalGames = games;
            
            runOnUiThread(() -> {
                progressBar.setVisibility(View.GONE);
                
                if (finalGames.isEmpty()) {
                    statusText.setText("لم يتم العثور على ألعاب مثبتة");
                    statusText.setVisibility(View.VISIBLE);
                } else {
                    statusText.setVisibility(View.GONE);
                    recyclerView.setVisibility(View.VISIBLE);
                    adapter.updateGames(finalGames);
                }
            });
        });
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
    }
}
