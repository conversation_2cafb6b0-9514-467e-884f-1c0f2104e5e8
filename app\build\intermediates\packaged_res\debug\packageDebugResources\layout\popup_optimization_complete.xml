<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="16dp"
    app:cardBackgroundColor="#1A1A1A"
    app:cardCornerRadius="16dp"
    app:cardElevation="8dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header with icon -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingBottom="16dp">

            <ImageView
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:contentDescription="@string/app_name"
                android:src="@drawable/ic_check_circle"
                app:tint="@color/vibrant_green" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:fontFamily="@font/font_for_ar_en"
                android:text="@string/optimization_complete"
                android:textColor="@color/text_color_primary"
                android:textSize="20sp" />
        </LinearLayout>

        <!-- Divider -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#333333" />

        <!-- Content -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/font_for_ar_en"
                android:text="@string/device_optimized_description"
                android:textColor="@color/text_color_secondary"
                android:textSize="16sp" />

            <!-- System stats -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:orientation="horizontal">

                <!-- Before/After CPU -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="sans-serif-light"
                        android:text="@string/cpu"
                        android:textColor="@color/text_color_secondary"
                        android:textSize="12sp" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/popup_cpu_before"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="sans-serif-medium"
                            android:text="75%"
                            android:textColor="@color/vibrant_orange"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="sans-serif-medium"
                            android:text=" → "
                            android:textColor="@color/text_color_secondary"
                            android:textSize="16sp" />

                        <TextView
                            android:id="@+id/popup_cpu_after"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="sans-serif-medium"
                            android:text="25%"
                            android:textColor="@color/vibrant_green"
                            android:textSize="16sp" />
                    </LinearLayout>
                </LinearLayout>

                <!-- Before/After Memory -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="sans-serif-light"
                        android:text="@string/ram"
                        android:textColor="@color/text_color_secondary"
                        android:textSize="12sp" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/popup_memory_before"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="sans-serif-medium"
                            android:text="68%"
                            android:textColor="@color/vibrant_orange"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="sans-serif-medium"
                            android:text=" → "
                            android:textColor="@color/text_color_secondary"
                            android:textSize="16sp" />

                        <TextView
                            android:id="@+id/popup_memory_after"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="sans-serif-medium"
                            android:text="32%"
                            android:textColor="@color/vibrant_green"
                            android:textSize="16sp" />
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>

            <!-- Optimized items -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:orientation="vertical">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/font_for_ar_en"
                    android:text="@string/optimized_items"
                    android:textColor="@color/text_color_primary"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/popup_apps_closed"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:drawablePadding="8dp"
                    android:fontFamily="sans-serif"
                    android:gravity="center_vertical"
                    android:text="@string/apps_closed_count"
                    android:textColor="@color/text_color_secondary"
                    android:textSize="14sp"
                    app:drawableStartCompat="@drawable/ic_check_small"
                    app:drawableTint="@color/vibrant_green" />

                <TextView
                    android:id="@+id/popup_cache_cleared"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:drawablePadding="8dp"
                    android:fontFamily="sans-serif"
                    android:gravity="center_vertical"
                    android:text="@string/cache_cleared_size"
                    android:textColor="@color/text_color_secondary"
                    android:textSize="14sp"
                    app:drawableStartCompat="@drawable/ic_check_small"
                    app:drawableTint="@color/vibrant_green" />

                <TextView
                    android:id="@+id/popup_ram_optimized"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:drawablePadding="8dp"
                    android:fontFamily="sans-serif"
                    android:gravity="center_vertical"
                    android:text="@string/ram_optimized"
                    android:textColor="@color/text_color_secondary"
                    android:textSize="14sp"
                    app:drawableStartCompat="@drawable/ic_check_small"
                    app:drawableTint="@color/vibrant_green" />
            </LinearLayout>
        </LinearLayout>

        <!-- Button -->
        <Button
            android:id="@+id/popup_done_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="8dp"
            android:background="@drawable/boost_button_background"
            android:fontFamily="@font/font_for_ar_en"
            android:text="@string/done"
            android:textColor="@color/text_color_primary" />
    </LinearLayout>
</androidx.cardview.widget.CardView>
