<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Outer frame shadow -->
    <item>
        <shape android:shape="rectangle">
            <corners android:radius="30dp" />
            <solid android:color="#50000000" />
            <padding
                android:bottom="5dp"
                android:left="5dp"
                android:right="5dp"
                android:top="5dp" />
        </shape>
    </item>

    <!-- Phone frame -->
    <item>
        <shape android:shape="rectangle">
            <corners android:radius="25dp" />
            <gradient
                android:angle="135"
                android:endColor="@color/gradient_end"
                android:centerColor="@color/gradient_center"
                android:startColor="@color/gradient_start"
                android:type="linear" />
            <stroke
                android:width="2dp"
                android:color="@color/accent_color_dim" />
        </shape>
    </item>

    <!-- Screen area -->
    <item
        android:bottom="12dp"
        android:left="8dp"
        android:right="8dp"
        android:top="12dp">
        <shape android:shape="rectangle">
            <corners android:radius="15dp" />
            <solid android:color="@color/main_background" />
        </shape>
    </item>

    <!-- Notch at top -->
    <item
        android:bottom="0dp"
        android:gravity="center_horizontal|top"
        android:left="0dp"
        android:right="0dp"
        android:top="2dp">
        <shape android:shape="rectangle">
            <size
                android:width="80dp"
                android:height="25dp" />
            <corners android:radius="15dp" />
            <solid android:color="#0f2027" />
        </shape>
    </item>
</layer-list>
