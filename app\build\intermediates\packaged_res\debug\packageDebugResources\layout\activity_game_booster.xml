<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/cyber_neon_gradient_bg"
    android:fillViewport="true"
    tools:context=".GameBoosterActivity">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- Speed lines overlay -->
        <ImageView
            android:id="@+id/energy_lines"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:alpha="0.4"
            android:contentDescription="@string/app_name"
            android:scaleType="fitXY"
            android:src="@drawable/blue_speed_lines" />

        <!-- Particle effects overlay -->
        <ImageView
            android:id="@+id/particles"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:alpha="0.2"
            android:contentDescription="@string/app_name"
            android:scaleType="fitXY"
            android:src="@drawable/particle_effect" />

        <!-- Header with back button -->
        <LinearLayout
            android:id="@+id/header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="16dp">

            <ImageView
                android:id="@+id/btn_back"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:contentDescription="@string/app_name"
                android:src="@drawable/ic_back"
                app:tint="@color/vibrant_blue" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:fontFamily="@font/font_for_ar_en"
                android:gravity="center"
                android:letterSpacing="0.1"
                android:shadowColor="@color/vibrant_blue"
                android:shadowDx="0"
                android:shadowDy="0"
                android:shadowRadius="10"
                android:text="@string/game_booster_title"
                android:textColor="@color/text_color_primary"
                android:textSize="24sp"
                android:textStyle="bold" />

            <View
                android:layout_width="32dp"
                android:layout_height="32dp" />
        </LinearLayout>

        <!-- Main content -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/header"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Description -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:fontFamily="@font/cairo_regular"
                android:text="@string/game_booster_description"
                android:textColor="@color/text_color_primary"
                android:textSize="16sp" />

            <!-- CPU Status Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardBackgroundColor="@android:color/transparent"
                app:cardCornerRadius="16dp"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/card_background"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:contentDescription="@string/app_name"
                            android:src="@drawable/ic_cpu" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:fontFamily="@font/font_for_ar_en"
                            android:text="@string/cpu_status"
                            android:textColor="@color/vibrant_blue"
                            android:textSize="18sp" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:orientation="horizontal">

                        <!-- Before optimization -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/cairo_regular"
                                android:text="@string/before"
                                android:textColor="@color/text_color_secondary"
                                android:textSize="14sp" />

                            <FrameLayout
                                android:layout_width="100dp"
                                android:layout_height="100dp"
                                android:layout_margin="8dp">

                                <ProgressBar
                                    android:id="@+id/cpu_before_progress"
                                    style="?android:attr/progressBarStyleHorizontal"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:indeterminate="false"
                                    android:max="100"
                                    android:progress="75"
                                    android:progressDrawable="@drawable/circular_progress_bar" />

                                <TextView
                                    android:id="@+id/cpu_before_text"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center"
                                    android:fontFamily="@font/cairo_regular"
                                    android:text="75%"
                                    android:textColor="@color/text_color_primary"
                                    android:textSize="20sp" />
                            </FrameLayout>
                        </LinearLayout>

                        <!-- After optimization -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/cairo_regular"
                                android:text="@string/after"
                                android:textColor="@color/text_color_secondary"
                                android:textSize="14sp" />

                            <FrameLayout
                                android:layout_width="100dp"
                                android:layout_height="100dp"
                                android:layout_margin="8dp">

                                <ProgressBar
                                    android:id="@+id/cpu_after_progress"
                                    style="?android:attr/progressBarStyleHorizontal"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:indeterminate="false"
                                    android:max="100"
                                    android:progress="25"
                                    android:progressDrawable="@drawable/circular_progress_bar" />

                                <TextView
                                    android:id="@+id/cpu_after_text"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center"
                                    android:fontFamily="@font/cairo_regular"
                                    android:text="25%"
                                    android:textColor="@color/text_color_primary"
                                    android:textSize="20sp" />
                            </FrameLayout>
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Memory Status Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardBackgroundColor="@android:color/transparent"
                app:cardCornerRadius="16dp"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/card_background"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:contentDescription="@string/app_name"
                            android:src="@drawable/ic_memory" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:fontFamily="@font/font_for_ar_en"
                            android:text="@string/memory_status"
                            android:textColor="@color/vibrant_blue"
                            android:textSize="18sp" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:orientation="horizontal">

                        <!-- Memory info -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/cairo_regular"
                                android:text="@string/total_memory"
                                android:textColor="@color/text_color_secondary"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/total_memory"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/cairo_regular"
                                android:text="4.0 GB"
                                android:textColor="@color/text_color_primary"
                                android:textSize="18sp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="16dp"
                                android:fontFamily="@font/cairo_regular"
                                android:text="@string/used_memory"
                                android:textColor="@color/text_color_secondary"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/used_memory"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/cairo_regular"
                                android:text="2.8 GB"
                                android:textColor="@color/text_color_primary"
                                android:textSize="18sp" />
                        </LinearLayout>

                        <!-- Memory progress -->
                        <FrameLayout
                            android:layout_width="120dp"
                            android:layout_height="120dp"
                            android:layout_gravity="center_vertical">

                            <ProgressBar
                                android:id="@+id/memory_progress"
                                style="?android:attr/progressBarStyleHorizontal"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:indeterminate="false"
                                android:max="100"
                                android:progress="70"
                                android:progressDrawable="@drawable/circular_progress_bar" />

                            <TextView
                                android:id="@+id/memory_percent"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:fontFamily="@font/cairo_regular"
                                android:text="70%"
                                android:textColor="@color/text_color_primary"
                                android:textSize="24sp" />
                        </FrameLayout>
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Ad Container -->
            <FrameLayout
                android:id="@+id/ad_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="16dp"
                android:background="#33FFFFFF"
                android:minHeight="100dp"
                android:visibility="visible" />

            <!-- RAM Speed Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardBackgroundColor="@android:color/transparent"
                app:cardCornerRadius="16dp"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/card_background"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:contentDescription="@string/app_name"
                            android:src="@drawable/ic_memory" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:fontFamily="@font/font_for_ar_en"
                            android:text="@string/ram_speed"
                            android:textColor="@color/vibrant_blue"
                            android:textSize="18sp" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:orientation="horizontal">

                        <!-- Before optimization -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/cairo_regular"
                                android:text="@string/before"
                                android:textColor="@color/text_color_secondary"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/ram_speed_before"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:fontFamily="@font/cairo_regular"
                                android:text="1240 MB/s"
                                android:textColor="@color/vibrant_orange"
                                android:textSize="20sp" />
                        </LinearLayout>

                        <!-- After optimization -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/cairo_regular"
                                android:text="@string/after"
                                android:textColor="@color/text_color_secondary"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/ram_speed_after"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:fontFamily="@font/cairo_regular"
                                android:text="--"
                                android:textColor="@color/vibrant_green"
                                android:textSize="20sp" />
                        </LinearLayout>
                    </LinearLayout>

                    <ProgressBar
                        android:id="@+id/ram_speed_progress"
                        style="@android:style/Widget.ProgressBar.Horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="8dp"
                        android:layout_marginTop="16dp"
                        android:max="100"
                        android:progress="0"
                        android:progressDrawable="@drawable/boost_button_background" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Background Apps Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardBackgroundColor="@android:color/transparent"
                app:cardCornerRadius="16dp"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/card_background"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:contentDescription="@string/app_name"
                            android:src="@drawable/ic_apps" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:fontFamily="@font/font_for_ar_en"
                            android:text="@string/high_usage_apps"
                            android:textColor="@color/vibrant_blue"
                            android:textSize="18sp" />
                    </LinearLayout>

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:fontFamily="@font/cairo_regular"
                        android:text="@string/apps_description"
                        android:textColor="@color/text_color_secondary"
                        android:textSize="14sp" />

                    <LinearLayout
                        android:id="@+id/apps_container"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:orientation="vertical">

                        <!-- App items will be added here dynamically -->
                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:drawablePadding="8dp"
                            android:fontFamily="@font/cairo_regular"
                            android:gravity="center_vertical"
                            android:padding="8dp"
                            android:text="Facebook"
                            android:textColor="@color/text_color_primary"
                            app:drawableStartCompat="@android:drawable/sym_def_app_icon" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:drawablePadding="8dp"
                            android:fontFamily="@font/cairo_regular"
                            android:gravity="center_vertical"
                            android:padding="8dp"
                            android:text="Instagram"
                            android:textColor="@color/text_color_primary"
                            app:drawableStartCompat="@android:drawable/sym_def_app_icon" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:drawablePadding="8dp"
                            android:fontFamily="@font/cairo_regular"
                            android:gravity="center_vertical"
                            android:padding="8dp"
                            android:text="Chrome"
                            android:textColor="@color/text_color_primary"
                            app:drawableStartCompat="@android:drawable/sym_def_app_icon" />
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Rocket Animation Container -->
            <FrameLayout
                android:id="@+id/rocket_container"
                android:layout_width="match_parent"
                android:layout_height="300dp"
                android:layout_marginBottom="16dp"
                android:visibility="gone">

                <ImageView
                    android:id="@+id/rocket_image"
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:layout_gravity="bottom|center_horizontal"
                    android:contentDescription="@string/app_name"
                    android:src="@drawable/ic_rocket"
                    app:tint="@color/vibrant_blue" />
            </FrameLayout>

            <!-- Boost Button -->
            <Button
                android:id="@+id/btn_boost"
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:layout_marginBottom="24dp"
                android:background="@drawable/boost_button_background"
                android:drawablePadding="8dp"
                android:fontFamily="@font/fonts"
                android:letterSpacing="0.1"
                android:text="@string/boost_now"
                android:textColor="@color/text_color_primary"
                android:textSize="20sp"
                app:drawableLeftCompat="@drawable/ic_rocket"
                app:drawableTint="@color/text_color_primary" />

            <!-- Status Text -->
            <TextView
                android:id="@+id/status_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:fontFamily="@font/cairo_regular"
                android:gravity="center"
                android:text="@string/ready_to_optimize"
                android:textColor="@color/vibrant_blue"
                android:textSize="16sp" />

            <!-- Important Disclaimer Notice -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:layout_marginHorizontal="8dp"
                app:cardBackgroundColor="#1A1A1A"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="12dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_info"
                        android:layout_marginEnd="8dp"
                        app:tint="@color/cyber_neon_cyan" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:fontFamily="@font/cairo_regular"
                        android:text="@string/performance_notice"
                        android:textColor="@color/cyber_neon_cyan"
                        android:textSize="11sp"
                        android:lineSpacingExtra="2dp" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>
        </LinearLayout>
    </RelativeLayout>
</androidx.core.widget.NestedScrollView>