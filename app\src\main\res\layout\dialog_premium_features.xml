<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="16dp"
    app:cardBackgroundColor="@color/cyber_card_bg"
    app:cardCornerRadius="16dp"
    app:cardElevation="8dp">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Header with glow effect -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/premium_header_bg"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="16dp">

                <ImageView
                    android:layout_width="64dp"
                    android:layout_height="64dp"
                    android:layout_marginBottom="8dp"
                    android:src="@drawable/ic_premium_crown" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/orbitron_bold"
                    android:text="@string/premium_features"
                    android:textAlignment="center"
                    android:textColor="@color/cyber_neon_gold"
                    android:textSize="22sp"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:fontFamily="@font/rajdhani_bold"
                    android:text="@string/premium_features_description"
                    android:textAlignment="center"
                    android:textColor="@android:color/white"
                    android:textSize="16sp" />
            </LinearLayout>

            <!-- Premium Features List -->
            <LinearLayout
                android:id="@+id/premium_features_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:orientation="vertical">

                <!-- Feature 1 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_check_circle"
                        app:tint="@color/cyber_neon_gold" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:fontFamily="@font/rajdhani_bold"
                        android:text="@string/premium_feature_aim_overlay"
                        android:textColor="@android:color/white"
                        android:textSize="16sp" />
                </LinearLayout>

                <!-- Feature 2 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_check_circle"
                        app:tint="@color/cyber_neon_gold" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:fontFamily="@font/rajdhani_bold"
                        android:text="@string/premium_feature_smart_aim"
                        android:textColor="@android:color/white"
                        android:textSize="16sp" />
                </LinearLayout>

                <!-- Feature 3 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_check_circle"
                        app:tint="@color/cyber_neon_gold" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:fontFamily="@font/rajdhani_bold"
                        android:text="@string/premium_feature_aim_button"
                        android:textColor="@android:color/white"
                        android:textSize="16sp" />
                </LinearLayout>

                <!-- Feature 4 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_check_circle"
                        app:tint="@color/cyber_neon_gold" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:fontFamily="@font/rajdhani_bold"
                        android:text="@string/premium_feature_no_ads"
                        android:textColor="@android:color/white"
                        android:textSize="16sp" />
                </LinearLayout>

                <!-- Feature 5 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_check_circle"
                        app:tint="@color/cyber_neon_gold" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:fontFamily="@font/rajdhani_bold"
                        android:text="@string/premium_feature_priority_updates"
                        android:textColor="@android:color/white"
                        android:textSize="16sp" />
                </LinearLayout>

                <!-- Feature 6 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_check_circle"
                        app:tint="@color/cyber_neon_gold" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:fontFamily="@font/rajdhani_bold"
                        android:text="@string/premium_feature_exclusive_content"
                        android:textColor="@android:color/white"
                        android:textSize="16sp" />
                </LinearLayout>
            </LinearLayout>

            <!-- Price Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:background="@drawable/premium_price_bg"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="12dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/orbitron_bold"
                    android:text="@string/premium_price_title"
                    android:textColor="@color/cyber_neon_gold"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/premium_price"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:fontFamily="@font/orbitron_bold"
                    android:text="@string/loading_price"
                    android:textColor="@android:color/white"
                    android:textSize="24sp"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:fontFamily="@font/rajdhani_bold"
                    android:text="@string/premium_price_description"
                    android:textAlignment="center"
                    android:textColor="@android:color/white"
                    android:textSize="14sp" />
            </LinearLayout>

            <!-- Action Buttons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:orientation="vertical">

                <Button
                    android:id="@+id/btn_subscribe"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/neon_button_bg_premium"
                    android:fontFamily="@font/rajdhani_bold"
                    android:padding="12dp"
                    android:text="@string/subscribe_now"
                    android:textColor="#000000"
                    android:textSize="16sp" />

                <Button
                    android:id="@+id/btn_maybe_later"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:background="@drawable/neon_button_bg"
                    android:fontFamily="@font/rajdhani_bold"
                    android:padding="12dp"
                    android:text="@string/maybe_later"
                    android:textColor="@android:color/white"
                    android:textSize="16sp" />
            </LinearLayout>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.cardview.widget.CardView>
