<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/gfx_tools_gradient_bg"
    android:orientation="vertical"
    android:padding="24dp">

    <ProgressBar
        android:layout_width="match_parent"
        android:layout_height="10dp"
        android:progressDrawable="@drawable/custom_progress"
        android:progress="50"
        android:max="100"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_margin="16dp"/>

    <TextView
        android:id="@+id/progress_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/suggest_best_settings_description"
        android:textSize="18sp"
        android:textColor="#FFFFFF"
        android:fontFamily="@font/font_for_ar_en" />

    <TextView
        android:id="@+id/progress_description"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="@string/please_wait_while_we_optimize_your_game_settings"
        android:textSize="14sp"
        android:textColor="#B3FFFFFF"
        android:fontFamily="@font/font_for_ar_en"
        android:gravity="center" />

</LinearLayout>
