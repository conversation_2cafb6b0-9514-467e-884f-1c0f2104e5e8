<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardBackgroundColor="#1A000000"
    app:cardCornerRadius="8dp"
    app:cardElevation="0dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="12dp">

        <ImageView
            android:id="@+id/app_icon"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:contentDescription="@string/app_name"
            android:src="@android:drawable/sym_def_app_icon" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/app_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="sans-serif-medium"
                android:text="App Name"
                android:textColor="@color/text_color_primary"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/app_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="sans-serif-light"
                android:text="Background process"
                android:textColor="@color/text_color_secondary"
                android:textSize="12sp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/app_usage"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="sans-serif-medium"
                android:text="25%"
                android:textColor="@color/vibrant_orange"
                android:textSize="18sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="sans-serif-light"
                android:text="CPU"
                android:textColor="@color/text_color_secondary"
                android:textSize="12sp" />
        </LinearLayout>

        <ImageView
            android:id="@+id/close_icon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginStart="12dp"
            android:contentDescription="@string/app_name"
            android:src="@android:drawable/ic_menu_close_clear_cancel"
            app:tint="@color/vibrant_blue" />
    </LinearLayout>
</androidx.cardview.widget.CardView>
