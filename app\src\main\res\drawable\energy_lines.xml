<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Horizontal energy lines -->
    <item>
        <shape android:shape="line">
            <stroke
                android:width="1dp"
                android:color="@color/accent_color_dim"
                android:dashGap="15dp"
                android:dashWidth="20dp" />
        </shape>
    </item>
    
    <!-- Vertical energy lines -->
    <item>
        <rotate
            android:fromDegrees="90"
            android:pivotX="50%"
            android:pivotY="50%"
            android:toDegrees="90">
            <shape android:shape="line">
                <stroke
                    android:width="1dp"
                    android:color="@color/accent_color_dim"
                    android:dashGap="25dp"
                    android:dashWidth="10dp" />
            </shape>
        </rotate>
    </item>
    
    <!-- Diagonal energy lines -->
    <item>
        <rotate
            android:fromDegrees="45"
            android:pivotX="50%"
            android:pivotY="50%"
            android:toDegrees="45">
            <shape android:shape="line">
                <stroke
                    android:width="1dp"
                    android:color="@color/accent_color_dim"
                    android:dashGap="30dp"
                    android:dashWidth="5dp" />
            </shape>
        </rotate>
    </item>
</layer-list>
