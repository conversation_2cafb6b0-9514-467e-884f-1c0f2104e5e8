<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="16dp"
    app:cardBackgroundColor="#0f2027"
    app:cardCornerRadius="20dp"
    app:cardElevation="12dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/gaming_popup_background"
        android:orientation="vertical"
        android:padding="30dp"
        android:gravity="center">

        <!-- Rocket/Boost Icon -->
        <ImageView
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_marginBottom="20dp"
            android:contentDescription="@string/app_name"
            android:src="@drawable/ic_rocket"
            app:tint="@color/cyan_accent" />

        <!-- Main Success Message -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fontFamily="@font/orbitron_bold"
            android:text="@string/device_optimized_success"
            android:textColor="@color/cyan_accent"
            android:textSize="24sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="12dp" />

        <!-- Description -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fontFamily="@font/cairo_regular"
            android:text="@string/optimization_success_description"
            android:textColor="@color/text_color_secondary"
            android:textSize="16sp"
            android:gravity="center"
            android:layout_marginBottom="30dp" />

        <!-- Simple Status Info -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/gaming_score_background"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="20dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/cairo_regular"
                android:text="@string/optimization_completed_items"
                android:textColor="@color/text_color_secondary"
                android:textSize="14sp"
                android:gravity="center" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:fontFamily="@font/cairo_regular"
                android:text="@string/background_apps_managed"
                android:textColor="@color/vibrant_green"
                android:textSize="13sp"
                android:gravity="center" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:fontFamily="@font/cairo_regular"
                android:text="@string/cache_cleaned"
                android:textColor="@color/vibrant_green"
                android:textSize="13sp"
                android:gravity="center" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:fontFamily="@font/cairo_regular"
                android:text="@string/memory_optimized"
                android:textColor="@color/vibrant_green"
                android:textSize="13sp"
                android:gravity="center" />
        </LinearLayout>

        <!-- Action Button -->
        <Button
            android:id="@+id/popup_done_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:background="@drawable/button_cyan_gradient"
            android:fontFamily="@font/cairo_regular"
            android:text="@string/got_it"
            android:textColor="@color/dark_background"
            android:textSize="16sp"
            android:padding="16dp" />
    </LinearLayout>
</androidx.cardview.widget.CardView>
