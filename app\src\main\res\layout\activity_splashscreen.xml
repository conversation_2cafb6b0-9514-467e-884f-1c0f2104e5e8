<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/animated_gradient_background"
    tools:context=".splashscreen">

    <!-- Animated crosshair lines -->

    <ImageView
        android:id="@+id/crosshair_lines"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerInParent="true"
        android:src="@drawable/crosshair_lines"
        android:alpha="0.6"
        android:contentDescription="@string/app_name" />

    <!-- Logo container with glow effect -->
    <FrameLayout
        android:id="@+id/logo_container"
        android:layout_width="200dp"
        android:layout_height="200dp"
        android:layout_centerInParent="true">

        <!-- Logo background with glow -->
        <ImageView
            android:id="@+id/logo_bg"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@drawable/headshot_logo"
            android:contentDescription="@string/app_name" />

        <!-- Lightning animation overlay -->
        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/lightning_anim"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:lottie_fileName="lightning_animation.json"
            app:lottie_autoPlay="true"
            app:lottie_loop="true" />
    </FrameLayout>

    <!-- Main title with glow effect -->
    <TextView
        android:id="@+id/logo_main"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/logo_container"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="30dp"
        android:fontFamily="@font/font_for_ar_en"
        android:letterSpacing="0.3"
        android:shadowColor="@color/vibrant_blue"
        android:shadowDx="0"
        android:shadowDy="0"
        android:shadowRadius="25"
        android:text="@string/app_name_welcom_screen"
        android:textColor="#FFFFFF"
        android:textSize="44sp"
        android:textStyle="bold" />

    <!-- Subtitle with glow effect -->
    <TextView
        android:id="@+id/logo_sub"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/logo_main"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="10dp"
        android:fontFamily="@font/font_for_ar_en"
        android:shadowColor="@color/vibrant_blue"
        android:shadowDx="0"
        android:shadowDy="0"
        android:shadowRadius="15"
        android:text="@string/settings_amp_game_booster_welcome_screen"
        android:textColor="@color/vibrant_blue"
        android:textSize="20sp" />

    <!-- Power indicator at bottom -->
    <TextView
        android:id="@+id/power_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="30dp"
        android:fontFamily="@font/cairo_regular"
        android:text="@string/powering_up_welcomescreen"
        android:textColor="@color/vibrant_blue"
        android:textSize="18sp"
        android:textStyle="bold"
        android:shadowColor="@color/vibrant_blue"
        android:shadowDx="0"
        android:shadowDy="0"
        android:shadowRadius="15" />

</RelativeLayout>