<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/cyber_neon_gradient_bg"
    android:fillViewport="true"
    tools:context=".GameBoosterActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Speed lines overlay -->
        <ImageView
            android:id="@+id/energy_lines"
            android:layout_width="match_parent"
            android:layout_height="200dp"
            android:alpha="0.3"
            android:contentDescription="@string/app_name"
            android:scaleType="fitXY"
            android:src="@drawable/blue_speed_lines" />

        <!-- Particle effects overlay -->
        <ImageView
            android:id="@+id/particles"
            android:layout_width="match_parent"
            android:layout_height="100dp"
            android:layout_marginTop="-150dp"
            android:alpha="0.2"
            android:contentDescription="@string/app_name"
            android:scaleType="fitXY"
            android:src="@drawable/particle_effect" />

        <!-- Header with back button -->
        <LinearLayout
            android:id="@+id/header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="-100dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="16dp">

            <ImageView
                android:id="@+id/btn_back"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:contentDescription="@string/app_name"
                android:src="@drawable/ic_back"
                app:tint="@color/cyan_accent" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:fontFamily="@font/orbitron_bold"
                android:gravity="center"
                android:letterSpacing="0.1"
                android:shadowColor="@color/cyan_accent"
                android:shadowDx="0"
                android:shadowDy="0"
                android:shadowRadius="8"
                android:text="@string/game_booster_title"
                android:textColor="@color/cyan_accent"
                android:textSize="22sp"
                android:textStyle="bold" />

            <View
                android:layout_width="32dp"
                android:layout_height="32dp" />
        </LinearLayout>

        <!-- Main content -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp">

            <!-- Description -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:fontFamily="@font/cairo_regular"
                android:text="@string/game_booster_description"
                android:textColor="@color/text_color_secondary"
                android:textSize="16sp"
                android:gravity="center"
                android:lineSpacingExtra="4dp" />

            <!-- System Status Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                app:cardBackgroundColor="@android:color/transparent"
                app:cardCornerRadius="16dp"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/card_background"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <!-- Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:layout_marginBottom="16dp">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:contentDescription="@string/app_name"
                            android:src="@drawable/ic_cpu"
                            app:tint="@color/cyan_accent" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="12dp"
                            android:fontFamily="@font/orbitron_bold"
                            android:text="@string/cpu_status"
                            android:textColor="@color/cyan_accent"
                            android:textSize="18sp" />
                    </LinearLayout>

                    <!-- Status Display -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center">

                        <!-- Before Status -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/cairo_regular"
                                android:text="@string/before"
                                android:textColor="@color/text_color_secondary"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/cpu_before_text"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:fontFamily="@font/orbitron_bold"
                                android:text="@string/system_analysis"
                                android:textColor="@color/text_color_primary"
                                android:textSize="16sp"
                                android:gravity="center" />
                        </LinearLayout>

                        <!-- Arrow -->
                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:layout_marginHorizontal="16dp"
                            android:contentDescription="@string/app_name"
                            android:src="@drawable/ic_arrow_right"
                            app:tint="@color/cyan_accent" />

                        <!-- After Status -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/cairo_regular"
                                android:text="@string/after"
                                android:textColor="@color/text_color_secondary"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/cpu_after_text"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:fontFamily="@font/orbitron_bold"
                                android:text="--"
                                android:textColor="@color/vibrant_green"
                                android:textSize="16sp"
                                android:gravity="center" />
                        </LinearLayout>
                    </LinearLayout>

                    <!-- Hidden Progress Bars for compatibility -->
                    <ProgressBar
                        android:id="@+id/cpu_before_progress"
                        style="?android:attr/progressBarStyleHorizontal"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:visibility="gone" />

                    <ProgressBar
                        android:id="@+id/cpu_after_progress"
                        style="?android:attr/progressBarStyleHorizontal"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:visibility="gone" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Memory Status Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                app:cardBackgroundColor="@android:color/transparent"
                app:cardCornerRadius="16dp"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/card_background"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <!-- Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:layout_marginBottom="16dp">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:contentDescription="@string/app_name"
                            android:src="@drawable/ic_memory"
                            app:tint="@color/cyan_accent" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="12dp"
                            android:fontFamily="@font/orbitron_bold"
                            android:text="@string/memory_status"
                            android:textColor="@color/cyan_accent"
                            android:textSize="18sp" />
                    </LinearLayout>

                    <!-- Memory Info Display -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center">

                        <!-- Total Memory -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/cairo_regular"
                                android:text="@string/total_memory"
                                android:textColor="@color/text_color_secondary"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/total_memory"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:fontFamily="@font/orbitron_bold"
                                android:text="4.0 GB"
                                android:textColor="@color/text_color_primary"
                                android:textSize="16sp"
                                android:gravity="center" />
                        </LinearLayout>

                        <!-- Used Memory -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/cairo_regular"
                                android:text="@string/used_memory"
                                android:textColor="@color/text_color_secondary"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/used_memory"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:fontFamily="@font/orbitron_bold"
                                android:text="2.8 GB"
                                android:textColor="@color/text_color_primary"
                                android:textSize="16sp"
                                android:gravity="center" />
                        </LinearLayout>

                        <!-- Usage Percentage -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/cairo_regular"
                                android:text="@string/memory_status"
                                android:textColor="@color/text_color_secondary"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/memory_percent"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:fontFamily="@font/orbitron_bold"
                                android:text="70%"
                                android:textColor="@color/vibrant_orange"
                                android:textSize="16sp"
                                android:gravity="center" />
                        </LinearLayout>
                    </LinearLayout>

                    <!-- Hidden Progress Bar for compatibility -->
                    <ProgressBar
                        android:id="@+id/memory_progress"
                        style="?android:attr/progressBarStyleHorizontal"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:visibility="gone" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- RAM Speed Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                app:cardBackgroundColor="@android:color/transparent"
                app:cardCornerRadius="16dp"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/card_background"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <!-- Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:layout_marginBottom="16dp">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:contentDescription="@string/app_name"
                            android:src="@drawable/ic_memory"
                            app:tint="@color/cyan_accent" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="12dp"
                            android:fontFamily="@font/orbitron_bold"
                            android:text="@string/ram_speed"
                            android:textColor="@color/cyan_accent"
                            android:textSize="18sp" />
                    </LinearLayout>

                    <!-- RAM Speed Display -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center">

                        <!-- Before Status -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/cairo_regular"
                                android:text="@string/before"
                                android:textColor="@color/text_color_secondary"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/ram_speed_before"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:fontFamily="@font/orbitron_bold"
                                android:text="@string/system_analysis"
                                android:textColor="@color/text_color_primary"
                                android:textSize="16sp"
                                android:gravity="center" />
                        </LinearLayout>

                        <!-- Arrow -->
                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:layout_marginHorizontal="16dp"
                            android:contentDescription="@string/app_name"
                            android:src="@drawable/ic_arrow_right"
                            app:tint="@color/cyan_accent" />

                        <!-- After Status -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/cairo_regular"
                                android:text="@string/after"
                                android:textColor="@color/text_color_secondary"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/ram_speed_after"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:fontFamily="@font/orbitron_bold"
                                android:text="--"
                                android:textColor="@color/vibrant_green"
                                android:textSize="16sp"
                                android:gravity="center" />
                        </LinearLayout>
                    </LinearLayout>

                    <!-- Hidden Progress Bar for compatibility -->
                    <ProgressBar
                        android:id="@+id/ram_speed_progress"
                        style="@android:style/Widget.ProgressBar.Horizontal"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:visibility="gone" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Background Apps Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="30dp"
                app:cardBackgroundColor="@android:color/transparent"
                app:cardCornerRadius="16dp"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/card_background"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <!-- Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:layout_marginBottom="16dp">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:contentDescription="@string/app_name"
                            android:src="@drawable/ic_apps"
                            app:tint="@color/cyan_accent" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="12dp"
                            android:fontFamily="@font/orbitron_bold"
                            android:text="@string/high_usage_apps"
                            android:textColor="@color/cyan_accent"
                            android:textSize="18sp" />
                    </LinearLayout>

                    <!-- Description -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:fontFamily="@font/cairo_regular"
                        android:text="@string/apps_description"
                        android:textColor="@color/text_color_secondary"
                        android:textSize="14sp"
                        android:gravity="center" />

                    <!-- Apps Container -->
                    <LinearLayout
                        android:id="@+id/apps_container"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">
                        <!-- App items will be added here dynamically -->
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Main Action Button -->
            <Button
                android:id="@+id/btn_boost"
                android:layout_width="match_parent"
                android:layout_height="64dp"
                android:layout_marginBottom="20dp"
                android:background="@drawable/boost_button_background"
                android:drawablePadding="12dp"
                android:fontFamily="@font/orbitron_bold"
                android:letterSpacing="0.1"
                android:text="@string/boost_now"
                android:textColor="@color/dark_background"
                android:textSize="18sp"
                android:textStyle="bold"
                app:drawableLeftCompat="@drawable/ic_rocket"
                app:drawableTint="@color/dark_background" />

            <!-- Status Text -->
            <TextView
                android:id="@+id/status_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                android:fontFamily="@font/cairo_regular"
                android:gravity="center"
                android:text="@string/ready_to_optimize"
                android:textColor="@color/cyan_accent"
                android:textSize="16sp"
                android:lineSpacingExtra="4dp" />

            <!-- Ad Container -->
            <FrameLayout
                android:id="@+id/ad_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                android:background="@drawable/card_background"
                android:minHeight="100dp"
                android:visibility="visible" />

            <!-- Rocket Animation Container (Hidden) -->
            <FrameLayout
                android:id="@+id/rocket_container"
                android:layout_width="match_parent"
                android:layout_height="200dp"
                android:layout_marginBottom="20dp"
                android:visibility="gone">

                <ImageView
                    android:id="@+id/rocket_image"
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:layout_gravity="bottom|center_horizontal"
                    android:contentDescription="@string/app_name"
                    android:src="@drawable/ic_rocket"
                    app:tint="@color/cyan_accent" />
            </FrameLayout>

            <!-- Disclaimer Notice -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                app:cardBackgroundColor="@android:color/transparent"
                app:cardCornerRadius="12dp"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/card_background"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_info"
                        android:layout_marginEnd="12dp"
                        app:tint="@color/cyan_accent" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:fontFamily="@font/cairo_regular"
                        android:text="@string/monitoring_notice"
                        android:textColor="@color/text_color_secondary"
                        android:textSize="12sp"
                        android:lineSpacingExtra="2dp" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>
        </LinearLayout>
    </LinearLayout>
</androidx.core.widget.NestedScrollView>