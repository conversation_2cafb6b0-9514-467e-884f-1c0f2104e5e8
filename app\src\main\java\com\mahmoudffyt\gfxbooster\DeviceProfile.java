package com.mahmoudffyt.gfxbooster;

/**
 * Class representing a device profile with recommended sensitivity settings
 * based on device specifications and graphics settings
 */
public class DeviceProfile {
    private String gfxLevel;
    private int dpi;
    private int sniperSensitivity;
    private int scope4xSensitivity;
    private int scope2xSensitivity;
    private int redDotSensitivity;
    private int generalSensitivity;
    private String deviceSpecs;
    
    /**
     * Constructor for DeviceProfile
     * 
     * @param gfxLevel Graphics level (smooth, standard, high)
     * @param dpi DPI setting
     * @param sniperSensitivity Sensitivity for sniper/AWM scope
     * @param scope4xSensitivity Sensitivity for 4x scope
     * @param scope2xSensitivity Sensitivity for 2x scope
     * @param redDotSensitivity Sensitivity for red dot
     * @param generalSensitivity General sensitivity
     * @param deviceSpecs Device specifications description
     */
    public DeviceProfile(String gfxLevel, int dpi, int sniperSensitivity, int scope4xSensitivity,
                         int scope2xSensitivity, int redDotSensitivity, int generalSensitivity,
                         String deviceSpecs) {
        this.gfxLevel = gfxLevel;
        this.dpi = dpi;
        this.sniperSensitivity = sniperSensitivity;
        this.scope4xSensitivity = scope4xSensitivity;
        this.scope2xSensitivity = scope2xSensitivity;
        this.redDotSensitivity = redDotSensitivity;
        this.generalSensitivity = generalSensitivity;
        this.deviceSpecs = deviceSpecs;
    }
    
    // Getters
    
    public String getGfxLevel() {
        return gfxLevel;
    }
    
    public int getDpi() {
        return dpi;
    }
    
    public int getSniperSensitivity() {
        return sniperSensitivity;
    }
    
    public int getScope4xSensitivity() {
        return scope4xSensitivity;
    }
    
    public int getScope2xSensitivity() {
        return scope2xSensitivity;
    }
    
    public int getRedDotSensitivity() {
        return redDotSensitivity;
    }
    
    public int getGeneralSensitivity() {
        return generalSensitivity;
    }
    
    public String getDeviceSpecs() {
        return deviceSpecs;
    }
    
    /**
     * Check if this profile matches the given RAM size
     * 
     * @param ramSizeGB RAM size in GB
     * @return true if matches, false otherwise
     */
    public boolean matchesRamSize(int ramSizeGB) {
        return deviceSpecs.contains(ramSizeGB + "gb ram") || 
               deviceSpecs.contains(ramSizeGB + "GB RAM");
    }
    
    /**
     * Check if this profile matches the given CPU type
     * 
     * @param isWeakCpu true if CPU is weak, false otherwise
     * @return true if matches, false otherwise
     */
    public boolean matchesCpuType(boolean isWeakCpu) {
        return (isWeakCpu && deviceSpecs.toLowerCase().contains("weak cpu")) ||
               (!isWeakCpu && !deviceSpecs.toLowerCase().contains("weak cpu"));
    }
    
    /**
     * Check if this profile matches the given screen size
     * 
     * @param screenSizeCategory "small", "medium", or "large"
     * @return true if matches, false otherwise
     */
    public boolean matchesScreenSize(String screenSizeCategory) {
        return deviceSpecs.toLowerCase().contains(screenSizeCategory.toLowerCase() + " screen");
    }
    
    /**
     * Check if this profile matches the given FPS range
     * 
     * @param fps Average FPS
     * @return true if matches, false otherwise
     */
    public boolean matchesFps(int fps) {
        return (fps < 30 && deviceSpecs.contains("<30 fps")) ||
               (fps >= 30 && !deviceSpecs.contains("<30 fps"));
    }
    
    @Override
    public String toString() {
        return "DeviceProfile{" +
                "gfxLevel='" + gfxLevel + '\'' +
                ", dpi=" + dpi +
                ", deviceSpecs='" + deviceSpecs + '\'' +
                '}';
    }
}
