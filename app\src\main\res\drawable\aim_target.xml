<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="48dp"
    android:height="48dp"
    android:viewportWidth="48"
    android:viewportHeight="48">

    <!-- Outer Circle -->
    <path
        android:fillColor="#33FFFFFF"
        android:pathData="M24,24m-24,0a24,24 0,1 1,48 0a24,24 0,1 1,-48 0" />

    <!-- Middle Circle -->
    <path
        android:fillColor="#664FC3F7"
        android:pathData="M24,24m-16,0a16,16 0,1 1,32 0a16,16 0,1 1,-32 0" />

    <!-- Inner Circle -->
    <path
        android:fillColor="#FF5252"
        android:pathData="M24,24m-8,0a8,8 0,1 1,16 0a8,8 0,1 1,-16 0" />

    <!-- Crosshair -->
    <path
        android:strokeColor="#FFFFFF"
        android:strokeWidth="1"
        android:pathData="M24,12 L24,36 M12,24 L36,24" />

</vector>
