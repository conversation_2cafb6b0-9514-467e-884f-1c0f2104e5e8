#Wed May 28 03:38:46 EEST 2025
com.mahmoudffyt.gfxbooster.app-main-7\:/anim/boost_success_animation.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\boost_success_animation.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/anim/button_press.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\button_press.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/anim/button_pulse.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\button_pulse.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/anim/button_pulse_glow.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\button_pulse_glow.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/anim/button_shake.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\button_shake.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/anim/card_fade_in_slide_up.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\card_fade_in_slide_up.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/anim/energy_grid_animation.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\energy_grid_animation.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/anim/fade_in.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\fade_in.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/anim/flame_animation.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\flame_animation.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/anim/float_animation.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\float_animation.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/anim/glow_pulse.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\glow_pulse.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/anim/glow_pulse_repeat.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\glow_pulse_repeat.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/anim/gold_pulse.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\gold_pulse.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/anim/hover_effect.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\hover_effect.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/anim/icon_pulse.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\icon_pulse.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/anim/lightning_flash.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\lightning_flash.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/anim/lines_animation.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\lines_animation.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/anim/logo_pulse.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\logo_pulse.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/anim/logo_shrink.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\logo_shrink.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/anim/premium_feature_animation.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\premium_feature_animation.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/anim/pulse.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\pulse.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/anim/pulse_animation.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\pulse_animation.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/anim/rocket_animation.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\rocket_animation.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/anim/rotate_animation.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\rotate_animation.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/anim/slide_down_bounce.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\slide_down_bounce.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/anim/slide_in_left.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\slide_in_left.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/anim/slide_in_right.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\slide_in_right.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/anim/slide_up.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\slide_up.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/anim/slide_up_fade_in.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\slide_up_fade_in.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/anim/speed_lines_animation.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\speed_lines_animation.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/anim/splash_logo_anim.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\splash_logo_anim.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/anim/staggered_fade_in.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\staggered_fade_in.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/anim/subtle_scale.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\subtle_scale.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/anim/zoom_in.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\zoom_in.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/animator/button_state_animator.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\animator\\button_state_animator.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable-v24/ic_launcher_foreground.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable-v24\\ic_launcher_foreground.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/aim_preview_background.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\aim_preview_background.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/aim_target.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\aim_target.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/animated_gradient_background.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\animated_gradient_background.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/blue_color_button.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\blue_color_button.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/blue_speed_lines.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\blue_speed_lines.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/boost_button_background.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\boost_button_background.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/button_cyan_gradient.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\button_cyan_gradient.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/button_glow.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\button_glow.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/card_background.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\card_background.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/card_bg_translucent.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\card_bg_translucent.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/card_dark_bg.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\card_dark_bg.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/card_shadow.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\card_shadow.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/circle_button.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\circle_button.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/circle_shape_blue.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\circle_shape_blue.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/circle_shape_green.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\circle_shape_green.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/circle_shape_red.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\circle_shape_red.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/circle_shape_yellow.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\circle_shape_yellow.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/circular_progress_bar.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\circular_progress_bar.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/circular_progress_bar_green.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\circular_progress_bar_green.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/crosshair_dot.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\crosshair_dot.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/crosshair_lines.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\crosshair_lines.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/crosshair_overlay.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\crosshair_overlay.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/crosshair_precision.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\crosshair_precision.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/crosshair_simple.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\crosshair_simple.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/custom_progress.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\custom_progress.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/cyber_border_gold.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\cyber_border_gold.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/cyber_button_bg.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\cyber_button_bg.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/cyber_neon_background.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\cyber_neon_background.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/cyber_neon_bg.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\cyber_neon_bg.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/cyber_neon_gradient_bg.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\cyber_neon_gradient_bg.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/cyber_neon_gradient_bg_gold.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\cyber_neon_gradient_bg_gold.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/dialog_background.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\dialog_background.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/disclaimer_background.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\disclaimer_background.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/edit_text_background.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\edit_text_background.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/energy_grid.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\energy_grid.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/energy_lines.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\energy_lines.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/energy_lines_bg.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\energy_lines_bg.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/enhanced_button_background.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\enhanced_button_background.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/enhanced_button_square_blue.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\enhanced_button_square_blue.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/enhanced_button_with_glow.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\enhanced_button_with_glow.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/game_booster_bg.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\game_booster_bg.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/game_mode_button_active.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\game_mode_button_active.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/game_mode_button_bg.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\game_mode_button_bg.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/game_mode_button_custom.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\game_mode_button_custom.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/game_mode_logo.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\game_mode_logo.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/gaming_button_secondary.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\gaming_button_secondary.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/gaming_popup_background.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\gaming_popup_background.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/gaming_recommendation_background.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\gaming_recommendation_background.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/gaming_score_background.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\gaming_score_background.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/gaming_tag_background.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\gaming_tag_background.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/gaming_tips_background.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\gaming_tips_background.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/gfx_tools_gradient_bg.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\gfx_tools_gradient_bg.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/gradient_background.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\gradient_background.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/green_color_button.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\green_color_button.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/headshot_logo.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\headshot_logo.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/headshot_logo_blue.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\headshot_logo_blue.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/headshot_tool_bg.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\headshot_tool_bg.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_about.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_about.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_advanced.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_advanced.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_aim_button.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_aim_button.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_analytics.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_analytics.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_android.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_android.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_apps.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_apps.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_arrow_right.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_arrow_right.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_back.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_back.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_battery.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_battery.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_boost.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_boost.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_check_circle.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_check_circle.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_check_small.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_check_small.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_cpu.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_cpu.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_crosshair.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_crosshair.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_crown.png=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_crown.png
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_dpi_tip.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_dpi_tip.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_exit.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_exit.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_fps.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_fps.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_game_booster.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_game_booster.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_gaming_controller.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_gaming_controller.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_gaming_fps.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_gaming_fps.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_gaming_performance.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_gaming_performance.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_gaming_trophy.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_gaming_trophy.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_gfx_logo.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_gfx_logo.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_gfx_tools.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_gfx_tools.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_graphics.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_graphics.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_headshot_tool.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_headshot_tool.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_help.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_help.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_info.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_info.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_launcher_background.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_launcher_background.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_logo.png=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_logo.png
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_memory.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_memory.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_menu.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_menu.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_mood_game.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_mood_game.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_network.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_network.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_play_game.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_play_game.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_power.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_power.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_premium.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_premium.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_premium_crown.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_premium_crown.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_privacy.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_privacy.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_ram.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_ram.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_rate.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_rate.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_recoil_tip.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_recoil_tip.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_red_dot_tip.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_red_dot_tip.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_resolution.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_resolution.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_rocket.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_rocket.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_sensitivity_tip.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_sensitivity_tip.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_settings.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_settings.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_smart_aim.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_smart_aim.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_speedometer_bg.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_speedometer_bg.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_speedometer_needle.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_speedometer_needle.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_storage.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_storage.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_swipe.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_swipe.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/ic_tap.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_tap.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/lightning_effect.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\lightning_effect.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/logo_background.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\logo_background.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/logo_drawable_android.png=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\logo_drawable_android.png
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/logo_glow.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\logo_glow.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/neon_button_active_bg.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\neon_button_active_bg.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/neon_button_bg.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\neon_button_bg.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/neon_button_bg_cyan.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\neon_button_bg_cyan.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/neon_button_bg_gold.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\neon_button_bg_gold.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/neon_button_bg_premium.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\neon_button_bg_premium.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/neon_button_bg_secondary.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\neon_button_bg_secondary.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/neon_radio_selector.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\neon_radio_selector.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/particle_effect.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\particle_effect.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/premium_button_bg.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\premium_button_bg.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/premium_glow_bg.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\premium_glow_bg.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/premium_header_bg.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\premium_header_bg.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/premium_price_bg.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\premium_price_bg.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/preview_background.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\preview_background.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/progress_bar_gradient.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\progress_bar_gradient.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/recoil_pattern.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\recoil_pattern.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/red_color_button.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\red_color_button.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/red_dot_shape.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\red_dot_shape.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/rounded_button_blue.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\rounded_button_blue.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/rounded_button_dark.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\rounded_button_dark.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/rounded_button_green.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\rounded_button_green.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/smart_aim_button_bg.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\smart_aim_button_bg.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/smart_aim_button_glow.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\smart_aim_button_glow.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/smartphone_frame.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\smartphone_frame.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/speed_lines_bg.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\speed_lines_bg.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/speed_lines_blue.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\speed_lines_blue.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/spinner_shape.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\spinner_shape.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/target_icon.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\target_icon.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/toast_background.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\toast_background.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/drawable/yellow_color_button.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\yellow_color_button.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/font/cairo_regular.ttf=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\font\\cairo_regular.ttf
com.mahmoudffyt.gfxbooster.app-main-7\:/font/cyberpunk_font.ttf=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\font\\cyberpunk_font.ttf
com.mahmoudffyt.gfxbooster.app-main-7\:/font/font.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\font\\font.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/font/font_for_ar_en.ttf=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\font\\font_for_ar_en.ttf
com.mahmoudffyt.gfxbooster.app-main-7\:/font/fonts.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\font\\fonts.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/font/orbitron_bold.otf=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\font\\orbitron_bold.otf
com.mahmoudffyt.gfxbooster.app-main-7\:/font/premium_font.ttf=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\font\\premium_font.ttf
com.mahmoudffyt.gfxbooster.app-main-7\:/font/rajdhani_bold.ttf=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\font\\rajdhani_bold.ttf
com.mahmoudffyt.gfxbooster.app-main-7\:/font/rajdhani_medium.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\font\\rajdhani_medium.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/font/tajawal_bold.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\font\\tajawal_bold.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/font/tajawal_medium.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\font\\tajawal_medium.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/font/tajawal_regular.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\font\\tajawal_regular.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/layout/activity_aim_overlay_settings.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_aim_overlay_settings.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/layout/activity_app_guide.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_app_guide.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/layout/activity_game_booster.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_game_booster.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/layout/activity_game_mode.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_game_mode.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/layout/activity_gfx_tools.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_gfx_tools.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/layout/activity_headshot_tool.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_headshot_tool.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/layout/activity_main.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_main.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/layout/activity_settings.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_settings.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/layout/activity_splashscreen.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_splashscreen.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/layout/activity_webview.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_webview.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/layout/aim_overlay.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\aim_overlay.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/layout/dialog_about.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_about.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/layout/dialog_aim_test.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_aim_test.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/layout/dialog_app_disclaimer.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_app_disclaimer.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/layout/dialog_premium_features.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_premium_features.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/layout/dialog_premium_trial.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_premium_trial.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/layout/dialog_progress.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_progress.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/layout/gfx_selection_layout.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\gfx_selection_layout.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/layout/item_app.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_app.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/layout/nav_footer_premium_new.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\nav_footer_premium_new.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/layout/nav_footer_pro_status.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\nav_footer_pro_status.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/layout/nav_header.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\nav_header.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/layout/popup_optimization_complete.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\popup_optimization_complete.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/layout/popup_optimization_needed.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\popup_optimization_needed.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/layout/popup_optimization_progress.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\popup_optimization_progress.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/layout/popup_system_analysis_report.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\popup_system_analysis_report.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/layout/premium_feature_item.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\premium_feature_item.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/menu/drawer_menu.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\menu\\drawer_menu.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/mipmap-anydpi-v26/ic_launcher.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-anydpi-v26\\ic_launcher.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/mipmap-anydpi-v26/ic_launcher_round.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-anydpi-v26\\ic_launcher_round.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/mipmap-hdpi/ic_launcher.webp=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher.webp
com.mahmoudffyt.gfxbooster.app-main-7\:/mipmap-hdpi/ic_launcher_foreground.webp=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher_foreground.webp
com.mahmoudffyt.gfxbooster.app-main-7\:/mipmap-hdpi/ic_launcher_round.webp=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher_round.webp
com.mahmoudffyt.gfxbooster.app-main-7\:/mipmap-mdpi/ic_launcher.webp=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher.webp
com.mahmoudffyt.gfxbooster.app-main-7\:/mipmap-mdpi/ic_launcher_foreground.webp=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher_foreground.webp
com.mahmoudffyt.gfxbooster.app-main-7\:/mipmap-mdpi/ic_launcher_round.webp=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher_round.webp
com.mahmoudffyt.gfxbooster.app-main-7\:/mipmap-xhdpi/ic_launcher.webp=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher.webp
com.mahmoudffyt.gfxbooster.app-main-7\:/mipmap-xhdpi/ic_launcher_foreground.webp=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher_foreground.webp
com.mahmoudffyt.gfxbooster.app-main-7\:/mipmap-xhdpi/ic_launcher_round.webp=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher_round.webp
com.mahmoudffyt.gfxbooster.app-main-7\:/mipmap-xxhdpi/ic_launcher.webp=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher.webp
com.mahmoudffyt.gfxbooster.app-main-7\:/mipmap-xxhdpi/ic_launcher_foreground.webp=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher_foreground.webp
com.mahmoudffyt.gfxbooster.app-main-7\:/mipmap-xxhdpi/ic_launcher_round.webp=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher_round.webp
com.mahmoudffyt.gfxbooster.app-main-7\:/mipmap-xxxhdpi/ic_launcher.webp=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher.webp
com.mahmoudffyt.gfxbooster.app-main-7\:/mipmap-xxxhdpi/ic_launcher_foreground.webp=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher_foreground.webp
com.mahmoudffyt.gfxbooster.app-main-7\:/mipmap-xxxhdpi/ic_launcher_round.webp=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher_round.webp
com.mahmoudffyt.gfxbooster.app-main-7\:/xml/backup_rules.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\backup_rules.xml
com.mahmoudffyt.gfxbooster.app-main-7\:/xml/data_extraction_rules.xml=C\:\\Users\\SST\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\data_extraction_rules.xml
