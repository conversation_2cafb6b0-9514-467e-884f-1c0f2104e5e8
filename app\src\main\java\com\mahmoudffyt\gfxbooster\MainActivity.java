package com.mahmoudffyt.gfxbooster;

import android.Manifest;
import android.app.ActivityManager;
import android.app.AlarmManager;
import android.content.ActivityNotFoundException;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.view.LayoutInflater;
import android.os.Build;
import android.os.Bundle;
import android.provider.Settings;
import android.view.MenuItem;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;
import android.os.Handler;
import android.util.Log;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.core.content.ContextCompat;

import com.mahmoudffyt.gfxbooster.utils.AdManager;
import com.mahmoudffyt.gfxbooster.utils.BillingManager;
import com.google.firebase.crashlytics.FirebaseCrashlytics;

import java.util.List;

// Removed AdMob imports

import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBarDrawerToggle;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.view.GravityCompat;
import androidx.drawerlayout.widget.DrawerLayout;

import com.google.android.material.navigation.NavigationView;
import com.mahmoudffyt.gfxbooster.utils.PremiumManager;

public class MainActivity extends AppCompatActivity implements NavigationView.OnNavigationItemSelectedListener {

    // Static instance for accessing MainActivity from other activities
    private static MainActivity instance;

    // Static method to get the instance
    public static MainActivity getInstance() {
        return instance;
    }

    private androidx.cardview.widget.CardView btnGameBooster, btnGfxTools, btnHeadshotTool, btnAimButton, btnMoodGame;
    private ImageView speed_lines_bg, energy_grid, menuIcon;
    private ImageView gameBoosterIcon, gfxToolsIcon, headshotToolIcon, aimButtonIcon, moodGameIcon;
    private TextView appTitle, appSubtitle, ram_info, processor_info, screen_info, android_info;
    private androidx.cardview.widget.CardView device_info_card;
    private DrawerLayout drawerLayout;
    private NavigationView navigationView;
    private ActionBarDrawerToggle toggle;
    private com.mahmoudffyt.gfxbooster.utils.PremiumManager premiumManager;
    private com.mahmoudffyt.gfxbooster.utils.AdManager adManager;

    // Permission request codes
    private static final int REQUEST_USAGE_STATS = 101;
    private static final int REQUEST_OVERLAY_PERMISSION = 102;
    private static final int REQUEST_NOTIFICATION_PERMISSION = 103;

    // Permission request launcher
    private ActivityResultLauncher<String> requestPermissionLauncher;

    private BillingManager billingManager;

    private androidx.appcompat.app.AlertDialog premiumDialog;
    private androidx.appcompat.app.AlertDialog premiumFeaturesDialog;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Set static instance
        instance = this;

        // Initialize Firebase Crashlytics
        FirebaseCrashlytics.getInstance().setCrashlyticsCollectionEnabled(true);

        // Apply language and theme settings
        SettingsActivity.applyLanguage(this);
        SettingsActivity.applyDarkMode(this);

        // Initialize premium manager (lightweight operation)
        premiumManager = new com.mahmoudffyt.gfxbooster.utils.PremiumManager(this);

        // Set fullscreen
        getWindow().setFlags(
                WindowManager.LayoutParams.FLAG_FULLSCREEN,
                WindowManager.LayoutParams.FLAG_FULLSCREEN
        );

        setContentView(R.layout.activity_main);

        // Initialize views (UI operation - must be on main thread)
        initializeViews();

        // Initialize permission launcher (must be on main thread)
        initializePermissionLauncher();

        // Setup navigation drawer (UI operation - must be on main thread)
        setupNavigationDrawer();

        // Set click listeners (UI operation - must be on main thread)
        setupClickListeners();

        // Check and request permissions (must be on main thread)
        checkAndRequestPermissions();

        // Start animations (UI operation - must be on main thread)
        startAnimations();

        // Show disclaimer dialog if needed
        showDisclaimerIfNeeded();

        // Move heavy operations to background threads
        runHeavyOperationsInBackground();

        PremiumManager premiumManager = new PremiumManager(this);
        // قم بتفعيل وضع Premium للاختبار
        premiumManager.setPremium(true);

        // Refresh navigation drawer to show Pro status
        new Handler().postDelayed(() -> {
            refreshNavigationDrawer();
        }, 1000);

    }

    /**
     * Run heavy operations in background threads to avoid UI jank
     */
    private void runHeavyOperationsInBackground() {
        // Initialize AdManager in background thread
        new Thread(() -> {
            try {
                // Initialize AdManager (heavy network operation)
                adManager = com.mahmoudffyt.gfxbooster.utils.AdManager.getInstance(getApplicationContext());

                // Post UI updates back to main thread with delay to avoid frame drops
                new Handler(getMainLooper()).postDelayed(() -> {
                    if (!isFinishing() && !isDestroyed()) {
                        // Load banner ad after AdManager is initialized
                        loadBannerAd();
                    }
                }, 1000); // Delay ad loading by 1 second
            } catch (Exception e) {
                Log.e("MainActivity", "Error initializing AdManager: " + e.getMessage());
            }
        }).start();

        // Get device information in background thread
        new Thread(() -> {
            try {
                // Get device specs (potentially heavy operation)
                final com.mahmoudffyt.gfxbooster.utils.DeviceAnalyzer deviceAnalyzer =
                    new com.mahmoudffyt.gfxbooster.utils.DeviceAnalyzer(getApplicationContext());
                final java.util.Map<String, Object> deviceSpecs = deviceAnalyzer.getDeviceSpecs();

                // Update UI on main thread
                runOnUiThread(() -> {
                    if (!isFinishing() && !isDestroyed()) {
                        updateDeviceInfoUI(deviceSpecs);
                    }
                });
            } catch (Exception e) {
                Log.e("MainActivity", "Error getting device info: " + e.getMessage());
            }
        }).start();

        // Initialize billing manager (potentially heavy operation)
        new Thread(() -> {
            try {
                // Initialize billing manager in background
                billingManager = new com.mahmoudffyt.gfxbooster.utils.BillingManager(
                    MainActivity.this,
                    new com.mahmoudffyt.gfxbooster.utils.BillingManager.BillingUpdatesListener() {
                        @Override
                        public void onPurchaseComplete(boolean success) {
                            if (success) {
                                runOnUiThread(() -> {
                                    Toast.makeText(MainActivity.this,
                                        getString(R.string.premium_features_unlocked),
                                        Toast.LENGTH_SHORT).show();
                                    // Refresh navigation drawer to show Pro status
                                    refreshNavigationDrawer();
                                });
                            }
                        }

                        @Override
                        public void onProductDetailsRetrieved(List<com.android.billingclient.api.ProductDetails> productDetailsList) {
                            // Product details retrieved, ready for purchase
                            Log.d("MainActivity", "Product details retrieved: " + productDetailsList.size());
                        }
                    }
                );

                // Show premium dialog after billing is initialized
                if (premiumManager.shouldShowPremiumDialog()) {
                    // Delay showing the dialog to allow animations to complete
                    new Handler(getMainLooper()).postDelayed(() -> {
                        try {
                            if (!isFinishing() && !isDestroyed()) {
                                showWelcomePremiumDialog();
                                premiumManager.recordPremiumDialogShown();
                            }
                        } catch (Exception e) {
                            Log.e("MainActivity", "Error showing welcome premium dialog: " + e.getMessage());
                            // Try again with a different approach if the first attempt fails
                            new Handler(getMainLooper()).postDelayed(() -> {
                                if (!isFinishing() && !isDestroyed()) {
                                    showPremiumFeaturesDialog();
                                    premiumManager.recordPremiumDialogShown();
                                }
                            }, 1000);
                        }
                    }, 3000); // Show dialog after 3 seconds
                }
            } catch (Exception e) {
                Log.e("MainActivity", "Error initializing BillingManager: " + e.getMessage());
            }
        }).start();
    }

    /**
     * Update device info UI with data from background thread
     */
    private void updateDeviceInfoUI(java.util.Map<String, Object> deviceSpecs) {
        try {
            // Get RAM info with correction applied
            long ramMB = (long) deviceSpecs.get("ram");
            double ramGB = ramMB / 1024.0;

            // Format RAM size with one decimal place if needed
            String formattedRam;
            if (ramGB % 1 < 0.1) {
                // If very close to a whole number, just show the whole number
                int roundedRam = (int)Math.round(ramGB);
                formattedRam = String.valueOf(roundedRam);
            } else {
                // Otherwise show with one decimal place
                formattedRam = String.format("%.1f", ramGB);
            }

            // Set RAM info with localized label
            ram_info.setText(formattedRam + " " + getString(R.string.gb));

            // Get processor info
            String processor = (String) deviceSpecs.get("processor");
            processor_info.setText(processor);

            // Get screen metrics
            int widthPixels = (int) deviceSpecs.get("screenWidth");
            int heightPixels = (int) deviceSpecs.get("screenHeight");
            screen_info.setText(widthPixels + " x " + heightPixels);

            // Get Android version with localized label
            String androidVersion = (String) deviceSpecs.get("androidVersion");
            android_info.setText(getString(R.string.android) + " " + androidVersion);
        } catch (Exception e) {
            Log.e("MainActivity", "Error updating device info UI: " + e.getMessage());
        }
    }

    // Method removed - replaced by updateDeviceInfoUI() which is called from background thread

    private void initializeViews() {
        btnGameBooster = findViewById(R.id.btn_game_booster);
        btnGfxTools = findViewById(R.id.btn_gfx_tools);
        btnHeadshotTool = findViewById(R.id.btn_headshot_tool);
        btnAimButton = findViewById(R.id.btn_aim_button);
        btnMoodGame = findViewById(R.id.btn_mood_game);

        speed_lines_bg = findViewById(R.id.speed_lines_bg);
        energy_grid = findViewById(R.id.energy_grid);
        menuIcon = findViewById(R.id.menu_icon);

        // Device info views
        ram_info = findViewById(R.id.ram_info);
        processor_info = findViewById(R.id.processor_info);
        screen_info = findViewById(R.id.screen_info);
        android_info = findViewById(R.id.android_info);
        device_info_card = findViewById(R.id.device_info_card);

        appTitle = findViewById(R.id.app_title);
        appSubtitle = findViewById(R.id.app_subtitle);

        // Initialize drawer components
        drawerLayout = findViewById(R.id.drawer_layout);
        navigationView = findViewById(R.id.nav_view);

        // Initialize icons
        try {
            gameBoosterIcon = btnGameBooster.findViewById(R.id.icon_game_booster);
            gfxToolsIcon = btnGfxTools.findViewById(R.id.icon_gfx_tools);
            headshotToolIcon = btnHeadshotTool.findViewById(R.id.icon_headshot_tool);
            aimButtonIcon = btnAimButton.findViewById(R.id.icon_aim_button);
            moodGameIcon = btnMoodGame.findViewById(R.id.icon_mood_game);
        } catch (Exception e) {
            // Fallback if IDs not found
            gameBoosterIcon = (ImageView) btnGameBooster.getChildAt(0);
        }
    }

    private void setupClickListeners() {
        btnGameBooster.setOnClickListener(v -> {
            // Add a nice animation effect when clicking the button
            Animation scaleAnim = AnimationUtils.loadAnimation(this, R.anim.hover_effect);
            v.startAnimation(scaleAnim);

            // Start activity immediately
            Intent intent = new Intent(MainActivity.this, GameBoosterActivity.class);
            startActivity(intent);
        });
        btnGfxTools.setOnClickListener(v -> {
            // Add a nice animation effect when clicking the button
            Animation scaleAnim = AnimationUtils.loadAnimation(this, R.anim.hover_effect);
            v.startAnimation(scaleAnim);

            // Start the GFX Tools activity
            Intent intent = new Intent(MainActivity.this, GfxToolsActivity.class);
            startActivity(intent);
        });
        btnHeadshotTool.setOnClickListener(v -> {
            // Add a nice animation effect when clicking the button
            Animation scaleAnim = AnimationUtils.loadAnimation(this, R.anim.hover_effect);
            v.startAnimation(scaleAnim);

            // Start the Headshot Tool activity
            Intent intent = new Intent(MainActivity.this, HeadshotToolActivity.class);
            startActivity(intent);
        });
        btnAimButton.setOnClickListener(v -> {
            // Add a nice animation effect when clicking the button
            Animation scaleAnim = AnimationUtils.loadAnimation(this, R.anim.hover_effect);
            v.startAnimation(scaleAnim);

            // Check if user has premium access
            if (!premiumManager.isPremium()) {
                // Show premium feature dialog
                showPremiumFeaturesDialog();
                return;
            }

            // Start the Aim Overlay Settings activity
            Intent intent = new Intent(MainActivity.this, AimOverlaySettingsActivity.class);
            startActivity(intent);
        });
        btnMoodGame.setOnClickListener(v -> {
            // Add a nice animation effect when clicking the button
            Animation scaleAnim = AnimationUtils.loadAnimation(this, R.anim.hover_effect);
            v.startAnimation(scaleAnim);

            // Start the Game Mode activity
            Intent intent = new Intent(MainActivity.this, GameModeActivity.class);
            startActivity(intent);
        });
    }

    /**
     * Load banner ad with optimized settings
     */
    private void loadBannerAd() {
        // Skip if premium user
        if (premiumManager.isPremium()) {
            return;
        }

        ViewGroup adContainer = findViewById(R.id.ad_banner_container);
        if (adContainer != null && adManager != null) {
            // Set minimum height to ensure the container is visible
            adContainer.setMinimumHeight(150);

            // Use production ad unit ID for release builds
            String adUnitId = AdManager.BANNER_AD_UNIT_ID;

            // Load the banner ad with minimal logging
            adManager.loadBannerAd(this, adContainer, adUnitId);
        }
    }

    private void showFeatureMessage(String feature) {
        Toast.makeText(this, feature + getString(R.string.feature_coming_soon) , Toast.LENGTH_SHORT).show();
    }

    private void showPremiumPromotionDialog() {
        try {
            // Create and show premium promotion dialog using regular AlertDialog.Builder
            androidx.appcompat.app.AlertDialog.Builder builder = new androidx.appcompat.app.AlertDialog.Builder(this);
            View premiumView = getLayoutInflater().inflate(R.layout.dialog_premium_features, null);
            builder.setView(premiumView);

            // Setup buttons
            Button upgradeButton = premiumView.findViewById(R.id.btn_subscribe);
            Button laterButton = premiumView.findViewById(R.id.btn_maybe_later);

            // Get price TextView
            TextView priceTextView = premiumView.findViewById(R.id.premium_price);

            // Update price from Google Play Billing
            if (billingManager != null) {
                // Get product details and update price
                billingManager.getMonthlySubscriptionDetails(new com.mahmoudffyt.gfxbooster.utils.BillingManager.ProductDetailsCallback() {
                    @Override
                    public void onProductDetailsReceived(com.android.billingclient.api.ProductDetails productDetails) {
                        if (productDetails != null) {
                            // Get the price from product details
                            String formattedPrice = "";

                            // For subscription products
                            if (productDetails.getSubscriptionOfferDetails() != null &&
                                !productDetails.getSubscriptionOfferDetails().isEmpty()) {

                                com.android.billingclient.api.ProductDetails.SubscriptionOfferDetails offerDetails =
                                    productDetails.getSubscriptionOfferDetails().get(0);

                                if (offerDetails.getPricingPhases() != null &&
                                    offerDetails.getPricingPhases().getPricingPhaseList() != null &&
                                    !offerDetails.getPricingPhases().getPricingPhaseList().isEmpty()) {

                                    com.android.billingclient.api.ProductDetails.PricingPhase pricingPhase =
                                        offerDetails.getPricingPhases().getPricingPhaseList().get(0);

                                    formattedPrice = pricingPhase.getFormattedPrice();
                                }
                            }

                            // Update UI on main thread
                            final String finalPrice = formattedPrice;
                            runOnUiThread(() -> {
                                if (priceTextView != null && !finalPrice.isEmpty()) {
                                    priceTextView.setText(finalPrice);
                                }
                            });
                        }
                    }
                });
            }

            // Create dialog
            androidx.appcompat.app.AlertDialog dialog = builder.create();

            upgradeButton.setOnClickListener(v -> {
                // Launch billing flow
                launchBillingFlow();
                dialog.dismiss();
                // Dismiss dialog
                if (dialog != null && dialog.isShowing()) {
                    dialog.dismiss();
                }
            });

            laterButton.setOnClickListener(v -> {
                // Just dismiss the dialog
                if (dialog != null && dialog.isShowing()) {
                    dialog.dismiss();
                }
            });

            // Show dialog
            dialog.show();
            premiumDialog = dialog;
        } catch (Exception e) {
            e.printStackTrace();

            // Fallback to regular AlertDialog if MaterialAlertDialog fails
            androidx.appcompat.app.AlertDialog.Builder builder = new androidx.appcompat.app.AlertDialog.Builder(this);
            builder.setTitle(getString(R.string.premium_features));
            builder.setMessage(getString(R.string.premium_features_description));
            builder.setPositiveButton(getString(R.string.subscribe_now), (dialog, which) -> {
                launchBillingFlow();
            });
            builder.setNegativeButton(getString(R.string.cancel), null);
            builder.show();
        }
    }


    //private com.game.headshot.utils.BillingManager billingManager;

    private void showWelcomePremiumDialog() {
        try {
            // Log that we're attempting to show the dialog
            Log.d("MainActivity", "Attempting to show welcome premium dialog");

            // Create and show welcome premium dialog
            androidx.appcompat.app.AlertDialog.Builder builder = new androidx.appcompat.app.AlertDialog.Builder(this, R.style.AlertDialogTheme);
            View premiumView = getLayoutInflater().inflate(R.layout.dialog_premium_features, null);
            builder.setView(premiumView);

            // Make sure dialog is not cancelable by touching outside
            builder.setCancelable(false);

            // Create dialog
            androidx.appcompat.app.AlertDialog dialog = builder.create();

            // Ensure dialog doesn't get dismissed when touching outside
            dialog.setCanceledOnTouchOutside(false);

            // Setup buttons
            Button upgradeButton = premiumView.findViewById(R.id.btn_subscribe);
            Button laterButton = premiumView.findViewById(R.id.btn_maybe_later);
           // androidx.cardview.widget.CardView monthlyOption = premiumView.findViewById(R.id.monthly_option);
            //androidx.cardview.widget.CardView trialOption = premiumView.findViewById(R.id.trial_option);

            // Get price TextView
            TextView priceTextView = premiumView.findViewById(R.id.premium_price);

            // Update price from Google Play Billing
            if (billingManager != null) {
                // Get product details and update price
                billingManager.getMonthlySubscriptionDetails(new com.mahmoudffyt.gfxbooster.utils.BillingManager.ProductDetailsCallback() {
                    @Override
                    public void onProductDetailsReceived(com.android.billingclient.api.ProductDetails productDetails) {
                        if (productDetails != null) {
                            // Get the price from product details
                            String formattedPrice = "";

                            // For subscription products
                            if (productDetails.getSubscriptionOfferDetails() != null &&
                                !productDetails.getSubscriptionOfferDetails().isEmpty()) {

                                com.android.billingclient.api.ProductDetails.SubscriptionOfferDetails offerDetails =
                                    productDetails.getSubscriptionOfferDetails().get(0);

                                if (offerDetails.getPricingPhases() != null &&
                                    offerDetails.getPricingPhases().getPricingPhaseList() != null &&
                                    !offerDetails.getPricingPhases().getPricingPhaseList().isEmpty()) {

                                    com.android.billingclient.api.ProductDetails.PricingPhase pricingPhase =
                                        offerDetails.getPricingPhases().getPricingPhaseList().get(0);

                                    formattedPrice = pricingPhase.getFormattedPrice();
                                }
                            }

                            // Update UI on main thread
                            final String finalPrice = formattedPrice;
                            runOnUiThread(() -> {
                                if (priceTextView != null && !finalPrice.isEmpty()) {
                                    priceTextView.setText(finalPrice);
                                }
                            });
                        }
                    }
                });
            }

            // Set click listeners
            upgradeButton.setOnClickListener(v -> {
                // Launch Google Play Billing flow
                launchBillingFlow();

                // Dismiss dialog
                if (dialog != null && dialog.isShowing()) {
                    dialog.dismiss();
                }
            });

            laterButton.setOnClickListener(v -> {
                // Just dismiss the dialog
                if (dialog != null && dialog.isShowing()) {
                    dialog.dismiss();
                }
            });
//            monthlyOption.setOnClickListener(v -> {
//                // Launch Google Play Billing flow for monthly subscription
//                launchBillingFlow(BillingManager.SUBSCRIPTION_MONTHLY);
//
//                // Dismiss dialog
//                if (dialog != null && dialog.isShowing()) {
//                    dialog.dismiss();
//                }
//            });
//
//            trialOption.setOnClickListener(v -> {
//                // For demo purposes, just set premium to true for 1 hour
//                // In a real app, this would be handled by the trial system
//                Toast.makeText(this, getString(R.string.trial_activated), Toast.LENGTH_SHORT).show();

                // Dismiss dialog
//                if (dialog != null && dialog.isShowing()) {
//                    dialog.dismiss();
//                }
           // });

            // Store dialog reference to prevent garbage collection
            premiumDialog = dialog;

            // Show dialog
            dialog.show();

            // Make sure dialog doesn't get cut off on smaller screens
            if (dialog.getWindow() != null) {
                dialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);

                // Add flags to ensure dialog appears on top
                dialog.getWindow().setFlags(
                    WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
                    WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
                );

                // Clear the flags after dialog is displayed
                new Handler().postDelayed(() -> {
                    if (dialog.getWindow() != null) {
                        dialog.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE);
                    }
                }, 100);
            }
        } catch (Exception e) {
            e.printStackTrace();
            // Fallback to simple toast if dialog fails
            Toast.makeText(this, getString(R.string.premium_features_description), Toast.LENGTH_LONG).show();
        }
    }

    private void showPremiumFeaturesDialog() {
        try {
            // Create and show premium features dialog
            androidx.appcompat.app.AlertDialog.Builder builder = new androidx.appcompat.app.AlertDialog.Builder(this, R.style.AlertDialogTheme);
            View premiumView = getLayoutInflater().inflate(R.layout.dialog_premium_features, null);
            builder.setView(premiumView);

            // Create dialog
            androidx.appcompat.app.AlertDialog dialog = builder.create();

            // Setup buttons
            Button subscribeButton = premiumView.findViewById(R.id.btn_subscribe);
            Button laterButton = premiumView.findViewById(R.id.btn_maybe_later);

            // Get price TextView
            TextView priceTextView = premiumView.findViewById(R.id.premium_price);

            // Update price from Google Play Billing
            if (billingManager != null) {
                // Get product details and update price
                billingManager.getMonthlySubscriptionDetails(new com.mahmoudffyt.gfxbooster.utils.BillingManager.ProductDetailsCallback() {
                    @Override
                    public void onProductDetailsReceived(com.android.billingclient.api.ProductDetails productDetails) {
                        if (productDetails != null) {
                            // Get the price from product details
                            String formattedPrice = "";

                            // For subscription products
                            if (productDetails.getSubscriptionOfferDetails() != null &&
                                !productDetails.getSubscriptionOfferDetails().isEmpty()) {

                                com.android.billingclient.api.ProductDetails.SubscriptionOfferDetails offerDetails =
                                    productDetails.getSubscriptionOfferDetails().get(0);

                                if (offerDetails.getPricingPhases() != null &&
                                    offerDetails.getPricingPhases().getPricingPhaseList() != null &&
                                    !offerDetails.getPricingPhases().getPricingPhaseList().isEmpty()) {

                                    com.android.billingclient.api.ProductDetails.PricingPhase pricingPhase =
                                        offerDetails.getPricingPhases().getPricingPhaseList().get(0);

                                    formattedPrice = pricingPhase.getFormattedPrice();
                                }
                            }

                            // Update UI on main thread
                            final String finalPrice = formattedPrice;
                            runOnUiThread(() -> {
                                if (priceTextView != null && !finalPrice.isEmpty()) {
                                    priceTextView.setText(finalPrice);
                                }
                            });
                        }
                    }
                });
            }

            // Set click listeners
            subscribeButton.setOnClickListener(v -> {
                // Launch Google Play Billing flow
                launchBillingFlow();

                // Dismiss dialog
                if (dialog != null && dialog.isShowing()) {
                    dialog.dismiss();
                }
            });

            laterButton.setOnClickListener(v -> {
                // Just dismiss the dialog
                if (dialog != null && dialog.isShowing()) {
                    dialog.dismiss();
                }
            });

            // Show dialog
            dialog.show();
            premiumFeaturesDialog = dialog;

            // Make sure dialog doesn't get cut off on smaller screens
            dialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        } catch (Exception e) {
            e.printStackTrace();

            // Fallback to regular AlertDialog if custom dialog fails
//            androidx.appcompat.app.AlertDialog.Builder builder = new androidx.appcompat.app.AlertDialog.Builder(this);
//            builder.setTitle(getString(R.string.premium_features));
//            builder.setMessage(getString(R.string.premium_features_description));
//            builder.setPositiveButton(getString(R.string.subscribe_now), (dialog, which) -> {
//                premiumManager.setPremium(true);
//                Toast.makeText(this, getString(R.string.premium_features_unlocked), Toast.LENGTH_SHORT).show();
//            });
//            builder.setNegativeButton(getString(R.string.cancel), null);
//            builder.show();
        }
    }


    private void startAnimations() {
        try {
            // Use a handler to stagger animations and reduce main thread load
            final Handler animationHandler = new Handler();

            // Start with the main button animation
            animationHandler.post(() -> {
                // Animate main button with slide down bounce effect
                Animation slideDownBounce = AnimationUtils.loadAnimation(this, R.anim.slide_down_bounce);
                if (btnGameBooster != null) {
                    btnGameBooster.startAnimation(slideDownBounce);
                }
            });

            // Add glow pulse effect to main button after a delay
            animationHandler.postDelayed(() -> {
                if (btnGameBooster != null && !isFinishing()) {
                    Animation glowPulseRepeat = AnimationUtils.loadAnimation(this, R.anim.glow_pulse_repeat);
                    btnGameBooster.startAnimation(glowPulseRepeat);
                }
            }, 1000);

            // Animate icons with pulse effect if available
            animationHandler.postDelayed(() -> {
                if (gameBoosterIcon != null && !isFinishing()) {
                    Animation iconPulse = AnimationUtils.loadAnimation(this, R.anim.icon_pulse);
                    gameBoosterIcon.startAnimation(iconPulse);
                }
            }, 200);

            // Stagger animations for other buttons with fade in
            animationHandler.postDelayed(() -> {
                if (btnGfxTools != null && !isFinishing()) {
                    Animation staggeredFadeIn = AnimationUtils.loadAnimation(this, R.anim.staggered_fade_in);
                    btnGfxTools.startAnimation(staggeredFadeIn);
                }
            }, 300);

            animationHandler.postDelayed(() -> {
                if (btnHeadshotTool != null && !isFinishing()) {
                    Animation staggeredFadeIn = AnimationUtils.loadAnimation(this, R.anim.staggered_fade_in);
                    btnHeadshotTool.startAnimation(staggeredFadeIn);
                }
            }, 500);

            animationHandler.postDelayed(() -> {
                if (btnAimButton != null && !isFinishing()) {
                    Animation staggeredFadeIn = AnimationUtils.loadAnimation(this, R.anim.staggered_fade_in);
                    btnAimButton.startAnimation(staggeredFadeIn);
                }
            }, 700);

            animationHandler.postDelayed(() -> {
                if (btnMoodGame != null && !isFinishing()) {
                    Animation staggeredFadeIn = AnimationUtils.loadAnimation(this, R.anim.staggered_fade_in);
                    btnMoodGame.startAnimation(staggeredFadeIn);
                }
            }, 900);

            // Animate background elements with lower priority
            animationHandler.postDelayed(() -> {
                // Animate speed lines with floating effect
                if (speed_lines_bg != null && !isFinishing()) {
                    Animation floatAnim = AnimationUtils.loadAnimation(this, R.anim.float_animation);
                    speed_lines_bg.startAnimation(floatAnim);
                }
            }, 400);

            animationHandler.postDelayed(() -> {
                // Animate energy grid with subtle scale
                if (energy_grid != null && !isFinishing()) {
                    Animation subtleScale = AnimationUtils.loadAnimation(this, R.anim.subtle_scale);
                    energy_grid.startAnimation(subtleScale);
                }
            }, 600);

            animationHandler.postDelayed(() -> {
                // Animate device info card with fade in
                if (device_info_card != null && !isFinishing()) {
                    Animation fadeIn = AnimationUtils.loadAnimation(this, R.anim.fade_in);
                    device_info_card.startAnimation(fadeIn);
                }

                // Set up touch animations for buttons (lowest priority)
                if (!isFinishing()) {
                    setupButtonTouchEffects();
                }
            }, 800);

        } catch (Exception e) {
            Log.e("MainActivity", "Error starting animations: " + e.getMessage());
        }
    }

    private void setupButtonTouchEffects() {
        try {
            // Create a more efficient touch listener with less animation overhead
            View.OnTouchListener touchListener = new View.OnTouchListener() {
                // Cache the animation to avoid repeated loading
                private Animation hoverEffect = null;

                @Override
                public boolean onTouch(View view, MotionEvent motionEvent) {
                    try {
                        switch (motionEvent.getAction()) {
                            case MotionEvent.ACTION_DOWN:
                                // Lazy load the animation only once
                                if (hoverEffect == null) {
                                    hoverEffect = AnimationUtils.loadAnimation(MainActivity.this, R.anim.hover_effect);
                                }

                                // Use property animation instead of view animation when possible
                                view.animate().alpha(0.9f).setDuration(100).start();

                                // Only apply the full animation if the device is powerful enough
                                if (isHighPerformanceDevice()) {
                                    view.startAnimation(hoverEffect);
                                }
                                break;

                            case MotionEvent.ACTION_UP:
                            case MotionEvent.ACTION_CANCEL:
                                // Use property animation for better performance
                                view.animate().alpha(1.0f).setDuration(100).start();
                                view.clearAnimation();
                                break;
                        }
                    } catch (Exception e) {
                        // Fail gracefully
                        Log.e("MainActivity", "Error in touch effect: " + e.getMessage());
                    }
                    return false; // Allow onClick to be triggered
                }
            };

            // Apply touch listener to buttons one by one with null checks
            if (btnGfxTools != null) btnGfxTools.setOnTouchListener(touchListener);
            if (btnHeadshotTool != null) btnHeadshotTool.setOnTouchListener(touchListener);
            if (btnAimButton != null) btnAimButton.setOnTouchListener(touchListener);
            if (btnMoodGame != null) btnMoodGame.setOnTouchListener(touchListener);
        } catch (Exception e) {
            Log.e("MainActivity", "Error setting up button effects: " + e.getMessage());
        }
    }

    /**
     * Check if the device has enough performance for complex animations
     * @return true if the device is high performance
     */
    private boolean isHighPerformanceDevice() {
        try {
            // Get ActivityManager
            ActivityManager activityManager = (ActivityManager) getSystemService(Context.ACTIVITY_SERVICE);

            // Check memory class - devices with more RAM are likely more powerful
            int memoryClass = activityManager.getMemoryClass();

            // Check processor cores
            int cores = Runtime.getRuntime().availableProcessors();

            // Consider high performance if device has 3GB+ RAM or 6+ cores
            return memoryClass >= 192 || cores >= 6;
        } catch (Exception e) {
            Log.e("MainActivity", "Error checking device performance: " + e.getMessage());
            return false; // Assume low performance on error
        }
    }

    private void setupNavigationDrawer() {
        // Set up the navigation drawer
        navigationView.setNavigationItemSelectedListener(this);

        // Create hamburger icon and sync drawer state
        toggle = new ActionBarDrawerToggle(
                this, drawerLayout, null, R.string.navigation_drawer_open, R.string.navigation_drawer_close);
        drawerLayout.addDrawerListener(toggle);
        toggle.syncState();

        // Add menu button click listener to open drawer
        menuIcon.setOnClickListener(v -> drawerLayout.openDrawer(GravityCompat.START));

        // Add click listener to app title to open drawer
        appTitle.setOnClickListener(v -> {
            // Open drawer
            drawerLayout.openDrawer(GravityCompat.START);
        });

        // Setup custom navigation menu items
        setupCustomNavigationItems();

        // Setup navigation drawer based on premium status
        setupNavigationDrawerContent();
    }

    private void setupCustomNavigationItems() {
        // Find all custom navigation items
        View settingsItem = findViewById(R.id.nav_settings_item);
        View rateItem = findViewById(R.id.nav_rate_item);
        View privacyItem = findViewById(R.id.nav_privacy_item);
        View appGuideItem = findViewById(R.id.nav_app_guide_item);
        View aboutItem = findViewById(R.id.nav_about_item);
        View exitItem = findViewById(R.id.nav_exit_item);

        // Set click listeners for each item
        if (settingsItem != null) {
            settingsItem.setOnClickListener(v -> {
                // Open Settings Activity
                Intent intent = new Intent(MainActivity.this, SettingsActivity.class);
                startActivity(intent);
                drawerLayout.closeDrawer(GravityCompat.START);
            });
        }

        if (rateItem != null) {
            rateItem.setOnClickListener(v -> {
                // Open Play Store for rating
                try {
                    startActivity(new Intent(Intent.ACTION_VIEW,
                            Uri.parse("market://details?id=" + getPackageName())));
                } catch (ActivityNotFoundException e) {
                    startActivity(new Intent(Intent.ACTION_VIEW,
                            Uri.parse("http://play.google.com/store/apps/details?id=" + getPackageName())));
                }
                drawerLayout.closeDrawer(GravityCompat.START);
            });
        }

        if (privacyItem != null) {
            privacyItem.setOnClickListener(v -> {
                // Open privacy policy in WebView
                Intent intent = new Intent(MainActivity.this, WebViewActivity.class);
                intent.putExtra("url", "https://sites.google.com/view/privacy-policy-app-gfx/%D8%A7%D9%84%D8%B5%D9%81%D8%AD%D8%A9-%D8%A7%D9%84%D8%B1%D8%A6%D9%8A%D8%B3%D9%8A%D8%A9");
                intent.putExtra("title", getString(R.string.privacy_policy));
                startActivity(intent);
                drawerLayout.closeDrawer(GravityCompat.START);
            });
        }

        if (appGuideItem != null) {
            appGuideItem.setOnClickListener(v -> {
                // Open App Guide Activity
                Intent intent = new Intent(MainActivity.this, AppGuideActivity.class);
                startActivity(intent);
                drawerLayout.closeDrawer(GravityCompat.START);
            });
        }

        if (aboutItem != null) {
            aboutItem.setOnClickListener(v -> {
                // Show about dialog
                showAboutDialog();
                drawerLayout.closeDrawer(GravityCompat.START);
            });
        }

        if (exitItem != null) {
            exitItem.setOnClickListener(v -> {
                // Show exit confirmation dialog
                showExitConfirmationDialog();
            });
        }
    }

    // Ad loading method removed

    private void setupNavigationDrawerContent() {
        try {
            // Check if user has premium access
            boolean isPremium = premiumManager.isPremium();

            // Get navigation drawer views
            View premiumFooterView = findViewById(R.id.nav_premium_footer);
            View navHeaderContainer = findViewById(R.id.nav_header_container);

            if (isPremium) {
                // User has premium - show pro status
                setupProStatusView(premiumFooterView, navHeaderContainer);
            } else {
                // User doesn't have premium - show premium features
                setupPremiumFeaturesAnimations(null);
            }

        } catch (Exception e) {
            Log.e("MainActivity", "Error setting up navigation drawer content: " + e.getMessage());
            // Fallback to premium features view
            setupPremiumFeaturesAnimations(null);
        }
    }

    private void setupProStatusView(View premiumFooterView, View navHeaderContainer) {
        try {
            if (premiumFooterView == null) return;

            // Hide premium features container
            LinearLayout premiumFeaturesContainer = premiumFooterView.findViewById(R.id.premium_features_container_new);
            Button subscribeButton = premiumFooterView.findViewById(R.id.btn_subscribe_standard);
            TextView premiumHeader = premiumFooterView.findViewById(R.id.premium_header_new);

            if (premiumFeaturesContainer != null) {
                premiumFeaturesContainer.setVisibility(View.GONE);
            }
            if (subscribeButton != null) {
                subscribeButton.setVisibility(View.GONE);
            }
            if (premiumHeader != null) {
                premiumHeader.setVisibility(View.GONE);
            }

            // Create and show pro status view
            LayoutInflater inflater = getLayoutInflater();
            View proStatusView = inflater.inflate(R.layout.nav_footer_pro_status, null);

            // Clear the footer container and add pro status view
            if (premiumFooterView instanceof ViewGroup) {
                ViewGroup container = (ViewGroup) premiumFooterView;
                container.removeAllViews();
                container.addView(proStatusView);
            }

            // Setup manage subscription button
            Button manageSubscriptionButton = proStatusView.findViewById(R.id.btn_manage_subscription);
            if (manageSubscriptionButton != null) {
                manageSubscriptionButton.setOnClickListener(v -> {
                    // Open Google Play Store subscription management
                    openSubscriptionManagement();
                });
            }

            // Update nav header to gold theme
            updateNavHeaderForPro(navHeaderContainer);

        } catch (Exception e) {
            Log.e("MainActivity", "Error setting up pro status view: " + e.getMessage());
        }
    }

    private void updateNavHeaderForPro(View navHeaderContainer) {
        try {
            if (navHeaderContainer != null) {
                // Change background to gold gradient
                navHeaderContainer.setBackgroundResource(R.drawable.cyber_neon_gradient_bg_gold);

                // Add gold glow effect to app title
                TextView appTitle = navHeaderContainer.findViewById(R.id.nav_app_title);
                if (appTitle != null) {
                    appTitle.setShadowLayer(15, 0, 0, getResources().getColor(R.color.cyber_neon_gold));
                }
            }
        } catch (Exception e) {
            Log.e("MainActivity", "Error updating nav header for pro: " + e.getMessage());
        }
    }

    private void openSubscriptionManagement() {
        try {
            // Open Google Play Store subscription management
            Intent intent = new Intent(Intent.ACTION_VIEW);
            intent.setData(Uri.parse("https://play.google.com/store/account/subscriptions"));
            intent.setPackage("com.android.vending");
            startActivity(intent);
        } catch (Exception e) {
            // Fallback to web browser
            try {
                Intent intent = new Intent(Intent.ACTION_VIEW);
                intent.setData(Uri.parse("https://play.google.com/store/account/subscriptions"));
                startActivity(intent);
            } catch (Exception ex) {
                Toast.makeText(this, getString(R.string.error_opening_subscription_management), Toast.LENGTH_SHORT).show();
            }
        }
    }

    /**
     * Public method to refresh navigation drawer when premium status changes
     * Call this method after premium status is updated
     */
    public void refreshNavigationDrawer() {
        runOnUiThread(() -> {
            try {
                setupNavigationDrawerContent();
            } catch (Exception e) {
                Log.e("MainActivity", "Error refreshing navigation drawer: " + e.getMessage());
            }
        });
    }

    private void setupPremiumFeaturesAnimations(View headerView) {
        try {
            // Get premium footer view
            View premiumFooterView = findViewById(R.id.nav_premium_footer);
            if (premiumFooterView == null) return;

            // Get premium header and features container from footer
            TextView premiumHeader = premiumFooterView.findViewById(R.id.premium_header_new);
            LinearLayout premiumFeaturesContainer = premiumFooterView.findViewById(R.id.premium_features_container_new);
            Button subscribeButton = premiumFooterView.findViewById(R.id.btn_subscribe_standard);
            Button showPremiumFeaturesButton = premiumFooterView.findViewById(R.id.btn_show_premium_features_standard);

            // Use a handler to stagger animations and reduce main thread load
            final Handler animHandler = new Handler();

            // Only apply complex animations on high-performance devices
            boolean useComplexAnimations = isHighPerformanceDevice();

            // Apply gold pulse animation to premium header with glow effect
            if (premiumHeader != null) {
                animHandler.post(() -> {
                    if (!isFinishing()) {
                        // Use property animation for better performance
                        if (useComplexAnimations) {
                            Animation headerPulse = AnimationUtils.loadAnimation(this, R.anim.gold_pulse);
                            premiumHeader.startAnimation(headerPulse);
                        } else {
                            // Simple alpha animation for low-end devices
                            premiumHeader.animate().alpha(0.8f).setDuration(500)
                                .withEndAction(() ->
                                    premiumHeader.animate().alpha(1.0f).setDuration(500).start()
                                ).start();
                        }
                    }
                });
            }

            // Apply animations to features container
            if (premiumFeaturesContainer != null) {
                animHandler.postDelayed(() -> {
                    if (!isFinishing()) {
                        // Use simpler animations on low-end devices
                        if (useComplexAnimations) {
                            Animation containerPulse = AnimationUtils.loadAnimation(this, R.anim.pulse_animation);
                            premiumFeaturesContainer.startAnimation(containerPulse);

                            // Apply premium feature animations to each feature item with staggered delay
                            // but limit the number of simultaneous animations
                            int childCount = Math.min(premiumFeaturesContainer.getChildCount(), 5);
                            for (int i = 0; i < childCount; i++) {
                                final int index = i;
                                animHandler.postDelayed(() -> {
                                    if (!isFinishing()) {
                                        View child = premiumFeaturesContainer.getChildAt(index);
                                        if (child != null) {
                                            Animation featureAnim = AnimationUtils.loadAnimation(
                                                MainActivity.this, R.anim.premium_feature_animation);
                                            child.startAnimation(featureAnim);
                                        }
                                    }
                                }, 200 * i); // Reduced delay between items
                            }
                        } else {
                            // Simple fade in for low-end devices
                            premiumFeaturesContainer.setAlpha(0f);
                            premiumFeaturesContainer.animate().alpha(1f).setDuration(500).start();
                        }
                    }
                }, 100);
            }

            // Apply animation to subscribe button
            if (subscribeButton != null) {
                animHandler.postDelayed(() -> {
                    if (!isFinishing()) {
                        if (useComplexAnimations) {
                            Animation buttonPulse = AnimationUtils.loadAnimation(this, R.anim.flame_animation);
                            subscribeButton.startAnimation(buttonPulse);
                        } else {
                            // Simple scale animation for low-end devices
                            subscribeButton.setScaleX(0.95f);
                            subscribeButton.setScaleY(0.95f);
                            subscribeButton.animate().scaleX(1f).scaleY(1f).setDuration(300).start();
                        }

                        // Set click listener for subscribe button
                        subscribeButton.setOnClickListener(v -> {
                            // Simple click animation
                            v.animate().scaleX(0.95f).scaleY(0.95f).setDuration(100)
                                .withEndAction(() -> {
                                    v.animate().scaleX(1f).scaleY(1f).setDuration(100).start();

                                    // Show premium features dialog instead of launching billing flow directly
                                    showPremiumFeaturesDialog();
                                    drawerLayout.closeDrawer(GravityCompat.START);
                                }).start();
                        });
                    }
                }, 200);
            }

            // Setup show premium features button
            if (showPremiumFeaturesButton != null) {
                animHandler.postDelayed(() -> {
                    if (!isFinishing()) {
                        // Simple fade in animation
                        showPremiumFeaturesButton.setAlpha(0f);
                        showPremiumFeaturesButton.animate().alpha(1f).setDuration(500).start();

                        // Set click listener to show premium features dialog
                        showPremiumFeaturesButton.setOnClickListener(v -> {
                            // Simple click animation
                            v.animate().scaleX(0.95f).scaleY(0.95f).setDuration(100)
                                .withEndAction(() -> {
                                    v.animate().scaleX(1f).scaleY(1f).setDuration(100).start();

                                    // Show premium features dialog
                                    showPremiumFeaturesDialog();

                                    // Close drawer
                                    drawerLayout.closeDrawer(GravityCompat.START);
                                }).start();
                        });
                    }
                }, 300);
            }

            // Add drawer listener to animate premium features when drawer opens
            // We'll just add a new listener - the overhead is minimal
            drawerLayout.addDrawerListener(new PremiumAnimationDrawerListener());

        } catch (Exception e) {
            Log.e("MainActivity", "Error setting up premium animations: " + e.getMessage());
        }
    }

    /**
     * Custom drawer listener class to avoid creating multiple anonymous instances
     */
    private class PremiumAnimationDrawerListener implements DrawerLayout.DrawerListener {
        @Override
        public void onDrawerSlide(@NonNull View drawerView, float slideOffset) {}

        @Override
        public void onDrawerOpened(@NonNull View drawerView) {
            // Restart animations when drawer is opened, but with a slight delay
            // to avoid jank during drawer opening animation
            new Handler().postDelayed(() -> {
                if (!isFinishing()) {
                    setupPremiumFeaturesAnimations(null);
                }
            }, 300);
        }

        @Override
        public void onDrawerClosed(@NonNull View drawerView) {}

        @Override
        public void onDrawerStateChanged(int newState) {}
    }

    @Override
    public boolean onNavigationItemSelected(@NonNull MenuItem item) {
        // Handle navigation view item clicks
        int id = item.getItemId();

        if (id == R.id.nav_settings) {
            // Open Settings Activity
            Intent intent = new Intent(MainActivity.this, SettingsActivity.class);
            startActivity(intent);
        } else if (id == R.id.nav_rate) {
            // Open Play Store for rating
            try {
                startActivity(new Intent(Intent.ACTION_VIEW,
                        Uri.parse("market://details?id=" + getPackageName())));
            } catch (ActivityNotFoundException e) {
                startActivity(new Intent(Intent.ACTION_VIEW,
                        Uri.parse("http://play.google.com/store/apps/details?id=" + getPackageName())));
            }
        } else if (id == R.id.nav_privacy) {
            // Open privacy policy in WebView
            Intent intent = new Intent(MainActivity.this, WebViewActivity.class);
            intent.putExtra("url", "https://sites.google.com/view/headshot-settings-game-booste/%D8%A7%D9%84%D8%B5%D9%81%D8%AD%D8%A9-%D8%A7%D9%84%D8%B1%D8%A6%D9%8A%D8%B3%D9%8A%D8%A9");
            intent.putExtra("title", getString(R.string.privacy_policy));
            startActivity(intent);
        } else if (id == R.id.nav_about) {
            // Show about dialog
            showAboutDialog();
        } else if (id == R.id.nav_exit) {
            // Show exit confirmation dialog
            showExitConfirmationDialog();
        }

        // Close the drawer
        drawerLayout.closeDrawer(GravityCompat.START);
        return true;
    }

    @Override
    public void onBackPressed() {
        // Close drawer on back press if open
        if (drawerLayout.isDrawerOpen(GravityCompat.START)) {
            drawerLayout.closeDrawer(GravityCompat.START);
        } else {
            // Show exit confirmation dialog
            showExitConfirmationDialog();
        }
    }

    private void showExitConfirmationDialog() {
        // Create and show exit confirmation dialog
        new androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle(R.string.exit_confirmation)
            .setPositiveButton(R.string.yes, (dialog, which) -> {
                // Exit the app
                finishAffinity();
            })
            .setNegativeButton(R.string.no, null)
            .show();
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        // Save instance state but don't save the state of the buttons
        // that are causing the issue
        View premiumFooterView = findViewById(R.id.nav_premium_footer);
        if (premiumFooterView != null) {
            premiumFooterView.setSaveEnabled(false);
        }
        super.onSaveInstanceState(outState);
    }

    @Override
    protected void onRestoreInstanceState(Bundle savedInstanceState) {
        // Completely disable state restoration to avoid the MaterialButton issue
        // Do NOT call super.onRestoreInstanceState(savedInstanceState)

        android.util.Log.d("MainActivity", "Completely skipping state restoration to avoid MaterialButton issue");

        // Instead, manually restore only the states we need
        if (savedInstanceState != null) {
            // Example: restore selected tab or other important states
            // int selectedTab = savedInstanceState.getInt("selected_tab", 0);
            // setCurrentTab(selectedTab);
        }
    }

    // Track last time we loaded an ad to avoid too frequent reloads
    private long lastAdLoadTime = 0;

    @Override
    protected void onResume() {
        super.onResume();

        // Check permissions again in case they were changed in settings, but with reduced frequency
        checkAndRequestPermissions();

        // Check subscription status when app comes to foreground
        if (billingManager != null) {
            // Use a background thread to avoid UI jank
            new Thread(() -> {
                try {
                    billingManager.queryPurchases();
                } catch (Exception e) {
                    Log.e("MainActivity", "Error querying purchases: " + e.getMessage());
                }
            }).start();
        }

        // Only reload banner ad if not premium user and container is empty
        if (!premiumManager.isPremium()) {
            ViewGroup adContainer = findViewById(R.id.ad_banner_container);
            if (adContainer != null && adManager != null && adContainer.getChildCount() == 0) {
                // Check if enough time has passed since last ad load (at least 30 seconds)
                long currentTime = System.currentTimeMillis();
                if (currentTime - lastAdLoadTime > 30000) {
                    // Load banner ad with minimal delay to avoid UI lag
                    new Handler().postDelayed(() -> {
                        if (!isFinishing() && !isDestroyed()) {
                            loadBannerAd();
                            lastAdLoadTime = System.currentTimeMillis();
                        }
                    }, 1000); // Increased delay to 1 second to reduce UI jank
                } else {
                    Log.d("MainActivity", "Skipping ad reload - too soon since last load");
                }
            }
        }
    }

    private void initializePermissionLauncher() {
        requestPermissionLauncher = registerForActivityResult(
            new ActivityResultContracts.RequestPermission(),
            isGranted -> {
                if (isGranted) {
                    // Permission granted, check if we need to request more permissions
                    checkAndRequestPermissions();
                } else {
                    // Permission denied, show explanation
                    Toast.makeText(this, getString(R.string.permission_required), Toast.LENGTH_LONG).show();
                }
            }
        );
    }

    private void checkAndRequestPermissions() {
        // Use SharedPreferences to avoid showing permission dialogs too frequently
        SharedPreferences prefs = getSharedPreferences("permission_prefs", MODE_PRIVATE);
        long lastPromptTime = prefs.getLong("last_permission_prompt_time", 0);
        long currentTime = System.currentTimeMillis();

        // Only show permission dialogs once per day (86400000 ms = 24 hours)
        boolean shouldPrompt = (currentTime - lastPromptTime) > 86400000;

        // If we shouldn't prompt now, just log and return
        if (!shouldPrompt) {
            Log.d("MainActivity", "Skipping permission prompts - already prompted recently");
            return;
        }

        // Use a sequential approach to request permissions
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.POST_NOTIFICATIONS)
                    != PackageManager.PERMISSION_GRANTED) {
                requestPermissionLauncher.launch(Manifest.permission.POST_NOTIFICATIONS);

                // Record that we prompted for permissions
                prefs.edit().putLong("last_permission_prompt_time", currentTime).apply();
                return; // Will continue with other permissions on next resume
            }
        }

        // Check for SCHEDULE_EXACT_ALARM permission (Android 12+)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            AlarmManager alarmManager = (AlarmManager) getSystemService(Context.ALARM_SERVICE);
            if (!alarmManager.canScheduleExactAlarms()) {
                showExactAlarmPermissionDialog();

                // Record that we prompted for permissions
                prefs.edit().putLong("last_permission_prompt_time", currentTime).apply();
                return; // Will continue with other permissions on next resume
            }
        }

        // Check for PACKAGE_USAGE_STATS permission
        if (!hasUsageStatsPermission()) {
            showUsageStatsPermissionDialog();

            // Record that we prompted for permissions
            prefs.edit().putLong("last_permission_prompt_time", currentTime).apply();
            return; // Will continue with other permissions on next resume
        }

        // Check for SYSTEM_ALERT_WINDOW permission
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (!Settings.canDrawOverlays(this)) {
                showOverlayPermissionDialog();

                // Record that we prompted for permissions
                prefs.edit().putLong("last_permission_prompt_time", currentTime).apply();
                return; // Will continue with other permissions on next resume
            }
        }

        // All permissions are granted, proceed with app initialization
        Log.d("MainActivity", "All required permissions are granted");
    }

    private boolean hasUsageStatsPermission() {
        android.app.usage.UsageStatsManager usageStatsManager =
                (android.app.usage.UsageStatsManager) getSystemService(Context.USAGE_STATS_SERVICE);
        if (usageStatsManager == null) {
            return false;
        }

        long currentTime = System.currentTimeMillis();
        // Check if we can get any stats - if we can, we have permission
        java.util.List<android.app.usage.UsageStats> stats = usageStatsManager.queryUsageStats(
                android.app.usage.UsageStatsManager.INTERVAL_DAILY, currentTime - 1000 * 3600, currentTime);

        return stats != null && !stats.isEmpty();
    }

    private void showExactAlarmPermissionDialog() {
        new androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle(R.string.alarm_permission_title)
            .setMessage(R.string.alarm_permission_message)
            .setPositiveButton(R.string.go_to_settings, (dialog, which) -> {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    Intent intent = new Intent(Settings.ACTION_REQUEST_SCHEDULE_EXACT_ALARM);
                    intent.setData(Uri.parse("package:" + getPackageName()));
                    startActivity(intent);
                }
            })
            .setNegativeButton(R.string.cancel, null)
            .show();
    }

    private void showUsageStatsPermissionDialog() {
        new androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle(R.string.usage_access_required)
            .setMessage(R.string.usage_access_message)
            .setPositiveButton(R.string.go_to_settings, (dialog, which) -> {
                Intent intent = new Intent(Settings.ACTION_USAGE_ACCESS_SETTINGS);
                startActivity(intent);
            })
            .setNegativeButton(R.string.cancel, null)
            .show();
    }

    private void showOverlayPermissionDialog() {
        new androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle(R.string.overlay_permission_title)
            .setMessage(R.string.overlay_permission_message)
            .setPositiveButton(R.string.go_to_settings, (dialog, which) -> {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    Intent intent = new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION);
                    intent.setData(Uri.parse("package:" + getPackageName()));
                    startActivity(intent);
                }
            })
            .setNegativeButton(R.string.cancel, null)
            .show();
    }



    /**
     * Launch the billing flow for subscription purchase
     * This method is public so it can be called from other activities
     */
    public void launchBillingFlow() {
        if (billingManager != null) {
            billingManager.purchaseMonthlySubscription(this);
        }
    }

    /**
     * Get the billing manager instance
     * This method is public so it can be called from other activities
     */
    public com.mahmoudffyt.gfxbooster.utils.BillingManager getBillingManager() {
        return billingManager;
    }

    /**
     * Launch the billing flow for a specific subscription type
     * This method is public so it can be called from other activities
     */
    public void launchBillingFlow(String subscriptionType) {
        if (billingManager != null) {
            if (subscriptionType.equals(com.mahmoudffyt.gfxbooster.utils.BillingManager.SUBSCRIPTION_MONTHLY)) {
                billingManager.purchaseMonthlySubscription(this);
            } //else if (subscriptionType.equals(com.game.headshot.utils.BillingManager.SUBSCRIPTION_YEARLY)) {
//                billingManager.purchaseYearlySubscription(this);
//            } else if (subscriptionType.equals(com.game.headshot.utils.BillingManager.SUBSCRIPTION_LIFETIME)) {
//                billingManager.purchaseLifetimeSubscription(this);
//            }
        }
    }



    /**
     * Show the About dialog with app information and contact details
     */
    private void showAboutDialog() {
        // Create and show about dialog
        androidx.appcompat.app.AlertDialog.Builder builder = new androidx.appcompat.app.AlertDialog.Builder(this);
        View aboutView = getLayoutInflater().inflate(R.layout.dialog_about, null);
        builder.setView(aboutView);

        // Create dialog
        final androidx.appcompat.app.AlertDialog dialog = builder.create();

        // Set up close button
        Button closeButton = aboutView.findViewById(R.id.close_button);
        closeButton.setOnClickListener(v -> {
            dialog.dismiss();
        });

        // Make email clickable
        TextView emailText = aboutView.findViewById(R.id.email_contact);
        emailText.setOnClickListener(v -> {
            Intent intent = new Intent(Intent.ACTION_SENDTO);
            intent.setData(Uri.parse("mailto:" + getString(R.string.email_contact)));
            intent.putExtra(Intent.EXTRA_SUBJECT, getString(R.string.app_name) + " - " + getString(R.string.feedback));
            try {
                startActivity(intent);
            } catch (ActivityNotFoundException e) {
                Toast.makeText(this, getString(R.string.error_message, e.getMessage()), Toast.LENGTH_SHORT).show();
            }
        });

        // Show dialog
        dialog.show();

        // Make sure dialog doesn't get cut off on smaller screens
        dialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
    }

    /**
     * Show disclaimer dialog if it hasn't been shown before
     */
    private void showDisclaimerIfNeeded() {
        com.gfxtools.headshotsettingsgamebooster.utils.DisclaimerDialog.showIfNeeded(this);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // Clean up any resources
        if (premiumDialog != null && premiumDialog.isShowing()) {
            premiumDialog.dismiss();
        }
        if (premiumFeaturesDialog != null && premiumFeaturesDialog.isShowing()) {
            premiumFeaturesDialog.dismiss();
        }

        // Clean up billing manager
        if (billingManager != null) {
            billingManager.destroy();
        }

        // Clear static instance if this is the current instance
        if (instance == this) {
            instance = null;
        }
    }
}