<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="#0A1A22">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/select_graphics_level"
        android:textSize="18sp"
        android:textColor="#4FC3F7"
        android:textStyle="bold"
        android:fontFamily="@font/font_for_ar_en"
        android:gravity="center" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/choose_the_graphics_level_you_use_in_free_fire"
        android:textSize="14sp"
        android:textColor="#B3FFFFFF"
        android:fontFamily="@font/font_for_ar_en"
        android:gravity="center"
        android:layout_marginTop="8dp" />

    <RadioGroup
        android:id="@+id/gfx_radio_group"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginTop="16dp">

        <RadioButton
            android:id="@+id/gfx_smooth"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fontFamily="@font/font_for_ar_en"
            android:text="@string/smooth"
            android:textColor="#FFFFFF"
            android:buttonTint="#4FC3F7"
            android:padding="8dp" />

        <RadioButton
            android:id="@+id/gfx_standard"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/standard_gfx_3"
            android:textColor="#FFFFFF"
            android:fontFamily="@font/font_for_ar_en"
            android:buttonTint="#4FC3F7"
            android:padding="8dp"
            android:checked="true" />

        <RadioButton
            android:id="@+id/gfx_high"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/high_gfx2"
            android:fontFamily="@font/font_for_ar_en"
            android:textColor="#FFFFFF"
            android:buttonTint="#4FC3F7"
            android:padding="8dp" />
    </RadioGroup>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="16dp">

        <Button
            android:id="@+id/gfx_cancel_button"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            android:text="@string/cancel"
            android:textColor="#FFFFFF"
            android:textSize="14sp"
            android:fontFamily="@font/font_for_ar_en"
            android:background="@drawable/rounded_button_dark" />

        <Button
            android:id="@+id/gfx_apply_button"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:text="@string/apply"
            android:textColor="#FFFFFF"
            android:textSize="14sp"
            android:fontFamily="@font/font_for_ar_en"
            android:background="@drawable/rounded_button_blue" />
    </LinearLayout>
</LinearLayout>
