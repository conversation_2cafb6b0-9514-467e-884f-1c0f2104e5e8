package com.gfxtools.headshotsettingsgamebooster.utils;

import android.app.Dialog;
import android.content.Context;
import android.content.SharedPreferences;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.widget.Button;

import com.mahmoudffyt.gfxbooster.R;


public class DisclaimerDialog {
    
    private static final String PREFS_NAME = "disclaimer_prefs";
    private static final String KEY_DISCLAIMER_SHOWN = "disclaimer_shown";
    
    public static void showIfNeeded(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        boolean disclaimerShown = prefs.getBoolean(KEY_DISCLAIMER_SHOWN, false);
        
        if (!disclaimerShown) {
            showDisclaimer(context);
        }
    }
    
    private static void showDisclaimer(Context context) {
        Dialog dialog = new Dialog(context);
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        
        View view = LayoutInflater.from(context).inflate(R.layout.dialog_app_disclaimer, null);
        dialog.setContentView(view);
        
        // Make dialog background transparent
        if (dialog.getWindow() != null) {
            dialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        }
        
        Button btnUnderstand = view.findViewById(R.id.btnUnderstand);
        btnUnderstand.setOnClickListener(v -> {
            // Mark disclaimer as shown
            SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
            prefs.edit().putBoolean(KEY_DISCLAIMER_SHOWN, true).apply();
            
            dialog.dismiss();
        });
        
        dialog.setCancelable(false);
        dialog.show();
    }
    
    public static void resetDisclaimer(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        prefs.edit().putBoolean(KEY_DISCLAIMER_SHOWN, false).apply();
    }
}
