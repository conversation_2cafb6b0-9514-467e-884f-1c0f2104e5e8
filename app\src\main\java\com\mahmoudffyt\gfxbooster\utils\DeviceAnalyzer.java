package com.mahmoudffyt.gfxbooster.utils;

import android.app.ActivityManager;
import android.content.Context;
import android.os.Build;
import android.util.DisplayMetrics;
import android.view.WindowManager;

import java.util.HashMap;
import java.util.Map;

/**
 * Utility class to analyze device specifications and recommend settings
 */
public class DeviceAnalyzer {

    private Context context;
    private Map<String, Object> deviceSpecs;

    public DeviceAnalyzer(Context context) {
        this.context = context;
        this.deviceSpecs = new HashMap<>();
        analyzeDevice();
    }

    /**
     * Analyze device specifications
     */
    private void analyzeDevice() {
        // Get RAM info
        ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        ActivityManager.MemoryInfo memoryInfo = new ActivityManager.MemoryInfo();
        activityManager.getMemoryInfo(memoryInfo);

        // Get RAM in GB first to apply correction
        double ramGB = memoryInfo.totalMem / (1024.0 * 1024.0 * 1024.0);

        // Apply correction factor to account for system reserved memory
        ramGB = adjustRamSize(ramGB);

        // Convert back to MB for storage in deviceSpecs
        long totalRam = (long)(ramGB * 1024);
        deviceSpecs.put("ram", totalRam);

        // Get processor info
        String processor = Build.HARDWARE;
        deviceSpecs.put("processor", processor);

        // Get device model
        String model = Build.MODEL;
        deviceSpecs.put("model", model);

        // Get manufacturer
        String manufacturer = Build.MANUFACTURER;
        deviceSpecs.put("manufacturer", manufacturer);

        // Get Android version
        String androidVersion = Build.VERSION.RELEASE;
        deviceSpecs.put("androidVersion", androidVersion);

        // Get screen metrics
        WindowManager windowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        DisplayMetrics displayMetrics = new DisplayMetrics();
        windowManager.getDefaultDisplay().getMetrics(displayMetrics);

        int widthPixels = displayMetrics.widthPixels;
        int heightPixels = displayMetrics.heightPixels;
        float density = displayMetrics.density;
        int dpi = displayMetrics.densityDpi;

        deviceSpecs.put("screenWidth", widthPixels);
        deviceSpecs.put("screenHeight", heightPixels);
        deviceSpecs.put("screenDensity", density);
        deviceSpecs.put("dpi", dpi);

        // Estimate FPS based on device specs (this is just an estimation)
        int estimatedFps = estimateFps(totalRam, processor, model);
        deviceSpecs.put("estimatedFps", estimatedFps);
    }

    /**
     * Estimate FPS based on device specs
     * This is a simplified estimation and not accurate for all devices
     */
    private int estimateFps(long totalRam, String processor, String model) {
        // Base FPS estimation on RAM and known device models
        int baseFps = 30; // Default base FPS

        // Adjust based on RAM
        if (totalRam >= 8192) { // 8GB or more
            baseFps = 90;
        } else if (totalRam >= 6144) { // 6GB or more
            baseFps = 60;
        } else if (totalRam >= 4096) { // 4GB or more
            baseFps = 45;
        }

        // Adjust based on known high-end device models
        String modelLower = model.toLowerCase();
        if (modelLower.contains("galaxy s") && !modelLower.contains("mini") ||
            modelLower.contains("pixel") ||
            modelLower.contains("oneplus") ||
            modelLower.contains("mi 10") ||
            modelLower.contains("mi 11") ||
            modelLower.contains("mi 12") ||
            modelLower.contains("poco f") ||
            modelLower.contains("rog phone")) {
            baseFps += 30; // Add 30 FPS for high-end devices
        }

        // Cap at 120 FPS
        return Math.min(baseFps, 120);
    }

    /**
     * Get all device specifications
     */
    public Map<String, Object> getDeviceSpecs() {
        return deviceSpecs;
    }

    /**
     * Get recommended sensitivity settings based on device analysis
     */
    public Map<String, Integer> getRecommendedSettings() {
        Map<String, Integer> settings = new HashMap<>();

        // Get device specs
        long ram = (long) deviceSpecs.get("ram");
        int dpi = (int) deviceSpecs.get("dpi");
        int estimatedFps = (int) deviceSpecs.get("estimatedFps");

        // Base sensitivity calculations on device specs
        int generalSensitivity = calculateGeneralSensitivity(ram, dpi, estimatedFps);
        int redDotSensitivity = calculateScopeSensitivity(generalSensitivity, 1.0f);
        int scope2xSensitivity = calculateScopeSensitivity(generalSensitivity, 0.8f);
        int scope4xSensitivity = calculateScopeSensitivity(generalSensitivity, 0.6f);
        int scopeAwmSensitivity = calculateScopeSensitivity(generalSensitivity, 0.5f);

        // Store calculated settings
        settings.put("generalSensitivity", generalSensitivity);
        settings.put("redDotSensitivity", redDotSensitivity);
        settings.put("scope2xSensitivity", scope2xSensitivity);
        settings.put("scope4xSensitivity", scope4xSensitivity);
        settings.put("scopeAwmSensitivity", scopeAwmSensitivity);

        return settings;
    }

    /**
     * Calculate general sensitivity based on device specs
     */
    private int calculateGeneralSensitivity(long ram, int dpi, int fps) {
        // Base sensitivity value
        int baseSensitivity = 50;

        // Adjust for RAM
        if (ram >= 8192) { // 8GB or more
            baseSensitivity += 10;
        } else if (ram >= 6144) { // 6GB or more
            baseSensitivity += 5;
        } else if (ram < 4096) { // Less than 4GB
            baseSensitivity -= 5;
        }

        // Adjust for DPI
        if (dpi >= 480) { // xxhdpi or higher
            baseSensitivity += 5;
        } else if (dpi <= 240) { // ldpi or lower
            baseSensitivity -= 5;
        }

        // Adjust for FPS
        if (fps >= 90) {
            baseSensitivity += 10;
        } else if (fps >= 60) {
            baseSensitivity += 5;
        } else if (fps < 30) {
            baseSensitivity -= 5;
        }

        // Ensure sensitivity is within valid range (10-100)
        return Math.max(10, Math.min(100, baseSensitivity));
    }

    /**
     * Calculate scope sensitivity based on general sensitivity and a multiplier
     */
    private int calculateScopeSensitivity(int generalSensitivity, float multiplier) {
        int scopeSensitivity = Math.round(generalSensitivity * multiplier);
        return Math.max(10, Math.min(100, scopeSensitivity));
    }

    /**
     * Get recommended DPI setting based on device analysis
     */
    public int getRecommendedDpi() {
        int dpi = (int) deviceSpecs.get("dpi");

        // Recommend DPI based on device's actual DPI
        if (dpi >= 480) {
            return 1600; // High DPI devices
        } else if (dpi >= 320) {
            return 800; // Medium DPI devices
        } else {
            return 400; // Low DPI devices
        }
    }

    /**
     * Adjusts the RAM size to account for system reserved memory
     * This helps show the marketed RAM size rather than available RAM
     *
     * @param detectedRamGB The detected RAM size in GB
     * @return The adjusted RAM size in GB
     */
    private double adjustRamSize(double detectedRamGB) {
        // Apply correction factor based on detected RAM size
        // These adjustments are based on common discrepancies between
        // reported RAM and marketed RAM in Android devices

        if (detectedRamGB >= 5.5 && detectedRamGB < 6.0) {
            return 6.0; // Likely a 6GB device
        } else if (detectedRamGB >= 3.5 && detectedRamGB < 4.0) {
            return 4.0; // Likely a 4GB device
        } else if (detectedRamGB >= 2.7 && detectedRamGB < 3.0) {
            return 3.0; // Likely a 3GB device
        } else if (detectedRamGB >= 1.7 && detectedRamGB < 2.0) {
            return 2.0; // Likely a 2GB device
        } else if (detectedRamGB >= 7.5 && detectedRamGB < 8.0) {
            return 8.0; // Likely an 8GB device
        } else if (detectedRamGB >= 11.5 && detectedRamGB < 12.0) {
            return 12.0; // Likely a 12GB device
        } else if (detectedRamGB >= 15.5 && detectedRamGB < 16.0) {
            return 16.0; // Likely a 16GB device
        }

        // For values that don't match common thresholds, apply a general adjustment
        // Add approximately 10% to account for system reserved memory
        return Math.round(detectedRamGB * 1.1 * 10) / 10.0;
    }

    /**
     * Get device info as formatted string
     */
    public String getDeviceInfoString() {
        StringBuilder sb = new StringBuilder();

        // Get localized strings
        String deviceLabel = context.getString(com.mahmoudffyt.gfxbooster.R.string.device_label);
        String ramLabel = context.getString(com.mahmoudffyt.gfxbooster.R.string.ram_label);
        String processorInfo = context.getString(com.mahmoudffyt.gfxbooster.R.string.processor_info);
        String androidLabel = context.getString(com.mahmoudffyt.gfxbooster.R.string.android_label);
        String screenInfo = context.getString(com.mahmoudffyt.gfxbooster.R.string.screen_info);
        String dpiInfo = context.getString(com.mahmoudffyt.gfxbooster.R.string.dpi_info);
        String estimatedFpsInfo = context.getString(com.mahmoudffyt.gfxbooster.R.string.estimated_fps_info);
        String gbUnit = context.getString(com.mahmoudffyt.gfxbooster.R.string.gb);

        // Build the device info text with localized strings
        sb.append(deviceLabel).append(": ").append(deviceSpecs.get("manufacturer")).append(" ").append(deviceSpecs.get("model")).append("\n");

        // Convert RAM from MB to GB with proper formatting
        long ramMB = (long) deviceSpecs.get("ram");
        double ramGB = ramMB / 1024.0;

        // Format RAM size with one decimal place
        String formattedRam;
        if (ramGB % 1 < 0.1) {
            // If very close to a whole number, just show the whole number
            // Convert to int first to avoid format exception
            int roundedRam = (int)Math.round(ramGB);
            formattedRam = String.valueOf(roundedRam);
        } else {
            // Otherwise show with one decimal place
            formattedRam = String.format("%.1f", ramGB);
        }

        sb.append(ramLabel).append(": ").append(formattedRam).append(" ").append(gbUnit).append("\n");
        sb.append(processorInfo).append(": ").append(deviceSpecs.get("processor")).append("\n");
        sb.append(androidLabel).append(": ").append(deviceSpecs.get("androidVersion")).append("\n");
        sb.append(screenInfo).append(": ").append(deviceSpecs.get("screenWidth")).append("x").append(deviceSpecs.get("screenHeight")).append(" px\n");
        sb.append(dpiInfo).append(": ").append(deviceSpecs.get("dpi")).append("\n");
        sb.append(estimatedFpsInfo).append(": ").append(deviceSpecs.get("estimatedFps"));

        return sb.toString();
    }
}
