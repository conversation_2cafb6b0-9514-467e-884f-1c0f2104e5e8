package com.mahmoudffyt.gfxbooster;

import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import android.os.Bundle;
import android.os.Handler;
import android.os.Vibrator;
import android.os.VibrationEffect;
import android.os.Build;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.SeekBar;
import android.widget.TextView;
import android.widget.Toast;

// Removed AdMob imports

import androidx.appcompat.app.AppCompatActivity;


import com.mahmoudffyt.gfxbooster.utils.AimOverlayManager;
import com.mahmoudffyt.gfxbooster.utils.GameBooster;

import java.util.HashSet;
import java.util.Set;

public class AimOverlaySettingsActivity extends AppCompatActivity {

    // UI Elements
    private ImageView btnBack, speedLinesBg, energyGrid;
    private TextView aimButtonTitle, aimButtonSubtitle;
    private View redColor, greenColor, blueColor, yellowColor;
    private Button toggleOverlayButton, cleanDeviceButton, activateGameModeButton, launchGameButton;
    private Button applySettingsButton, resetButton;

    // Crosshair Size Slider and Value

    private final Set<String> installedPackagesCache = new HashSet<>();
    private SeekBar crosshairSizeSlider;
    private TextView crosshairSizeValue;

    // Aim Preview Elements
    private FrameLayout aimPreviewFrame;
    private ImageView targetImage, crosshairOverlay;
    private View redDotSight;

    // Selected Color
    private String selectedColor = "#00FFFF"; // Default cyan

    // Handler for animations
    private Handler handler = new Handler();

    // Shared Preferences
    private SharedPreferences preferences;
    private static final String PREFS_NAME = "aim_overlay_prefs";

    // Aim Overlay Manager
    private AimOverlayManager aimOverlayManager;

    // Game Booster
    private GameBooster gameBooster;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Set fullscreen
        getWindow().setFlags(
                WindowManager.LayoutParams.FLAG_FULLSCREEN,
                WindowManager.LayoutParams.FLAG_FULLSCREEN
        );

        setContentView(R.layout.activity_aim_overlay_settings);

        // Initialize preferences
        preferences = getSharedPreferences(PREFS_NAME, MODE_PRIVATE);

        // Check if user has premium access
        com.mahmoudffyt.gfxbooster.utils.PremiumManager premiumManager = new com.mahmoudffyt.gfxbooster.utils.PremiumManager(this);
        if (!premiumManager.isPremium()) {
            // Show premium feature dialog
            showPremiumFeatureDialog();
            return;
        }

        // Initialize aim overlay manager
        aimOverlayManager = new AimOverlayManager(this);

        // Initialize game booster
        gameBooster = new GameBooster(this);

        // Initialize views
        initializeViews();

        // Set up listeners
        setupListeners();

        // Load saved settings
        loadSavedSettings();

        // Start animations
        startAnimations();
    }

    private void initializeViews() {
        // Basic UI elements
        btnBack = findViewById(R.id.btn_back);
        speedLinesBg = findViewById(R.id.speed_lines_bg);
        energyGrid = findViewById(R.id.energy_grid);
        aimButtonTitle = findViewById(R.id.aim_button_title);
        aimButtonSubtitle = findViewById(R.id.aim_button_subtitle);

        // Crosshair size slider
        crosshairSizeSlider = findViewById(R.id.crosshair_size_slider);
        crosshairSizeValue = findViewById(R.id.crosshair_size_value);

        // Color buttons
        redColor = findViewById(R.id.red_color);
        greenColor = findViewById(R.id.green_color);
        blueColor = findViewById(R.id.blue_color);
        yellowColor = findViewById(R.id.yellow_color);

        // Aim Preview elements
        aimPreviewFrame = findViewById(R.id.aim_preview_frame);
        targetImage = findViewById(R.id.target_image);
        crosshairOverlay = findViewById(R.id.crosshair_overlay);
        redDotSight = findViewById(R.id.red_dot_sight);

        // Action buttons
        toggleOverlayButton = findViewById(R.id.toggle_overlay_button);
        cleanDeviceButton = findViewById(R.id.clean_device_button);
        activateGameModeButton = findViewById(R.id.activate_game_mode_button);
        launchGameButton = findViewById(R.id.launch_game_button);
        applySettingsButton = findViewById(R.id.apply_settings_button);
        resetButton = findViewById(R.id.reset_button);

        // Initialize red dot sight with default color
        updateCrosshairColor("#00FFFF");

        // Update toggle button text based on current state
        updateToggleButtonState();
    }

    private void setupListeners() {
        // Back button
        btnBack.setOnClickListener(v -> {
            vibrateDevice(20);
            finish();
        });

        // Toggle Overlay button
        toggleOverlayButton.setOnClickListener(v -> {
            vibrateDevice(50);
            Animation buttonPress = AnimationUtils.loadAnimation(this, R.anim.button_press);
            v.startAnimation(buttonPress);
            toggleOverlay();
        });

        // Clean Device button
        cleanDeviceButton.setOnClickListener(v -> {
            vibrateDevice(50);
            Animation buttonPress = AnimationUtils.loadAnimation(this, R.anim.button_press);
            v.startAnimation(buttonPress);
            cleanDevice();
        });

        // Activate Game Mode button
        activateGameModeButton.setOnClickListener(v -> {
            vibrateDevice(50);
            Animation buttonPress = AnimationUtils.loadAnimation(this, R.anim.button_press);
            v.startAnimation(buttonPress);
            activateGameMode();
        });

        // Launch Game button
        launchGameButton.setOnClickListener(v -> {
            vibrateDevice(50);
            Animation buttonPress = AnimationUtils.loadAnimation(this, R.anim.button_press);
            v.startAnimation(buttonPress);
            launchGame();
        });

        // Apply settings button
        applySettingsButton.setOnClickListener(v -> {
            vibrateDevice(50);
            Animation buttonPress = AnimationUtils.loadAnimation(this, R.anim.button_press);
            v.startAnimation(buttonPress);
            applySettings();
        });

        // Reset button
        resetButton.setOnClickListener(v -> {
            vibrateDevice(30);
            Animation buttonPress = AnimationUtils.loadAnimation(this, R.anim.button_press);
            v.startAnimation(buttonPress);
            resetSettings();
        });

        // Set up seekbar listener
        setupSeekBarListener();

        // Set up color button listeners
        setupColorButtonListeners();

        // Set up aim preview frame click listener
        if (aimPreviewFrame != null) {
            aimPreviewFrame.setOnClickListener(v -> {
                // Simulate aiming effect
                simulateAiming();
            });
        }
    }

    private void setupSeekBarListener() {
        // Crosshair size
        crosshairSizeSlider.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                crosshairSizeValue.setText(String.valueOf(progress));
                updateCrosshairSize(progress);
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                vibrateDevice(20);
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                vibrateDevice(20);
            }
        });
    }

    private void updateCrosshairSize(int size) {
        // Update the crosshair size in the preview
        if (crosshairOverlay != null) {
            float scale = 0.5f + (size / 100f);
            crosshairOverlay.setScaleX(scale);
            crosshairOverlay.setScaleY(scale);
        }

        // Save the size setting
        preferences.edit().putInt("crosshair_size", size).apply();

        // Update the actual overlay if it's showing
        if (aimOverlayManager != null && aimOverlayManager.isShowing()) {
            aimOverlayManager.updateCrosshairSize(size);
        }
    }

    private void setupColorButtonListeners() {
        // Red color
        redColor.setOnClickListener(v -> {
            vibrateDevice(20);
            selectedColor = "#FF0000";
            highlightSelectedColor(redColor);
            updateCrosshairColor(selectedColor);
        });

        // Green color
        greenColor.setOnClickListener(v -> {
            vibrateDevice(20);
            selectedColor = "#00FF00";
            highlightSelectedColor(greenColor);
            updateCrosshairColor(selectedColor);
        });

        // Blue color
        blueColor.setOnClickListener(v -> {
            vibrateDevice(20);
            selectedColor = "#00FFFF";
            highlightSelectedColor(blueColor);
            updateCrosshairColor(selectedColor);
        });

        // Yellow color
        yellowColor.setOnClickListener(v -> {
            vibrateDevice(20);
            selectedColor = "#FFFF00";
            highlightSelectedColor(yellowColor);
            updateCrosshairColor(selectedColor);
        });

        // Set initial highlight based on saved color
        String savedColor = preferences.getString("crosshair_color", "#00FFFF");
        selectedColor = savedColor;

        // Highlight the appropriate color button
        if (savedColor.equals("#FF0000")) {
            highlightSelectedColor(redColor);
        } else if (savedColor.equals("#00FF00")) {
            highlightSelectedColor(greenColor);
        } else if (savedColor.equals("#00FFFF")) {
            highlightSelectedColor(blueColor);
        } else if (savedColor.equals("#FFFF00")) {
            highlightSelectedColor(yellowColor);
        } else {
            highlightSelectedColor(blueColor); // Default to blue
        }

        updateCrosshairColor(selectedColor);
    }

    private void updateCrosshairColor(String colorHex) {
        // Update the crosshair color in the preview
        if (redDotSight != null) {
            try {
                int color = Color.parseColor(colorHex);

                // Create a GradientDrawable to make a colored circle
                GradientDrawable shape = new GradientDrawable();
                shape.setShape(GradientDrawable.OVAL);
                shape.setColor(color);
                shape.setStroke(2, Color.WHITE); // Add white stroke

                // Apply the drawable to the red dot sight
                redDotSight.setBackground(shape);

                // Save the color setting
                preferences.edit().putString("crosshair_color", colorHex).apply();

                // Update the actual overlay if it's showing
                if (aimOverlayManager != null && aimOverlayManager.isShowing()) {
                    aimOverlayManager.updateCrosshairColor(colorHex);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private void highlightSelectedColor(View selectedButton) {
        // Reset all buttons
        redColor.setAlpha(0.7f);
        greenColor.setAlpha(0.7f);
        blueColor.setAlpha(0.7f);
        yellowColor.setAlpha(0.7f);

        // Highlight selected button
        selectedButton.setAlpha(1.0f);
    }

    private void simulateAiming() {
        // Animate the target to simulate aiming
        if (targetImage != null) {
            // Calculate random movement
            float moveX = (float) (Math.random() * 10 - 5);
            float moveY = (float) (Math.random() * 10 - 5);

            // Get current position
            float currentX = targetImage.getTranslationX();
            float currentY = targetImage.getTranslationY();

            // Animate to new position
            targetImage.animate()
                    .translationX(currentX + moveX)
                    .translationY(currentY + moveY)
                    .setDuration(200)
                    .start();

            // Vibrate for feedback
            vibrateDevice(10);
        }
    }

    private void toggleOverlay() {
        if (aimOverlayManager != null) {
            if (aimOverlayManager.isShowing()) {
                aimOverlayManager.hideOverlay();
                Toast.makeText(this, R.string.overlay_disabled, Toast.LENGTH_SHORT).show();
            } else {
                // Apply current settings before showing
                int size = crosshairSizeSlider.getProgress();
                aimOverlayManager.updateCrosshairSize(size);
                aimOverlayManager.updateCrosshairColor(selectedColor);

                // Show the overlay
                aimOverlayManager.showOverlay();
                Toast.makeText(this, R.string.overlay_enabled, Toast.LENGTH_SHORT).show();
            }

            // Update button state
            updateToggleButtonState();
        }
    }

    private void updateToggleButtonState() {
        if (toggleOverlayButton != null) {
            if (aimOverlayManager != null && aimOverlayManager.isShowing()) {
                toggleOverlayButton.setText(R.string.overlay_disabled);
                toggleOverlayButton.setBackgroundResource(R.drawable.neon_button_active_bg);
            } else {
                toggleOverlayButton.setText(R.string.overlay_enabled);
                toggleOverlayButton.setBackgroundResource(R.drawable.neon_button_bg);
            }
        }
    }

    private void cleanDevice() {
        // Show cleaning animation using the same popup as GameBoosterActivity
        showOptimizationProgressDialog();

        // Perform actual cleaning
        if (gameBooster != null) {
            gameBooster.optimizeDevice();
        }

        // Show success message
        new Handler().postDelayed(() -> {
            // Dismiss the dialog
            if (optimizationDialog != null && optimizationDialog.isShowing()) {
                optimizationDialog.dismiss();
            }

            // Show notification
            showCleaningCompleteNotification();

            Toast.makeText(this, R.string.device_cleaned, Toast.LENGTH_SHORT).show();
        }, 5000); // 5 seconds delay to match GameBoosterActivity
    }

    private androidx.appcompat.app.AlertDialog optimizationDialog;

    private void showOptimizationProgressDialog() {
        try {
            // Create and show optimization progress dialog
            androidx.appcompat.app.AlertDialog.Builder builder = new androidx.appcompat.app.AlertDialog.Builder(this);
            View dialogView = getLayoutInflater().inflate(R.layout.popup_optimization_progress, null);
            builder.setView(dialogView);
            builder.setCancelable(false);

            // Create dialog
            optimizationDialog = builder.create();

            // Show dialog
            optimizationDialog.show();
        } catch (Exception e) {
            e.printStackTrace();
            // Fallback to toast
            Toast.makeText(this, R.string.cleaning_device, Toast.LENGTH_SHORT).show();
        }
    }

    private void showCleaningCompleteNotification() {
        // Create notification for cleaning complete
        android.app.NotificationManager notificationManager = (android.app.NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);

        // Create intent for when notification is clicked
        Intent intent = new Intent(this, AimOverlaySettingsActivity.class);
        intent.setAction("com.game.headshot.ACTION_SHOW_RESULTS");
        intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);

        // Create pending intent
        int flags = android.app.PendingIntent.FLAG_UPDATE_CURRENT;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            flags |= android.app.PendingIntent.FLAG_IMMUTABLE;
        }
        android.app.PendingIntent pendingIntent = android.app.PendingIntent.getActivity(this, 0, intent, flags);

        // Build the notification
        androidx.core.app.NotificationCompat.Builder builder = new androidx.core.app.NotificationCompat.Builder(this, "game_booster_channel")
                .setSmallIcon(R.drawable.ic_rocket)
                .setContentTitle(getString(R.string.optimization_success))
                .setContentText(getString(R.string.cleaning_complete_notification))
                .setPriority(androidx.core.app.NotificationCompat.PRIORITY_DEFAULT)
                .setContentIntent(pendingIntent)
                .setAutoCancel(true);

        // Show the notification
        notificationManager.notify(1001, builder.build());
    }

    // Ad loading method removed

    private void activateGameMode() {
        // Launch GameModeActivity to activate game mode
        Intent intent = new Intent(this, GameModeActivity.class);
        startActivity(intent);
    }

   //----------------------




    private void launchGame() {
        String[] freeFirePackages = {"com.dts.freefiremax", "com.dts.freefiremax"}; // دعم نسختين

        boolean found = false;
        for (String pkg : freeFirePackages) {
            if (isPackageInstalled(pkg)) {
                cleanDevice();          // تنظيف الجهاز
                activateGameMode();     // تفعيل وضع اللعب

                Intent launchIntent = getPackageManager().getLaunchIntentForPackage(pkg);
                if (launchIntent != null) {
                    Toast.makeText(this, R.string.game_launched, Toast.LENGTH_SHORT).show();
                    startActivity(launchIntent);
                } else {
                    Toast.makeText(this, R.string.launch_error, Toast.LENGTH_SHORT).show();
                }
                found = true;
                break;
            }
        }

        if (!found) {
            Toast.makeText(this, R.string.game_not_installed, Toast.LENGTH_SHORT).show();
        }
    }

    //---------------------------

    private boolean isPackageInstalled(String packageName) {
        try {
            getPackageManager().getPackageInfo(packageName, 0);
            return true;
        } catch (PackageManager.NameNotFoundException e) {
            return false;
        }
    }

    private void applySettings() {
        // Apply settings to the overlay
        if (aimOverlayManager != null) {
            int size = crosshairSizeSlider.getProgress();
            aimOverlayManager.updateCrosshairSize(size);
            aimOverlayManager.updateCrosshairColor(selectedColor);

            // Save settings
            preferences.edit()
                .putInt("crosshair_size", size)
                .putString("crosshair_color", selectedColor)
                .apply();

            Toast.makeText(this, R.string.settings_applied, Toast.LENGTH_SHORT).show();
        }
    }

    private void resetSettings() {
        // Reset crosshair size
        crosshairSizeSlider.setProgress(50);

        // Reset color to cyan
        selectedColor = "#00FFFF";
        highlightSelectedColor(blueColor);
        updateCrosshairColor(selectedColor);

        // Apply reset settings to the overlay
        if (aimOverlayManager != null) {
            aimOverlayManager.updateCrosshairSize(50);
            aimOverlayManager.updateCrosshairColor("#00FFFF");
        }

        // Save reset settings
        preferences.edit()
            .putInt("crosshair_size", 50)
            .putString("crosshair_color", "#00FFFF")
            .apply();

        Toast.makeText(this, R.string.settings_reset, Toast.LENGTH_SHORT).show();
    }

    private void loadSavedSettings() {
        // Load crosshair size
        int savedSize = preferences.getInt("crosshair_size", 50);
        crosshairSizeSlider.setProgress(savedSize);
        crosshairSizeValue.setText(String.valueOf(savedSize));
        updateCrosshairSize(savedSize);

        // Load color (already handled in setupColorButtonListeners)
    }

    /**
     * Show premium feature dialog
     */
    private void showPremiumFeatureDialog() {
        try {
            // Create and show premium feature dialog
            androidx.appcompat.app.AlertDialog.Builder builder = new androidx.appcompat.app.AlertDialog.Builder(this, R.style.AlertDialogTheme);
            View premiumView = getLayoutInflater().inflate(R.layout.dialog_premium_features, null);
            builder.setView(premiumView);

            // Create dialog
            androidx.appcompat.app.AlertDialog dialog = builder.create();

            // Setup buttons
            com.google.android.material.button.MaterialButton subscribeButton = premiumView.findViewById(R.id.btn_subscribe);
            com.google.android.material.button.MaterialButton laterButton = premiumView.findViewById(R.id.btn_maybe_later);

            // Get price TextView
            TextView priceTextView = premiumView.findViewById(R.id.premium_price);

            // Update price from Google Play Billing
            MainActivity mainActivity = MainActivity.getInstance();
            if (mainActivity != null && mainActivity.getBillingManager() != null) {
                // Get product details and update price
                mainActivity.getBillingManager().getMonthlySubscriptionDetails(new com.mahmoudffyt.gfxbooster.utils.BillingManager.ProductDetailsCallback() {
                    @Override
                    public void onProductDetailsReceived(com.android.billingclient.api.ProductDetails productDetails) {
                        if (productDetails != null) {
                            // Get the price from product details
                            String formattedPrice = "";

                            // For subscription products
                            if (productDetails.getSubscriptionOfferDetails() != null &&
                                !productDetails.getSubscriptionOfferDetails().isEmpty()) {

                                com.android.billingclient.api.ProductDetails.SubscriptionOfferDetails offerDetails =
                                    productDetails.getSubscriptionOfferDetails().get(0);

                                if (offerDetails.getPricingPhases() != null &&
                                    offerDetails.getPricingPhases().getPricingPhaseList() != null &&
                                    !offerDetails.getPricingPhases().getPricingPhaseList().isEmpty()) {

                                    com.android.billingclient.api.ProductDetails.PricingPhase pricingPhase =
                                        offerDetails.getPricingPhases().getPricingPhaseList().get(0);

                                    formattedPrice = pricingPhase.getFormattedPrice();
                                }
                            }

                            // Update UI on main thread
                            final String finalPrice = formattedPrice;
                            runOnUiThread(() -> {
                                if (priceTextView != null && !finalPrice.isEmpty()) {
                                    priceTextView.setText(finalPrice);
                                }
                            });
                        }
                    }
                });
            }

            // Set click listeners
            subscribeButton.setOnClickListener(v -> {
                // For demo purposes, just set premium to true
                com.mahmoudffyt.gfxbooster.utils.PremiumManager premiumManager = new com.mahmoudffyt.gfxbooster.utils.PremiumManager(this);
                premiumManager.setPremium(true);
                Toast.makeText(this, getString(R.string.premium_features_unlocked), Toast.LENGTH_SHORT).show();

                // Dismiss dialog and recreate activity
                if (dialog != null && dialog.isShowing()) {
                    dialog.dismiss();
                }
                recreate();
            });

            laterButton.setOnClickListener(v -> {
                // Just dismiss the dialog and finish activity
                if (dialog != null && dialog.isShowing()) {
                    dialog.dismiss();
                }
                finish();
            });

            // Show dialog
            dialog.show();

            // Make sure dialog doesn't get cut off on smaller screens
            dialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);

            // Set dialog to not cancelable
            dialog.setCancelable(false);
            dialog.setCanceledOnTouchOutside(false);
        } catch (Exception e) {
            e.printStackTrace();

            // Fallback to regular AlertDialog if custom dialog fails
            androidx.appcompat.app.AlertDialog.Builder builder = new androidx.appcompat.app.AlertDialog.Builder(this);
            builder.setTitle(getString(R.string.premium_feature));
            builder.setMessage(getString(R.string.aim_overlay_premium_message));
            builder.setPositiveButton(getString(R.string.upgrade_now), (dialog, which) -> {
                com.mahmoudffyt.gfxbooster.utils.PremiumManager premiumManager = new com.mahmoudffyt.gfxbooster.utils.PremiumManager(this);
                premiumManager.setPremium(true);
                Toast.makeText(this, getString(R.string.premium_features_unlocked), Toast.LENGTH_SHORT).show();
                recreate();
            });
            builder.setNegativeButton(getString(R.string.maybe_later), (dialog, which) -> {
                finish();
            });
            builder.setCancelable(false);
            builder.show();
        }
    }

    private void startAnimations() {
        // Animate speed lines
        if (speedLinesBg != null) {
            Animation speedLinesAnim = AnimationUtils.loadAnimation(this, R.anim.speed_lines_animation);
            speedLinesBg.startAnimation(speedLinesAnim);
        }

        // Animate energy grid
        if (energyGrid != null) {
            Animation energyGridAnim = AnimationUtils.loadAnimation(this, R.anim.energy_grid_animation);
            energyGrid.startAnimation(energyGridAnim);
        }

        // Animate buttons with pulse effect
        animateButtons();
    }

    private void animateButtons() {
        // Apply pulse animation to all buttons
        Animation pulseAnim = AnimationUtils.loadAnimation(this, R.anim.button_pulse_glow);

        if (toggleOverlayButton != null) toggleOverlayButton.startAnimation(pulseAnim);
        if (cleanDeviceButton != null) cleanDeviceButton.startAnimation(pulseAnim);
        if (activateGameModeButton != null) activateGameModeButton.startAnimation(pulseAnim);
        if (launchGameButton != null) launchGameButton.startAnimation(pulseAnim);
        if (applySettingsButton != null) applySettingsButton.startAnimation(pulseAnim);
        if (resetButton != null) resetButton.startAnimation(pulseAnim);
    }

    private void vibrateDevice(long milliseconds) {
        Vibrator vibrator = (Vibrator) getSystemService(Context.VIBRATOR_SERVICE);
        if (vibrator != null && vibrator.hasVibrator()) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                vibrator.vibrate(VibrationEffect.createOneShot(milliseconds, VibrationEffect.DEFAULT_AMPLITUDE));
            } else {
                vibrator.vibrate(milliseconds);
            }
        }
    }

    @Override
    protected void onResume() {
        super.onResume();

        // Update toggle button state
        updateToggleButtonState();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        // Clean up animations
        if (speedLinesBg != null) speedLinesBg.clearAnimation();
        if (energyGrid != null) energyGrid.clearAnimation();
        if (toggleOverlayButton != null) toggleOverlayButton.clearAnimation();
        if (cleanDeviceButton != null) cleanDeviceButton.clearAnimation();
        if (activateGameModeButton != null) activateGameModeButton.clearAnimation();
        if (launchGameButton != null) launchGameButton.clearAnimation();
        if (applySettingsButton != null) applySettingsButton.clearAnimation();
        if (resetButton != null) resetButton.clearAnimation();
    }
}
