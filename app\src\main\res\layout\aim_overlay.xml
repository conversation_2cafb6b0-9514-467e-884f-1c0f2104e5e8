<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:padding="16dp"
    android:background="@android:color/transparent"
    android:layoutDirection="ltr">

    <ImageView
        android:id="@+id/aim_crosshair"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_centerInParent="true"
        android:src="@drawable/crosshair_precision"
        android:contentDescription="@string/aim_crosshair"
        android:importantForAccessibility="yes"
        android:keepScreenOn="true" />

    <ImageButton
        android:id="@+id/btn_close_overlay"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:background="@drawable/neon_button_bg"
        android:src="@android:drawable/ic_menu_close_clear_cancel"
        android:contentDescription="@string/close"
        android:padding="4dp"
        android:scaleType="fitCenter"
        android:tint="#00FFFF" />

</RelativeLayout>
