package com.mahmoudffyt.gfxbooster;

import android.app.ActivityManager;
import android.content.Context;
import android.os.Build;
import android.util.DisplayMetrics;
import android.view.WindowManager;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Class for analyzing device specifications and providing recommended settings
 */
public class DeviceAnalyzer {
    private Context context;
    private List<DeviceProfile> deviceProfiles;
    private String selectedGfxLevel = "standard"; // Default GFX level

    /**
     * Constructor for DeviceAnalyzer
     *
     * @param context Application context
     */
    public DeviceAnalyzer(Context context) {
        this.context = context;
        this.deviceProfiles = loadDeviceProfiles();
    }

    /**
     * Load device profiles from the database
     *
     * @return List of device profiles
     */
    private List<DeviceProfile> loadDeviceProfiles() {
        List<DeviceProfile> profiles = new ArrayList<>();

        // Add profiles from the database
        profiles.add(new DeviceProfile("smooth", 460, 140, 180, 180, 200, 200, "2gb ram - medium screen - <30 fps - weak cpu"));
        profiles.add(new DeviceProfile("smooth", 500, 140, 190, 195, 200, 200, "3gb ram - medium screen - <30 fps - weak cpu"));
        profiles.add(new DeviceProfile("standard", 550, 130, 180, 190, 200, 200, "4gb ram"));
        profiles.add(new DeviceProfile("high", 600, 140, 188, 192, 200, 200, "6ram"));

        return profiles;
    }

    /**
     * Set the selected GFX level
     *
     * @param gfxLevel GFX level (smooth, standard, high)
     */
    public void setSelectedGfxLevel(String gfxLevel) {
        this.selectedGfxLevel = gfxLevel;
    }

    /**
     * Get the selected GFX level
     *
     * @return Selected GFX level
     */
    public String getSelectedGfxLevel() {
        return selectedGfxLevel;
    }

    /**
     * Get device RAM size in GB
     *
     * @return RAM size in GB
     */
    public double getDeviceRamSizeGB() {
        ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        ActivityManager.MemoryInfo memoryInfo = new ActivityManager.MemoryInfo();
        activityManager.getMemoryInfo(memoryInfo);

        long totalMemoryBytes = memoryInfo.totalMem;
        double ramSizeGB = totalMemoryBytes / (1024.0 * 1024.0 * 1024.0);

        // Apply correction factor to account for system reserved memory
        ramSizeGB = adjustRamSize(ramSizeGB);

        // Ensure minimum 1GB
        return Math.max(1.0, ramSizeGB);
    }

    /**
     * Adjusts the RAM size to account for system reserved memory
     * This helps show the marketed RAM size rather than available RAM
     *
     * @param detectedRamGB The detected RAM size in GB
     * @return The adjusted RAM size in GB
     */
    private double adjustRamSize(double detectedRamGB) {
        // Apply correction factor based on detected RAM size
        // These adjustments are based on common discrepancies between
        // reported RAM and marketed RAM in Android devices

        if (detectedRamGB >= 5.5 && detectedRamGB < 6.0) {
            return 6.0; // Likely a 6GB device
        } else if (detectedRamGB >= 3.5 && detectedRamGB < 4.0) {
            return 4.0; // Likely a 4GB device
        } else if (detectedRamGB >= 2.7 && detectedRamGB < 3.0) {
            return 3.0; // Likely a 3GB device
        } else if (detectedRamGB >= 1.7 && detectedRamGB < 2.0) {
            return 2.0; // Likely a 2GB device
        } else if (detectedRamGB >= 7.5 && detectedRamGB < 8.0) {
            return 8.0; // Likely an 8GB device
        } else if (detectedRamGB >= 11.5 && detectedRamGB < 12.0) {
            return 12.0; // Likely a 12GB device
        } else if (detectedRamGB >= 15.5 && detectedRamGB < 16.0) {
            return 16.0; // Likely a 16GB device
        }

        // For values that don't match common thresholds, apply a general adjustment
        // Add approximately 10% to account for system reserved memory
        return Math.round(detectedRamGB * 1.1 * 10) / 10.0;
    }

    /**
     * Determine if the device has a weak CPU
     *
     * @return true if CPU is considered weak, false otherwise
     */
    public boolean hasWeakCpu() {
        int cpuCores = Runtime.getRuntime().availableProcessors();
        int sdkVersion = Build.VERSION.SDK_INT;

        // Simple heuristic: consider CPU weak if it has fewer than 4 cores or old Android version
        return cpuCores < 4 || sdkVersion < Build.VERSION_CODES.M;
    }

    /**
     * Get screen size category
     *
     * @return "small", "medium", or "large"
     */
    public String getScreenSizeCategory() {
        WindowManager windowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        DisplayMetrics metrics = new DisplayMetrics();
        windowManager.getDefaultDisplay().getMetrics(metrics);

        float density = metrics.density;
        float widthDp = metrics.widthPixels / density;
        float heightDp = metrics.heightPixels / density;
        float screenSizeDp = (float) Math.sqrt(widthDp * widthDp + heightDp * heightDp);

        if (screenSizeDp < 6.5) {
            return "small";
        } else if (screenSizeDp < 7.5) {
            return "medium";
        } else {
            return "large";
        }
    }

    /**
     * Estimate average FPS based on device specs
     *
     * @return Estimated average FPS
     */
    public int estimateAverageFps() {
        double ramSizeGB = getDeviceRamSizeGB();
        boolean isWeakCpu = hasWeakCpu();

        if (ramSizeGB <= 2 || isWeakCpu) {
            return 25; // Low FPS
        } else if (ramSizeGB <= 4) {
            return 40; // Medium FPS
        } else {
            return 60; // High FPS
        }
    }

    /**
     * Find the best matching device profile based on device specs and selected GFX level
     *
     * @return Best matching device profile
     */
    public DeviceProfile findBestMatchingProfile() {
        double ramSizeGB = getDeviceRamSizeGB();
        boolean isWeakCpu = hasWeakCpu();
        String screenSizeCategory = getScreenSizeCategory();
        int estimatedFps = estimateAverageFps();

        DeviceProfile bestMatch = deviceProfiles.get(0); // Default to first profile
        int bestMatchScore = 0;

        for (DeviceProfile profile : deviceProfiles) {
            int score = 0;

            // Match GFX level (highest priority)
            if (profile.getGfxLevel().equalsIgnoreCase(selectedGfxLevel)) {
                score += 5;
            }

            // Match RAM size
            if (profile.matchesRamSize((int)Math.round(ramSizeGB))) {
                score += 3;
            } else if (Math.abs(ramSizeGB - extractRamSizeGB(profile.getDeviceSpecs())) <= 1) {
                score += 1;
            }

            // Match CPU type
            if (profile.matchesCpuType(isWeakCpu)) {
                score += 2;
            }

            // Match screen size
            if (profile.matchesScreenSize(screenSizeCategory)) {
                score += 1;
            }

            // Match FPS
            if (profile.matchesFps(estimatedFps)) {
                score += 1;
            }

            if (score > bestMatchScore) {
                bestMatchScore = score;
                bestMatch = profile;
            }
        }

        return bestMatch;
    }

    /**
     * Extract RAM size in GB from device specs string
     *
     * @param deviceSpecs Device specs string
     * @return RAM size in GB, defaults to 4 if not found
     */
    private int extractRamSizeGB(String deviceSpecs) {
        if (deviceSpecs == null) {
            return 4;
        }

        deviceSpecs = deviceSpecs.toLowerCase();

        if (deviceSpecs.contains("2gb ram")) {
            return 2;
        } else if (deviceSpecs.contains("3gb ram")) {
            return 3;
        } else if (deviceSpecs.contains("4gb ram")) {
            return 4;
        } else if (deviceSpecs.contains("6ram") || deviceSpecs.contains("6gb ram")) {
            return 6;
        } else if (deviceSpecs.contains("8gb ram")) {
            return 8;
        }

        return 4; // Default
    }

    /**
     * Get recommended settings based on device analysis
     *
     * @return Map of recommended settings
     */
    public Map<String, Integer> getRecommendedSettings() {
        DeviceProfile bestProfile = findBestMatchingProfile();

        Map<String, Integer> settings = new HashMap<>();
        settings.put("generalSensitivity", bestProfile.getGeneralSensitivity());
        settings.put("redDotSensitivity", bestProfile.getRedDotSensitivity());
        settings.put("scope2xSensitivity", bestProfile.getScope2xSensitivity());
        settings.put("scope4xSensitivity", bestProfile.getScope4xSensitivity());
        settings.put("scopeAwmSensitivity", bestProfile.getSniperSensitivity());
        settings.put("recommendedDpi", bestProfile.getDpi());

        return settings;
    }

    /**
     * Get device information as a formatted string
     *
     * @return Device information string
     */
    public String getDeviceInfoString() {
        double ramSizeGB = getDeviceRamSizeGB();
        boolean isWeakCpu = hasWeakCpu();
        String screenSizeCategory = getScreenSizeCategory();
        int estimatedFps = estimateAverageFps();
        DeviceProfile bestProfile = findBestMatchingProfile();

        // Format RAM size with one decimal place
        String formattedRam;
        if (ramSizeGB % 1 < 0.1) {
            // If very close to a whole number, just show the whole number
            // Convert to int first to avoid format exception
            int roundedRam = (int)Math.round(ramSizeGB);
            formattedRam = String.valueOf(roundedRam);
        } else {
            // Otherwise show with one decimal place
            formattedRam = String.format("%.1f", ramSizeGB);
        }

        StringBuilder sb = new StringBuilder();
        sb.append("RAM: ").append(formattedRam).append("GB\n");
        sb.append("CPU: ").append(isWeakCpu ? "Basic" : "Powerful").append("\n");
        sb.append("Screen: ").append(screenSizeCategory).append("\n");
        sb.append("Est. FPS: ").append(estimatedFps).append("\n");
        sb.append("GFX: ").append(selectedGfxLevel).append("\n");
        sb.append("Recommended DPI: ").append(bestProfile.getDpi());

        return sb.toString();
    }

    /**
     * Get recommended DPI setting
     *
     * @return Recommended DPI value
     */
    public int getRecommendedDpi() {
        return findBestMatchingProfile().getDpi();
    }
}
