{"logs": [{"outputFile": "com.mahmoudffyt.gfxbooster.app-mergeDebugResources-49:/values-ar/values-ar.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\464e6a7dcfefd0da870c4050aa381b0c\\transformed\\material-1.12.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,116,117,118,119,120,135,136,142,204,205,215,295,296,309,310,311,312,313,314,315,316,317,318,319,320,321,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,434,545,546,565", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,8703,8781,8857,8941,9033,10271,10372,10901,15848,15907,16559,22368,22437,23226,23326,23389,23454,23515,23583,23645,23703,23817,23877,23938,23995,24068,25161,25242,25334,25441,25539,25619,25767,25848,25929,26057,26146,26222,26275,26329,26395,26473,26553,26624,26706,26778,26852,26925,26995,27104,27195,27266,27356,27451,27525,27608,27701,27750,27831,27900,27986,28071,28133,28197,28260,28329,28438,28548,28645,28745,28802,32928,40793,40872,42281", "endLines": "9,116,117,118,119,120,135,136,142,204,205,215,295,296,309,310,311,312,313,314,315,316,317,318,319,320,321,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,434,545,546,565", "endColumns": "12,77,75,83,91,82,100,118,76,58,62,90,68,66,99,62,64,60,67,61,57,113,59,60,56,72,122,80,91,106,97,79,147,80,80,127,88,75,52,53,65,77,79,70,81,71,73,72,69,108,90,70,89,94,73,82,92,48,80,68,85,84,61,63,62,68,108,109,96,99,56,57,79,78,74,75", "endOffsets": "510,8776,8852,8936,9028,9111,10367,10486,10973,15902,15965,16645,22432,22499,23321,23384,23449,23510,23578,23640,23698,23812,23872,23933,23990,24063,24186,25237,25329,25436,25534,25614,25762,25843,25924,26052,26141,26217,26270,26324,26390,26468,26548,26619,26701,26773,26847,26920,26990,27099,27190,27261,27351,27446,27520,27603,27696,27745,27826,27895,27981,28066,28128,28192,28255,28324,28433,28543,28640,28740,28797,28855,33003,40867,40942,42352"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1a44ad6b726c152c4fe16ed54c5a8c02\\transformed\\appcompat-1.7.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,544", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "561,669,773,880,962,1063,1177,1257,1336,1427,1520,1612,1706,1806,1899,1994,2087,2178,2272,2351,2456,2554,2652,2760,2860,2963,3118,40711", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "664,768,875,957,1058,1172,1252,1331,1422,1515,1607,1701,1801,1894,1989,2082,2173,2267,2346,2451,2549,2647,2755,2855,2958,3113,3210,40788"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\46c7e1c2c774c76fe5e74081b097ef9a\\transformed\\browser-1.8.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "166,216,217,218", "startColumns": "4,4,4,4", "startOffsets": "13385,16650,16748,16856", "endColumns": "99,97,107,101", "endOffsets": "13480,16743,16851,16953"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\src\\main\\res\\values-ar\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,304,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,291,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,19757,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,18891,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,63,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,71,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,19816,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,18958,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "10,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,121,122,123,131,132,133,134,137,138,139,140,141,143,144,145,164,165,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,206,207,208,209,210,211,212,213,214,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,297,298,299,300,301,302,303,304,305,306,307,308,322,323,324,325,326,327,328,329,330,331,332,333,334,383,384,385,386,387,388,389,390,391,392,393,394,395,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,532,533,534,535,536,537,538,539,540,541,542,543,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,566,567,568,569,570,571,572,573,574,575,576,577,578,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "515,3215,3260,3311,3366,3475,3539,3616,3708,3773,3866,3917,3965,4037,4100,4139,4190,4249,4357,4412,4469,4540,4610,4776,4860,5022,5102,5172,5228,5288,5343,5402,5454,5511,5667,5739,5806,5896,5957,6023,6067,6117,6171,6226,6289,6342,6504,6563,6614,6695,6756,6810,6850,6913,6976,7048,7119,7204,7247,7394,7467,7532,7637,7684,7750,7822,7875,7937,8018,8082,8122,8185,8282,8352,8414,8477,8519,8571,8649,9116,9166,9225,10007,10089,10130,10173,10491,10562,10685,10739,10836,10978,11051,11091,13251,13313,13485,13525,13576,13668,13726,13777,13827,13882,13936,14010,14113,14227,14278,14341,14396,14453,14527,14617,14672,14731,14779,14874,14949,14991,15038,15079,15143,15205,15267,15317,15370,15442,15535,15600,15698,15763,15804,15970,16022,16117,16207,16271,16341,16385,16442,16480,16958,17023,17068,17205,17278,17320,17362,17404,17461,17513,17566,17633,17707,17781,17848,17932,18004,18068,18124,18249,18323,18377,18528,18588,18643,18707,18817,18886,18984,19046,19177,19243,19332,19419,19482,19586,19674,19730,19794,19853,19908,19981,20040,20107,20161,20215,20415,20451,20491,20555,20645,20698,20755,20875,20916,20976,21035,21115,21181,21253,21306,21355,21397,21441,21554,21610,21672,21724,21762,21815,21858,21949,22017,22154,22235,22303,22504,22547,22644,22704,22784,22840,22916,22976,23029,23074,23112,23163,24191,24243,24284,24340,24404,24462,24537,24604,24658,24736,24894,25019,25068,29015,29085,29154,29214,29267,29324,29422,29486,29544,29596,29630,29701,29757,30845,30882,30938,30981,31087,31161,31227,31330,31398,31493,31572,31673,31756,31806,31867,31927,31993,32048,32122,32189,32255,32313,32370,32458,32549,32664,32841,33008,33106,33164,33228,33387,33436,33480,33522,33565,33687,33744,33785,33855,33899,33955,34011,34102,34206,34304,34438,34537,34648,34739,34818,34881,35019,35099,35202,35332,35430,35490,35565,35642,35700,35795,35900,36006,36099,36192,36296,36356,36414,36517,36573,36643,36730,36781,36831,36909,36988,37082,37120,37161,37203,37259,37317,37399,37453,37495,37562,37626,37689,37751,37842,37912,38023,38073,38133,38225,38319,38410,38503,38592,38683,38775,38869,38938,39007,39089,39144,39195,39239,39286,39349,39421,39470,39521,39569,39618,39689,40109,40154,40218,40263,40327,40374,40428,40475,40521,40568,40617,40664,40947,41028,41100,41172,41228,41297,41388,41435,41525,41589,41678,41760,41865,41962,42003,42092,42160,42210,42357,42401,42447,42491,42543,42645,42715,42788,42877,42985,43024,43100,43143,43293,43348,43415,43485,43596,43657,43764,43831,43891,43955,44041,44118,44188,44286,44362,44414,44473,44553,44642,44729,44780,44839,44895,44998,45059,45117,45171,45215,45268,45385,45543,45624,45682,45748,45798,45945,46050,46174,46352,46432,46548,46625,46728,46805,46873,46935,47015,47088,47160,47211", "endColumns": "45,44,50,54,108,63,76,91,64,92,50,47,71,62,38,50,58,107,54,56,70,69,165,83,161,79,69,55,59,54,58,51,56,155,71,66,89,60,65,43,49,53,54,62,52,161,58,50,80,60,53,39,62,62,71,70,84,42,146,72,64,104,46,65,71,52,61,80,63,39,62,96,69,61,62,41,51,77,53,49,58,69,81,40,42,97,70,122,53,96,64,72,39,63,61,71,39,50,91,57,50,49,54,53,73,102,113,50,62,54,56,73,89,54,58,47,94,74,41,46,40,63,61,61,49,52,71,92,64,97,64,40,43,51,94,89,63,69,43,56,37,78,64,44,136,72,41,41,41,56,51,52,66,73,73,66,83,71,63,55,124,73,53,150,59,54,63,109,68,97,61,130,65,88,86,62,103,87,55,63,58,54,72,58,66,53,53,199,35,39,63,89,52,56,119,40,59,58,79,65,71,52,48,41,43,112,55,61,51,37,52,42,90,67,136,80,67,64,42,96,59,79,55,75,59,52,44,37,50,62,51,40,55,63,57,74,66,53,77,157,124,48,92,69,68,59,52,56,97,63,57,51,33,70,55,72,36,55,42,105,73,65,102,67,94,78,100,82,49,60,59,65,54,73,66,65,57,56,87,90,114,176,86,97,57,63,158,48,43,41,42,121,56,40,69,43,55,55,90,103,97,133,98,110,90,78,62,137,79,102,129,97,59,74,76,57,94,104,105,92,92,103,59,57,102,55,69,86,50,49,77,78,93,37,40,41,55,57,81,53,41,66,63,62,61,90,69,110,49,59,91,93,90,92,88,90,91,93,68,68,81,54,50,43,46,62,71,48,50,47,48,70,60,44,63,44,63,46,53,46,45,46,48,46,46,80,71,71,55,68,90,46,89,63,88,81,104,96,40,88,67,49,70,43,45,43,51,101,69,72,88,107,38,75,42,48,54,66,69,110,60,106,66,59,63,85,76,69,97,75,51,58,79,88,86,50,58,55,102,60,57,53,43,52,116,157,80,57,65,49,86,104,123,177,79,115,76,102,76,67,61,79,72,71,50,36", "endOffsets": "556,3255,3306,3361,3470,3534,3611,3703,3768,3861,3912,3960,4032,4095,4134,4185,4244,4352,4407,4464,4535,4605,4771,4855,5017,5097,5167,5223,5283,5338,5397,5449,5506,5662,5734,5801,5891,5952,6018,6062,6112,6166,6221,6284,6337,6499,6558,6609,6690,6751,6805,6845,6908,6971,7043,7114,7199,7242,7389,7462,7527,7632,7679,7745,7817,7870,7932,8013,8077,8117,8180,8277,8347,8409,8472,8514,8566,8644,8698,9161,9220,9290,10084,10125,10168,10266,10557,10680,10734,10831,10896,11046,11086,11150,13308,13380,13520,13571,13663,13721,13772,13822,13877,13931,14005,14108,14222,14273,14336,14391,14448,14522,14612,14667,14726,14774,14869,14944,14986,15033,15074,15138,15200,15262,15312,15365,15437,15530,15595,15693,15758,15799,15843,16017,16112,16202,16266,16336,16380,16437,16475,16554,17018,17063,17200,17273,17315,17357,17399,17456,17508,17561,17628,17702,17776,17843,17927,17999,18063,18119,18244,18318,18372,18523,18583,18638,18702,18812,18881,18979,19041,19172,19238,19327,19414,19477,19581,19669,19725,19789,19848,19903,19976,20035,20102,20156,20210,20410,20446,20486,20550,20640,20693,20750,20870,20911,20971,21030,21110,21176,21248,21301,21350,21392,21436,21549,21605,21667,21719,21757,21810,21853,21944,22012,22149,22230,22298,22363,22542,22639,22699,22779,22835,22911,22971,23024,23069,23107,23158,23221,24238,24279,24335,24399,24457,24532,24599,24653,24731,24889,25014,25063,25156,29080,29149,29209,29262,29319,29417,29481,29539,29591,29625,29696,29752,29825,30877,30933,30976,31082,31156,31222,31325,31393,31488,31567,31668,31751,31801,31862,31922,31988,32043,32117,32184,32250,32308,32365,32453,32544,32659,32836,32923,33101,33159,33223,33382,33431,33475,33517,33560,33682,33739,33780,33850,33894,33950,34006,34097,34201,34299,34433,34532,34643,34734,34813,34876,35014,35094,35197,35327,35425,35485,35560,35637,35695,35790,35895,36001,36094,36187,36291,36351,36409,36512,36568,36638,36725,36776,36826,36904,36983,37077,37115,37156,37198,37254,37312,37394,37448,37490,37557,37621,37684,37746,37837,37907,38018,38068,38128,38220,38314,38405,38498,38587,38678,38770,38864,38933,39002,39084,39139,39190,39234,39281,39344,39416,39465,39516,39564,39613,39684,39745,40149,40213,40258,40322,40369,40423,40470,40516,40563,40612,40659,40706,41023,41095,41167,41223,41292,41383,41430,41520,41584,41673,41755,41860,41957,41998,42087,42155,42205,42276,42396,42442,42486,42538,42640,42710,42783,42872,42980,43019,43095,43138,43187,43343,43410,43480,43591,43652,43759,43826,43886,43950,44036,44113,44183,44281,44357,44409,44468,44548,44637,44724,44775,44834,44890,44993,45054,45112,45166,45210,45263,45380,45538,45619,45677,45743,45793,45880,46045,46169,46347,46427,46543,46620,46723,46800,46868,46930,47010,47083,47155,47206,47243"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\aff62db08cff511cc4373673ada3b6cf\\transformed\\play-services-basement-18.5.0\\res\\values-ar\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "154", "startColumns": "4", "startOffsets": "12109", "endColumns": "129", "endOffsets": "12234"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8ba171b74415ef9d04b1f3a055c632e8\\transformed\\play-services-ads-24.2.0\\res\\values-ar\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "380,381,382,396,397,398,399,400,401,402,403,404,405,406,525,526,527,528,529,530,531,615", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "28860,28905,28957,29830,29898,29970,30073,30138,30263,30383,30522,30579,30640,30756,39750,39792,39875,39911,39946,39993,40065,45885", "endColumns": "44,51,57,67,71,102,64,124,119,138,56,60,115,88,41,82,35,34,46,71,43,59", "endOffsets": "28900,28952,29010,29893,29965,30068,30133,30258,30378,30517,30574,30635,30751,30840,39787,39870,39906,39941,39988,40060,40104,45940"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cf3dd3f063a8dc2168576dc8a6639ef4\\transformed\\core-1.13.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "124,125,126,127,128,129,130,579", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "9295,9388,9490,9585,9688,9791,9893,43192", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "9383,9485,9580,9683,9786,9888,10002,43288"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ef3205b01dd1e8d62ca95413fbcfcde0\\transformed\\play-services-base-18.5.0\\res\\values-ar\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "146,147,148,149,150,151,152,153,155,156,157,158,159,160,161,162,163", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "11155,11259,11403,11525,11630,11768,11896,12007,12239,12376,12480,12630,12752,12891,13037,13101,13167", "endColumns": "103,143,121,104,137,127,110,101,136,103,149,121,138,145,63,65,83", "endOffsets": "11254,11398,11520,11625,11763,11891,12002,12104,12371,12475,12625,12747,12886,13032,13096,13162,13246"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-mergeDebugResources-49:\\values-ar\\values-ar.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\464e6a7dcfefd0da870c4050aa381b0c\\transformed\\material-1.12.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,465,543,619,703,795,878,979,1098,1175,1234,1297,1388,1457,1524,1624,1687,1752,1813,1881,1943,2001,2115,2175,2236,2293,2366,2489,2570,2662,2769,2867,2947,3095,3176,3257,3385,3474,3550,3603,3657,3723,3801,3881,3952,4034,4106,4180,4253,4323,4432,4523,4594,4684,4779,4853,4936,5029,5078,5159,5228,5314,5399,5461,5525,5588,5657,5766,5876,5973,6073,6130,6188,6268,6347,6422", "endLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84", "endColumns": "12,77,75,83,91,82,100,118,76,58,62,90,68,66,99,62,64,60,67,61,57,113,59,60,56,72,122,80,91,106,97,79,147,80,80,127,88,75,52,53,65,77,79,70,81,71,73,72,69,108,90,70,89,94,73,82,92,48,80,68,85,84,61,63,62,68,108,109,96,99,56,57,79,78,74,75", "endOffsets": "460,538,614,698,790,873,974,1093,1170,1229,1292,1383,1452,1519,1619,1682,1747,1808,1876,1938,1996,2110,2170,2231,2288,2361,2484,2565,2657,2764,2862,2942,3090,3171,3252,3380,3469,3545,3598,3652,3718,3796,3876,3947,4029,4101,4175,4248,4318,4427,4518,4589,4679,4774,4848,4931,5024,5073,5154,5223,5309,5394,5456,5520,5583,5652,5761,5871,5968,6068,6125,6183,6263,6342,6417,6493"}, "to": {"startLines": "2,115,116,117,118,119,133,134,140,201,202,212,292,293,306,307,308,309,310,311,312,313,314,315,316,317,318,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,427,538,539,558", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,8631,8709,8785,8869,8961,10140,10241,10770,15652,15711,16363,22172,22241,23030,23130,23193,23258,23319,23387,23449,23507,23621,23681,23742,23799,23872,24901,24982,25074,25181,25279,25359,25507,25588,25669,25797,25886,25962,26015,26069,26135,26213,26293,26364,26446,26518,26592,26665,26735,26844,26935,27006,27096,27191,27265,27348,27441,27490,27571,27640,27726,27811,27873,27937,28000,28069,28178,28288,28385,28485,28542,32433,40300,40379,41786", "endLines": "9,115,116,117,118,119,133,134,140,201,202,212,292,293,306,307,308,309,310,311,312,313,314,315,316,317,318,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,427,538,539,558", "endColumns": "12,77,75,83,91,82,100,118,76,58,62,90,68,66,99,62,64,60,67,61,57,113,59,60,56,72,122,80,91,106,97,79,147,80,80,127,88,75,52,53,65,77,79,70,81,71,73,72,69,108,90,70,89,94,73,82,92,48,80,68,85,84,61,63,62,68,108,109,96,99,56,57,79,78,74,75", "endOffsets": "510,8704,8780,8864,8956,9039,10236,10355,10842,15706,15769,16449,22236,22303,23125,23188,23253,23314,23382,23444,23502,23616,23676,23737,23794,23867,23990,24977,25069,25176,25274,25354,25502,25583,25664,25792,25881,25957,26010,26064,26130,26208,26288,26359,26441,26513,26587,26660,26730,26839,26930,27001,27091,27186,27260,27343,27436,27485,27566,27635,27721,27806,27868,27932,27995,28064,28173,28283,28380,28480,28537,28595,32508,40374,40449,41857"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1a44ad6b726c152c4fe16ed54c5a8c02\\transformed\\appcompat-1.7.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,424,506,607,721,801,880,971,1064,1156,1250,1350,1443,1538,1631,1722,1816,1895,2000,2098,2196,2304,2404,2507,2662,2759", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "208,312,419,501,602,716,796,875,966,1059,1151,1245,1345,1438,1533,1626,1717,1811,1890,1995,2093,2191,2299,2399,2502,2657,2754,2836"}, "to": {"startLines": "11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,537", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "561,669,773,880,962,1063,1177,1257,1336,1427,1520,1612,1706,1806,1899,1994,2087,2178,2272,2351,2456,2554,2652,2760,2860,2963,3118,40218", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "664,768,875,957,1058,1172,1252,1331,1422,1515,1607,1701,1801,1894,1989,2082,2173,2267,2346,2451,2549,2647,2755,2855,2958,3113,3210,40295"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\46c7e1c2c774c76fe5e74081b097ef9a\\transformed\\browser-1.8.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,361", "endColumns": "99,97,107,101", "endOffsets": "150,248,356,458"}, "to": {"startLines": "164,213,214,215", "startColumns": "4,4,4,4", "startOffsets": "13254,16454,16552,16660", "endColumns": "99,97,107,101", "endOffsets": "13349,16547,16655,16757"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\src\\main\\res\\values-ar\\strings.xml", "from": {"startLines": "102,9,76,359,461,358,475,460,473,482,462,292,189,97,31,199,170,221,279,365,281,280,284,283,322,321,364,366,367,184,174,398,193,450,449,70,474,493,131,326,330,100,343,42,41,490,489,274,1,403,412,396,187,98,151,55,37,18,267,122,108,109,103,104,144,163,29,38,344,85,115,114,86,305,30,198,276,61,145,56,119,50,269,526,476,392,369,416,388,65,282,67,190,246,389,79,113,112,80,335,27,374,523,439,440,15,433,77,130,372,499,331,91,351,53,256,52,354,261,259,260,136,177,195,288,410,406,149,255,17,456,469,530,337,355,509,501,10,443,273,457,409,121,157,158,159,506,340,183,146,147,123,387,371,129,504,34,293,297,164,26,25,167,315,301,373,481,251,75,107,90,89,249,87,88,74,316,375,298,498,502,503,500,510,511,325,132,133,257,168,49,528,512,99,134,272,182,254,341,101,342,166,407,169,128,192,162,95,395,505,36,438,202,175,434,16,21,20,417,51,413,368,299,486,160,93,527,217,161,94,28,120,435,309,250,491,495,171,124,5,4,306,83,111,110,84,307,445,39,310,296,318,468,508,69,60,418,45,46,71,165,62,54,64,66,252,253,58,68,384,383,287,286,285,452,451,492,78,448,453,311,313,312,314,397,317,345,405,11,529,319,210,211,215,320,212,213,209,214,205,206,218,208,207,485,484,12,220,8,524,424,428,425,426,427,522,411,422,423,421,294,352,328,455,118,454,390,334,327,81,82,57,35,7,63,304,181,180,196,338,225,137,139,264,243,244,263,245,262,242,265,179,138,197,361,300,153,268,266,188,92,339,22,471,472,360,140,141,142,143,401,228,353,329,348,347,336,346,295,291,391,19,178,194,6,404,385,258,386,270,277,154,229,230,191,247,382,381,380,148,150,224,271,223,222,393,507,135,394,219,525,275,278,116,117,201,43,44,40,200,399,185,152,186,248,400,226,402,227,32,431,432,429,96,494,216,483,48,47,33,176,59,430,477,465,467,466,478,239,234,240,237,235,233,238,236,308,444", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6204,414,4586,22537,29075,22473,30144,28983,29989,30657,29184,18980,11243,5915,1549,11924,10149,13655,18003,22813,18128,18058,18323,18239,20839,20759,22743,22870,22926,10918,10290,24538,11479,28111,28039,4214,30054,31596,8060,21066,21248,6101,21860,2230,2177,31178,31119,17596,16,24875,25655,24376,11108,5978,9261,3202,1880,784,17036,7600,6474,6539,6250,6297,8779,9751,1445,1965,21915,5100,7026,6956,5163,19840,1507,11872,17717,3608,8851,3273,7370,2902,17230,33716,30221,24122,23046,25834,23907,3839,18199,3978,11315,15526,23972,4769,6864,6806,4820,21431,1332,23328,33480,27557,27660,646,27197,4646,8003,23185,31937,21298,5593,22186,3046,16194,2999,22332,16539,16415,16477,8332,10483,11627,18783,25532,25096,9094,16153,740,28862,29695,34053,21528,22373,32595,32081,459,27801,17531,28914,25395,7527,9508,9550,9592,32419,21713,10865,8901,8968,7673,23840,23101,7931,32264,1702,19028,19394,9804,1181,1121,9985,20347,19627,23259,30559,15890,4455,6408,5504,5417,15749,5225,5329,4399,20411,23382,19468,31864,32138,32197,32027,32639,32693,21030,8126,8166,16236,10040,2845,33877,32893,6041,8230,17451,10799,16081,21765,6155,21818,9941,25194,10093,7869,11427,9713,5808,24333,32328,1812,27420,12112,10349,27260,697,943,883,25931,2943,25709,22986,19523,31025,9634,5701,33814,13391,9672,5752,1387,7452,27325,20059,15812,31340,31701,10208,7747,197,128,19903,4985,6708,6644,5042,19956,27917,2029,20113,19321,20532,29639,32552,4108,3542,26011,2443,2511,4281,9858,3662,3141,3779,3912,15952,16007,3425,4042,23636,23579,18695,18604,18489,28354,28267,31498,4711,27975,28531,20169,20260,20218,20304,24416,20475,21955,25026,497,33997,20569,12756,12847,13240,20625,12951,13050,12665,13161,12231,12294,13443,12562,12432,30927,30867,541,13578,356,33554,26387,26782,26492,26585,26678,33420,25597,26228,26331,26158,19153,22234,21152,28784,7291,28690,24012,21390,21110,4871,4927,3343,1758,314,3712,19774,10736,10674,11699,21592,14030,8382,8501,16787,15252,15346,16694,15437,16603,15160,16879,10605,8432,11790,22653,19576,9385,17183,16973,11171,5652,21662,1040,29869,29918,22592,8561,8606,8670,8715,24777,14287,22285,21202,22092,22043,21481,21996,19240,18910,24050,827,10536,11536,267,24936,23694,16326,23758,17273,17795,9429,14341,14430,11377,15598,23535,23489,23445,9042,9159,13960,17378,13871,13763,24245,32476,8289,24284,13523,33649,17647,17892,7123,7184,12045,2293,2357,2100,11975,24590,10973,9333,11049,15669,24688,14141,24824,14228,1588,27033,27136,26888,5861,31657,13338,30750,2687,2606,1644,10417,3492,26946,30292,29257,29461,29381,30397,15005,14627,15082,14864,14730,14547,14932,14792,20008,27880", "endColumns": "45,44,59,54,108,63,76,91,64,92,50,47,71,62,38,50,58,107,54,56,70,69,165,83,161,79,69,55,59,54,58,51,56,155,71,66,89,60,65,43,49,53,54,62,52,161,58,50,80,60,53,39,62,62,71,70,84,42,146,72,64,104,46,65,71,52,61,63,39,62,96,69,61,62,41,51,77,53,49,69,81,40,42,97,70,122,53,96,64,72,39,63,61,71,39,50,91,57,50,49,54,53,73,102,113,50,62,64,56,73,89,54,58,47,94,41,46,40,63,61,61,49,52,71,92,64,97,64,40,43,51,94,89,63,69,43,56,37,78,64,44,136,72,41,41,41,56,51,52,66,73,73,66,83,71,63,55,124,73,53,150,59,54,63,109,68,97,61,130,65,88,86,62,103,87,55,63,58,54,72,58,66,53,53,199,35,39,63,89,52,56,119,40,59,58,79,65,71,52,48,41,43,112,55,61,51,37,52,42,90,67,136,80,67,64,42,96,59,79,55,75,59,52,44,37,50,62,51,40,55,57,74,66,53,77,157,124,48,92,69,68,52,56,97,63,57,51,33,70,55,72,36,55,42,105,65,102,67,94,78,82,49,60,59,65,54,73,66,65,57,56,87,90,114,176,86,97,57,63,158,48,43,41,42,121,56,40,69,43,55,55,90,103,97,133,98,110,90,78,62,137,79,102,129,97,59,74,76,57,94,104,105,92,92,103,59,57,102,55,69,86,50,49,77,78,93,37,40,41,55,57,81,53,41,66,65,62,61,90,69,110,49,59,91,93,90,92,88,90,91,93,68,68,81,54,50,43,46,62,71,48,50,47,48,70,60,44,63,44,63,46,53,46,45,46,48,46,46,80,69,71,55,68,90,46,89,63,88,81,104,96,40,88,67,49,70,43,45,43,51,101,69,72,88,107,38,75,42,48,54,66,69,110,60,106,66,63,85,76,69,97,75,51,58,79,88,86,50,58,55,102,60,57,53,43,52,116,157,80,57,65,49,86,104,123,177,79,115,76,102,76,67,61,79,72,71,50,36", "endOffsets": "6245,454,4641,22587,29179,22532,30216,29070,30049,30745,29230,19023,11310,5973,1583,11970,10203,13758,18053,22865,18194,18123,18484,18318,20996,20834,22808,22921,22981,10968,10344,24585,11531,28262,28106,4276,30139,31652,8121,21105,21293,6150,21910,2288,2225,31335,31173,17642,92,24931,25704,24411,11166,6036,9328,3268,1960,822,17178,7668,6534,6639,6292,6358,8846,9799,1502,2024,21950,5158,7118,7021,5220,19898,1544,11919,17790,3657,8896,3338,7447,2938,17268,33809,30287,24240,23095,25926,23967,3907,18234,4037,11372,15593,24007,4815,6951,6859,4866,21476,1382,23377,33549,27655,27769,692,27255,4706,8055,23254,32022,21348,5647,22229,3136,16231,3041,22368,16598,16472,16534,8377,10531,11694,18871,25592,25189,9154,16189,779,28909,29785,34138,21587,22438,32634,32133,492,27875,17591,28954,25527,7595,9545,9587,9629,32471,21760,10913,8963,9037,7742,23902,23180,7998,32323,1753,19148,19463,9853,1327,1176,10035,20406,19732,23323,30652,15947,4581,6469,5588,5499,15807,5324,5412,4450,20470,23436,19518,31932,32192,32259,32076,32688,32888,21061,8161,8225,16321,10088,2897,33992,32929,6096,8284,17526,10860,16148,21813,6199,21855,9980,25302,10144,7926,11474,9746,5856,24371,32414,1875,27552,12188,10412,27320,735,1035,938,26006,2994,25780,23041,19571,31065,9667,5747,33872,13438,9708,5803,1440,7522,27387,20108,15885,31493,31821,10252,7835,262,192,19951,5037,6801,6703,5095,20003,27946,2095,20164,19389,20564,29690,32590,4209,3603,26109,2506,2601,4355,9936,3707,3197,3834,3973,16002,16076,3487,4103,23689,23631,18778,18690,18599,28526,28349,31591,4764,28034,28685,20213,20299,20255,20342,24533,20527,21991,25091,536,34048,20620,12842,12946,13333,20754,13045,13156,12751,13235,12289,12427,13518,12660,12557,31020,30922,611,13650,409,33644,26487,26883,26580,26673,26777,33475,25650,26326,26382,26223,19235,22280,21197,28857,7365,28779,24045,21426,21147,4922,4980,3420,1807,351,3774,19835,10794,10731,11785,21657,14136,8427,8556,16874,15341,15432,16782,15521,16689,15247,16968,10669,8496,11867,22703,19622,9424,17225,17031,11238,5696,21708,1083,29913,29984,22648,8601,8665,8710,8774,24819,14336,22327,21243,22134,22087,21523,22038,19316,18975,24117,878,10600,11622,309,25021,23753,16410,23835,17373,17887,9465,14425,14493,11422,15664,23574,23530,23484,9089,9256,14025,17446,13955,13866,24279,32547,8327,24328,13573,33711,17712,17998,7179,7286,12107,2352,2438,2172,12040,24683,11044,9380,11103,15744,24772,14223,24870,14282,1639,27131,27192,26941,5910,31696,13386,30862,2840,2682,1697,10478,3537,27028,30392,29376,29634,29456,30508,15077,14725,15154,14927,14787,14622,15000,14859,20054,27912"}, "to": {"startLines": "10,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,120,121,129,130,131,132,135,136,137,138,139,141,142,143,162,163,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,203,204,205,206,207,208,209,210,211,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,294,295,296,297,298,299,300,301,302,303,304,305,319,320,321,322,323,324,325,326,327,328,329,330,379,380,381,382,383,384,385,386,387,388,389,390,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,525,526,527,528,529,530,531,532,533,534,535,536,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,559,560,561,562,563,564,565,566,567,568,569,570,571,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "515,3215,3260,3320,3375,3484,3548,3625,3717,3782,3875,3926,3974,4046,4109,4148,4199,4258,4366,4421,4478,4549,4619,4785,4869,5031,5111,5181,5237,5297,5352,5411,5463,5520,5676,5748,5815,5905,5966,6032,6076,6126,6180,6235,6298,6351,6513,6572,6623,6704,6765,6819,6859,6922,6985,7057,7128,7213,7256,7403,7476,7541,7646,7693,7759,7831,7884,7946,8010,8050,8113,8210,8280,8342,8405,8447,8499,8577,9044,9094,9876,9958,9999,10042,10360,10431,10554,10608,10705,10847,10920,10960,13120,13182,13354,13394,13445,13537,13595,13646,13696,13751,13805,13879,13982,14096,14147,14210,14275,14332,14406,14496,14551,14610,14658,14753,14795,14842,14883,14947,15009,15071,15121,15174,15246,15339,15404,15502,15567,15608,15774,15826,15921,16011,16075,16145,16189,16246,16284,16762,16827,16872,17009,17082,17124,17166,17208,17265,17317,17370,17437,17511,17585,17652,17736,17808,17872,17928,18053,18127,18181,18332,18392,18447,18511,18621,18690,18788,18850,18981,19047,19136,19223,19286,19390,19478,19534,19598,19657,19712,19785,19844,19911,19965,20019,20219,20255,20295,20359,20449,20502,20559,20679,20720,20780,20839,20919,20985,21057,21110,21159,21201,21245,21358,21414,21476,21528,21566,21619,21662,21753,21821,21958,22039,22107,22308,22351,22448,22508,22588,22644,22720,22780,22833,22878,22916,22967,23995,24047,24088,24144,24202,24277,24344,24398,24476,24634,24759,24808,28755,28825,28894,28947,29004,29102,29166,29224,29276,29310,29381,29437,30525,30562,30618,30661,30767,30833,30936,31004,31099,31178,31261,31311,31372,31432,31498,31553,31627,31694,31760,31818,31875,31963,32054,32169,32346,32513,32611,32669,32733,32892,32941,32985,33027,33070,33192,33249,33290,33360,33404,33460,33516,33607,33711,33809,33943,34042,34153,34244,34323,34386,34524,34604,34707,34837,34935,34995,35070,35147,35205,35300,35405,35511,35604,35697,35801,35861,35919,36022,36078,36148,36235,36286,36336,36414,36493,36587,36625,36666,36708,36764,36822,36904,36958,37000,37067,37133,37196,37258,37349,37419,37530,37580,37640,37732,37826,37917,38010,38099,38190,38282,38376,38445,38514,38596,38651,38702,38746,38793,38856,38928,38977,39028,39076,39125,39196,39616,39661,39725,39770,39834,39881,39935,39982,40028,40075,40124,40171,40454,40535,40605,40677,40733,40802,40893,40940,41030,41094,41183,41265,41370,41467,41508,41597,41665,41715,41862,41906,41952,41996,42048,42150,42220,42293,42382,42490,42529,42605,42648,42798,42853,42920,42990,43101,43162,43269,43336,43400,43486,43563,43633,43731,43807,43859,43918,43998,44087,44174,44225,44284,44340,44443,44504,44562,44616,44660,44713,44830,44988,45069,45127,45193,45243,45390,45495,45619,45797,45877,45993,46070,46173,46250,46318,46380,46460,46533,46605,46656", "endColumns": "45,44,59,54,108,63,76,91,64,92,50,47,71,62,38,50,58,107,54,56,70,69,165,83,161,79,69,55,59,54,58,51,56,155,71,66,89,60,65,43,49,53,54,62,52,161,58,50,80,60,53,39,62,62,71,70,84,42,146,72,64,104,46,65,71,52,61,63,39,62,96,69,61,62,41,51,77,53,49,69,81,40,42,97,70,122,53,96,64,72,39,63,61,71,39,50,91,57,50,49,54,53,73,102,113,50,62,64,56,73,89,54,58,47,94,41,46,40,63,61,61,49,52,71,92,64,97,64,40,43,51,94,89,63,69,43,56,37,78,64,44,136,72,41,41,41,56,51,52,66,73,73,66,83,71,63,55,124,73,53,150,59,54,63,109,68,97,61,130,65,88,86,62,103,87,55,63,58,54,72,58,66,53,53,199,35,39,63,89,52,56,119,40,59,58,79,65,71,52,48,41,43,112,55,61,51,37,52,42,90,67,136,80,67,64,42,96,59,79,55,75,59,52,44,37,50,62,51,40,55,57,74,66,53,77,157,124,48,92,69,68,52,56,97,63,57,51,33,70,55,72,36,55,42,105,65,102,67,94,78,82,49,60,59,65,54,73,66,65,57,56,87,90,114,176,86,97,57,63,158,48,43,41,42,121,56,40,69,43,55,55,90,103,97,133,98,110,90,78,62,137,79,102,129,97,59,74,76,57,94,104,105,92,92,103,59,57,102,55,69,86,50,49,77,78,93,37,40,41,55,57,81,53,41,66,65,62,61,90,69,110,49,59,91,93,90,92,88,90,91,93,68,68,81,54,50,43,46,62,71,48,50,47,48,70,60,44,63,44,63,46,53,46,45,46,48,46,46,80,69,71,55,68,90,46,89,63,88,81,104,96,40,88,67,49,70,43,45,43,51,101,69,72,88,107,38,75,42,48,54,66,69,110,60,106,66,63,85,76,69,97,75,51,58,79,88,86,50,58,55,102,60,57,53,43,52,116,157,80,57,65,49,86,104,123,177,79,115,76,102,76,67,61,79,72,71,50,36", "endOffsets": "556,3255,3315,3370,3479,3543,3620,3712,3777,3870,3921,3969,4041,4104,4143,4194,4253,4361,4416,4473,4544,4614,4780,4864,5026,5106,5176,5232,5292,5347,5406,5458,5515,5671,5743,5810,5900,5961,6027,6071,6121,6175,6230,6293,6346,6508,6567,6618,6699,6760,6814,6854,6917,6980,7052,7123,7208,7251,7398,7471,7536,7641,7688,7754,7826,7879,7941,8005,8045,8108,8205,8275,8337,8400,8442,8494,8572,8626,9089,9159,9953,9994,10037,10135,10426,10549,10603,10700,10765,10915,10955,11019,13177,13249,13389,13440,13532,13590,13641,13691,13746,13800,13874,13977,14091,14142,14205,14270,14327,14401,14491,14546,14605,14653,14748,14790,14837,14878,14942,15004,15066,15116,15169,15241,15334,15399,15497,15562,15603,15647,15821,15916,16006,16070,16140,16184,16241,16279,16358,16822,16867,17004,17077,17119,17161,17203,17260,17312,17365,17432,17506,17580,17647,17731,17803,17867,17923,18048,18122,18176,18327,18387,18442,18506,18616,18685,18783,18845,18976,19042,19131,19218,19281,19385,19473,19529,19593,19652,19707,19780,19839,19906,19960,20014,20214,20250,20290,20354,20444,20497,20554,20674,20715,20775,20834,20914,20980,21052,21105,21154,21196,21240,21353,21409,21471,21523,21561,21614,21657,21748,21816,21953,22034,22102,22167,22346,22443,22503,22583,22639,22715,22775,22828,22873,22911,22962,23025,24042,24083,24139,24197,24272,24339,24393,24471,24629,24754,24803,24896,28820,28889,28942,28999,29097,29161,29219,29271,29305,29376,29432,29505,30557,30613,30656,30762,30828,30931,30999,31094,31173,31256,31306,31367,31427,31493,31548,31622,31689,31755,31813,31870,31958,32049,32164,32341,32428,32606,32664,32728,32887,32936,32980,33022,33065,33187,33244,33285,33355,33399,33455,33511,33602,33706,33804,33938,34037,34148,34239,34318,34381,34519,34599,34702,34832,34930,34990,35065,35142,35200,35295,35400,35506,35599,35692,35796,35856,35914,36017,36073,36143,36230,36281,36331,36409,36488,36582,36620,36661,36703,36759,36817,36899,36953,36995,37062,37128,37191,37253,37344,37414,37525,37575,37635,37727,37821,37912,38005,38094,38185,38277,38371,38440,38509,38591,38646,38697,38741,38788,38851,38923,38972,39023,39071,39120,39191,39252,39656,39720,39765,39829,39876,39930,39977,40023,40070,40119,40166,40213,40530,40600,40672,40728,40797,40888,40935,41025,41089,41178,41260,41365,41462,41503,41592,41660,41710,41781,41901,41947,41991,42043,42145,42215,42288,42377,42485,42524,42600,42643,42692,42848,42915,42985,43096,43157,43264,43331,43395,43481,43558,43628,43726,43802,43854,43913,43993,44082,44169,44220,44279,44335,44438,44499,44557,44611,44655,44708,44825,44983,45064,45122,45188,45238,45325,45490,45614,45792,45872,45988,46065,46168,46245,46313,46375,46455,46528,46600,46651,46688"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\aff62db08cff511cc4373673ada3b6cf\\transformed\\play-services-basement-18.5.0\\res\\values-ar\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "152", "startColumns": "4", "startOffsets": "11978", "endColumns": "129", "endOffsets": "12103"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8ba171b74415ef9d04b1f3a055c632e8\\transformed\\play-services-ads-24.2.0\\res\\values-ar\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,240,288,342,406,474,573,634,755,871,1006,1059,1116,1228,1313,1351,1430,1462,1493,1536,1604,1644", "endColumns": "40,47,53,63,67,98,60,120,115,134,52,56,111,84,37,78,31,30,42,67,39,55", "endOffsets": "239,287,341,405,473,572,633,754,870,1005,1058,1115,1227,1312,1350,1429,1461,1492,1535,1603,1643,1699"}, "to": {"startLines": "376,377,378,391,392,393,394,395,396,397,398,399,400,401,518,519,520,521,522,523,524,607", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "28600,28645,28697,29510,29578,29650,29753,29818,29943,30063,30202,30259,30320,30436,39257,39299,39382,39418,39453,39500,39572,45330", "endColumns": "44,51,57,67,71,102,64,124,119,138,56,60,115,88,41,82,35,34,46,71,43,59", "endOffsets": "28640,28692,28750,29573,29645,29748,29813,29938,30058,30197,30254,30315,30431,30520,39294,39377,39413,39448,39495,39567,39611,45385"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cf3dd3f063a8dc2168576dc8a6639ef4\\transformed\\core-1.13.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,250,345,448,551,653,767", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "143,245,340,443,546,648,762,863"}, "to": {"startLines": "122,123,124,125,126,127,128,572", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "9164,9257,9359,9454,9557,9660,9762,42697", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "9252,9354,9449,9552,9655,9757,9871,42793"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ef3205b01dd1e8d62ca95413fbcfcde0\\transformed\\play-services-base-18.5.0\\res\\values-ar\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,433,551,652,786,910,1017,1115,1248,1348,1494,1612,1747,1889,1949,2011", "endColumns": "99,139,117,100,133,123,106,97,132,99,145,117,134,141,59,61,79", "endOffsets": "292,432,550,651,785,909,1016,1114,1247,1347,1493,1611,1746,1888,1948,2010,2090"}, "to": {"startLines": "144,145,146,147,148,149,150,151,153,154,155,156,157,158,159,160,161", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "11024,11128,11272,11394,11499,11637,11765,11876,12108,12245,12349,12499,12621,12760,12906,12970,13036", "endColumns": "103,143,121,104,137,127,110,101,136,103,149,121,138,145,63,65,83", "endOffsets": "11123,11267,11389,11494,11632,11760,11871,11973,12240,12344,12494,12616,12755,12901,12965,13031,13115"}}]}]}