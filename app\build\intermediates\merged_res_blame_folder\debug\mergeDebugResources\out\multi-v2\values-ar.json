{"logs": [{"outputFile": "com.mahmoudffyt.gfxbooster.app-mergeDebugResources-49:/values-ar/values-ar.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\464e6a7dcfefd0da870c4050aa381b0c\\transformed\\material-1.12.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,113,114,115,116,117,131,132,138,199,200,210,285,286,298,299,300,301,302,303,304,305,306,307,308,309,310,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,411,515,516,533", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,8550,8628,8704,8788,8880,10059,10160,10689,15571,15630,16282,21786,21855,22586,22686,22749,22814,22875,22943,23005,23063,23177,23237,23298,23355,23428,24376,24457,24549,24656,24754,24834,24982,25063,25144,25272,25361,25437,25490,25544,25610,25688,25768,25839,25921,25993,26067,26140,26210,26319,26410,26481,26571,26666,26740,26823,26916,26965,27046,27115,27201,27286,27348,27412,27475,27544,27653,27763,27860,27960,28017,31380,38944,39023,40279", "endLines": "9,113,114,115,116,117,131,132,138,199,200,210,285,286,298,299,300,301,302,303,304,305,306,307,308,309,310,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,411,515,516,533", "endColumns": "12,77,75,83,91,82,100,118,76,58,62,90,68,66,99,62,64,60,67,61,57,113,59,60,56,72,122,80,91,106,97,79,147,80,80,127,88,75,52,53,65,77,79,70,81,71,73,72,69,108,90,70,89,94,73,82,92,48,80,68,85,84,61,63,62,68,108,109,96,99,56,57,79,78,74,75", "endOffsets": "510,8623,8699,8783,8875,8958,10155,10274,10761,15625,15688,16368,21850,21917,22681,22744,22809,22870,22938,23000,23058,23172,23232,23293,23350,23423,23546,24452,24544,24651,24749,24829,24977,25058,25139,25267,25356,25432,25485,25539,25605,25683,25763,25834,25916,25988,26062,26135,26205,26314,26405,26476,26566,26661,26735,26818,26911,26960,27041,27110,27196,27281,27343,27407,27470,27539,27648,27758,27855,27955,28012,28070,31455,39018,39093,40350"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1a44ad6b726c152c4fe16ed54c5a8c02\\transformed\\appcompat-1.7.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,514", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "561,669,773,880,962,1063,1177,1257,1336,1427,1520,1612,1706,1806,1899,1994,2087,2178,2272,2351,2456,2554,2652,2760,2860,2963,3118,38862", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "664,768,875,957,1058,1172,1252,1331,1422,1515,1607,1701,1801,1894,1989,2082,2173,2267,2346,2451,2549,2647,2755,2855,2958,3113,3210,38939"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\46c7e1c2c774c76fe5e74081b097ef9a\\transformed\\browser-1.8.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "162,211,212,213", "startColumns": "4,4,4,4", "startOffsets": "13173,16373,16471,16579", "endColumns": "99,97,107,101", "endOffsets": "13268,16466,16574,16676"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\src\\main\\res\\values-ar\\strings.xml", "from": {"startLines": "-1,-1,76,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,108,109,-1,-1,-1,-1,-1,-1,-1,85,115,114,86,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,79,113,112,80,-1,-1,-1,-1,-1,-1,-1,-1,77,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,75,488,107,90,89,-1,87,88,74,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,83,111,110,84,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,78,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,81,82,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,486,485,484,487,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,-1,-1,-1,-1,-1,-1,-1,4,4,4,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,4,4,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,4,4,4,-1,4,4,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,4,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,4,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,4608,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,6508,6573,-1,-1,-1,-1,-1,-1,-1,5122,7060,6990,5185,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4791,6898,6840,4842,-1,-1,-1,-1,-1,-1,-1,-1,4668,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4477,31371,6430,5526,5439,-1,5247,5351,4408,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,5007,6742,6678,5064,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4733,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4893,4949,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,31276,31229,31176,31324,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endColumns": "-1,-1,59,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,64,104,-1,-1,-1,-1,-1,-1,-1,62,96,69,61,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,50,91,57,50,-1,-1,-1,-1,-1,-1,-1,-1,64,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,130,165,77,88,86,-1,103,87,68,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,56,97,63,57,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,57,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,55,57,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,47,46,52,46,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endOffsets": "-1,-1,4663,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,6568,6673,-1,-1,-1,-1,-1,-1,-1,5180,7152,7055,5242,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4837,6985,6893,4888,-1,-1,-1,-1,-1,-1,-1,-1,4728,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4603,31532,6503,5610,5521,-1,5346,5434,4472,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,5059,6835,6737,5117,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4786,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4944,5002,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,31319,31271,31224,31366,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "10,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,118,119,127,128,129,130,133,134,135,136,137,139,140,141,160,161,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,201,202,203,204,205,206,207,208,209,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,287,288,289,290,291,292,293,294,295,296,297,311,312,313,314,315,316,317,318,319,320,321,370,371,372,373,374,375,376,377,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,502,503,504,505,506,507,508,509,510,511,512,513,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,534,535,536,537,538,539,540,541,542,543,544,545,546,547,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,587,588,589,590,591,592,593,594,595,596,597,598,599,600", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "515,3215,3260,3320,3375,3484,3548,3625,3717,3782,3875,3926,3998,4061,4103,4154,4213,4321,4376,4433,4504,4574,4740,4824,4986,5066,5136,5192,5252,5307,5366,5418,5475,5631,5703,5770,5860,5921,5987,6031,6081,6135,6190,6253,6306,6495,6554,6605,6686,6747,6801,6841,6904,6967,7039,7110,7195,7238,7385,7458,7523,7628,7675,7741,7813,7866,7928,7992,8032,8095,8192,8262,8324,8366,8418,8496,8963,9013,9795,9877,9918,9961,10279,10350,10473,10527,10624,10766,10839,10879,13039,13101,13273,13313,13364,13456,13514,13565,13615,13670,13724,13798,13901,14015,14066,14129,14194,14251,14325,14415,14470,14529,14577,14672,14714,14761,14802,14866,14928,14990,15040,15093,15165,15258,15323,15421,15486,15527,15693,15745,15840,15930,15994,16064,16108,16165,16203,16681,16746,16791,16928,17001,17043,17085,17127,17184,17236,17289,17356,17430,17504,17576,17640,17696,17750,17901,17967,18022,18084,18153,18251,18313,18444,18610,18688,18777,18864,18927,19031,19119,19188,19267,19326,19399,19458,19525,19579,19633,19833,19869,19909,19973,20063,20116,20173,20293,20334,20394,20453,20533,20599,20671,20724,20773,20815,20859,20972,21028,21090,21142,21180,21233,21276,21367,21435,21572,21653,21721,21922,21965,22062,22122,22202,22258,22334,22389,22434,22472,22523,23551,23603,23644,23700,23758,23833,23900,23978,24109,24234,24283,28230,28300,28369,28426,28524,28588,28646,28680,29766,29803,29859,29902,30008,30074,30177,30245,30340,30419,30502,30552,30613,30673,30739,30794,30868,30935,31001,31059,31116,31293,31460,31637,31695,31759,31918,32040,32097,32138,32208,32252,32308,32364,32455,32559,32657,32791,32890,33001,33092,33171,33234,33372,33452,33555,33685,33783,33843,33918,33995,34053,34148,34253,34359,34452,34545,34649,34709,34767,34870,34926,34996,35047,35097,35175,35254,35348,35386,35427,35469,35525,35583,35665,35719,35761,35828,35891,35953,36044,36114,36225,36275,36335,36427,36521,36612,36705,36794,36885,36977,37071,37140,37209,37291,37346,37390,37437,37500,37572,37621,37672,37720,37769,37840,38260,38305,38369,38414,38478,38525,38579,38626,38672,38719,38768,38815,39098,39170,39226,39295,39386,39433,39523,39587,39676,39758,39863,39960,40001,40090,40158,40208,40355,40399,40445,40489,40541,40643,40713,40786,40875,40983,41022,41098,41141,41190,41339,41386,41439,41486,41541,41608,41678,41789,41850,41957,42024,42088,42174,42251,42321,42419,42495,42547,42606,42686,42775,42862,42913,42972,43028,43131,43192,43250,43304,43348,43401,43518,43676,43757,43815,43881,43931,44078,44183,44307,44485,44565,44681,44758,44861,44938,45006,45068,45148,45221,45293", "endColumns": "45,44,59,54,108,63,76,91,64,92,50,71,62,41,50,58,107,54,56,70,69,165,83,161,79,69,55,59,54,58,51,56,155,71,66,89,60,65,43,49,53,54,62,52,188,58,50,80,60,53,39,62,62,71,70,84,42,146,72,64,104,46,65,71,52,61,63,39,62,96,69,61,41,51,77,53,49,69,81,40,42,97,70,122,53,96,64,72,39,63,61,71,39,50,91,57,50,49,54,53,73,102,113,50,62,64,56,73,89,54,58,47,94,41,46,40,63,61,61,49,52,71,92,64,97,64,40,43,51,94,89,63,69,43,56,37,78,64,44,136,72,41,41,41,56,51,52,66,73,73,71,63,55,53,150,65,54,61,68,97,61,130,165,77,88,86,62,103,87,68,78,58,72,58,66,53,53,199,35,39,63,89,52,56,119,40,59,58,79,65,71,52,48,41,43,112,55,61,51,37,52,42,90,67,136,80,67,64,42,96,59,79,55,75,54,44,37,50,62,51,40,55,57,74,66,77,130,124,48,92,69,68,56,97,63,57,33,70,36,55,42,105,65,102,67,94,78,82,49,60,59,65,54,73,66,65,57,56,176,86,176,57,63,158,121,56,40,69,43,55,55,90,103,97,133,98,110,90,78,62,137,79,102,129,97,59,74,76,57,94,104,105,92,92,103,59,57,102,55,69,50,49,77,78,93,37,40,41,55,57,81,53,41,66,62,61,90,69,110,49,59,91,93,90,92,88,90,91,93,68,68,81,54,43,46,62,71,48,50,47,48,70,60,44,63,44,63,46,53,46,45,46,48,46,46,71,55,68,90,46,89,63,88,81,104,96,40,88,67,49,70,43,45,43,51,101,69,72,88,107,38,75,42,48,47,46,52,46,54,66,69,110,60,106,66,63,85,76,69,97,75,51,58,79,88,86,50,58,55,102,60,57,53,43,52,116,157,80,57,65,49,86,104,123,177,79,115,76,102,76,67,61,79,72,71,36", "endOffsets": "556,3255,3315,3370,3479,3543,3620,3712,3777,3870,3921,3993,4056,4098,4149,4208,4316,4371,4428,4499,4569,4735,4819,4981,5061,5131,5187,5247,5302,5361,5413,5470,5626,5698,5765,5855,5916,5982,6026,6076,6130,6185,6248,6301,6490,6549,6600,6681,6742,6796,6836,6899,6962,7034,7105,7190,7233,7380,7453,7518,7623,7670,7736,7808,7861,7923,7987,8027,8090,8187,8257,8319,8361,8413,8491,8545,9008,9078,9872,9913,9956,10054,10345,10468,10522,10619,10684,10834,10874,10938,13096,13168,13308,13359,13451,13509,13560,13610,13665,13719,13793,13896,14010,14061,14124,14189,14246,14320,14410,14465,14524,14572,14667,14709,14756,14797,14861,14923,14985,15035,15088,15160,15253,15318,15416,15481,15522,15566,15740,15835,15925,15989,16059,16103,16160,16198,16277,16741,16786,16923,16996,17038,17080,17122,17179,17231,17284,17351,17425,17499,17571,17635,17691,17745,17896,17962,18017,18079,18148,18246,18308,18439,18605,18683,18772,18859,18922,19026,19114,19183,19262,19321,19394,19453,19520,19574,19628,19828,19864,19904,19968,20058,20111,20168,20288,20329,20389,20448,20528,20594,20666,20719,20768,20810,20854,20967,21023,21085,21137,21175,21228,21271,21362,21430,21567,21648,21716,21781,21960,22057,22117,22197,22253,22329,22384,22429,22467,22518,22581,23598,23639,23695,23753,23828,23895,23973,24104,24229,24278,24371,28295,28364,28421,28519,28583,28641,28675,28746,29798,29854,29897,30003,30069,30172,30240,30335,30414,30497,30547,30608,30668,30734,30789,30863,30930,30996,31054,31111,31288,31375,31632,31690,31754,31913,32035,32092,32133,32203,32247,32303,32359,32450,32554,32652,32786,32885,32996,33087,33166,33229,33367,33447,33550,33680,33778,33838,33913,33990,34048,34143,34248,34354,34447,34540,34644,34704,34762,34865,34921,34991,35042,35092,35170,35249,35343,35381,35422,35464,35520,35578,35660,35714,35756,35823,35886,35948,36039,36109,36220,36270,36330,36422,36516,36607,36700,36789,36880,36972,37066,37135,37204,37286,37341,37385,37432,37495,37567,37616,37667,37715,37764,37835,37896,38300,38364,38409,38473,38520,38574,38621,38667,38714,38763,38810,38857,39165,39221,39290,39381,39428,39518,39582,39671,39753,39858,39955,39996,40085,40153,40203,40274,40394,40440,40484,40536,40638,40708,40781,40870,40978,41017,41093,41136,41185,41233,41381,41434,41481,41536,41603,41673,41784,41845,41952,42019,42083,42169,42246,42316,42414,42490,42542,42601,42681,42770,42857,42908,42967,43023,43126,43187,43245,43299,43343,43396,43513,43671,43752,43810,43876,43926,44013,44178,44302,44480,44560,44676,44753,44856,44933,45001,45063,45143,45216,45288,45325"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\aff62db08cff511cc4373673ada3b6cf\\transformed\\play-services-basement-18.5.0\\res\\values-ar\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "150", "startColumns": "4", "startOffsets": "11897", "endColumns": "129", "endOffsets": "12022"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8ba171b74415ef9d04b1f3a055c632e8\\transformed\\play-services-ads-24.2.0\\res\\values-ar\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "367,368,369,378,379,380,381,382,383,384,385,386,387,388,495,496,497,498,499,500,501,586", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "28075,28120,28172,28751,28819,28891,28994,29059,29184,29304,29443,29500,29561,29677,37901,37943,38026,38062,38097,38144,38216,44018", "endColumns": "44,51,57,67,71,102,64,124,119,138,56,60,115,88,41,82,35,34,46,71,43,59", "endOffsets": "28115,28167,28225,28814,28886,28989,29054,29179,29299,29438,29495,29556,29672,29761,37938,38021,38057,38092,38139,38211,38255,44073"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cf3dd3f063a8dc2168576dc8a6639ef4\\transformed\\core-1.13.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "120,121,122,123,124,125,126,548", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "9083,9176,9278,9373,9476,9579,9681,41238", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "9171,9273,9368,9471,9574,9676,9790,41334"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ef3205b01dd1e8d62ca95413fbcfcde0\\transformed\\play-services-base-18.5.0\\res\\values-ar\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "142,143,144,145,146,147,148,149,151,152,153,154,155,156,157,158,159", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10943,11047,11191,11313,11418,11556,11684,11795,12027,12164,12268,12418,12540,12679,12825,12889,12955", "endColumns": "103,143,121,104,137,127,110,101,136,103,149,121,138,145,63,65,83", "endOffsets": "11042,11186,11308,11413,11551,11679,11790,11892,12159,12263,12413,12535,12674,12820,12884,12950,13034"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-mergeDebugResources-49:\\values-ar\\values-ar.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\464e6a7dcfefd0da870c4050aa381b0c\\transformed\\material-1.12.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,465,543,619,703,795,878,979,1098,1175,1234,1297,1388,1457,1524,1624,1687,1752,1813,1881,1943,2001,2115,2175,2236,2293,2366,2489,2570,2662,2769,2867,2947,3095,3176,3257,3385,3474,3550,3603,3657,3723,3801,3881,3952,4034,4106,4180,4253,4323,4432,4523,4594,4684,4779,4853,4936,5029,5078,5159,5228,5314,5399,5461,5525,5588,5657,5766,5876,5973,6073,6130,6188,6268,6347,6422", "endLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84", "endColumns": "12,77,75,83,91,82,100,118,76,58,62,90,68,66,99,62,64,60,67,61,57,113,59,60,56,72,122,80,91,106,97,79,147,80,80,127,88,75,52,53,65,77,79,70,81,71,73,72,69,108,90,70,89,94,73,82,92,48,80,68,85,84,61,63,62,68,108,109,96,99,56,57,79,78,74,75", "endOffsets": "460,538,614,698,790,873,974,1093,1170,1229,1292,1383,1452,1519,1619,1682,1747,1808,1876,1938,1996,2110,2170,2231,2288,2361,2484,2565,2657,2764,2862,2942,3090,3171,3252,3380,3469,3545,3598,3652,3718,3796,3876,3947,4029,4101,4175,4248,4318,4427,4518,4589,4679,4774,4848,4931,5024,5073,5154,5223,5309,5394,5456,5520,5583,5652,5761,5871,5968,6068,6125,6183,6263,6342,6417,6493"}, "to": {"startLines": "2,110,111,112,113,114,127,128,134,193,194,201,264,265,276,277,278,279,280,281,282,283,284,285,286,287,288,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,386,486,487,504", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,8247,8325,8401,8485,8577,9663,9764,10297,15014,15073,15534,19927,19996,20664,20764,20827,20892,20953,21021,21083,21141,21255,21315,21376,21433,21506,22200,22281,22373,22480,22578,22658,22806,22887,22968,23096,23185,23261,23314,23368,23434,23512,23592,23663,23745,23817,23891,23964,24034,24143,24234,24305,24395,24490,24564,24647,24740,24789,24870,24939,25025,25110,25172,25236,25299,25368,25477,25587,25684,25784,25841,29145,36317,36396,37652", "endLines": "9,110,111,112,113,114,127,128,134,193,194,201,264,265,276,277,278,279,280,281,282,283,284,285,286,287,288,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,386,486,487,504", "endColumns": "12,77,75,83,91,82,100,118,76,58,62,90,68,66,99,62,64,60,67,61,57,113,59,60,56,72,122,80,91,106,97,79,147,80,80,127,88,75,52,53,65,77,79,70,81,71,73,72,69,108,90,70,89,94,73,82,92,48,80,68,85,84,61,63,62,68,108,109,96,99,56,57,79,78,74,75", "endOffsets": "510,8320,8396,8480,8572,8655,9759,9878,10369,15068,15131,15620,19991,20058,20759,20822,20887,20948,21016,21078,21136,21250,21310,21371,21428,21501,21624,22276,22368,22475,22573,22653,22801,22882,22963,23091,23180,23256,23309,23363,23429,23507,23587,23658,23740,23812,23886,23959,24029,24138,24229,24300,24390,24485,24559,24642,24735,24784,24865,24934,25020,25105,25167,25231,25294,25363,25472,25582,25679,25779,25836,25894,29220,36391,36466,37723"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1a44ad6b726c152c4fe16ed54c5a8c02\\transformed\\appcompat-1.7.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,424,506,607,721,801,880,971,1064,1156,1250,1350,1443,1538,1631,1722,1816,1895,2000,2098,2196,2304,2404,2507,2662,2759", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "208,312,419,501,602,716,796,875,966,1059,1151,1245,1345,1438,1533,1626,1717,1811,1890,1995,2093,2191,2299,2399,2502,2657,2754,2836"}, "to": {"startLines": "11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,485", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "561,669,773,880,962,1063,1177,1257,1336,1427,1520,1612,1706,1806,1899,1994,2087,2178,2272,2351,2456,2554,2652,2760,2860,2963,3118,36235", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "664,768,875,957,1058,1172,1252,1331,1422,1515,1607,1701,1801,1894,1989,2082,2173,2267,2346,2451,2549,2647,2755,2855,2958,3113,3210,36312"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\46c7e1c2c774c76fe5e74081b097ef9a\\transformed\\browser-1.8.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,361", "endColumns": "99,97,107,101", "endOffsets": "150,248,356,458"}, "to": {"startLines": "158,202,203,204", "startColumns": "4,4,4,4", "startOffsets": "12782,15625,15723,15831", "endColumns": "99,97,107,101", "endOffsets": "12877,15718,15826,15928"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\src\\main\\res\\values-ar\\strings.xml", "from": {"startLines": "102,9,76,328,430,327,444,429,442,451,431,189,97,31,199,170,221,279,334,281,280,284,283,291,290,333,335,336,184,174,367,193,419,418,70,443,131,295,299,100,312,42,41,274,1,372,381,365,187,98,151,55,37,18,267,122,108,109,103,104,144,163,29,38,313,85,115,114,86,30,198,276,61,145,56,119,50,269,445,361,338,385,357,65,282,67,190,246,358,79,113,112,80,304,27,343,408,409,15,402,77,130,341,300,91,320,53,256,52,323,261,259,260,136,177,195,285,379,375,149,255,17,425,438,306,324,10,412,273,426,378,121,157,158,159,309,183,146,147,123,129,34,164,26,25,167,356,342,450,251,75,107,90,89,249,87,88,74,340,344,294,132,133,257,168,49,99,134,272,182,254,310,101,311,166,376,169,128,192,162,95,364,36,407,202,175,403,16,21,20,386,51,382,337,455,160,93,217,161,94,28,120,404,250,171,124,5,4,83,111,110,84,414,39,287,437,69,60,387,45,46,71,165,62,54,64,66,252,253,58,68,353,352,421,420,78,417,422,366,286,314,374,11,288,210,211,215,289,212,213,209,214,205,206,218,208,207,454,453,12,220,8,393,397,394,395,396,380,391,392,390,321,297,424,118,423,359,303,296,81,82,57,35,7,63,181,180,196,307,225,137,139,264,243,244,263,245,262,242,265,179,138,197,330,153,268,266,188,92,308,22,440,441,329,140,141,142,143,370,228,322,298,317,316,305,315,360,19,178,194,6,373,354,258,355,270,277,154,229,230,191,247,351,350,349,148,150,224,271,223,222,362,135,363,219,275,278,116,117,201,43,44,40,200,368,185,152,186,248,369,226,371,227,32,400,401,398,96,216,452,48,47,33,176,59,399,446,434,436,435,447,239,234,240,237,235,233,238,236,413", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6102,402,4506,20575,27101,20511,28170,27009,28015,28683,27210,11174,5813,1511,11855,10080,13586,17934,20851,18059,17989,18254,18170,18877,18797,20781,20908,20964,10849,10221,22561,11410,26137,26065,4147,28080,7991,19104,19286,5999,19898,2190,2137,17527,16,22898,23678,22399,11039,5876,9192,3144,1838,772,16967,7521,6360,6423,6148,6195,8710,9682,1410,1923,19953,5036,6923,6853,5102,1472,11803,17648,3550,8782,3215,7276,2852,17161,28247,22145,21079,23856,21930,3782,18130,3920,11246,15457,21995,4706,6761,6703,4759,19469,1303,21356,25583,25686,634,25223,4570,7934,21213,19336,5491,20224,2985,16125,2949,20370,16470,16346,16408,8263,10414,11558,18420,23555,23119,9025,16084,728,26888,27721,19566,20411,447,25827,17462,26940,23418,7443,9439,9481,9523,19751,10796,8832,8899,7599,7862,1663,9735,1163,1103,9916,21868,21287,28585,15821,4377,6294,5406,5332,15680,5163,5257,4314,21134,21410,19068,8057,8097,16167,9971,2795,5939,8161,17382,10730,16012,19803,6053,19856,9872,23217,10024,7800,11358,9644,5706,22356,1768,25446,12043,10280,25286,685,931,871,23957,2893,23732,21024,29051,9565,5599,13322,9603,5650,1355,7363,25351,15743,10139,7678,185,116,4921,6597,6532,4980,25943,1987,18570,27665,4058,3482,24037,2403,2470,4213,9789,3612,3083,3725,3849,15883,15938,3363,3991,21664,21607,26380,26293,4644,26001,26557,22439,18513,19993,23049,485,18607,12687,12778,13171,18663,12882,12981,12596,13092,12162,12225,13374,12493,12363,28953,28893,529,13509,344,24413,24808,24518,24611,24704,23620,24254,24357,24184,20272,19190,26810,7192,26716,22035,19428,19148,4808,4865,3285,1719,302,3661,10667,10605,11630,19630,13961,8313,8432,16718,15183,15277,16625,15368,16534,15091,16810,10536,8363,11721,20691,9316,17114,16904,11102,5550,19700,1028,27895,27944,20630,8492,8537,8601,8646,22800,14218,20323,19240,20130,20081,19519,20034,22073,815,10467,11467,255,22959,21722,16257,21786,17204,17726,9360,14272,14361,11308,15529,21563,21517,21473,8973,9090,13891,17309,13802,13694,22268,8220,22307,13454,17578,17823,7025,7086,11976,2253,2317,2058,11906,22613,10904,9264,10980,15600,22711,14072,22847,14159,1549,25059,25162,24914,5759,13269,28776,2637,2556,1605,10348,3432,24972,28318,27283,27487,27407,28423,14936,14558,15013,14795,14661,14478,14863,14723,25906", "endColumns": "45,44,63,54,108,63,76,91,64,92,50,71,62,37,50,58,107,54,56,70,69,165,83,161,79,69,55,59,54,58,51,56,155,71,65,89,65,43,49,53,54,62,52,50,68,60,53,39,62,62,71,70,84,42,146,77,62,108,46,65,71,52,61,63,39,65,101,69,60,38,51,77,61,49,69,86,40,42,70,122,53,100,64,66,39,70,61,71,39,52,91,57,48,49,51,53,102,113,50,62,73,56,73,54,58,47,97,41,35,40,63,61,61,49,52,71,92,64,97,64,40,43,51,94,63,69,37,78,64,44,136,77,41,41,41,51,52,66,73,78,71,55,53,139,59,54,61,68,97,61,128,65,84,73,62,93,74,62,78,58,35,39,63,89,52,56,59,58,79,65,71,52,48,41,43,112,55,61,51,37,52,42,69,136,80,67,64,42,96,59,79,55,75,54,44,37,50,51,40,55,54,79,66,77,48,92,69,68,58,105,64,55,33,70,36,55,88,67,102,66,85,74,82,48,60,56,70,54,73,68,66,57,56,176,86,61,63,158,121,56,40,69,43,55,90,103,97,133,98,110,90,78,62,137,79,102,129,97,59,74,76,57,104,105,92,92,103,57,102,55,69,50,49,77,83,93,37,40,41,56,55,77,48,41,63,62,61,90,69,110,49,59,91,93,90,92,88,90,91,93,68,68,81,54,43,46,62,71,48,50,47,48,70,60,44,63,44,63,46,53,46,45,46,48,46,46,71,55,68,90,46,89,63,88,81,104,96,40,88,67,49,70,43,45,43,51,101,69,72,88,107,38,42,48,54,69,110,60,105,66,63,85,78,69,97,75,51,58,79,88,86,50,58,55,102,60,57,53,52,116,157,80,57,65,49,86,104,123,177,79,115,76,102,76,67,61,79,72,71,36", "endOffsets": "6143,442,4565,20625,27205,20570,28242,27096,28075,28771,27256,11241,5871,1544,11901,10134,13689,17984,20903,18125,18054,18415,18249,19034,18872,20846,20959,21019,10899,10275,22608,11462,26288,26132,4208,28165,8052,19143,19331,6048,19948,2248,2185,17573,80,22954,23727,22434,11097,5934,9259,3210,1918,810,17109,7594,6418,6527,6190,6256,8777,9730,1467,1982,19988,5097,7020,6918,5158,1506,11850,17721,3607,8827,3280,7358,2888,17199,28313,22263,21128,23952,21990,3844,18165,3986,11303,15524,22030,4754,6848,6756,4803,19514,1350,21405,25681,25795,680,25281,4639,7986,21282,19386,5545,20267,3078,16162,2980,20406,16529,16403,16465,8308,10462,11625,18508,23615,23212,9085,16120,767,26935,27811,19625,20476,480,25901,17522,26980,23550,7516,9476,9518,9560,19798,10844,8894,8968,7673,7929,1714,9784,1298,1158,9966,21925,21351,28678,15878,4501,6355,5486,5401,15738,5252,5327,4372,21208,21464,19099,8092,8156,16252,10019,2847,5994,8215,17457,10791,16079,19851,6097,19893,9911,23325,10075,7857,11405,9677,5754,22394,1833,25578,12119,10343,25346,723,1023,926,24032,2944,23803,21074,29091,9598,5645,13369,9639,5701,1405,7438,25413,15816,10183,7766,250,180,4975,6698,6592,5031,25972,2053,18602,27716,4142,3545,24135,2465,2551,4283,9867,3656,3139,3777,3915,15933,16007,3427,4053,21717,21659,26552,26375,4701,26060,26711,22556,18565,20029,23114,524,18658,12773,12877,13264,18792,12976,13087,12682,13166,12220,12358,13449,12591,12488,29046,28948,599,13581,397,24513,24909,24606,24699,24803,23673,24352,24408,24249,20318,19235,26883,7271,26805,22068,19464,19185,4860,4916,3358,1763,339,3720,10725,10662,11716,19695,14067,8358,8487,16805,15272,15363,16713,15452,16620,15178,16899,10600,8427,11798,20741,9355,17156,16962,11169,5594,19746,1071,27939,28010,20686,8532,8596,8641,8705,22842,14267,20365,19281,20172,20125,19561,20076,22140,866,10531,11553,297,23044,21781,16341,21863,17304,17818,9396,14356,14424,11353,15595,21602,21558,21512,9020,9187,13956,17377,13886,13797,22302,8258,22351,13504,17643,17929,7081,7187,12038,2312,2398,2132,11971,22706,10975,9311,11034,15675,22795,14154,22893,14213,1600,25157,25218,24967,5808,13317,28888,2790,2632,1658,10409,3477,25054,28418,27402,27660,27482,28534,15008,14656,15085,14858,14718,14553,14931,14790,25938"}, "to": {"startLines": "10,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,115,116,124,125,126,129,130,131,132,133,135,136,137,156,157,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,195,196,197,198,199,200,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,266,267,268,269,270,271,272,273,274,275,289,290,291,292,293,294,295,296,297,346,347,348,349,350,351,352,353,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,473,474,475,476,477,478,479,480,481,482,483,484,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,505,506,507,508,509,510,511,512,513,514,515,516,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,551,552,553,554,555,556,557,558,559,560,561,562,563,564", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "515,3215,3260,3324,3379,3488,3552,3629,3721,3786,3879,3930,4002,4065,4103,4154,4213,4321,4376,4433,4504,4574,4740,4824,4986,5066,5136,5192,5252,5307,5366,5418,5475,5631,5703,5769,5859,5925,5969,6019,6073,6128,6191,6244,6295,6364,6425,6479,6519,6582,6645,6717,6788,6873,6916,7063,7141,7204,7313,7360,7426,7498,7551,7613,7677,7717,7783,7885,7955,8016,8055,8107,8185,8660,8710,9492,9579,9620,9883,9954,10077,10131,10232,10374,10441,10481,12648,12710,12882,12922,12975,13067,13125,13174,13224,13276,13330,13433,13547,13598,13661,13735,13792,13866,13921,13980,14028,14126,14168,14204,14245,14309,14371,14433,14483,14536,14608,14701,14766,14864,14929,14970,15136,15188,15283,15347,15417,15455,15933,15998,16043,16180,16258,16300,16342,16384,16436,16489,16556,16630,16709,16781,16837,16891,17031,17091,17146,17208,17277,17375,17437,17566,17632,17717,17791,17854,17948,18023,18086,18165,18224,18260,18300,18364,18454,18507,18564,18624,18683,18763,18829,18901,18954,19003,19045,19089,19202,19258,19320,19372,19410,19463,19506,19576,19713,19794,19862,20063,20106,20203,20263,20343,20399,20475,20530,20575,20613,21629,21681,21722,21778,21833,21913,21980,22058,22107,26054,26124,26193,26252,26358,26423,26479,26513,27599,27636,27692,27781,27849,27952,28019,28105,28180,28263,28312,28373,28430,28501,28556,28630,28699,28766,28824,28881,29058,29225,29287,29351,29510,29632,29689,29730,29800,29844,29900,29991,30095,30193,30327,30426,30537,30628,30707,30770,30908,30988,31091,31221,31319,31379,31454,31531,31589,31694,31800,31893,31986,32090,32148,32251,32307,32377,32428,32478,32556,32640,32734,32772,32813,32855,32912,32968,33046,33095,33137,33201,33264,33326,33417,33487,33598,33648,33708,33800,33894,33985,34078,34167,34258,34350,34444,34513,34582,34664,34719,34763,34810,34873,34945,34994,35045,35093,35142,35213,35633,35678,35742,35787,35851,35898,35952,35999,36045,36092,36141,36188,36471,36543,36599,36668,36759,36806,36896,36960,37049,37131,37236,37333,37374,37463,37531,37581,37728,37772,37818,37862,37914,38016,38086,38159,38248,38356,38395,38438,38588,38643,38713,38824,38885,38991,39058,39122,39208,39287,39357,39455,39531,39583,39642,39722,39811,39898,39949,40008,40064,40167,40228,40286,40340,40393,40510,40668,40749,40807,40873,40923,41070,41175,41299,41477,41557,41673,41750,41853,41930,41998,42060,42140,42213,42285", "endColumns": "45,44,63,54,108,63,76,91,64,92,50,71,62,37,50,58,107,54,56,70,69,165,83,161,79,69,55,59,54,58,51,56,155,71,65,89,65,43,49,53,54,62,52,50,68,60,53,39,62,62,71,70,84,42,146,77,62,108,46,65,71,52,61,63,39,65,101,69,60,38,51,77,61,49,69,86,40,42,70,122,53,100,64,66,39,70,61,71,39,52,91,57,48,49,51,53,102,113,50,62,73,56,73,54,58,47,97,41,35,40,63,61,61,49,52,71,92,64,97,64,40,43,51,94,63,69,37,78,64,44,136,77,41,41,41,51,52,66,73,78,71,55,53,139,59,54,61,68,97,61,128,65,84,73,62,93,74,62,78,58,35,39,63,89,52,56,59,58,79,65,71,52,48,41,43,112,55,61,51,37,52,42,69,136,80,67,64,42,96,59,79,55,75,54,44,37,50,51,40,55,54,79,66,77,48,92,69,68,58,105,64,55,33,70,36,55,88,67,102,66,85,74,82,48,60,56,70,54,73,68,66,57,56,176,86,61,63,158,121,56,40,69,43,55,90,103,97,133,98,110,90,78,62,137,79,102,129,97,59,74,76,57,104,105,92,92,103,57,102,55,69,50,49,77,83,93,37,40,41,56,55,77,48,41,63,62,61,90,69,110,49,59,91,93,90,92,88,90,91,93,68,68,81,54,43,46,62,71,48,50,47,48,70,60,44,63,44,63,46,53,46,45,46,48,46,46,71,55,68,90,46,89,63,88,81,104,96,40,88,67,49,70,43,45,43,51,101,69,72,88,107,38,42,48,54,69,110,60,105,66,63,85,78,69,97,75,51,58,79,88,86,50,58,55,102,60,57,53,52,116,157,80,57,65,49,86,104,123,177,79,115,76,102,76,67,61,79,72,71,36", "endOffsets": "556,3255,3319,3374,3483,3547,3624,3716,3781,3874,3925,3997,4060,4098,4149,4208,4316,4371,4428,4499,4569,4735,4819,4981,5061,5131,5187,5247,5302,5361,5413,5470,5626,5698,5764,5854,5920,5964,6014,6068,6123,6186,6239,6290,6359,6420,6474,6514,6577,6640,6712,6783,6868,6911,7058,7136,7199,7308,7355,7421,7493,7546,7608,7672,7712,7778,7880,7950,8011,8050,8102,8180,8242,8705,8775,9574,9615,9658,9949,10072,10126,10227,10292,10436,10476,10547,12705,12777,12917,12970,13062,13120,13169,13219,13271,13325,13428,13542,13593,13656,13730,13787,13861,13916,13975,14023,14121,14163,14199,14240,14304,14366,14428,14478,14531,14603,14696,14761,14859,14924,14965,15009,15183,15278,15342,15412,15450,15529,15993,16038,16175,16253,16295,16337,16379,16431,16484,16551,16625,16704,16776,16832,16886,17026,17086,17141,17203,17272,17370,17432,17561,17627,17712,17786,17849,17943,18018,18081,18160,18219,18255,18295,18359,18449,18502,18559,18619,18678,18758,18824,18896,18949,18998,19040,19084,19197,19253,19315,19367,19405,19458,19501,19571,19708,19789,19857,19922,20101,20198,20258,20338,20394,20470,20525,20570,20608,20659,21676,21717,21773,21828,21908,21975,22053,22102,22195,26119,26188,26247,26353,26418,26474,26508,26579,27631,27687,27776,27844,27947,28014,28100,28175,28258,28307,28368,28425,28496,28551,28625,28694,28761,28819,28876,29053,29140,29282,29346,29505,29627,29684,29725,29795,29839,29895,29986,30090,30188,30322,30421,30532,30623,30702,30765,30903,30983,31086,31216,31314,31374,31449,31526,31584,31689,31795,31888,31981,32085,32143,32246,32302,32372,32423,32473,32551,32635,32729,32767,32808,32850,32907,32963,33041,33090,33132,33196,33259,33321,33412,33482,33593,33643,33703,33795,33889,33980,34073,34162,34253,34345,34439,34508,34577,34659,34714,34758,34805,34868,34940,34989,35040,35088,35137,35208,35269,35673,35737,35782,35846,35893,35947,35994,36040,36087,36136,36183,36230,36538,36594,36663,36754,36801,36891,36955,37044,37126,37231,37328,37369,37458,37526,37576,37647,37767,37813,37857,37909,38011,38081,38154,38243,38351,38390,38433,38482,38638,38708,38819,38880,38986,39053,39117,39203,39282,39352,39450,39526,39578,39637,39717,39806,39893,39944,40003,40059,40162,40223,40281,40335,40388,40505,40663,40744,40802,40868,40918,41005,41170,41294,41472,41552,41668,41745,41848,41925,41993,42055,42135,42208,42280,42317"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\aff62db08cff511cc4373673ada3b6cf\\transformed\\play-services-basement-18.5.0\\res\\values-ar\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "146", "startColumns": "4", "startOffsets": "11506", "endColumns": "129", "endOffsets": "11631"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8ba171b74415ef9d04b1f3a055c632e8\\transformed\\play-services-ads-24.2.0\\res\\values-ar\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,240,288,342,406,474,573,634,755,871,1006,1059,1116,1228,1313,1351,1430,1462,1493,1536,1604,1644", "endColumns": "40,47,53,63,67,98,60,120,115,134,52,56,111,84,37,78,31,30,42,67,39,55", "endOffsets": "239,287,341,405,473,572,633,754,870,1005,1058,1115,1227,1312,1350,1429,1461,1492,1535,1603,1643,1699"}, "to": {"startLines": "343,344,345,354,355,356,357,358,359,360,361,362,363,364,466,467,468,469,470,471,472,550", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "25899,25944,25996,26584,26652,26724,26827,26892,27017,27137,27276,27333,27394,27510,35274,35316,35399,35435,35470,35517,35589,41010", "endColumns": "44,51,57,67,71,102,64,124,119,138,56,60,115,88,41,82,35,34,46,71,43,59", "endOffsets": "25939,25991,26049,26647,26719,26822,26887,27012,27132,27271,27328,27389,27505,27594,35311,35394,35430,35465,35512,35584,35628,41065"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cf3dd3f063a8dc2168576dc8a6639ef4\\transformed\\core-1.13.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,250,345,448,551,653,767", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "143,245,340,443,546,648,762,863"}, "to": {"startLines": "117,118,119,120,121,122,123,517", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "8780,8873,8975,9070,9173,9276,9378,38487", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "8868,8970,9065,9168,9271,9373,9487,38583"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ef3205b01dd1e8d62ca95413fbcfcde0\\transformed\\play-services-base-18.5.0\\res\\values-ar\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,433,551,652,786,910,1017,1115,1248,1348,1494,1612,1747,1889,1949,2011", "endColumns": "99,139,117,100,133,123,106,97,132,99,145,117,134,141,59,61,79", "endOffsets": "292,432,550,651,785,909,1016,1114,1247,1347,1493,1611,1746,1888,1948,2010,2090"}, "to": {"startLines": "138,139,140,141,142,143,144,145,147,148,149,150,151,152,153,154,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10552,10656,10800,10922,11027,11165,11293,11404,11636,11773,11877,12027,12149,12288,12434,12498,12564", "endColumns": "103,143,121,104,137,127,110,101,136,103,149,121,138,145,63,65,83", "endOffsets": "10651,10795,10917,11022,11160,11288,11399,11501,11768,11872,12022,12144,12283,12429,12493,12559,12643"}}]}]}