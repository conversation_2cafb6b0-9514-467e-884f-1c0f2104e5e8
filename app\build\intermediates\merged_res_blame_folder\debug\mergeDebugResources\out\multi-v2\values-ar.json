{"logs": [{"outputFile": "com.mahmoudffyt.gfxbooster.app-mergeDebugResources-49:/values-ar/values-ar.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\464e6a7dcfefd0da870c4050aa381b0c\\transformed\\material-1.12.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,113,114,115,116,117,131,132,138,199,200,210,284,285,297,298,299,300,301,302,303,304,305,306,307,308,309,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,410,514,515,532", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,8578,8656,8732,8816,8908,10087,10188,10717,15608,15667,16319,21624,21693,22424,22524,22587,22652,22713,22781,22843,22901,23015,23075,23136,23193,23266,24214,24295,24387,24494,24592,24672,24820,24901,24982,25110,25199,25275,25328,25382,25448,25526,25606,25677,25759,25831,25905,25978,26048,26157,26248,26319,26409,26504,26578,26661,26754,26803,26884,26953,27039,27124,27186,27250,27313,27382,27491,27601,27698,27798,27855,31210,38776,38855,40111", "endLines": "9,113,114,115,116,117,131,132,138,199,200,210,284,285,297,298,299,300,301,302,303,304,305,306,307,308,309,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,410,514,515,532", "endColumns": "12,77,75,83,91,82,100,118,76,58,62,90,68,66,99,62,64,60,67,61,57,113,59,60,56,72,122,80,91,106,97,79,147,80,80,127,88,75,52,53,65,77,79,70,81,71,73,72,69,108,90,70,89,94,73,82,92,48,80,68,85,84,61,63,62,68,108,109,96,99,56,57,79,78,74,75", "endOffsets": "510,8651,8727,8811,8903,8986,10183,10302,10789,15662,15725,16405,21688,21755,22519,22582,22647,22708,22776,22838,22896,23010,23070,23131,23188,23261,23384,24290,24382,24489,24587,24667,24815,24896,24977,25105,25194,25270,25323,25377,25443,25521,25601,25672,25754,25826,25900,25973,26043,26152,26243,26314,26404,26499,26573,26656,26749,26798,26879,26948,27034,27119,27181,27245,27308,27377,27486,27596,27693,27793,27850,27908,31285,38850,38925,40182"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1a44ad6b726c152c4fe16ed54c5a8c02\\transformed\\appcompat-1.7.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,513", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "561,669,773,880,962,1063,1177,1257,1336,1427,1520,1612,1706,1806,1899,1994,2087,2178,2272,2351,2456,2554,2652,2760,2860,2963,3118,38694", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "664,768,875,957,1058,1172,1252,1331,1422,1515,1607,1701,1801,1894,1989,2082,2173,2267,2346,2451,2549,2647,2755,2855,2958,3113,3210,38771"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\46c7e1c2c774c76fe5e74081b097ef9a\\transformed\\browser-1.8.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "162,211,212,213", "startColumns": "4,4,4,4", "startOffsets": "13201,16410,16508,16616", "endColumns": "99,97,107,101", "endOffsets": "13296,16503,16611,16713"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\src\\main\\res\\values-ar\\strings.xml", "from": {"startLines": "-1,-1,76,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,31,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,70,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,86,30,-1,-1,61,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,65,-1,67,-1,-1,-1,-1,-1,-1,80,-1,27,-1,-1,-1,-1,-1,-1,77,-1,-1,468,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,478,470,-1,-1,-1,-1,-1,-1,-1,-1,-1,475,-1,-1,-1,-1,-1,-1,473,34,-1,26,25,-1,-1,-1,-1,-1,75,-1,90,89,-1,87,88,74,-1,-1,467,471,472,469,-1,480,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,474,36,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,28,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,84,-1,-1,-1,-1,477,69,60,-1,-1,-1,71,-1,62,-1,64,66,-1,-1,58,68,-1,-1,-1,-1,461,78,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,82,57,35,-1,63,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,476,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,4,-1,-1,-1,-1,-1,-1,4,-1,4,-1,-1,-1,-1,-1,-1,4,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,4,4,-1,4,4,-1,-1,-1,-1,-1,4,-1,4,4,-1,4,4,4,-1,-1,4,4,4,4,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,4,4,4,-1,-1,-1,4,-1,4,-1,4,4,-1,-1,4,4,-1,-1,-1,-1,4,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,4,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,4589,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,1555,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4223,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,5179,1513,-1,-1,3617,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,3848,-1,3987,-1,-1,-1,-1,-1,-1,4833,-1,1338,-1,-1,-1,-1,-1,-1,4652,-1,-1,30115,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,30773,30259,-1,-1,-1,-1,-1,-1,-1,-1,-1,30597,-1,-1,-1,-1,-1,-1,30442,1711,-1,1187,1121,-1,-1,-1,-1,-1,4459,-1,5505,5423,-1,5241,5340,4395,-1,-1,30042,30316,30375,30205,-1,30871,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,30506,1821,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,1393,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,5057,-1,-1,-1,-1,30730,4117,3551,-1,-1,-1,4290,-1,3671,-1,3788,3921,-1,-1,3434,4051,-1,-1,-1,-1,29577,4722,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4941,3352,1767,-1,3721,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,30654,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endColumns": "-1,-1,62,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,41,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,66,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,61,41,-1,-1,53,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,72,-1,63,-1,-1,-1,-1,-1,-1,50,-1,54,-1,-1,-1,-1,-1,-1,69,-1,-1,89,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,43,56,-1,-1,-1,-1,-1,-1,-1,-1,-1,56,-1,-1,-1,-1,-1,-1,63,55,-1,150,65,-1,-1,-1,-1,-1,129,-1,88,81,-1,98,82,63,-1,-1,72,58,66,53,-1,199,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,90,67,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,57,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,57,-1,-1,-1,-1,42,105,65,-1,-1,-1,78,-1,49,-1,59,65,-1,-1,66,65,-1,-1,-1,-1,176,58,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,57,81,53,-1,66,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,75,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endOffsets": "-1,-1,4647,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,1592,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4285,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,5236,1550,-1,-1,3666,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,3916,-1,4046,-1,-1,-1,-1,-1,-1,4879,-1,1388,-1,-1,-1,-1,-1,-1,4717,-1,-1,30200,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,30812,30311,-1,-1,-1,-1,-1,-1,-1,-1,-1,30649,-1,-1,-1,-1,-1,-1,30501,1762,-1,1333,1182,-1,-1,-1,-1,-1,4584,-1,5589,5500,-1,5335,5418,4454,-1,-1,30110,30370,30437,30254,-1,31066,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,30592,1884,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,1446,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,5110,-1,-1,-1,-1,30768,4218,3612,-1,-1,-1,4364,-1,3716,-1,3843,3982,-1,-1,3496,4112,-1,-1,-1,-1,29749,4776,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4994,3429,1816,-1,3783,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,30725,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "10,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,118,119,127,128,129,130,133,134,135,136,137,139,140,141,160,161,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,201,202,203,204,205,206,207,208,209,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,286,287,288,289,290,291,292,293,294,295,296,310,311,312,313,314,315,316,317,318,319,320,369,370,371,372,373,374,375,376,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,501,502,503,504,505,506,507,508,509,510,511,512,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,533,534,535,536,537,538,539,540,541,542,543,544,545,546,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,586,587,588,589,590,591,592,593,594,595,596,597,598,599", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "515,3215,3260,3323,3378,3487,3551,3628,3720,3785,3878,3929,4001,4064,4106,4157,4216,4324,4379,4436,4507,4577,4743,4827,4989,5069,5139,5195,5255,5310,5369,5421,5478,5634,5706,5773,5863,5944,6010,6054,6104,6158,6213,6276,6329,6518,6577,6628,6709,6770,6824,6864,6927,6990,7062,7133,7218,7261,7408,7481,7546,7656,7703,7769,7841,7894,7956,8020,8060,8124,8220,8290,8352,8394,8446,8524,8991,9041,9823,9905,9946,9989,10307,10378,10501,10555,10652,10794,10867,10907,13067,13129,13301,13341,13393,13488,13546,13597,13647,13702,13756,13830,13933,14047,14098,14161,14231,14288,14362,14452,14507,14566,14614,14709,14751,14798,14839,14903,14965,15027,15077,15130,15202,15295,15360,15458,15523,15564,15730,15782,15877,15967,16031,16101,16145,16202,16240,16718,16783,16828,16965,17038,17080,17122,17164,17221,17273,17326,17393,17467,17541,17613,17677,17733,17787,17938,18004,18059,18121,18190,18288,18350,18480,18546,18635,18717,18780,18879,18962,19026,19105,19164,19237,19296,19363,19417,19471,19671,19707,19747,19811,19901,19954,20011,20131,20172,20232,20291,20371,20437,20509,20562,20611,20653,20697,20810,20866,20928,20980,21018,21071,21114,21205,21273,21410,21491,21559,21760,21803,21900,21960,22040,22096,22172,22227,22272,22310,22361,23389,23441,23482,23538,23596,23671,23738,23816,23947,24072,24121,28068,28138,28207,28265,28354,28418,28476,28510,29596,29633,29689,29732,29838,29904,30007,30075,30170,30249,30332,30382,30443,30503,30569,30624,30698,30765,30831,30889,30946,31123,31290,31467,31526,31590,31749,31871,31928,31969,32039,32083,32139,32195,32286,32390,32488,32622,32721,32832,32923,33002,33065,33203,33283,33386,33516,33614,33674,33749,33826,33884,33979,34084,34190,34283,34376,34480,34540,34598,34701,34757,34827,34878,34928,35006,35085,35179,35217,35258,35300,35357,35415,35497,35551,35593,35660,35723,35785,35876,35946,36057,36107,36167,36259,36353,36444,36537,36626,36717,36809,36903,36972,37041,37123,37178,37222,37269,37332,37404,37453,37504,37552,37601,37672,38092,38137,38201,38246,38310,38357,38411,38458,38504,38551,38600,38647,38930,39002,39058,39127,39218,39265,39355,39419,39508,39590,39695,39792,39833,39922,39990,40040,40187,40231,40277,40321,40373,40475,40545,40618,40707,40815,40854,40930,40973,41022,41169,41217,41270,41317,41372,41439,41509,41620,41681,41788,41855,41919,42005,42082,42152,42250,42326,42378,42437,42517,42606,42693,42744,42803,42859,42962,43023,43081,43135,43179,43232,43349,43507,43588,43646,43712,43762,43909,44014,44138,44316,44396,44512,44589,44692,44769,44837,44899,44979,45052,45124", "endColumns": "45,44,62,54,108,63,76,91,64,92,50,71,62,41,50,58,107,54,56,70,69,165,83,161,79,69,55,59,54,58,51,56,155,71,66,89,80,65,43,49,53,54,62,52,188,58,50,80,60,53,39,62,62,71,70,84,42,146,72,64,109,46,65,71,52,61,63,39,63,95,69,61,41,51,77,53,49,69,81,40,42,97,70,122,53,96,64,72,39,63,61,71,39,51,94,57,50,49,54,53,73,102,113,50,62,69,56,73,89,54,58,47,94,41,46,40,63,61,61,49,52,71,92,64,97,64,40,43,51,94,89,63,69,43,56,37,78,64,44,136,72,41,41,41,56,51,52,66,73,73,71,63,55,53,150,65,54,61,68,97,61,129,65,88,81,62,98,82,63,78,58,72,58,66,53,53,199,35,39,63,89,52,56,119,40,59,58,79,65,71,52,48,41,43,112,55,61,51,37,52,42,90,67,136,80,67,64,42,96,59,79,55,75,54,44,37,50,62,51,40,55,57,74,66,77,130,124,48,92,69,68,57,88,63,57,33,70,36,55,42,105,65,102,67,94,78,82,49,60,59,65,54,73,66,65,57,56,176,86,176,58,63,158,121,56,40,69,43,55,55,90,103,97,133,98,110,90,78,62,137,79,102,129,97,59,74,76,57,94,104,105,92,92,103,59,57,102,55,69,50,49,77,78,93,37,40,41,56,57,81,53,41,66,62,61,90,69,110,49,59,91,93,90,92,88,90,91,93,68,68,81,54,43,46,62,71,48,50,47,48,70,60,44,63,44,63,46,53,46,45,46,48,46,46,71,55,68,90,46,89,63,88,81,104,96,40,88,67,49,70,43,45,43,51,101,69,72,88,107,38,75,42,48,45,47,52,46,54,66,69,110,60,106,66,63,85,76,69,97,75,51,58,79,88,86,50,58,55,102,60,57,53,43,52,116,157,80,57,65,49,86,104,123,177,79,115,76,102,76,67,61,79,72,71,36", "endOffsets": "556,3255,3318,3373,3482,3546,3623,3715,3780,3873,3924,3996,4059,4101,4152,4211,4319,4374,4431,4502,4572,4738,4822,4984,5064,5134,5190,5250,5305,5364,5416,5473,5629,5701,5768,5858,5939,6005,6049,6099,6153,6208,6271,6324,6513,6572,6623,6704,6765,6819,6859,6922,6985,7057,7128,7213,7256,7403,7476,7541,7651,7698,7764,7836,7889,7951,8015,8055,8119,8215,8285,8347,8389,8441,8519,8573,9036,9106,9900,9941,9984,10082,10373,10496,10550,10647,10712,10862,10902,10966,13124,13196,13336,13388,13483,13541,13592,13642,13697,13751,13825,13928,14042,14093,14156,14226,14283,14357,14447,14502,14561,14609,14704,14746,14793,14834,14898,14960,15022,15072,15125,15197,15290,15355,15453,15518,15559,15603,15777,15872,15962,16026,16096,16140,16197,16235,16314,16778,16823,16960,17033,17075,17117,17159,17216,17268,17321,17388,17462,17536,17608,17672,17728,17782,17933,17999,18054,18116,18185,18283,18345,18475,18541,18630,18712,18775,18874,18957,19021,19100,19159,19232,19291,19358,19412,19466,19666,19702,19742,19806,19896,19949,20006,20126,20167,20227,20286,20366,20432,20504,20557,20606,20648,20692,20805,20861,20923,20975,21013,21066,21109,21200,21268,21405,21486,21554,21619,21798,21895,21955,22035,22091,22167,22222,22267,22305,22356,22419,23436,23477,23533,23591,23666,23733,23811,23942,24067,24116,24209,28133,28202,28260,28349,28413,28471,28505,28576,29628,29684,29727,29833,29899,30002,30070,30165,30244,30327,30377,30438,30498,30564,30619,30693,30760,30826,30884,30941,31118,31205,31462,31521,31585,31744,31866,31923,31964,32034,32078,32134,32190,32281,32385,32483,32617,32716,32827,32918,32997,33060,33198,33278,33381,33511,33609,33669,33744,33821,33879,33974,34079,34185,34278,34371,34475,34535,34593,34696,34752,34822,34873,34923,35001,35080,35174,35212,35253,35295,35352,35410,35492,35546,35588,35655,35718,35780,35871,35941,36052,36102,36162,36254,36348,36439,36532,36621,36712,36804,36898,36967,37036,37118,37173,37217,37264,37327,37399,37448,37499,37547,37596,37667,37728,38132,38196,38241,38305,38352,38406,38453,38499,38546,38595,38642,38689,38997,39053,39122,39213,39260,39350,39414,39503,39585,39690,39787,39828,39917,39985,40035,40106,40226,40272,40316,40368,40470,40540,40613,40702,40810,40849,40925,40968,41017,41063,41212,41265,41312,41367,41434,41504,41615,41676,41783,41850,41914,42000,42077,42147,42245,42321,42373,42432,42512,42601,42688,42739,42798,42854,42957,43018,43076,43130,43174,43227,43344,43502,43583,43641,43707,43757,43844,44009,44133,44311,44391,44507,44584,44687,44764,44832,44894,44974,45047,45119,45156"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\aff62db08cff511cc4373673ada3b6cf\\transformed\\play-services-basement-18.5.0\\res\\values-ar\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "150", "startColumns": "4", "startOffsets": "11925", "endColumns": "129", "endOffsets": "12050"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8ba171b74415ef9d04b1f3a055c632e8\\transformed\\play-services-ads-24.2.0\\res\\values-ar\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "366,367,368,377,378,379,380,381,382,383,384,385,386,387,494,495,496,497,498,499,500,585", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "27913,27958,28010,28581,28649,28721,28824,28889,29014,29134,29273,29330,29391,29507,37733,37775,37858,37894,37929,37976,38048,43849", "endColumns": "44,51,57,67,71,102,64,124,119,138,56,60,115,88,41,82,35,34,46,71,43,59", "endOffsets": "27953,28005,28063,28644,28716,28819,28884,29009,29129,29268,29325,29386,29502,29591,37770,37853,37889,37924,37971,38043,38087,43904"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cf3dd3f063a8dc2168576dc8a6639ef4\\transformed\\core-1.13.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "120,121,122,123,124,125,126,547", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "9111,9204,9306,9401,9504,9607,9709,41068", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "9199,9301,9396,9499,9602,9704,9818,41164"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ef3205b01dd1e8d62ca95413fbcfcde0\\transformed\\play-services-base-18.5.0\\res\\values-ar\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "142,143,144,145,146,147,148,149,151,152,153,154,155,156,157,158,159", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10971,11075,11219,11341,11446,11584,11712,11823,12055,12192,12296,12446,12568,12707,12853,12917,12983", "endColumns": "103,143,121,104,137,127,110,101,136,103,149,121,138,145,63,65,83", "endOffsets": "11070,11214,11336,11441,11579,11707,11818,11920,12187,12291,12441,12563,12702,12848,12912,12978,13062"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-mergeDebugResources-49:\\values-ar\\values-ar.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\464e6a7dcfefd0da870c4050aa381b0c\\transformed\\material-1.12.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,465,543,619,703,795,878,979,1098,1175,1234,1297,1388,1457,1524,1624,1687,1752,1813,1881,1943,2001,2115,2175,2236,2293,2366,2489,2570,2662,2769,2867,2947,3095,3176,3257,3385,3474,3550,3603,3657,3723,3801,3881,3952,4034,4106,4180,4253,4323,4432,4523,4594,4684,4779,4853,4936,5029,5078,5159,5228,5314,5399,5461,5525,5588,5657,5766,5876,5973,6073,6130,6188,6268,6347,6422", "endLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84", "endColumns": "12,77,75,83,91,82,100,118,76,58,62,90,68,66,99,62,64,60,67,61,57,113,59,60,56,72,122,80,91,106,97,79,147,80,80,127,88,75,52,53,65,77,79,70,81,71,73,72,69,108,90,70,89,94,73,82,92,48,80,68,85,84,61,63,62,68,108,109,96,99,56,57,79,78,74,75", "endOffsets": "460,538,614,698,790,873,974,1093,1170,1229,1292,1383,1452,1519,1619,1682,1747,1808,1876,1938,1996,2110,2170,2231,2288,2361,2484,2565,2657,2764,2862,2942,3090,3171,3252,3380,3469,3545,3598,3652,3718,3796,3876,3947,4029,4101,4175,4248,4318,4427,4518,4589,4679,4774,4848,4931,5024,5073,5154,5223,5309,5394,5456,5520,5583,5652,5761,5871,5968,6068,6125,6183,6263,6342,6417,6493"}, "to": {"startLines": "2,110,111,112,113,114,127,128,134,193,194,201,264,265,276,277,278,279,280,281,282,283,284,285,286,287,288,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,386,486,487,504", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,8247,8325,8401,8485,8577,9663,9764,10297,15014,15073,15534,19927,19996,20664,20764,20827,20892,20953,21021,21083,21141,21255,21315,21376,21433,21506,22200,22281,22373,22480,22578,22658,22806,22887,22968,23096,23185,23261,23314,23368,23434,23512,23592,23663,23745,23817,23891,23964,24034,24143,24234,24305,24395,24490,24564,24647,24740,24789,24870,24939,25025,25110,25172,25236,25299,25368,25477,25587,25684,25784,25841,29145,36317,36396,37652", "endLines": "9,110,111,112,113,114,127,128,134,193,194,201,264,265,276,277,278,279,280,281,282,283,284,285,286,287,288,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,386,486,487,504", "endColumns": "12,77,75,83,91,82,100,118,76,58,62,90,68,66,99,62,64,60,67,61,57,113,59,60,56,72,122,80,91,106,97,79,147,80,80,127,88,75,52,53,65,77,79,70,81,71,73,72,69,108,90,70,89,94,73,82,92,48,80,68,85,84,61,63,62,68,108,109,96,99,56,57,79,78,74,75", "endOffsets": "510,8320,8396,8480,8572,8655,9759,9878,10369,15068,15131,15620,19991,20058,20759,20822,20887,20948,21016,21078,21136,21250,21310,21371,21428,21501,21624,22276,22368,22475,22573,22653,22801,22882,22963,23091,23180,23256,23309,23363,23429,23507,23587,23658,23740,23812,23886,23959,24029,24138,24229,24300,24390,24485,24559,24642,24735,24784,24865,24934,25020,25105,25167,25231,25294,25363,25472,25582,25679,25779,25836,25894,29220,36391,36466,37723"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1a44ad6b726c152c4fe16ed54c5a8c02\\transformed\\appcompat-1.7.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,424,506,607,721,801,880,971,1064,1156,1250,1350,1443,1538,1631,1722,1816,1895,2000,2098,2196,2304,2404,2507,2662,2759", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "208,312,419,501,602,716,796,875,966,1059,1151,1245,1345,1438,1533,1626,1717,1811,1890,1995,2093,2191,2299,2399,2502,2657,2754,2836"}, "to": {"startLines": "11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,485", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "561,669,773,880,962,1063,1177,1257,1336,1427,1520,1612,1706,1806,1899,1994,2087,2178,2272,2351,2456,2554,2652,2760,2860,2963,3118,36235", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "664,768,875,957,1058,1172,1252,1331,1422,1515,1607,1701,1801,1894,1989,2082,2173,2267,2346,2451,2549,2647,2755,2855,2958,3113,3210,36312"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\46c7e1c2c774c76fe5e74081b097ef9a\\transformed\\browser-1.8.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,361", "endColumns": "99,97,107,101", "endOffsets": "150,248,356,458"}, "to": {"startLines": "158,202,203,204", "startColumns": "4,4,4,4", "startOffsets": "12782,15625,15723,15831", "endColumns": "99,97,107,101", "endOffsets": "12877,15718,15826,15928"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\HeadshotSettingsGameBooster\\app\\src\\main\\res\\values-ar\\strings.xml", "from": {"startLines": "102,9,76,328,430,327,444,429,442,451,431,189,97,31,199,170,221,279,334,281,280,284,283,291,290,333,335,336,184,174,367,193,419,418,70,443,131,295,299,100,312,42,41,274,1,372,381,365,187,98,151,55,37,18,267,122,108,109,103,104,144,163,29,38,313,85,115,114,86,30,198,276,61,145,56,119,50,269,445,361,338,385,357,65,282,67,190,246,358,79,113,112,80,304,27,343,408,409,15,402,77,130,341,300,91,320,53,256,52,323,261,259,260,136,177,195,285,379,375,149,255,17,425,438,306,324,10,412,273,426,378,121,157,158,159,309,183,146,147,123,129,34,164,26,25,167,356,342,450,251,75,107,90,89,249,87,88,74,340,344,294,132,133,257,168,49,99,134,272,182,254,310,101,311,166,376,169,128,192,162,95,364,36,407,202,175,403,16,21,20,386,51,382,337,455,160,93,217,161,94,28,120,404,250,171,124,5,4,83,111,110,84,414,39,287,437,69,60,387,45,46,71,165,62,54,64,66,252,253,58,68,353,352,421,420,78,417,422,366,286,314,374,11,288,210,211,215,289,212,213,209,214,205,206,218,208,207,454,453,12,220,8,393,397,394,395,396,380,391,392,390,321,297,424,118,423,359,303,296,81,82,57,35,7,63,181,180,196,307,225,137,139,264,243,244,263,245,262,242,265,179,138,197,330,153,268,266,188,92,308,22,440,441,329,140,141,142,143,370,228,322,298,317,316,305,315,360,19,178,194,6,373,354,258,355,270,277,154,229,230,191,247,351,350,349,148,150,224,271,223,222,362,135,363,219,275,278,116,117,201,43,44,40,200,368,185,152,186,248,369,226,371,227,32,400,401,398,96,216,452,48,47,33,176,59,399,446,434,436,435,447,239,234,240,237,235,233,238,236,413", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6102,402,4506,20575,27101,20511,28170,27009,28015,28683,27210,11174,5813,1511,11855,10080,13586,17934,20851,18059,17989,18254,18170,18877,18797,20781,20908,20964,10849,10221,22561,11410,26137,26065,4147,28080,7991,19104,19286,5999,19898,2190,2137,17527,16,22898,23678,22399,11039,5876,9192,3144,1838,772,16967,7521,6360,6423,6148,6195,8710,9682,1410,1923,19953,5036,6923,6853,5102,1472,11803,17648,3550,8782,3215,7276,2852,17161,28247,22145,21079,23856,21930,3782,18130,3920,11246,15457,21995,4706,6761,6703,4759,19469,1303,21356,25583,25686,634,25223,4570,7934,21213,19336,5491,20224,2985,16125,2949,20370,16470,16346,16408,8263,10414,11558,18420,23555,23119,9025,16084,728,26888,27721,19566,20411,447,25827,17462,26940,23418,7443,9439,9481,9523,19751,10796,8832,8899,7599,7862,1663,9735,1163,1103,9916,21868,21287,28585,15821,4377,6294,5406,5332,15680,5163,5257,4314,21134,21410,19068,8057,8097,16167,9971,2795,5939,8161,17382,10730,16012,19803,6053,19856,9872,23217,10024,7800,11358,9644,5706,22356,1768,25446,12043,10280,25286,685,931,871,23957,2893,23732,21024,29051,9565,5599,13322,9603,5650,1355,7363,25351,15743,10139,7678,185,116,4921,6597,6532,4980,25943,1987,18570,27665,4058,3482,24037,2403,2470,4213,9789,3612,3083,3725,3849,15883,15938,3363,3991,21664,21607,26380,26293,4644,26001,26557,22439,18513,19993,23049,485,18607,12687,12778,13171,18663,12882,12981,12596,13092,12162,12225,13374,12493,12363,28953,28893,529,13509,344,24413,24808,24518,24611,24704,23620,24254,24357,24184,20272,19190,26810,7192,26716,22035,19428,19148,4808,4865,3285,1719,302,3661,10667,10605,11630,19630,13961,8313,8432,16718,15183,15277,16625,15368,16534,15091,16810,10536,8363,11721,20691,9316,17114,16904,11102,5550,19700,1028,27895,27944,20630,8492,8537,8601,8646,22800,14218,20323,19240,20130,20081,19519,20034,22073,815,10467,11467,255,22959,21722,16257,21786,17204,17726,9360,14272,14361,11308,15529,21563,21517,21473,8973,9090,13891,17309,13802,13694,22268,8220,22307,13454,17578,17823,7025,7086,11976,2253,2317,2058,11906,22613,10904,9264,10980,15600,22711,14072,22847,14159,1549,25059,25162,24914,5759,13269,28776,2637,2556,1605,10348,3432,24972,28318,27283,27487,27407,28423,14936,14558,15013,14795,14661,14478,14863,14723,25906", "endColumns": "45,44,63,54,108,63,76,91,64,92,50,71,62,37,50,58,107,54,56,70,69,165,83,161,79,69,55,59,54,58,51,56,155,71,65,89,65,43,49,53,54,62,52,50,68,60,53,39,62,62,71,70,84,42,146,77,62,108,46,65,71,52,61,63,39,65,101,69,60,38,51,77,61,49,69,86,40,42,70,122,53,100,64,66,39,70,61,71,39,52,91,57,48,49,51,53,102,113,50,62,73,56,73,54,58,47,97,41,35,40,63,61,61,49,52,71,92,64,97,64,40,43,51,94,63,69,37,78,64,44,136,77,41,41,41,51,52,66,73,78,71,55,53,139,59,54,61,68,97,61,128,65,84,73,62,93,74,62,78,58,35,39,63,89,52,56,59,58,79,65,71,52,48,41,43,112,55,61,51,37,52,42,69,136,80,67,64,42,96,59,79,55,75,54,44,37,50,51,40,55,54,79,66,77,48,92,69,68,58,105,64,55,33,70,36,55,88,67,102,66,85,74,82,48,60,56,70,54,73,68,66,57,56,176,86,61,63,158,121,56,40,69,43,55,90,103,97,133,98,110,90,78,62,137,79,102,129,97,59,74,76,57,104,105,92,92,103,57,102,55,69,50,49,77,83,93,37,40,41,56,55,77,48,41,63,62,61,90,69,110,49,59,91,93,90,92,88,90,91,93,68,68,81,54,43,46,62,71,48,50,47,48,70,60,44,63,44,63,46,53,46,45,46,48,46,46,71,55,68,90,46,89,63,88,81,104,96,40,88,67,49,70,43,45,43,51,101,69,72,88,107,38,42,48,54,69,110,60,105,66,63,85,78,69,97,75,51,58,79,88,86,50,58,55,102,60,57,53,52,116,157,80,57,65,49,86,104,123,177,79,115,76,102,76,67,61,79,72,71,36", "endOffsets": "6143,442,4565,20625,27205,20570,28242,27096,28075,28771,27256,11241,5871,1544,11901,10134,13689,17984,20903,18125,18054,18415,18249,19034,18872,20846,20959,21019,10899,10275,22608,11462,26288,26132,4208,28165,8052,19143,19331,6048,19948,2248,2185,17573,80,22954,23727,22434,11097,5934,9259,3210,1918,810,17109,7594,6418,6527,6190,6256,8777,9730,1467,1982,19988,5097,7020,6918,5158,1506,11850,17721,3607,8827,3280,7358,2888,17199,28313,22263,21128,23952,21990,3844,18165,3986,11303,15524,22030,4754,6848,6756,4803,19514,1350,21405,25681,25795,680,25281,4639,7986,21282,19386,5545,20267,3078,16162,2980,20406,16529,16403,16465,8308,10462,11625,18508,23615,23212,9085,16120,767,26935,27811,19625,20476,480,25901,17522,26980,23550,7516,9476,9518,9560,19798,10844,8894,8968,7673,7929,1714,9784,1298,1158,9966,21925,21351,28678,15878,4501,6355,5486,5401,15738,5252,5327,4372,21208,21464,19099,8092,8156,16252,10019,2847,5994,8215,17457,10791,16079,19851,6097,19893,9911,23325,10075,7857,11405,9677,5754,22394,1833,25578,12119,10343,25346,723,1023,926,24032,2944,23803,21074,29091,9598,5645,13369,9639,5701,1405,7438,25413,15816,10183,7766,250,180,4975,6698,6592,5031,25972,2053,18602,27716,4142,3545,24135,2465,2551,4283,9867,3656,3139,3777,3915,15933,16007,3427,4053,21717,21659,26552,26375,4701,26060,26711,22556,18565,20029,23114,524,18658,12773,12877,13264,18792,12976,13087,12682,13166,12220,12358,13449,12591,12488,29046,28948,599,13581,397,24513,24909,24606,24699,24803,23673,24352,24408,24249,20318,19235,26883,7271,26805,22068,19464,19185,4860,4916,3358,1763,339,3720,10725,10662,11716,19695,14067,8358,8487,16805,15272,15363,16713,15452,16620,15178,16899,10600,8427,11798,20741,9355,17156,16962,11169,5594,19746,1071,27939,28010,20686,8532,8596,8641,8705,22842,14267,20365,19281,20172,20125,19561,20076,22140,866,10531,11553,297,23044,21781,16341,21863,17304,17818,9396,14356,14424,11353,15595,21602,21558,21512,9020,9187,13956,17377,13886,13797,22302,8258,22351,13504,17643,17929,7081,7187,12038,2312,2398,2132,11971,22706,10975,9311,11034,15675,22795,14154,22893,14213,1600,25157,25218,24967,5808,13317,28888,2790,2632,1658,10409,3477,25054,28418,27402,27660,27482,28534,15008,14656,15085,14858,14718,14553,14931,14790,25938"}, "to": {"startLines": "10,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,115,116,124,125,126,129,130,131,132,133,135,136,137,156,157,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,195,196,197,198,199,200,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,266,267,268,269,270,271,272,273,274,275,289,290,291,292,293,294,295,296,297,346,347,348,349,350,351,352,353,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,473,474,475,476,477,478,479,480,481,482,483,484,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,505,506,507,508,509,510,511,512,513,514,515,516,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,551,552,553,554,555,556,557,558,559,560,561,562,563,564", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "515,3215,3260,3324,3379,3488,3552,3629,3721,3786,3879,3930,4002,4065,4103,4154,4213,4321,4376,4433,4504,4574,4740,4824,4986,5066,5136,5192,5252,5307,5366,5418,5475,5631,5703,5769,5859,5925,5969,6019,6073,6128,6191,6244,6295,6364,6425,6479,6519,6582,6645,6717,6788,6873,6916,7063,7141,7204,7313,7360,7426,7498,7551,7613,7677,7717,7783,7885,7955,8016,8055,8107,8185,8660,8710,9492,9579,9620,9883,9954,10077,10131,10232,10374,10441,10481,12648,12710,12882,12922,12975,13067,13125,13174,13224,13276,13330,13433,13547,13598,13661,13735,13792,13866,13921,13980,14028,14126,14168,14204,14245,14309,14371,14433,14483,14536,14608,14701,14766,14864,14929,14970,15136,15188,15283,15347,15417,15455,15933,15998,16043,16180,16258,16300,16342,16384,16436,16489,16556,16630,16709,16781,16837,16891,17031,17091,17146,17208,17277,17375,17437,17566,17632,17717,17791,17854,17948,18023,18086,18165,18224,18260,18300,18364,18454,18507,18564,18624,18683,18763,18829,18901,18954,19003,19045,19089,19202,19258,19320,19372,19410,19463,19506,19576,19713,19794,19862,20063,20106,20203,20263,20343,20399,20475,20530,20575,20613,21629,21681,21722,21778,21833,21913,21980,22058,22107,26054,26124,26193,26252,26358,26423,26479,26513,27599,27636,27692,27781,27849,27952,28019,28105,28180,28263,28312,28373,28430,28501,28556,28630,28699,28766,28824,28881,29058,29225,29287,29351,29510,29632,29689,29730,29800,29844,29900,29991,30095,30193,30327,30426,30537,30628,30707,30770,30908,30988,31091,31221,31319,31379,31454,31531,31589,31694,31800,31893,31986,32090,32148,32251,32307,32377,32428,32478,32556,32640,32734,32772,32813,32855,32912,32968,33046,33095,33137,33201,33264,33326,33417,33487,33598,33648,33708,33800,33894,33985,34078,34167,34258,34350,34444,34513,34582,34664,34719,34763,34810,34873,34945,34994,35045,35093,35142,35213,35633,35678,35742,35787,35851,35898,35952,35999,36045,36092,36141,36188,36471,36543,36599,36668,36759,36806,36896,36960,37049,37131,37236,37333,37374,37463,37531,37581,37728,37772,37818,37862,37914,38016,38086,38159,38248,38356,38395,38438,38588,38643,38713,38824,38885,38991,39058,39122,39208,39287,39357,39455,39531,39583,39642,39722,39811,39898,39949,40008,40064,40167,40228,40286,40340,40393,40510,40668,40749,40807,40873,40923,41070,41175,41299,41477,41557,41673,41750,41853,41930,41998,42060,42140,42213,42285", "endColumns": "45,44,63,54,108,63,76,91,64,92,50,71,62,37,50,58,107,54,56,70,69,165,83,161,79,69,55,59,54,58,51,56,155,71,65,89,65,43,49,53,54,62,52,50,68,60,53,39,62,62,71,70,84,42,146,77,62,108,46,65,71,52,61,63,39,65,101,69,60,38,51,77,61,49,69,86,40,42,70,122,53,100,64,66,39,70,61,71,39,52,91,57,48,49,51,53,102,113,50,62,73,56,73,54,58,47,97,41,35,40,63,61,61,49,52,71,92,64,97,64,40,43,51,94,63,69,37,78,64,44,136,77,41,41,41,51,52,66,73,78,71,55,53,139,59,54,61,68,97,61,128,65,84,73,62,93,74,62,78,58,35,39,63,89,52,56,59,58,79,65,71,52,48,41,43,112,55,61,51,37,52,42,69,136,80,67,64,42,96,59,79,55,75,54,44,37,50,51,40,55,54,79,66,77,48,92,69,68,58,105,64,55,33,70,36,55,88,67,102,66,85,74,82,48,60,56,70,54,73,68,66,57,56,176,86,61,63,158,121,56,40,69,43,55,90,103,97,133,98,110,90,78,62,137,79,102,129,97,59,74,76,57,104,105,92,92,103,57,102,55,69,50,49,77,83,93,37,40,41,56,55,77,48,41,63,62,61,90,69,110,49,59,91,93,90,92,88,90,91,93,68,68,81,54,43,46,62,71,48,50,47,48,70,60,44,63,44,63,46,53,46,45,46,48,46,46,71,55,68,90,46,89,63,88,81,104,96,40,88,67,49,70,43,45,43,51,101,69,72,88,107,38,42,48,54,69,110,60,105,66,63,85,78,69,97,75,51,58,79,88,86,50,58,55,102,60,57,53,52,116,157,80,57,65,49,86,104,123,177,79,115,76,102,76,67,61,79,72,71,36", "endOffsets": "556,3255,3319,3374,3483,3547,3624,3716,3781,3874,3925,3997,4060,4098,4149,4208,4316,4371,4428,4499,4569,4735,4819,4981,5061,5131,5187,5247,5302,5361,5413,5470,5626,5698,5764,5854,5920,5964,6014,6068,6123,6186,6239,6290,6359,6420,6474,6514,6577,6640,6712,6783,6868,6911,7058,7136,7199,7308,7355,7421,7493,7546,7608,7672,7712,7778,7880,7950,8011,8050,8102,8180,8242,8705,8775,9574,9615,9658,9949,10072,10126,10227,10292,10436,10476,10547,12705,12777,12917,12970,13062,13120,13169,13219,13271,13325,13428,13542,13593,13656,13730,13787,13861,13916,13975,14023,14121,14163,14199,14240,14304,14366,14428,14478,14531,14603,14696,14761,14859,14924,14965,15009,15183,15278,15342,15412,15450,15529,15993,16038,16175,16253,16295,16337,16379,16431,16484,16551,16625,16704,16776,16832,16886,17026,17086,17141,17203,17272,17370,17432,17561,17627,17712,17786,17849,17943,18018,18081,18160,18219,18255,18295,18359,18449,18502,18559,18619,18678,18758,18824,18896,18949,18998,19040,19084,19197,19253,19315,19367,19405,19458,19501,19571,19708,19789,19857,19922,20101,20198,20258,20338,20394,20470,20525,20570,20608,20659,21676,21717,21773,21828,21908,21975,22053,22102,22195,26119,26188,26247,26353,26418,26474,26508,26579,27631,27687,27776,27844,27947,28014,28100,28175,28258,28307,28368,28425,28496,28551,28625,28694,28761,28819,28876,29053,29140,29282,29346,29505,29627,29684,29725,29795,29839,29895,29986,30090,30188,30322,30421,30532,30623,30702,30765,30903,30983,31086,31216,31314,31374,31449,31526,31584,31689,31795,31888,31981,32085,32143,32246,32302,32372,32423,32473,32551,32635,32729,32767,32808,32850,32907,32963,33041,33090,33132,33196,33259,33321,33412,33482,33593,33643,33703,33795,33889,33980,34073,34162,34253,34345,34439,34508,34577,34659,34714,34758,34805,34868,34940,34989,35040,35088,35137,35208,35269,35673,35737,35782,35846,35893,35947,35994,36040,36087,36136,36183,36230,36538,36594,36663,36754,36801,36891,36955,37044,37126,37231,37328,37369,37458,37526,37576,37647,37767,37813,37857,37909,38011,38081,38154,38243,38351,38390,38433,38482,38638,38708,38819,38880,38986,39053,39117,39203,39282,39352,39450,39526,39578,39637,39717,39806,39893,39944,40003,40059,40162,40223,40281,40335,40388,40505,40663,40744,40802,40868,40918,41005,41170,41294,41472,41552,41668,41745,41848,41925,41993,42055,42135,42208,42280,42317"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\aff62db08cff511cc4373673ada3b6cf\\transformed\\play-services-basement-18.5.0\\res\\values-ar\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "146", "startColumns": "4", "startOffsets": "11506", "endColumns": "129", "endOffsets": "11631"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8ba171b74415ef9d04b1f3a055c632e8\\transformed\\play-services-ads-24.2.0\\res\\values-ar\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,240,288,342,406,474,573,634,755,871,1006,1059,1116,1228,1313,1351,1430,1462,1493,1536,1604,1644", "endColumns": "40,47,53,63,67,98,60,120,115,134,52,56,111,84,37,78,31,30,42,67,39,55", "endOffsets": "239,287,341,405,473,572,633,754,870,1005,1058,1115,1227,1312,1350,1429,1461,1492,1535,1603,1643,1699"}, "to": {"startLines": "343,344,345,354,355,356,357,358,359,360,361,362,363,364,466,467,468,469,470,471,472,550", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "25899,25944,25996,26584,26652,26724,26827,26892,27017,27137,27276,27333,27394,27510,35274,35316,35399,35435,35470,35517,35589,41010", "endColumns": "44,51,57,67,71,102,64,124,119,138,56,60,115,88,41,82,35,34,46,71,43,59", "endOffsets": "25939,25991,26049,26647,26719,26822,26887,27012,27132,27271,27328,27389,27505,27594,35311,35394,35430,35465,35512,35584,35628,41065"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cf3dd3f063a8dc2168576dc8a6639ef4\\transformed\\core-1.13.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,250,345,448,551,653,767", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "143,245,340,443,546,648,762,863"}, "to": {"startLines": "117,118,119,120,121,122,123,517", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "8780,8873,8975,9070,9173,9276,9378,38487", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "8868,8970,9065,9168,9271,9373,9487,38583"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ef3205b01dd1e8d62ca95413fbcfcde0\\transformed\\play-services-base-18.5.0\\res\\values-ar\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,433,551,652,786,910,1017,1115,1248,1348,1494,1612,1747,1889,1949,2011", "endColumns": "99,139,117,100,133,123,106,97,132,99,145,117,134,141,59,61,79", "endOffsets": "292,432,550,651,785,909,1016,1114,1247,1347,1493,1611,1746,1888,1948,2010,2090"}, "to": {"startLines": "138,139,140,141,142,143,144,145,147,148,149,150,151,152,153,154,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10552,10656,10800,10922,11027,11165,11293,11404,11636,11773,11877,12027,12149,12288,12434,12498,12564", "endColumns": "103,143,121,104,137,127,110,101,136,103,149,121,138,145,63,65,83", "endOffsets": "10651,10795,10917,11022,11160,11288,11399,11501,11768,11872,12022,12144,12283,12429,12493,12559,12643"}}]}]}