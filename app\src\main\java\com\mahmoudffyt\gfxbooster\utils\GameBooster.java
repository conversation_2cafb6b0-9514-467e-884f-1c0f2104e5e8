package com.mahmoudffyt.gfxbooster.utils;

import android.app.ActivityManager;
import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Handler;
import android.os.PowerManager;
import android.util.Log;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Utility class to optimize device performance for gaming
 */
public class GameBooster {
    private Context context;
    private ActivityManager activityManager;
    private PowerManager powerManager;
    private PackageManager packageManager;

    // Cache for high usage apps
    private List<AppUsageInfo> highUsageApps = new ArrayList<>();
    private long lastUpdateTime = 0;
    private static final long UPDATE_INTERVAL = 120000; // 2 minutes

    /**
     * Constructor
     * @param context Application context
     */
    public GameBooster(Context context) {
        this.context = context;
        this.activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        this.powerManager = (PowerManager) context.getSystemService(Context.POWER_SERVICE);
        this.packageManager = context.getPackageManager();
    }

    /**
     * Optimize device for gaming
     * - Clear cache
     * - Close background apps
     * - Optimize RAM
     * - Optimize CPU
     */
    public void optimizeDevice() {
        // Run optimization in background thread
        new Thread(() -> {
            try {
                // Clear cache
                clearCache();

                // Close background apps
                closeBackgroundApps();

                // Optimize RAM
                optimizeRAM();

                // Optimize CPU
                optimizeCPU();

                // Update high usage apps list
                updateHighUsageApps();

                Log.d("GameBooster", "Device optimization completed");
            } catch (Exception e) {
                Log.e("GameBooster", "Error optimizing device: " + e.getMessage());
            }
        }).start();
    }

    /**
     * Clear app cache
     */
    private void clearCache() {
        try {
            // Clear app cache
            File cacheDir = context.getCacheDir();
            if (cacheDir != null && cacheDir.exists()) {
                deleteDir(cacheDir);
                Log.d("GameBooster", "App cache cleared");
            }

            // Clear external cache if available
            File externalCacheDir = context.getExternalCacheDir();
            if (externalCacheDir != null && externalCacheDir.exists()) {
                deleteDir(externalCacheDir);
                Log.d("GameBooster", "External cache cleared");
            }
        } catch (Exception e) {
            Log.e("GameBooster", "Error clearing cache: " + e.getMessage());
        }
    }

    /**
     * Delete directory recursively
     * @param dir Directory to delete
     * @return true if successful, false otherwise
     */
    private boolean deleteDir(File dir) {
        if (dir != null && dir.isDirectory()) {
            String[] children = dir.list();
            if (children != null) {
                for (String child : children) {
                    boolean success = deleteDir(new File(dir, child));
                    if (!success) {
                        return false;
                    }
                }
            }
        }

        // Delete the file/directory itself
        return dir.delete();
    }

    /**
     * Close background apps
     */
    private void closeBackgroundApps() {
        try {
            // Get running apps
            List<ActivityManager.RunningAppProcessInfo> runningProcesses = activityManager.getRunningAppProcesses();
            if (runningProcesses == null) {
                Log.d("GameBooster", "No running processes found");
                return;
            }

            // Get our own package name
            String ourPackage = context.getPackageName();

            // Close background apps
            for (ActivityManager.RunningAppProcessInfo processInfo : runningProcesses) {
                // Skip system processes and our own app
                if (processInfo.importance > ActivityManager.RunningAppProcessInfo.IMPORTANCE_SERVICE &&
                    !processInfo.processName.equals(ourPackage)) {

                    // Check if this is a user app (not system app)
                    boolean isUserApp = false;
                    try {
                        ApplicationInfo appInfo = packageManager.getApplicationInfo(processInfo.processName, 0);
                        isUserApp = (appInfo.flags & ApplicationInfo.FLAG_SYSTEM) == 0;
                    } catch (PackageManager.NameNotFoundException e) {
                        // Skip if we can't get app info
                        continue;
                    }

                    // Only kill user apps
                    if (isUserApp) {
                        Log.d("GameBooster", "Killing background app: " + processInfo.processName);
                        activityManager.killBackgroundProcesses(processInfo.processName);
                    }
                }
            }

            Log.d("GameBooster", "Background apps closed");
        } catch (Exception e) {
            Log.e("GameBooster", "Error closing background apps: " + e.getMessage());
        }
    }

    /**
     * Optimize RAM
     */
    private void optimizeRAM() {
        try {
            // Force garbage collection
            System.gc();
            Runtime.getRuntime().gc();

            // Trim memory on all services
            activityManager.getMemoryClass();

            Log.d("GameBooster", "RAM optimized");
        } catch (Exception e) {
            Log.e("GameBooster", "Error optimizing RAM: " + e.getMessage());
        }
    }

    /**
     * Optimize CPU
     */
    private void optimizeCPU() {
        try {
            // Request high performance mode if available
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                PowerManager.WakeLock wakeLock = powerManager.newWakeLock(
                        PowerManager.PARTIAL_WAKE_LOCK, "GameBooster:CPUBoost");

                // Acquire wake lock for a short time to boost CPU
                wakeLock.acquire(5000);

                // Release in a separate thread after 5 seconds
                new Handler().postDelayed(() -> {
                    if (wakeLock.isHeld()) {
                        wakeLock.release();
                    }
                }, 5000);
            }

            Log.d("GameBooster", "CPU optimized");
        } catch (Exception e) {
            Log.e("GameBooster", "Error optimizing CPU: " + e.getMessage());
        }
    }

    /**
     * Activate game mode
     * - Set high performance mode
     * - Disable notifications
     * - Optimize device
     */
    public void activateGameMode() {
        // Run in background thread
        new Thread(() -> {
            try {
                // Optimize device first
                optimizeDevice();

                // Request high performance mode
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    PowerManager.WakeLock wakeLock = powerManager.newWakeLock(
                            PowerManager.PARTIAL_WAKE_LOCK, "GameBooster:GameMode");

                    // Acquire wake lock for gaming session (max 1 hour)
                    wakeLock.acquire(3600000);

                    // Release in a separate thread after 1 hour
                    new Handler().postDelayed(() -> {
                        if (wakeLock.isHeld()) {
                            wakeLock.release();
                        }
                    }, 3600000);
                }

                Log.d("GameBooster", "Game mode activated");
            } catch (Exception e) {
                Log.e("GameBooster", "Error activating game mode: " + e.getMessage());
            }
        }).start();
    }

    /**
     * Update list of high usage apps
     */
    private void updateHighUsageApps() {
        long currentTime = System.currentTimeMillis();

        // Only update if enough time has passed since last update
        if (currentTime - lastUpdateTime < UPDATE_INTERVAL) {
            return;
        }

        try {
            // Get running apps
            List<ActivityManager.RunningAppProcessInfo> runningProcesses = activityManager.getRunningAppProcesses();
            if (runningProcesses == null) {
                return;
            }

            // Get memory info for each process
            List<AppUsageInfo> appUsageList = new ArrayList<>();
            for (ActivityManager.RunningAppProcessInfo processInfo : runningProcesses) {
                try {
                    // Skip our own app
                    if (processInfo.processName.equals(context.getPackageName())) {
                        continue;
                    }

                    // Get app name
                    String appName = processInfo.processName;
                    try {
                        ApplicationInfo appInfo = packageManager.getApplicationInfo(processInfo.processName, 0);
                        appName = packageManager.getApplicationLabel(appInfo).toString();
                    } catch (PackageManager.NameNotFoundException e) {
                        // Use process name if app name not found
                        Log.d("GameBooster", "App not found: " + processInfo.processName);
                        continue;
                    }

                    // Get memory usage
                    int[] pids = new int[1];
                    pids[0] = processInfo.pid;
                    android.os.Debug.MemoryInfo[] memoryInfoArray = activityManager.getProcessMemoryInfo(pids);

                    if (memoryInfoArray != null && memoryInfoArray.length > 0) {
                        int memoryUsage = memoryInfoArray[0].getTotalPss();

                        // Add to list if memory usage is significant
                        if (memoryUsage > 1000) { // More than 1MB
                            appUsageList.add(new AppUsageInfo(
                                    processInfo.processName,
                                    appName,
                                    memoryUsage,
                                    processInfo.importance
                            ));
                        }
                    }
                } catch (Exception e) {
                    Log.e("GameBooster", "Error processing package " + processInfo.processName + ": " + processInfo.processName);
                    // Skip this app if there's an error
                    continue;
                }
            }

            // Sort by memory usage (highest first)
            Collections.sort(appUsageList, (a1, a2) -> Integer.compare(a2.memoryUsage, a1.memoryUsage));

            // Keep only top 3 apps
            if (appUsageList.size() > 3) {
                appUsageList = appUsageList.subList(0, 3);
            }

            // Update the list
            highUsageApps = appUsageList;
            lastUpdateTime = currentTime;

            Log.d("GameBooster", "High usage apps updated: " + highUsageApps.size() + " apps");
        } catch (Exception e) {
            Log.e("GameBooster", "Error updating high usage apps: " + e.getMessage());
        }
    }

    /**
     * Get list of high usage apps
     * @return List of high usage apps
     */
    public List<AppUsageInfo> getHighUsageApps() {
        // Update if needed
        if (System.currentTimeMillis() - lastUpdateTime > UPDATE_INTERVAL) {
            updateHighUsageApps();
        }

        return highUsageApps;
    }

    /**
     * Class to hold app usage information
     */
    public static class AppUsageInfo {
        public String packageName;
        public String appName;
        public int memoryUsage; // in KB
        public int importance;

        public AppUsageInfo(String packageName, String appName, int memoryUsage, int importance) {
            this.packageName = packageName;
            this.appName = appName;
            this.memoryUsage = memoryUsage;
            this.importance = importance;
        }
    }
}
