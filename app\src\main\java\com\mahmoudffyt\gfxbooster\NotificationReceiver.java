package com.mahmoudffyt.gfxbooster;

import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.util.Log;

import androidx.core.app.NotificationCompat;

/**
 * BroadcastReceiver to handle scheduled notifications
 */
public class NotificationReceiver extends BroadcastReceiver {
    private static final String TAG = "NotificationReceiver";
    private static final String CHANNEL_ID = "game_booster_channel";
    private static final int NOTIFICATION_ID_BASE = 1001;

    @Override
    public void onReceive(Context context, Intent intent) {
        Log.d(TAG, "Received intent: " + intent.getAction());

        if (intent.getAction() == null) {
            return;
        }

        switch (intent.getAction()) {
            case "com.game.headshot.ACTION_NOON_REMINDER":
                showDailyReminderNotification(context, 1);
                break;
            case "com.game.headshot.ACTION_EVENING_REMINDER":
                showDailyReminderNotification(context, 2);
                break;
            case "com.game.headshot.ACTION_HIGH_USAGE":
                int cpuUsage = intent.getIntExtra("cpu_usage", 70);
                int memoryUsage = intent.getIntExtra("memory_usage", 80);
                showHighUsageNotification(context, cpuUsage, memoryUsage);
                break;
            case "com.game.headshot.ACTION_CHECK_SYSTEM":
                // This would normally check system status, but for now just show a notification
                showSystemCheckNotification(context);
                break;
        }
    }

    private void showDailyReminderNotification(Context context, int type) {
        Log.d(TAG, "Showing daily reminder notification type: " + type);
        
        // Create notification for daily reminder
        NotificationManager notificationManager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);

        // Create intent for when notification is clicked
        Intent intent = new Intent(context, GameBoosterActivity.class);
        intent.setAction("com.game.headshot.ACTION_CHECK_SYSTEM");
        intent.putExtra("notification_launch", true);
        intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);

        // Create pending intent
        int flags = PendingIntent.FLAG_UPDATE_CURRENT;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            flags |= PendingIntent.FLAG_IMMUTABLE;
        }
        PendingIntent pendingIntent = PendingIntent.getActivity(context, 0, intent, flags);

        // Get the appropriate message based on type
        String message;
        if (type == 1) {
            message = context.getString(R.string.daily_reminder_notification_1);
        } else {
            message = context.getString(R.string.daily_reminder_notification_2);
        }

        // Build the notification
        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, CHANNEL_ID)
                .setSmallIcon(R.drawable.ic_rocket)
                .setContentTitle(context.getString(R.string.game_booster_title))
                .setContentText(message)
                .setPriority(NotificationCompat.PRIORITY_HIGH)
                .setContentIntent(pendingIntent)
                .setAutoCancel(true);

        // Show the notification
        notificationManager.notify(NOTIFICATION_ID_BASE + 10 + type, builder.build());
    }

    private void showHighUsageNotification(Context context, int cpuUsage, int memoryUsage) {
        Log.d(TAG, "Showing high usage notification: CPU=" + cpuUsage + "%, Memory=" + memoryUsage + "%");
        
        // Create notification for high system usage
        NotificationManager notificationManager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);

        // Create intent for when notification is clicked
        Intent intent = new Intent(context, GameBoosterActivity.class);
        intent.setAction("com.game.headshot.ACTION_CHECK_SYSTEM");
        intent.putExtra("notification_launch", true);
        intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);

        // Create pending intent
        int flags = PendingIntent.FLAG_UPDATE_CURRENT;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            flags |= PendingIntent.FLAG_IMMUTABLE;
        }
        PendingIntent pendingIntent = PendingIntent.getActivity(context, 0, intent, flags);

        // Build the notification
        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, CHANNEL_ID)
                .setSmallIcon(R.drawable.ic_rocket)
                .setContentTitle(context.getString(R.string.optimization_needed))
                .setContentText(context.getString(R.string.high_usage_notification, cpuUsage, memoryUsage))
                .setPriority(NotificationCompat.PRIORITY_HIGH)
                .setContentIntent(pendingIntent)
                .setAutoCancel(true);

        // Show the notification
        notificationManager.notify(NOTIFICATION_ID_BASE + 3, builder.build());
    }

    private void showSystemCheckNotification(Context context) {
        Log.d(TAG, "Showing system check notification");
        
        // Create notification for system check
        NotificationManager notificationManager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);

        // Create intent for when notification is clicked
        Intent intent = new Intent(context, GameBoosterActivity.class);
        intent.setAction("com.game.headshot.ACTION_CHECK_SYSTEM");
        intent.putExtra("notification_launch", true);
        intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);

        // Create pending intent
        int flags = PendingIntent.FLAG_UPDATE_CURRENT;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            flags |= PendingIntent.FLAG_IMMUTABLE;
        }
        PendingIntent pendingIntent = PendingIntent.getActivity(context, 0, intent, flags);

        // Build the notification
        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, CHANNEL_ID)
                .setSmallIcon(R.drawable.ic_rocket)
                .setContentTitle(context.getString(R.string.optimization_needed))
                .setContentText(context.getString(R.string.daily_reminder_notification_2))
                .setPriority(NotificationCompat.PRIORITY_HIGH)
                .setContentIntent(pendingIntent)
                .setAutoCancel(true);

        // Show the notification
        notificationManager.notify(NOTIFICATION_ID_BASE + 5, builder.build());
    }
}
