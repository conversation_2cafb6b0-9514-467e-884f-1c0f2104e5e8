<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardBackgroundColor="@color/card_background"
    app:cardCornerRadius="12dp"
    app:cardElevation="8dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- Game Icon -->
        <ImageView
            android:id="@+id/game_icon"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:scaleType="centerCrop"
            android:src="@drawable/ic_games" />

        <!-- Game Info -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/game_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/cairo_regular"
                android:text="Game Name"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/package_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:fontFamily="@font/cairo_regular"
                android:text="com.example.game"
                android:textColor="@color/text_color_secondary"
                android:textSize="12sp" />

        </LinearLayout>

        <!-- Game Badge -->
        <ImageView
            android:id="@+id/game_badge"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginStart="8dp"
            android:src="@drawable/ic_games"
            android:visibility="gone"
            app:tint="@color/neon_blue" />

        <!-- Selection Arrow -->
        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginStart="8dp"
            android:src="@drawable/ic_arrow_forward"
            app:tint="@color/neon_blue" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
