plugins {
    alias(libs.plugins.android.application)
    //id 'com.android.application'
    alias(libs.plugins.google.services)
    alias(libs.plugins.firebase.crashlytics)
}

android {
    namespace 'com.mahmoudffyt.gfxbooster'
    compileSdk 35

    defaultConfig {
        applicationId "com.mahmoudffyt.gfxbooster"
        minSdk 23
        targetSdk 35
        versionCode 7
        versionName "1.1.4"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    signingConfigs {
        release {
            storeFile file("keygoogle.jks")
            storePassword "mahmoudff.yt1"
            keyAlias "key0"
            keyPassword "mahmoudff.yt1"
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            // Enable Firebase Crashlytics for release builds
            firebaseCrashlytics {
                mappingFileUploadEnabled true
            }
        }
        debug {
            // Enable Firebase Crashlytics for debug builds
            firebaseCrashlytics {
                mappingFileUploadEnabled false
            }
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
}

dependencies {

    implementation libs.appcompat
    implementation libs.material
    implementation libs.activity
    implementation libs.constraintlayout
    testImplementation libs.junit
    androidTestImplementation libs.ext.junit
    androidTestImplementation libs.espresso.core
    implementation libs.lottie
    implementation libs.material.v100
    implementation platform(libs.firebase.bom)
    implementation libs.firebase.analytics.ktx
    implementation libs.firebase.crashlytics.ktx
    implementation libs.firebase.crashlytics
    implementation libs.firebase.analytics
    implementation libs.billing
    implementation libs.admob
}