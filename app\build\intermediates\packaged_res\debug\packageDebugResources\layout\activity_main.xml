<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:context=".MainActivity"
    tools:openDrawer="start">

    <!-- Main content container -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/main"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/cyber_neon_gradient_bg">

        <!-- Background Effects -->
        <ImageView
            android:id="@+id/speed_lines_bg"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:src="@drawable/blue_speed_lines"
            android:alpha="0.4"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:contentDescription="@string/app_name" />

        <ImageView
            android:id="@+id/energy_grid"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:src="@drawable/energy_grid"
            android:alpha="0.2"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:contentDescription="@string/app_name" />

        <!-- Top App Bar -->
        <LinearLayout
            android:id="@+id/top_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingTop="@dimen/padding_medium"
            android:paddingStart="@dimen/padding_medium"
            android:paddingEnd="@dimen/padding_medium"
            android:paddingBottom="@dimen/padding_small"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <ImageView
                android:id="@+id/menu_icon"
                android:layout_width="@dimen/icon_size_medium"
                android:layout_height="@dimen/icon_size_medium"
                android:src="@drawable/ic_menu"
                android:contentDescription="@string/navigation_drawer_open" />

            <TextView
                android:id="@+id/app_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:fontFamily="@font/font_for_ar_en"
                android:letterSpacing="0.2"
                android:shadowColor="@color/cyber_neon_cyan"
                android:shadowDx="0"
                android:shadowDy="0"
                android:shadowRadius="10"
                android:text="@string/app_name_welcom_screen"
                android:textColor="@color/cyber_text_primary"
                android:textSize="@dimen/text_size_title"
                android:textStyle="bold" />

            <View
                android:layout_width="@dimen/icon_size_medium"
                android:layout_height="@dimen/icon_size_medium" />
        </LinearLayout>

        <!-- Subtitle -->
        <TextView
            android:id="@+id/app_subtitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/font_for_ar_en"
            android:text="@string/game_enhancer"
            android:textColor="@color/cyber_neon_cyan"
            android:textSize="@dimen/text_size_subtitle"
            android:layout_marginTop="@dimen/margin_tiny"
            app:layout_constraintTop_toBottomOf="@id/top_bar"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Device Info Card -->
        <androidx.cardview.widget.CardView
            android:id="@+id/device_info_card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_medium"
            android:layout_marginEnd="@dimen/margin_medium"
            android:layout_marginTop="@dimen/margin_medium"
            app:cardCornerRadius="@dimen/card_corner_radius"
            app:cardElevation="@dimen/card_elevation"
            app:cardBackgroundColor="@color/cyber_card_bg"
            app:layout_constraintTop_toBottomOf="@id/app_subtitle"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="@dimen/padding_medium">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/device_info"
                    android:textSize="@dimen/text_size_card_title"
                    android:textColor="@color/cyber_neon_cyan"
                    android:textStyle="bold"
                    android:fontFamily="@font/font_for_ar_en" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/cyber_card_stroke"
                    android:layout_marginTop="@dimen/margin_tiny"
                    android:layout_marginBottom="@dimen/margin_small" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/ram_label"
                            android:textSize="@dimen/text_size_small"
                            android:textColor="@color/cyber_text_secondary"
                            android:fontFamily="@font/cairo_regular" />

                        <TextView
                            android:id="@+id/ram_info"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="4GB / 8GB"
                            android:textSize="@dimen/text_size_card_content"
                            android:textColor="@color/cyber_neon_cyan"
                            android:fontFamily="@font/cairo_regular" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/processor_label"
                            android:textSize="12sp"
                            android:textColor="@color/cyber_text_secondary"
                            android:fontFamily="@font/cairo_regular" />

                        <TextView
                            android:id="@+id/processor_info"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Snapdragon 888"
                            android:textSize="14sp"
                            android:textColor="@color/cyber_neon_cyan"
                            android:fontFamily="@font/cairo_regular" />
                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginTop="@dimen/margin_small">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/screen_label"
                            android:textSize="12sp"
                            android:textColor="@color/cyber_text_secondary"
                            android:fontFamily="@font/cairo_regular" />

                        <TextView
                            android:id="@+id/screen_info"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="1080 x 2400"
                            android:textSize="14sp"
                            android:textColor="@color/cyber_neon_cyan"
                            android:fontFamily="@font/cairo_regular" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/android_label"
                            android:textSize="12sp"
                            android:textColor="@color/cyber_text_secondary"
                            android:fontFamily="@font/cairo_regular" />

                        <TextView
                            android:id="@+id/android_info"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Android 12"
                            android:textSize="14sp"
                            android:textColor="@color/cyber_neon_cyan"
                            android:fontFamily="@font/cairo_regular" />
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- Game Booster Button (Large) -->
        <androidx.cardview.widget.CardView
            android:id="@+id/btn_game_booster"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_medium"
            android:layout_marginEnd="@dimen/margin_medium"
            android:layout_marginTop="@dimen/margin_medium"
            app:cardCornerRadius="@dimen/card_corner_radius"
            app:cardElevation="@dimen/card_elevation"
            app:cardBackgroundColor="@color/cyber_card_bg"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?attr/selectableItemBackground"
            app:layout_constraintTop_toBottomOf="@id/device_info_card"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:padding="@dimen/padding_medium">

                <ImageView
                    android:id="@+id/icon_game_booster"
                    android:layout_width="@dimen/icon_size_large"
                    android:layout_height="@dimen/icon_size_large"
                    android:layout_marginBottom="@dimen/margin_small"
                    android:contentDescription="@string/app_name"
                    android:src="@drawable/ic_game_booster"
                    app:tint="@color/cyber_neon_cyan" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/font_for_ar_en"
                    android:text="@string/game_booster"
                    android:textColor="@color/cyber_neon_cyan"
                    android:textSize="@dimen/text_size_title"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/margin_tiny"
                    android:fontFamily="@font/cairo_regular"
                    android:text="@string/optimize_your_gaming_experience"
                    android:textColor="@color/cyber_text_secondary"
                    android:textSize="@dimen/text_size_subtitle"
                    android:gravity="center" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- Other buttons in grid layout -->
        <GridLayout
            android:id="@+id/features_grid"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_medium"
            android:layout_marginEnd="@dimen/margin_medium"
            android:layout_marginTop="@dimen/margin_medium"
            android:columnCount="2"
            android:rowCount="2"
            app:layout_constraintTop_toBottomOf="@id/btn_game_booster"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">


            <!-- GFX Tools Button -->
            <androidx.cardview.widget.CardView
                android:id="@+id/btn_gfx_tools"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_columnWeight="1"
                android:layout_rowWeight="1"
                android:layout_margin="@dimen/margin_small"
                app:cardCornerRadius="@dimen/card_small_corner_radius"
                app:cardElevation="@dimen/card_small_elevation"
                app:cardBackgroundColor="@color/cyber_card_bg"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?attr/selectableItemBackground">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="@dimen/padding_medium">

                    <ImageView
                        android:id="@+id/icon_gfx_tools"
                        android:layout_width="@dimen/icon_size_medium"
                        android:layout_height="@dimen/icon_size_medium"
                        android:layout_marginBottom="@dimen/margin_small"
                        android:contentDescription="@string/app_name"
                        android:src="@drawable/ic_gfx_tools"
                        app:tint="@color/cyber_neon_cyan" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/font_for_ar_en"
                        android:text="@string/gfx_tools_main"
                        android:textColor="@color/cyber_neon_cyan"
                        android:textSize="@dimen/text_size_card_title" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Headshot Tool Button -->
            <androidx.cardview.widget.CardView
                android:id="@+id/btn_headshot_tool"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_columnWeight="1"
                android:layout_rowWeight="1"
                android:layout_margin="@dimen/margin_small"
                app:cardCornerRadius="@dimen/card_small_corner_radius"
                app:cardElevation="@dimen/card_small_elevation"
                app:cardBackgroundColor="@color/cyber_card_bg"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?attr/selectableItemBackground">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="@dimen/padding_medium">

                    <ImageView
                        android:id="@+id/icon_headshot_tool"
                        android:layout_width="@dimen/icon_size_medium"
                        android:layout_height="@dimen/icon_size_medium"
                        android:layout_marginBottom="@dimen/margin_small"
                        android:contentDescription="@string/app_name"
                        android:src="@drawable/ic_headshot_tool"
                        app:tint="@color/cyber_neon_cyan" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/font_for_ar_en"
                        android:text="@string/headshot_tool"
                        android:textColor="@color/cyber_neon_cyan"
                        android:textSize="@dimen/text_size_card_title" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Aim Button -->
            <androidx.cardview.widget.CardView
                android:id="@+id/btn_aim_button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_columnWeight="1"
                android:layout_rowWeight="1"
                android:layout_margin="@dimen/margin_small"
                app:cardCornerRadius="@dimen/card_small_corner_radius"
                app:cardElevation="@dimen/card_small_elevation"
                app:cardBackgroundColor="@color/cyber_card_bg"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?attr/selectableItemBackground">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="@dimen/padding_medium">

                    <ImageView
                        android:id="@+id/icon_aim_button"
                        android:layout_width="@dimen/icon_size_medium"
                        android:layout_height="@dimen/icon_size_medium"
                        android:layout_marginBottom="@dimen/margin_small"
                        android:contentDescription="@string/app_name"
                        android:src="@drawable/ic_aim_button"
                        app:tint="@color/cyber_neon_cyan" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/font_for_ar_en"
                        android:text="@string/aim_button"
                        android:textColor="@color/cyber_neon_cyan"
                        android:textSize="@dimen/text_size_card_title" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Mood Game Button -->
            <androidx.cardview.widget.CardView
                android:id="@+id/btn_mood_game"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_columnWeight="1"
                android:layout_rowWeight="1"
                android:layout_margin="@dimen/margin_small"
                app:cardCornerRadius="@dimen/card_small_corner_radius"
                app:cardElevation="@dimen/card_small_elevation"
                app:cardBackgroundColor="@color/cyber_card_bg"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?attr/selectableItemBackground">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="@dimen/padding_medium">

                    <ImageView
                        android:id="@+id/icon_mood_game"
                        android:layout_width="@dimen/icon_size_medium"
                        android:layout_height="@dimen/icon_size_medium"
                        android:layout_marginBottom="@dimen/margin_small"
                        android:contentDescription="@string/app_name"
                        android:src="@drawable/ic_mood_game"
                        app:tint="@color/cyber_neon_cyan" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/font_for_ar_en"
                        android:text="@string/mood_game"
                        android:textColor="@color/cyber_neon_cyan"
                        android:textSize="@dimen/text_size_card_title" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>
        </GridLayout>

        <!-- Ad Banner Container -->
        <FrameLayout
            android:id="@+id/ad_banner_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:minHeight="50dp"
            android:layout_marginTop="@dimen/margin_medium"
            android:layout_marginStart="@dimen/margin_medium"
            android:layout_marginEnd="@dimen/margin_medium"
            android:layout_marginBottom="@dimen/margin_medium"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <!-- Version text -->
        <TextView
            android:id="@+id/version_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/cyberpunk_font"
            android:text="BYDEV: mahmoud ff yt"
            android:textColor="@color/cyber_text_secondary"
            android:textSize="@dimen/text_size_small"
            app:layout_constraintBottom_toTopOf="@+id/ad_banner_container"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/features_grid"
            app:layout_constraintVertical_bias="0.885" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Navigation View for the drawer with fixed layout -->

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:layout_gravity="start"
            android:background="@color/drawer_background">

            <!-- Custom Navigation Layout -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <!-- Header Layout -->
                <include
                    layout="@layout/nav_header"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

                <!-- Menu Items -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:padding="@dimen/padding_small">

                    <!-- Settings -->
                    <LinearLayout
                        android:id="@+id/nav_settings_item"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_weight="1"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingVertical="8dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:background="?attr/selectableItemBackground">

                        <ImageView
                            android:layout_width="@dimen/icon_size_small"
                            android:layout_height="@dimen/icon_size_small"
                            android:src="@drawable/ic_settings"
                            android:tint="@color/cyber_neon_cyan"
                            android:layout_marginStart="@dimen/margin_medium"
                            android:contentDescription="@string/settings" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/settings"
                            android:textColor="@color/cyber_text_primary"
                            android:fontFamily="@font/font_for_ar_en"
                            android:layout_marginStart="@dimen/margin_large"
                            android:textSize="@dimen/text_size_card_content" />
                    </LinearLayout>

                    <!-- Rate Us -->
                    <LinearLayout
                        android:id="@+id/nav_rate_item"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_weight="1"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingVertical="8dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:background="?attr/selectableItemBackground">

                        <ImageView
                            android:layout_width="@dimen/icon_size_small"
                            android:layout_height="@dimen/icon_size_small"
                            android:src="@drawable/ic_rate"
                            android:tint="@color/cyber_neon_cyan"
                            android:layout_marginStart="@dimen/margin_medium"
                            android:contentDescription="@string/rate_us" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/rate_us"
                            android:textColor="@color/cyber_text_primary"
                            android:fontFamily="@font/font_for_ar_en"
                            android:layout_marginStart="@dimen/margin_large"
                            android:textSize="@dimen/text_size_card_content" />
                    </LinearLayout>

                    <!-- Privacy Policy -->
                    <LinearLayout
                        android:id="@+id/nav_privacy_item"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_weight="1"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingVertical="8dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:background="?attr/selectableItemBackground">

                        <ImageView
                            android:layout_width="@dimen/icon_size_small"
                            android:layout_height="@dimen/icon_size_small"
                            android:src="@drawable/ic_privacy"
                            android:tint="@color/cyber_neon_cyan"
                            android:layout_marginStart="@dimen/margin_medium"
                            android:contentDescription="@string/privacy_policy" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/privacy_policy"
                            android:textColor="@color/cyber_text_primary"
                            android:fontFamily="@font/font_for_ar_en"
                            android:layout_marginStart="@dimen/margin_large"
                            android:textSize="@dimen/text_size_card_content" />
                    </LinearLayout>

                    <!-- App Guide -->
                    <LinearLayout
                        android:id="@+id/nav_app_guide_item"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_weight="1"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingVertical="8dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:background="?attr/selectableItemBackground">

                        <ImageView
                            android:layout_width="@dimen/icon_size_small"
                            android:layout_height="@dimen/icon_size_small"
                            android:src="@drawable/ic_help"
                            android:tint="@color/cyber_neon_cyan"
                            android:layout_marginStart="@dimen/margin_medium"
                            android:contentDescription="@string/app_guide" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/app_guide"
                            android:textColor="@color/cyber_text_primary"
                            android:fontFamily="@font/font_for_ar_en"
                            android:layout_marginStart="@dimen/margin_large"
                            android:textSize="@dimen/text_size_card_content" />
                    </LinearLayout>

                    <!-- About -->
                    <LinearLayout
                        android:id="@+id/nav_about_item"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_weight="1"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingVertical="8dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:background="?attr/selectableItemBackground">

                        <ImageView
                            android:layout_width="@dimen/icon_size_small"
                            android:layout_height="@dimen/icon_size_small"
                            android:src="@drawable/ic_about"
                            android:tint="@color/cyber_neon_cyan"
                            android:layout_marginStart="@dimen/margin_medium"
                            android:contentDescription="@string/about" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/about"
                            android:textColor="@color/cyber_text_primary"
                            android:fontFamily="@font/font_for_ar_en"
                            android:layout_marginStart="@dimen/margin_large"
                            android:textSize="@dimen/text_size_card_content" />
                    </LinearLayout>

                    <!-- Exit -->
                    <LinearLayout
                        android:id="@+id/nav_exit_item"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_weight="1"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingVertical="8dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:background="?attr/selectableItemBackground">

                        <ImageView
                            android:layout_width="@dimen/icon_size_small"
                            android:layout_height="@dimen/icon_size_small"
                            android:src="@drawable/ic_exit"
                            android:tint="@color/cyber_neon_cyan"
                            android:layout_marginStart="@dimen/margin_medium"
                            android:contentDescription="@string/exit" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/exit"
                            android:textColor="@color/cyber_text_primary"
                            android:fontFamily="@font/font_for_ar_en"
                            android:layout_marginStart="@dimen/margin_large"
                            android:textSize="@dimen/text_size_card_content" />
                    </LinearLayout>

                <!-- Premium Features Footer -->

                </LinearLayout>

                <!-- Footer -->
                <include
                    android:id="@+id/nav_premium_footer"
                    layout="@layout/nav_footer_premium_new"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

                <!-- Hidden NavigationView for compatibility -->
                <com.google.android.material.navigation.NavigationView
                    android:id="@+id/nav_view"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:visibility="gone"
                    app:menu="@menu/drawer_menu" />
            </LinearLayout>
        </LinearLayout>
</androidx.drawerlayout.widget.DrawerLayout>