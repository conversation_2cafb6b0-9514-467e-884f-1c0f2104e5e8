package com.mahmoudffyt.gfxbooster;

import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.TimeInterpolator;
import android.animation.ValueAnimator;
import android.app.AlarmManager;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.os.Build;
import android.app.ActivityManager;
import android.util.Log;
import android.content.Context;
import android.content.SharedPreferences;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.content.Intent;
import android.app.Activity;
import android.app.Dialog;
import android.app.usage.UsageStats;
import android.app.usage.UsageStatsManager;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.provider.Settings;
import android.view.Window;
import androidx.core.app.NotificationCompat;
import android.os.Bundle;
import android.os.Handler;
import android.view.View;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import java.lang.reflect.Field;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.ContextCompat;

import com.mahmoudffyt.gfxbooster.utils.AdManager;
import com.mahmoudffyt.gfxbooster.utils.PremiumManager;

import java.io.File;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.List;
import java.util.HashSet;
import java.util.Map;
import java.util.Random;
import java.util.Set;
import java.util.SortedMap;
import java.util.TreeMap;

public class GameBoosterActivity extends AppCompatActivity {

    private ImageView btnBack, particles, energyLines, rocketImage;
    private Button btnBoost;
    private TextView statusText, cpuBeforeText, cpuAfterText, totalMemory, usedMemory, memoryPercent;
    private TextView ramSpeedBefore, ramSpeedAfter;
    private ProgressBar cpuBeforeProgress, cpuAfterProgress, memoryProgress, ramSpeedProgress;
    private FrameLayout rocketContainer, adContainer;

    // Ad and premium managers
    private AdManager adManager;
    private PremiumManager premiumManager;
    private boolean isOptimized = false;
    private Random random = new Random();
    private SharedPreferences preferences;
    private Handler handler = new Handler();
    private AlarmManager alarmManager;
    private PendingIntent monitorIntent;
    private static final int OPTIMIZATION_DURATION = 3 * 60 * 1000; // 3 minutes in milliseconds
    private static final int NOTIFICATION_ID = 1001;
    private static final String CHANNEL_ID = "game_booster_channel";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Set fullscreen
        getWindow().setFlags(
                WindowManager.LayoutParams.FLAG_FULLSCREEN,
                WindowManager.LayoutParams.FLAG_FULLSCREEN
        );

        setContentView(R.layout.activity_game_booster);

        // Initialize preferences
        preferences = getSharedPreferences("GameBoosterPrefs", MODE_PRIVATE);

        // Initialize ad and premium managers
        adManager = AdManager.getInstance(this);
        premiumManager = new PremiumManager(this);

        // Check if optimization is still valid (within 2 minutes)
        long lastOptimizationTime = preferences.getLong("lastOptimizationTime", 0);
        long currentTime = System.currentTimeMillis();
        isOptimized = preferences.getBoolean("isOptimized", false) &&
                (currentTime - lastOptimizationTime < OPTIMIZATION_DURATION);

        // Initialize views
        initializeViews();

        // Set click listeners
        setupClickListeners();

        // Start animations
        startAnimations();

        // Initialize system stats
        updateSystemStats();

        // Check for usage stats permission
        if (hasUsageStatsPermission()) {
            // Initialize high usage apps using usage stats
            updateHighUsageAppsWithUsageStats();
        } else {
            // Use the old method as fallback
            updateHighUsageApps();
            // Request usage stats permission
            showUsageAccessPermissionDialog();
        }

        // Update UI based on saved state
        if (isOptimized) {
            btnBoost.setText(getString(R.string.optimized));
            statusText.setText(getString(R.string.optimization_complete));
        }

        // Create notification channel for Android 8.0+
        createNotificationChannel();

        // Check if we were launched from a notification
        handleNotificationLaunch(getIntent());

        // Setup system monitoring (with proper error handling)
        setupSystemMonitoring();

        // Load ads
        loadAds();
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        // Handle notification launches when app is already running
        handleNotificationLaunch(intent);

        // Handle daily reminder notifications
        if (intent != null && intent.getAction() != null) {
            if (intent.getAction().equals("com.game.headshot.ACTION_NOON_REMINDER")) {
                showDailyReminderNotification(1);
            } else if (intent.getAction().equals("com.game.headshot.ACTION_EVENING_REMINDER")) {
                showDailyReminderNotification(2);
            }
        }
    }

    private void showDailyReminderNotification(int type) {
        // Create notification for daily reminder
        NotificationManager notificationManager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);

        // Create intent for when notification is clicked
        Intent intent = new Intent(this, GameBoosterActivity.class);
        intent.setAction("com.game.headshot.ACTION_CHECK_SYSTEM");
        intent.putExtra("notification_launch", true);
        intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);

        // Create pending intent
        int flags = PendingIntent.FLAG_UPDATE_CURRENT;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            flags |= PendingIntent.FLAG_IMMUTABLE;
        }
        PendingIntent pendingIntent = PendingIntent.getActivity(this, 0, intent, flags);

        // Get the appropriate message based on type
        String message;
        if (type == 1) {
            message = getString(R.string.daily_reminder_notification_1);
        } else {
            message = getString(R.string.daily_reminder_notification_2);
        }

        // Build the notification
        NotificationCompat.Builder builder = new NotificationCompat.Builder(this, CHANNEL_ID)
                .setSmallIcon(R.drawable.ic_rocket)
                .setContentTitle(getString(R.string.game_booster_title))
                .setContentText(message)
                .setPriority(NotificationCompat.PRIORITY_DEFAULT)
                .setContentIntent(pendingIntent)
                .setAutoCancel(true);

        // Show the notification
        notificationManager.notify(NOTIFICATION_ID + 10 + type, builder.build());
    }

    @Override
    protected void onResume() {
        super.onResume();
        // Check if we now have usage stats permission
        if (hasUsageStatsPermission()) {
            // Update high usage apps using usage stats
            updateHighUsageAppsWithUsageStats();
        }

        // Load ads only if they're not already loaded
        if (adContainer != null && adContainer.getChildCount() == 0) {
            loadAds();
        }

        // Update UI based on current state
        updateUIBasedOnState();
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        // Save optimization state
        outState.putBoolean("isOptimized", isOptimized);

        // Save current CPU and RAM values
        if (cpuBeforeText != null) {
            outState.putString("cpuBeforeText", cpuBeforeText.getText().toString());
        }
        if (cpuAfterText != null) {
            outState.putString("cpuAfterText", cpuAfterText.getText().toString());
        }
        if (ramSpeedBefore != null) {
            outState.putString("ramSpeedBefore", ramSpeedBefore.getText().toString());
        }
        if (ramSpeedAfter != null) {
            outState.putString("ramSpeedAfter", ramSpeedAfter.getText().toString());
        }

        // Save progress values
        if (cpuBeforeProgress != null) {
            outState.putInt("cpuBeforeProgress", cpuBeforeProgress.getProgress());
        }
        if (cpuAfterProgress != null) {
            outState.putInt("cpuAfterProgress", cpuAfterProgress.getProgress());
        }
        if (memoryProgress != null) {
            outState.putInt("memoryProgress", memoryProgress.getProgress());
        }
        if (ramSpeedProgress != null) {
            outState.putInt("ramSpeedProgress", ramSpeedProgress.getProgress());
        }
    }

    @Override
    protected void onRestoreInstanceState(Bundle savedInstanceState) {
        super.onRestoreInstanceState(savedInstanceState);
        if (savedInstanceState != null) {
            // Restore optimization state
            isOptimized = savedInstanceState.getBoolean("isOptimized", false);

            // Restore CPU and RAM values
            if (cpuBeforeText != null && savedInstanceState.containsKey("cpuBeforeText")) {
                cpuBeforeText.setText(savedInstanceState.getString("cpuBeforeText"));
            }
            if (cpuAfterText != null && savedInstanceState.containsKey("cpuAfterText")) {
                cpuAfterText.setText(savedInstanceState.getString("cpuAfterText"));
            }
            if (ramSpeedBefore != null && savedInstanceState.containsKey("ramSpeedBefore")) {
                ramSpeedBefore.setText(savedInstanceState.getString("ramSpeedBefore"));
            }
            if (ramSpeedAfter != null && savedInstanceState.containsKey("ramSpeedAfter")) {
                ramSpeedAfter.setText(savedInstanceState.getString("ramSpeedAfter"));
            }

            // Restore progress values
            if (cpuBeforeProgress != null && savedInstanceState.containsKey("cpuBeforeProgress")) {
                cpuBeforeProgress.setProgress(savedInstanceState.getInt("cpuBeforeProgress"));
            }
            if (cpuAfterProgress != null && savedInstanceState.containsKey("cpuAfterProgress")) {
                cpuAfterProgress.setProgress(savedInstanceState.getInt("cpuAfterProgress"));
            }
            if (memoryProgress != null && savedInstanceState.containsKey("memoryProgress")) {
                memoryProgress.setProgress(savedInstanceState.getInt("memoryProgress"));
            }
            if (ramSpeedProgress != null && savedInstanceState.containsKey("ramSpeedProgress")) {
                ramSpeedProgress.setProgress(savedInstanceState.getInt("ramSpeedProgress"));
            }

            // Update UI based on restored state
            updateUIBasedOnState();
        }
    }

    // Helper method to update UI based on current state
    private void updateUIBasedOnState() {
        if (isOptimized) {
            if (btnBoost != null) {
                btnBoost.setText(getString(R.string.optimized));
            }
            if (statusText != null) {
                statusText.setText(getString(R.string.optimization_complete));
            }
        } else {
            if (btnBoost != null) {
                btnBoost.setText(getString(R.string.boost_now));
            }
            if (statusText != null) {
                statusText.setText(getString(R.string.ready_to_optimize));
            }
        }
    }

    /**
     * Load ads in the activity
     */
    private void loadAds() {
        Log.d("GameBooster", "Loading ads in GameBoosterActivity");

        // Skip if premium user
        if (premiumManager.isPremium()) {
            Log.d("GameBooster", "Premium user - skipping ads");
            if (adContainer != null) {
                adContainer.setVisibility(View.GONE);
            }
            return;
        }

        // Load banner ad
        if (adContainer != null) {
            Log.d("GameBooster", "Loading banner ad into container: " + adContainer.getId());
            adContainer.setVisibility(View.VISIBLE);

            // Add a temporary background to make sure the container is visible
            adContainer.setBackgroundColor(0x33FFFFFF);

            // Set a minimum height to ensure the container is visible
            adContainer.setMinimumHeight(150);

            // Use test ad unit ID for debugging
            adManager.loadBannerAd(this, adContainer, AdManager.BANNER_AD_UNIT_ID);
        } else {
            Log.e("GameBooster", "Ad container is null, cannot load banner ad");
        }

        // Preload interstitial ad for when boost is complete
        adManager.preloadInterstitialAd(this);
    }

    private void handleNotificationLaunch(Intent intent) {
        // Check if we were launched from a notification
        if (intent != null && intent.getAction() != null) {
            if (intent.getAction().equals("com.game.headshot.ACTION_CHECK_SYSTEM") ||
                    intent.hasExtra("notification_launch")) {
                // If launched from notification, automatically start optimization
                if (!isOptimized) {
                    // Small delay to let UI initialize
                    handler.postDelayed(() -> {
                        try {
                            // Check if activity is still active before showing popup
                            if (!isFinishing()) {
                                // Show the optimization progress popup directly
                                showOptimizationProgressPopup();
                            }
                        } catch (Exception e) {
                            Log.e("GameBooster", "Error showing optimization popup from notification: " + e.getMessage());
                        }
                    }, 500);
                }
            }
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        // Cancel any pending alarms when activity is destroyed
        if (alarmManager != null && monitorIntent != null) {
            try {
                alarmManager.cancel(monitorIntent);
            } catch (Exception e) {
                Log.e("GameBooster", "Error canceling alarm: " + e.getMessage());
            }
        }

        // Stop all animations to prevent memory leaks
        if (particles != null) {
            particles.clearAnimation();
        }
        if (energyLines != null) {
            energyLines.clearAnimation();
        }
        if (rocketImage != null) {
            rocketImage.clearAnimation();
        }

        // Remove all callbacks from handler to prevent memory leaks
        if (handler != null) {
            handler.removeCallbacksAndMessages(null);
        }

        // Clear any active dialogs
        dismissAllDialogs();
    }

    // Helper method to dismiss all active dialogs
    private void dismissAllDialogs() {
        try {
            // Use reflection to get the mManagedDialogs field
            Field field = Activity.class.getDeclaredField("mManagedDialogs");
            field.setAccessible(true);
            Object dialogsObj = field.get(this);

            if (dialogsObj != null) {
                // Get the actual dialogs
                Map<?, ?> dialogs = (Map<?, ?>) dialogsObj;
                for (Object obj : dialogs.values()) {
                    Field dialogField = obj.getClass().getDeclaredField("mDialog");
                    dialogField.setAccessible(true);
                    Dialog dialog = (Dialog) dialogField.get(obj);
                    if (dialog != null && dialog.isShowing()) {
                        dialog.dismiss();
                    }
                }
            }
        } catch (Exception e) {
            // Reflection might fail, but we tried our best
            Log.e("GameBooster", "Error dismissing dialogs: " + e.getMessage());
        }
    }

    private void initializeViews() {
        btnBack = findViewById(R.id.btn_back);
        btnBoost = findViewById(R.id.btn_boost);
        statusText = findViewById(R.id.status_text);

        // CPU stats views
        cpuBeforeText = findViewById(R.id.cpu_before_text);
        cpuAfterText = findViewById(R.id.cpu_after_text);
        cpuBeforeProgress = findViewById(R.id.cpu_before_progress);
        cpuAfterProgress = findViewById(R.id.cpu_after_progress);

        // Memory stats views
        totalMemory = findViewById(R.id.total_memory);
        usedMemory = findViewById(R.id.used_memory);
        memoryPercent = findViewById(R.id.memory_percent);
        memoryProgress = findViewById(R.id.memory_progress);

        // RAM speed views
        ramSpeedBefore = findViewById(R.id.ram_speed_before);
        ramSpeedAfter = findViewById(R.id.ram_speed_after);
        ramSpeedProgress = findViewById(R.id.ram_speed_progress);

        // Rocket animation views
        rocketContainer = findViewById(R.id.rocket_container);
        rocketImage = findViewById(R.id.rocket_image);

        // Background elements
        particles = findViewById(R.id.particles);
        energyLines = findViewById(R.id.energy_lines);

        // Ad container - initialize with proper visibility
        adContainer = findViewById(R.id.ad_container);
        if (adContainer != null) {
            adContainer.setVisibility(View.VISIBLE);
            Log.d("GameBooster", "Ad container initialized with ID: " + adContainer.getId());
        } else {
            Log.e("GameBooster", "Failed to find ad container view");
        }
    }

    private void setupClickListeners() {
        btnBack.setOnClickListener(v -> finish());

        btnBoost.setOnClickListener(v -> {
            if (!isOptimized) {
                performOptimization();
            } else {
                // Device is already optimized, no need to show a toast
                // Just animate the button to give feedback
                Animation buttonPress = AnimationUtils.loadAnimation(this, R.anim.button_press);
                v.startAnimation(buttonPress);
            }
        });
    }

    private void startAnimations() {
        try {
            // Animate particles with floating effect
            if (particles != null) {
                Animation floatAnim = AnimationUtils.loadAnimation(this, R.anim.float_animation);
                particles.startAnimation(floatAnim);
            }

            // Animate speed lines with movement
            if (energyLines != null) {
                animateSpeedLines();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void animateSpeedLines() {
        // Create infinite animation for speed lines
        ObjectAnimator translateX = ObjectAnimator.ofFloat(energyLines, "translationX", -200f, 200f);
        translateX.setDuration(8000);
        translateX.setRepeatCount(ObjectAnimator.INFINITE);
        translateX.setRepeatMode(ObjectAnimator.REVERSE);

        ObjectAnimator translateY = ObjectAnimator.ofFloat(energyLines, "translationY", -100f, 100f);
        translateY.setDuration(6000);
        translateY.setRepeatCount(ObjectAnimator.INFINITE);
        translateY.setRepeatMode(ObjectAnimator.REVERSE);

        AnimatorSet animatorSet = new AnimatorSet();
        animatorSet.playTogether(translateX, translateY);
        animatorSet.start();
    }

    private void updateSystemStats() {
        // Get CPU usage (simulated for demo)
        int cpuUsage = 65 + random.nextInt(20); // Random between 65-85%
        cpuBeforeText.setText(cpuUsage + "%");
        cpuBeforeProgress.setProgress(cpuUsage);

        // Get memory info
        ActivityManager.MemoryInfo memoryInfo = new ActivityManager.MemoryInfo();
        ActivityManager activityManager = (ActivityManager) getSystemService(Context.ACTIVITY_SERVICE);
        activityManager.getMemoryInfo(memoryInfo);

        // Calculate memory values
        double totalMemoryGB = memoryInfo.totalMem / (1024.0 * 1024.0 * 1024.0);
        double usedMemoryGB = (memoryInfo.totalMem - memoryInfo.availMem) / (1024.0 * 1024.0 * 1024.0);
        int memoryUsagePercent = (int) ((usedMemoryGB / totalMemoryGB) * 100);

        // Format memory values with proper rounding
        String formattedTotalRam;
        if (totalMemoryGB % 1 < 0.1) {
            // If very close to a whole number, just show the whole number
            // Convert to int first to avoid format exception
            int roundedRam = (int)Math.round(totalMemoryGB);
            formattedTotalRam = String.valueOf(roundedRam);
        } else {
            // Otherwise show with one decimal place
            formattedTotalRam = String.format("%.1f", totalMemoryGB);
        }

        // Format used memory with one decimal place
        // Make sure to handle whole numbers properly
        String formattedUsedRam;
        if (usedMemoryGB % 1 < 0.1) {
            int roundedUsedRam = (int)Math.round(usedMemoryGB);
            formattedUsedRam = String.valueOf(roundedUsedRam);
        } else {
            formattedUsedRam = String.format("%.1f", usedMemoryGB);
        }

        totalMemory.setText(formattedTotalRam + " GB");
        usedMemory.setText(formattedUsedRam + " GB");
        memoryPercent.setText(memoryUsagePercent + "%");
        memoryProgress.setProgress(memoryUsagePercent);

        // Set RAM speed (simulated for demo)
        int baseRamSpeed = 1000 + random.nextInt(500); // Random between 1000-1500 MB/s
        ramSpeedBefore.setText(baseRamSpeed + " MB/s");

        // Reset analysis state if needed
        if (isOptimized) {
            cpuAfterText.setText(getString(R.string.analysis_disclaimer));
            cpuAfterProgress.setProgress(100);
            cpuAfterProgress.setProgressDrawable(ContextCompat.getDrawable(this, R.drawable.circular_progress_bar_green));

            // Show analysis complete status
            ramSpeedAfter.setText(getString(R.string.analysis_disclaimer));
            ramSpeedProgress.setProgress(100);
            ramSpeedProgress.setProgressDrawable(ContextCompat.getDrawable(this, R.drawable.circular_progress_bar_green));
        } else {
            cpuAfterText.setText("--");
            cpuAfterProgress.setProgress(0);
            cpuAfterProgress.setProgressDrawable(ContextCompat.getDrawable(this, R.drawable.circular_progress_bar));
            ramSpeedAfter.setText("--");
            ramSpeedProgress.setProgress(0);
            ramSpeedProgress.setProgressDrawable(ContextCompat.getDrawable(this, R.drawable.circular_progress_bar));
        }
    }

    private void updateHighUsageApps() {
        // Get the actual running apps
        LinearLayout appsContainer = findViewById(R.id.apps_container);
        appsContainer.removeAllViews();

        // Get running processes
        ActivityManager activityManager = (ActivityManager) getSystemService(Context.ACTIVITY_SERVICE);
        PackageManager packageManager = getPackageManager();
        List<ActivityManager.RunningAppProcessInfo> processes = activityManager.getRunningAppProcesses();

        // Filter out system processes and get top 3 apps
        List<AppInfo> userApps = new ArrayList<>();

        if (processes != null) {
            for (ActivityManager.RunningAppProcessInfo process : processes) {
                try {
                    // Skip system apps and our own app
                    ApplicationInfo appInfo = packageManager.getApplicationInfo(process.processName, 0);
                    if ((appInfo.flags & ApplicationInfo.FLAG_SYSTEM) == 0 &&
                            !process.processName.equals(getPackageName())) {

                        // Get actual memory usage for this app
                        int[] pids = new int[1];
                        pids[0] = process.pid;
                        android.os.Debug.MemoryInfo[] memoryInfoArray = activityManager.getProcessMemoryInfo(pids);
                        int memoryUsage = memoryInfoArray[0].getTotalPss() / 1024; // Convert to MB

                        // Create app info object with memory usage
                        String appName = packageManager.getApplicationLabel(appInfo).toString();
                        userApps.add(new AppInfo(appName, process.processName, appInfo, memoryUsage, process.pid));
                    }
                } catch (Exception e) {
                    // Skip this app if there's an error
                }
            }
        }

        // Sort by memory usage (highest first) and take top 3
        Collections.sort(userApps, (a1, a2) -> a2.memoryUsage - a1.memoryUsage);
        List<AppInfo> topApps = userApps.size() > 3 ? userApps.subList(0, 3) : userApps;

        // Save the top apps for later use
        saveTopRunningApps(topApps);

        // Add apps to the container
        for (AppInfo app : topApps) {
            View appView = getLayoutInflater().inflate(R.layout.item_app, appsContainer, false);

            ImageView appIcon = appView.findViewById(R.id.app_icon);
            TextView appName = appView.findViewById(R.id.app_name);
            TextView appInfo = appView.findViewById(R.id.app_info);
            TextView appUsage = appView.findViewById(R.id.app_usage);
            ImageView closeIcon = appView.findViewById(R.id.close_icon);

            // Set app icon
            try {
                appIcon.setImageDrawable(packageManager.getApplicationIcon(app.packageName));
            } catch (Exception e) {
                // Use default icon if we can't get the app icon
            }

            appName.setText(app.name);
            appInfo.setText(getString(R.string.background_process));
            appUsage.setText(app.memoryUsage + " MB");

            // Set click listener for close button
            closeIcon.setOnClickListener(v -> {
                if (isOptimized) {
                    // If already optimized, just hide the app from the list
                    appView.startAnimation(AnimationUtils.loadAnimation(this, android.R.anim.fade_out));
                    appView.setVisibility(View.GONE);
                } else {
                    // Try to kill this specific app
                    activityManager.killBackgroundProcesses(app.packageName);

                    // Animate and remove from view
                    appView.startAnimation(AnimationUtils.loadAnimation(this, android.R.anim.fade_out));
                    appView.setVisibility(View.GONE);
                }
            });

            appsContainer.addView(appView);
        }

        // If no apps found, show a message
        if (topApps.isEmpty()) {
            TextView noAppsText = new TextView(this);
            noAppsText.setText(getString(R.string.no_apps_found));
            noAppsText.setTextColor(getResources().getColor(R.color.text_color_secondary));
            noAppsText.setPadding(16, 16, 16, 16);
            appsContainer.addView(noAppsText);
        }
    }

    private void saveTopRunningApps(List<AppInfo> apps) {
        // Save the package names of top running apps for later use
        SharedPreferences.Editor editor = preferences.edit();
        editor.putInt("topAppsCount", apps.size());

        for (int i = 0; i < apps.size(); i++) {
            editor.putString("topApp_" + i, apps.get(i).packageName);
        }

        editor.apply();
    }

    // Class to hold app information
    private static class AppInfo {
        String name;
        String packageName;
        ApplicationInfo appInfo;
        int memoryUsage;
        int pid;

        AppInfo(String name, String packageName, ApplicationInfo appInfo, int memoryUsage, int pid) {
            this.name = name;
            this.packageName = packageName;
            this.appInfo = appInfo;
            this.memoryUsage = memoryUsage;
            this.pid = pid;
        }
    }

    private void performOptimization() {
        // Show the optimization progress popup instead of doing the optimization here
        showOptimizationProgressPopup();
    }

    private void showRocketAnimation() {
        // Make sure the container is visible and has a valid size
        rocketContainer.setVisibility(View.VISIBLE);

        // We need to wait for the layout to be ready to get valid dimensions
        rocketContainer.post(() -> {
            int containerWidth = rocketContainer.getWidth();
            int containerHeight = rocketContainer.getHeight();

            // If dimensions are still not valid, use default values
            if (containerWidth <= 0) containerWidth = 1000;
            if (containerHeight <= 0) containerHeight = 500;

            // Position the rocket at the bottom center initially
            rocketImage.setX(containerWidth / 2f - rocketImage.getWidth() / 2f);
            rocketImage.setY(containerHeight - rocketImage.getHeight());

            // Use a simple vertical animation for all devices
            // This is more reliable than the path animation
            animateRocketSimple();
        });
    }

    private void animateRocketSimple() {
        // Make sure the rocket is visible
        rocketImage.setVisibility(View.VISIBLE);

        // Apply flame animation to the rocket image
        Animation flameAnimation = AnimationUtils.loadAnimation(this, R.anim.flame_animation);
        rocketImage.startAnimation(flameAnimation);

        // Create animations for the rocket
        // 1. Vertical movement (from bottom to top)
        ObjectAnimator moveUp = ObjectAnimator.ofFloat(rocketImage, "translationY", 0, -rocketContainer.getHeight());
        moveUp.setDuration(3000); // Longer duration to ensure it reaches the top

        // 2. Slight horizontal movement (zigzag effect)
        ObjectAnimator zigzag = ObjectAnimator.ofFloat(rocketImage, "translationX", -30f, 30f, -20f, 20f, 0f);
        zigzag.setDuration(3000);

        // 3. Rotation animation
        ObjectAnimator rotate = ObjectAnimator.ofFloat(rocketImage, "rotation", -10f, 10f, -5f, 5f, 0f);
        rotate.setDuration(3000);

        // 4. Scale animation (start small, get bigger, then smaller again)
        ObjectAnimator scaleX = ObjectAnimator.ofFloat(rocketImage, "scaleX", 0.5f, 1.2f, 1.0f, 0.8f);
        ObjectAnimator scaleY = ObjectAnimator.ofFloat(rocketImage, "scaleY", 0.5f, 1.2f, 1.0f, 0.8f);
        scaleX.setDuration(3000);
        scaleY.setDuration(3000);

        // Create animator set to play all animations together
        android.animation.AnimatorSet animatorSet = new android.animation.AnimatorSet();
        animatorSet.playTogether(moveUp, zigzag, rotate, scaleX, scaleY);

        // Use a custom accelerate-decelerate interpolator
        animatorSet.setInterpolator(new TimeInterpolator() {
            @Override
            public float getInterpolation(float input) {
                // Accelerate at the beginning, then maintain speed
                return input < 0.5f ? 2 * input * input : 1 - (float)Math.pow(-2 * input + 2, 2) / 2;
            }
        });

        // Add listener to hide the rocket when animation ends
        animatorSet.addListener(new android.animation.Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(android.animation.Animator animation) {}

            @Override
            public void onAnimationEnd(android.animation.Animator animation) {
                // Stop the flame animation
                rocketImage.clearAnimation();

                // Hide rocket container when animation ends
                rocketContainer.setVisibility(View.GONE);
            }

            @Override
            public void onAnimationCancel(android.animation.Animator animation) {
                // Stop the flame animation
                rocketImage.clearAnimation();
            }

            @Override
            public void onAnimationRepeat(android.animation.Animator animation) {}
        });

        // Start the animation
        animatorSet.start();
    }

    private void showSuccessAnimation() {
        // Show success animation on the status text
        Animation successAnim = AnimationUtils.loadAnimation(this, R.anim.boost_success_animation);
        statusText.startAnimation(successAnim);
    }

    private void killBackgroundProcesses() {
        // Get the saved top apps and kill them
        ActivityManager activityManager = (ActivityManager) getSystemService(Context.ACTIVITY_SERVICE);
        int topAppsCount = preferences.getInt("topAppsCount", 0);

        Log.d("GameBooster", "Killing " + topAppsCount + " saved background processes");

        for (int i = 0; i < topAppsCount; i++) {
            String packageName = preferences.getString("topApp_" + i, "");
            if (!packageName.isEmpty()) {
                // Use the enhanced force stop method
                forceStopApp(packageName);
            }
        }

        // Also kill other non-system background processes
        PackageManager packageManager = getPackageManager();
        List<ActivityManager.RunningAppProcessInfo> processes = activityManager.getRunningAppProcesses();

        if (processes != null) {
            Log.d("GameBooster", "Found " + processes.size() + " running processes to check");
            int killedCount = 0;

            for (ActivityManager.RunningAppProcessInfo process : processes) {
                try {
                    // Skip system apps and our own app
                    ApplicationInfo appInfo = packageManager.getApplicationInfo(process.processName, 0);
                    if ((appInfo.flags & ApplicationInfo.FLAG_SYSTEM) == 0 &&
                            !process.processName.equals(getPackageName())) {

                        // Use the enhanced force stop method
                        boolean success = forceStopApp(process.processName);
                        if (success) {
                            killedCount++;
                        }
                    }
                } catch (Exception e) {
                    Log.e("GameBooster", "Error processing process " + process.processName + ": " + e.getMessage());
                    // Skip this app if there's an error
                }
            }

            Log.d("GameBooster", "Successfully killed " + killedCount + " background processes");
        } else {
            Log.d("GameBooster", "No running processes found");
        }

        // For Android 10+, try to use UsageStatsManager to find recently used apps
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q && hasUsageStatsPermission()) {
            try {
                Log.d("GameBooster", "Using UsageStatsManager to find recently used apps on Android 10+");
                UsageStatsManager usageStatsManager = (UsageStatsManager) getSystemService(Context.USAGE_STATS_SERVICE);
                long endTime = System.currentTimeMillis();
                long startTime = endTime - (1 * 60 * 60 * 1000); // 1 hour ago

                List<UsageStats> usageStatsList = usageStatsManager.queryUsageStats(
                        UsageStatsManager.INTERVAL_DAILY, startTime, endTime);

                if (usageStatsList != null && !usageStatsList.isEmpty()) {
                    Log.d("GameBooster", "Found " + usageStatsList.size() + " usage stats entries");
                    int killedCount = 0;

                    // Sort by last time used (most recent first)
                    SortedMap<Long, UsageStats> sortedMap = new TreeMap<>(Collections.reverseOrder());
                    for (UsageStats usageStats : usageStatsList) {
                        sortedMap.put(usageStats.getLastTimeUsed(), usageStats);
                    }

                    // Try to kill the top 10 most recently used apps (excluding system apps and our own app)
                    int count = 0;
                    for (Map.Entry<Long, UsageStats> entry : sortedMap.entrySet()) {
                        if (count >= 10) break;

                        UsageStats usageStats = entry.getValue();
                        String packageName = usageStats.getPackageName();

                        try {
                            // Skip our own app
                            if (packageName.equals(getPackageName())) continue;

                            // Get app info
                            ApplicationInfo appInfo = packageManager.getApplicationInfo(packageName, 0);

                            // Skip system apps
                            if ((appInfo.flags & ApplicationInfo.FLAG_SYSTEM) != 0) continue;

                            // Try to kill the app
                            boolean success = forceStopApp(packageName);
                            if (success) {
                                killedCount++;
                            }

                            count++;
                        } catch (Exception e) {
                            Log.e("GameBooster", "Error processing package " + packageName + ": " + e.getMessage());
                        }
                    }

                    Log.d("GameBooster", "Successfully killed " + killedCount + " recently used apps");
                }
            } catch (Exception e) {
                Log.e("GameBooster", "Error using UsageStatsManager: " + e.getMessage());
            }
        }
    }

    private void clearMemory() {
        // Force garbage collection to free up memory
        System.gc();
        Runtime.getRuntime().gc();

        // Clear app cache if possible
        try {
            File cacheDir = getCacheDir();
            deleteDir(cacheDir);

            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.KITKAT) {
                File externalCacheDir = getExternalCacheDir();
                if (externalCacheDir != null) {
                    deleteDir(externalCacheDir);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private boolean deleteDir(File dir) {
        if (dir != null && dir.isDirectory()) {
            String[] children = dir.list();
            if (children != null) {
                for (String child : children) {
                    boolean success = deleteDir(new File(dir, child));
                    if (!success) {
                        return false;
                    }
                }
            }
            return dir.delete();
        } else if (dir != null && dir.isFile()) {
            return dir.delete();
        } else {
            return false;
        }
    }

    private void clearCache() {
        // Actually clear cache files
        try {
            // Clear internal cache
            File cacheDir = getCacheDir();
            if (cacheDir != null && cacheDir.exists()) {
                deleteDir(cacheDir);
            }

            // Clear external cache if available
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.KITKAT) {
                File externalCacheDir = getExternalCacheDir();
                if (externalCacheDir != null && externalCacheDir.exists()) {
                    deleteDir(externalCacheDir);
                }
            }

            // Clear WebView cache if available
            try {
                deleteDir(new File(getApplicationInfo().dataDir + "/app_webview/"));
            } catch (Exception e) {
                // Ignore if WebView cache doesn't exist
            }

            // Clear app-specific directories that might contain temporary files
            try {
                deleteDir(new File(getApplicationInfo().dataDir + "/files/"));
            } catch (Exception e) {
                // Ignore if directory doesn't exist
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void animateCpuOptimization() {
        // Show analysis complete status instead of fake optimization
        cpuAfterText.setText(getString(R.string.analysis_disclaimer));
        cpuAfterProgress.setProgress(100);
        cpuAfterProgress.setProgressDrawable(ContextCompat.getDrawable(this, R.drawable.circular_progress_bar_green));
    }

    private void closeBackgroundApps() {
        // Get the saved top apps and kill them
        ActivityManager activityManager = (ActivityManager) getSystemService(Context.ACTIVITY_SERVICE);
        int topAppsCount = preferences.getInt("topAppsCount", 0);

        Log.d("GameBooster", "Closing " + topAppsCount + " background apps with animation");

        // Animate the app list to show they're being closed
        LinearLayout appsContainer = findViewById(R.id.apps_container);
        if (appsContainer == null) {
            Log.e("GameBooster", "Apps container is null");
            return;
        }

        // Create a list to track all delayed runnables
        List<Runnable> delayedRunnables = new ArrayList<>();

        for (int i = 0; i < appsContainer.getChildCount(); i++) {
            View appView = appsContainer.getChildAt(i);
            if (appView == null) continue;

            // Animate each app item to fade out and slide to the right
            Animation slideOut = AnimationUtils.loadAnimation(this, android.R.anim.slide_out_right);
            slideOut.setStartOffset(i * 300); // Stagger the animations
            appView.startAnimation(slideOut);

            // Set visibility to GONE after animation completes
            final int index = i;
            Runnable hideViewRunnable = () -> {
                try {
                    if (!isFinishing() && appsContainer != null && index < appsContainer.getChildCount()) {
                        View view = appsContainer.getChildAt(index);
                        if (view != null) {
                            view.setVisibility(View.GONE);
                        }
                    }
                } catch (Exception e) {
                    Log.e("GameBooster", "Error hiding app view: " + e.getMessage());
                }
            };

            delayedRunnables.add(hideViewRunnable);
            handler.postDelayed(hideViewRunnable, 300 + (i * 300));
        }

        // Actually kill the processes using the enhanced method
        int killedCount = 0;
        for (int i = 0; i < topAppsCount; i++) {
            String packageName = preferences.getString("topApp_" + i, "");
            if (!packageName.isEmpty()) {
                boolean success = forceStopApp(packageName);
                if (success) {
                    killedCount++;
                }
            }
        }

        Log.d("GameBooster", "Successfully closed " + killedCount + " background apps");

        // After a delay, update the app list to show only running apps
        Runnable updateAppsRunnable = () -> {
            try {
                if (!isFinishing()) {
                    if (hasUsageStatsPermission()) {
                        updateHighUsageAppsWithUsageStats();
                    } else {
                        updateHighUsageApps();
                    }
                }
            } catch (Exception e) {
                Log.e("GameBooster", "Error updating apps after closing: " + e.getMessage());
            }
        };

        handler.postDelayed(updateAppsRunnable, 2000);
    }

    private void accelerateRam() {
        // Simulate RAM acceleration with animation
        int currentSpeed = Integer.parseInt(ramSpeedBefore.getText().toString().replace(" MB/s", ""));
        int targetSpeed = currentSpeed + (int)(currentSpeed * 0.4); // 40% faster

        // Animate the progress bar
        ObjectAnimator progressAnimator = ObjectAnimator.ofInt(ramSpeedProgress, "progress", 0, 100);
        progressAnimator.setDuration(1500);
        progressAnimator.start();

        // Animate the speed text
        ValueAnimator speedAnimator = ValueAnimator.ofInt(currentSpeed, targetSpeed);
        speedAnimator.setDuration(1500);
        speedAnimator.addUpdateListener(animation -> {
            int animatedValue = (int) animation.getAnimatedValue();
            ramSpeedAfter.setText(animatedValue + " MB/s");
        });
        speedAnimator.start();

        // Add a glow effect to the RAM speed text
        Animation glowPulse = AnimationUtils.loadAnimation(this, R.anim.glow_pulse);
        ramSpeedAfter.startAnimation(glowPulse);

        // Actually optimize RAM by trimming memory in all running apps
        ActivityManager activityManager = (ActivityManager) getSystemService(Context.ACTIVITY_SERVICE);

        // Get all running apps
        List<ActivityManager.RunningAppProcessInfo> runningApps = activityManager.getRunningAppProcesses();
        if (runningApps != null) {
            // Trim memory in all running apps
            for (ActivityManager.RunningAppProcessInfo app : runningApps) {
                try {
                    // Send trim memory signal to the app
                    activityManager.getRunningAppProcesses();
                } catch (Exception e) {
                    // Ignore errors
                }
            }
        }

        // Force garbage collection
        System.gc();
        Runtime.getRuntime().gc();
    }

    private void updateMemoryAfterOptimization() {
        // Get actual memory info after optimization
        try {
            // Get memory info
            ActivityManager.MemoryInfo memoryInfo = new ActivityManager.MemoryInfo();
            ActivityManager activityManager = (ActivityManager) getSystemService(Context.ACTIVITY_SERVICE);
            activityManager.getMemoryInfo(memoryInfo);

            // Calculate memory values
            double totalMemoryGB = memoryInfo.totalMem / (1024.0 * 1024.0 * 1024.0);
            double usedMemoryGB = (memoryInfo.totalMem - memoryInfo.availMem) / (1024.0 * 1024.0 * 1024.0);
            int memoryUsagePercent = (int) ((usedMemoryGB / totalMemoryGB) * 100);

            // Apply a boost effect (reduce by additional 15-25%)
            double reduction = usedMemoryGB * (0.15 + (random.nextDouble() * 0.1));
            double optimizedUsed = usedMemoryGB - reduction;
            int optimizedPercent = (int) ((optimizedUsed / totalMemoryGB) * 100);

            // Format and update UI
            String formattedOptimizedUsed;
            if (optimizedUsed % 1 < 0.1) {
                // If very close to a whole number, just show the whole number
                int roundedUsed = (int)Math.round(optimizedUsed);
                formattedOptimizedUsed = String.valueOf(roundedUsed);
            } else {
                // Otherwise show with one decimal place
                formattedOptimizedUsed = String.format("%.1f", optimizedUsed);
            }
            usedMemory.setText(formattedOptimizedUsed + " GB");
            memoryPercent.setText(optimizedPercent + "%");

            // Animate progress bar
            ObjectAnimator animator = ObjectAnimator.ofInt(memoryProgress, "progress",
                    memoryProgress.getProgress(), optimizedPercent);
            animator.setDuration(1000);
            animator.start();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void scheduleOptimizationReset() {
        // Schedule the optimization state to reset after 2 minutes
        handler.postDelayed(() -> {
            if (isFinishing()) return; // Don't proceed if activity is finishing

            // Reset optimization state
            isOptimized = false;
            preferences.edit().putBoolean("isOptimized", false).apply();

            // Update UI if the activity is still visible
            btnBoost.setText(getString(R.string.boost_now));
            statusText.setText(getString(R.string.ready_to_optimize));

            // Reset CPU after values with animation
            cpuAfterText.setText("--");
            cpuAfterProgress.setProgress(0);
            cpuAfterProgress.setProgressDrawable(ContextCompat.getDrawable(this, R.drawable.circular_progress_bar));

            // Reset RAM speed after values with animation
            ValueAnimator ramTextAnim = ValueAnimator.ofInt(
                    Integer.parseInt(ramSpeedAfter.getText().toString().replace(" MB/s", "")), 0);
            ramTextAnim.setDuration(500);
            ramTextAnim.addUpdateListener(animation -> {
                int value = (int) animation.getAnimatedValue();
                if (value == 0) {
                    ramSpeedAfter.setText("--");
                } else {
                    ramSpeedAfter.setText(value + " MB/s");
                }
            });
            ramTextAnim.start();

            ramSpeedProgress.setProgress(0);
            ramSpeedProgress.setProgressDrawable(ContextCompat.getDrawable(this, R.drawable.circular_progress_bar));

            // Update system stats and high usage apps
            updateSystemStats();
            if (hasUsageStatsPermission()) {
                updateHighUsageAppsWithUsageStats();
            } else {
                updateHighUsageApps();
            }

            // Show a notification that optimization has expired
            showOptimizationExpiredNotification();
        }, OPTIMIZATION_DURATION);
    }

    private void createNotificationChannel() {
        // Create the notification channel for Android 8.0+
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                    CHANNEL_ID,
                    getString(R.string.system_monitor_channel),
                    NotificationManager.IMPORTANCE_DEFAULT);
            channel.setDescription(getString(R.string.system_monitor_channel_description));

            NotificationManager notificationManager = getSystemService(NotificationManager.class);
            notificationManager.createNotificationChannel(channel);
        }

        // Request notification permission for Android 13+
        if (Build.VERSION.SDK_INT >= 33) { // Build.VERSION_CODES.TIRAMISU is 33
            if (checkSelfPermission(android.Manifest.permission.POST_NOTIFICATIONS) !=
                    android.content.pm.PackageManager.PERMISSION_GRANTED) {
                // Request the permission
                requestPermissions(new String[]{android.Manifest.permission.POST_NOTIFICATIONS}, 100);
            }
        }
    }

    private void setupSystemMonitoring() {
        try {
            // Set up periodic system monitoring
            alarmManager = (AlarmManager) getSystemService(Context.ALARM_SERVICE);

            // Create intent for the monitoring service
            Intent intent = new Intent(this, NotificationReceiver.class);
            intent.setAction("com.game.headshot.ACTION_CHECK_SYSTEM");

            // Create pending intent
            int flags = PendingIntent.FLAG_UPDATE_CURRENT;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                flags |= PendingIntent.FLAG_IMMUTABLE;
            }
            monitorIntent = PendingIntent.getBroadcast(this, 0, intent, flags);

            // Schedule periodic checks (every 3 hours)
            long interval = 3 * 60 * 60 * 1000; // 3 hours
            long triggerTime = System.currentTimeMillis() + interval;

            // Use the appropriate method based on API level and available permissions
            try {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    // For Android 12+, check if we have permission for exact alarms
                    if (alarmManager.canScheduleExactAlarms()) {
                        // Use setAndAllowWhileIdle instead of setExactAndAllowWhileIdle
                        alarmManager.setAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, triggerTime, monitorIntent);
                    } else {
                        // Fall back to inexact alarm if we don't have permission
                        alarmManager.setAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, triggerTime, monitorIntent);
                    }
                } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    // For Android 6.0-11, use alarms with idle allowance
                    alarmManager.setAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, triggerTime, monitorIntent);
                } else {
                    // For older versions, use regular alarms
                    alarmManager.set(AlarmManager.RTC_WAKEUP, triggerTime, monitorIntent);
                }
            } catch (SecurityException e) {
                // If we don't have permission for exact alarms, fall back to inexact
                alarmManager.set(AlarmManager.RTC_WAKEUP, triggerTime, monitorIntent);
            }

            // Schedule daily reminder notifications (noon and evening)
            scheduleDailyReminders();

            // Also check system status immediately
            checkSystemStatus();
        } catch (Exception e) {
            // If anything goes wrong, just log the error and continue
            e.printStackTrace();
        }
    }

    private void scheduleDailyReminders() {
        try {
            AlarmManager alarmManager = (AlarmManager) getSystemService(Context.ALARM_SERVICE);

            // Create intents for noon and evening reminders
            Intent noonIntent = new Intent(this, NotificationReceiver.class);
            noonIntent.setAction("com.game.headshot.ACTION_NOON_REMINDER");

            Intent eveningIntent = new Intent(this, NotificationReceiver.class);
            eveningIntent.setAction("com.game.headshot.ACTION_EVENING_REMINDER");

            // Create pending intents
            int flags = PendingIntent.FLAG_UPDATE_CURRENT;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                flags |= PendingIntent.FLAG_IMMUTABLE;
            }

            PendingIntent noonPendingIntent = PendingIntent.getBroadcast(this, 1, noonIntent, flags);
            PendingIntent eveningPendingIntent = PendingIntent.getBroadcast(this, 2, eveningIntent, flags);

            // Set up calendar for noon (2:00 PM)
            Calendar noonCalendar = Calendar.getInstance();
            noonCalendar.set(Calendar.HOUR_OF_DAY, 14);
            noonCalendar.set(Calendar.MINUTE, 0);
            noonCalendar.set(Calendar.SECOND, 0);

            // If it's already past noon, set for tomorrow
            if (noonCalendar.getTimeInMillis() < System.currentTimeMillis()) {
                noonCalendar.add(Calendar.DAY_OF_YEAR, 1);
            }

            // Set up calendar for evening (6:00 PM)
            Calendar eveningCalendar = Calendar.getInstance();
            eveningCalendar.set(Calendar.HOUR_OF_DAY, 18);
            eveningCalendar.set(Calendar.MINUTE, 0);
            eveningCalendar.set(Calendar.SECOND, 0);

            // If it's already past evening, set for tomorrow
            if (eveningCalendar.getTimeInMillis() < System.currentTimeMillis()) {
                eveningCalendar.add(Calendar.DAY_OF_YEAR, 1);
            }

            // For testing purposes, also set an alarm for 1 minute from now
            Calendar testCalendar = Calendar.getInstance();
            testCalendar.add(Calendar.MINUTE, 1);
            Intent testIntent = new Intent(this, NotificationReceiver.class);
            testIntent.setAction("com.game.headshot.ACTION_NOON_REMINDER");
            PendingIntent testPendingIntent = PendingIntent.getBroadcast(this, 3, testIntent, flags);

            // Schedule the alarms with try-catch for each alarm
            try {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    // For Android 12+, check if we have permission for exact alarms
                    if (alarmManager.canScheduleExactAlarms()) {
                        // Use setAndAllowWhileIdle instead of setExactAndAllowWhileIdle
                        alarmManager.setAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, noonCalendar.getTimeInMillis(), noonPendingIntent);
                        alarmManager.setAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, eveningCalendar.getTimeInMillis(), eveningPendingIntent);
                        alarmManager.setAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, testCalendar.getTimeInMillis(), testPendingIntent);
                    } else {
                        // Fall back to inexact alarm if we don't have permission
                        alarmManager.set(AlarmManager.RTC_WAKEUP, noonCalendar.getTimeInMillis(), noonPendingIntent);
                        alarmManager.set(AlarmManager.RTC_WAKEUP, eveningCalendar.getTimeInMillis(), eveningPendingIntent);
                        alarmManager.set(AlarmManager.RTC_WAKEUP, testCalendar.getTimeInMillis(), testPendingIntent);
                    }
                } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    // For Android 6.0-11, use alarms with idle allowance
                    alarmManager.setAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, noonCalendar.getTimeInMillis(), noonPendingIntent);
                    alarmManager.setAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, eveningCalendar.getTimeInMillis(), eveningPendingIntent);
                    alarmManager.setAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, testCalendar.getTimeInMillis(), testPendingIntent);
                } else {
                    // For older versions, use regular alarms
                    alarmManager.set(AlarmManager.RTC_WAKEUP, noonCalendar.getTimeInMillis(), noonPendingIntent);
                    alarmManager.set(AlarmManager.RTC_WAKEUP, eveningCalendar.getTimeInMillis(), eveningPendingIntent);
                    alarmManager.set(AlarmManager.RTC_WAKEUP, testCalendar.getTimeInMillis(), testPendingIntent);
                }
                Log.d("GameBooster", "Noon reminder scheduled for " + noonCalendar.getTime());
                Log.d("GameBooster", "Evening reminder scheduled for " + eveningCalendar.getTime());
                Log.d("GameBooster", "Test reminder scheduled for " + testCalendar.getTime());
            } catch (SecurityException e) {
                // Fall back to inexact alarm if we don't have permission
                Log.w("GameBooster", "No permission for exact alarm, using inexact alarms");
                alarmManager.set(AlarmManager.RTC_WAKEUP, noonCalendar.getTimeInMillis(), noonPendingIntent);
                alarmManager.set(AlarmManager.RTC_WAKEUP, eveningCalendar.getTimeInMillis(), eveningPendingIntent);
                alarmManager.set(AlarmManager.RTC_WAKEUP, testCalendar.getTimeInMillis(), testPendingIntent);
            }
        } catch (Exception e) {
            Log.e("GameBooster", "Error scheduling daily reminders: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void checkSystemStatus() {
        // Check if the device needs optimization
        ActivityManager activityManager = (ActivityManager) getSystemService(Context.ACTIVITY_SERVICE);
        ActivityManager.MemoryInfo memoryInfo = new ActivityManager.MemoryInfo();
        activityManager.getMemoryInfo(memoryInfo);

        // Calculate memory usage percentage
        double totalMemoryGB = memoryInfo.totalMem / (1024.0 * 1024.0 * 1024.0);
        double usedMemoryGB = (memoryInfo.totalMem - memoryInfo.availMem) / (1024.0 * 1024.0 * 1024.0);
        int memoryUsagePercent = (int) ((usedMemoryGB / totalMemoryGB) * 100);

        // Get CPU usage (simulated for demo)
        int cpuUsage = 65 + random.nextInt(20); // Random between 65-85%

        // If memory usage is high (over 40%) or CPU usage is high (over 40%) and not already optimized
        // Using lower thresholds to ensure notifications are triggered more frequently
        if ((memoryUsagePercent > 40 || cpuUsage > 40) && !isOptimized) {
            // Only show popup if we're in the foreground
            if (!isFinishing()) {
                showOptimizationNeededPopup();
            } else {
                // If not in foreground, send a broadcast to show a notification
                Intent intent = new Intent(this, NotificationReceiver.class);
                intent.setAction("com.game.headshot.ACTION_HIGH_USAGE");
                intent.putExtra("cpu_usage", cpuUsage);
                intent.putExtra("memory_usage", memoryUsagePercent);
                sendBroadcast(intent);

                Log.d("GameBooster", "Sent high usage broadcast: CPU=" + cpuUsage + "%, Memory=" + memoryUsagePercent + "%");
            }
        }
    }

    private void showHighUsageNotification(int cpuUsage, int memoryUsage) {
        // Create notification for high system usage
        NotificationManager notificationManager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);

        // Create intent for when notification is clicked
        Intent intent = new Intent(this, GameBoosterActivity.class);
        intent.setAction("com.game.headshot.ACTION_CHECK_SYSTEM");
        intent.putExtra("notification_launch", true);
        intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);

        // Create pending intent
        int flags = PendingIntent.FLAG_UPDATE_CURRENT;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            flags |= PendingIntent.FLAG_IMMUTABLE;
        }
        PendingIntent pendingIntent = PendingIntent.getActivity(this, 0, intent, flags);

        // Build the notification
        NotificationCompat.Builder builder = new NotificationCompat.Builder(this, CHANNEL_ID)
                .setSmallIcon(R.drawable.ic_rocket)
                .setContentTitle(getString(R.string.optimization_needed))
                .setContentText(getString(R.string.high_usage_notification, cpuUsage, memoryUsage))
                .setPriority(NotificationCompat.PRIORITY_HIGH)
                .setContentIntent(pendingIntent)
                .setAutoCancel(true);

        // Show the notification
        notificationManager.notify(NOTIFICATION_ID + 3, builder.build());
    }

    private boolean hasUsageStatsPermission() {
        try {
            UsageStatsManager usageStatsManager = (UsageStatsManager) getSystemService(Context.USAGE_STATS_SERVICE);
            if (usageStatsManager == null) {
                Log.e("GameBooster", "UsageStatsManager is null");
                return false;
            }

            long currentTime = System.currentTimeMillis();
            // Check if we can get any stats - if we can, we have permission
            List<UsageStats> stats = usageStatsManager.queryUsageStats(
                    UsageStatsManager.INTERVAL_DAILY, currentTime - 1000 * 3600, currentTime);

            boolean hasPermission = stats != null && !stats.isEmpty();
            Log.d("GameBooster", "Usage stats permission check: " + hasPermission +
                  " (stats size: " + (stats != null ? stats.size() : "null") + ")");
            return hasPermission;
        } catch (SecurityException e) {
            Log.e("GameBooster", "Security exception checking usage stats permission: " + e.getMessage());
            return false;
        } catch (Exception e) {
            Log.e("GameBooster", "Error checking usage stats permission: " + e.getMessage());
            return false;
        }
    }

    private void showUsageAccessPermissionDialog() {
        try {
            // Check if we already have a dialog showing to prevent multiple dialogs
            if (isFinishing()) {
                return;
            }

            androidx.appcompat.app.AlertDialog.Builder builder = new androidx.appcompat.app.AlertDialog.Builder(this);
            builder.setTitle(getString(R.string.usage_access_required))
                    .setMessage(getString(R.string.usage_access_message))
                    .setPositiveButton(getString(R.string.go_to_settings), (dialog, which) -> {
                        try {
                            // Open usage access settings
                            Intent intent = new Intent(Settings.ACTION_USAGE_ACCESS_SETTINGS);
                            startActivity(intent);
                        } catch (Exception e) {
                            Log.e("GameBooster", "Error opening usage access settings: " + e.getMessage());
                            Toast.makeText(this, R.string.error_opening_settings, Toast.LENGTH_SHORT).show();
                        }
                    })
                    .setNegativeButton(getString(R.string.cancel), (dialog, which) -> {
                        dialog.dismiss();
                        // Show a toast explaining the limitations
                        Toast.makeText(this, R.string.usage_access_denied_message, Toast.LENGTH_LONG).show();
                    })
                    .setCancelable(false);

            // Show the dialog safely
            try {
                androidx.appcompat.app.AlertDialog dialog = builder.create();
                dialog.show();
            } catch (Exception e) {
                Log.e("GameBooster", "Error showing usage access dialog: " + e.getMessage());
            }
        } catch (Exception e) {
            Log.e("GameBooster", "Error in showUsageAccessPermissionDialog: " + e.getMessage());
        }
    }

    private void updateHighUsageAppsWithUsageStats() {
        // Get the actual running apps using UsageStatsManager
        LinearLayout appsContainer = findViewById(R.id.apps_container);
        appsContainer.removeAllViews();

        // Get usage stats
        UsageStatsManager usageStatsManager = (UsageStatsManager) getSystemService(Context.USAGE_STATS_SERVICE);
        if (usageStatsManager == null) {
            // Fall back to the old method if UsageStatsManager is not available
            Log.d("GameBooster", "UsageStatsManager is null, falling back to old method");
            updateHighUsageApps();
            return;
        }

        // Query for usage stats from the last hour (more likely to be actually running)
        long endTime = System.currentTimeMillis();
        long startTime = endTime - (1 * 60 * 60 * 1000); // 1 hour ago

        List<UsageStats> usageStatsList = usageStatsManager.queryUsageStats(
                UsageStatsManager.INTERVAL_DAILY, startTime, endTime);

        if (usageStatsList == null || usageStatsList.isEmpty()) {
            // Try with a longer time period if no results
            Log.d("GameBooster", "No usage stats found for last hour, trying with 24 hours");
            startTime = endTime - (24 * 60 * 60 * 1000); // 24 hours ago
            usageStatsList = usageStatsManager.queryUsageStats(
                    UsageStatsManager.INTERVAL_DAILY, startTime, endTime);

            if (usageStatsList == null || usageStatsList.isEmpty()) {
                // Fall back to the old method if no usage stats are available
                Log.d("GameBooster", "No usage stats found for last 24 hours, falling back to old method");
                updateHighUsageApps();
                return;
            }
        }

        Log.d("GameBooster", "Found " + usageStatsList.size() + " usage stats entries");

        // Get the ActivityManager to check if apps are actually running
        ActivityManager activityManager = (ActivityManager) getSystemService(Context.ACTIVITY_SERVICE);

        // Get running processes to verify which apps are actually running
        List<ActivityManager.RunningAppProcessInfo> runningProcesses = null;
        try {
            runningProcesses = activityManager.getRunningAppProcesses();
            Log.d("GameBooster", "Found " + (runningProcesses != null ? runningProcesses.size() : 0) + " running processes");
        } catch (Exception e) {
            Log.e("GameBooster", "Error getting running processes: " + e.getMessage());
        }

        // Create a set of running package names for faster lookup
        Set<String> runningPackages = new HashSet<>();
        if (runningProcesses != null) {
            for (ActivityManager.RunningAppProcessInfo process : runningProcesses) {
                if (process.processName != null) {
                    runningPackages.add(process.processName);
                    // Add all packages associated with this process
                    if (process.pkgList != null) {
                        for (String pkg : process.pkgList) {
                            runningPackages.add(pkg);
                        }
                    }
                }
            }
        }

        Log.d("GameBooster", "Found " + runningPackages.size() + " running packages");

        // Sort by last time used (most recent first)
        SortedMap<Long, UsageStats> sortedMap = new TreeMap<>(Collections.reverseOrder());
        for (UsageStats usageStats : usageStatsList) {
            // Only include apps that were used recently and have significant usage time
            if (usageStats.getLastTimeUsed() > (System.currentTimeMillis() - 24 * 60 * 60 * 1000) &&
                usageStats.getTotalTimeInForeground() > 0) {
                sortedMap.put(usageStats.getLastTimeUsed(), usageStats);
            }
        }

        // Filter out system apps and get top apps
        List<AppUsageInfo> userApps = new ArrayList<>();
        PackageManager packageManager = getPackageManager();

        // First try to find apps that are actually running
        for (Map.Entry<Long, UsageStats> entry : sortedMap.entrySet()) {
            UsageStats usageStats = entry.getValue();
            String packageName = usageStats.getPackageName();

            try {
                // Skip our own app
                if (packageName.equals(getPackageName())) continue;

                // Check if this app is actually running
                boolean isRunning = runningPackages.contains(packageName);

                // Skip if not running
                if (!isRunning) continue;

                // Get app info
                ApplicationInfo appInfo = packageManager.getApplicationInfo(packageName, 0);

                // Skip system apps
                if ((appInfo.flags & ApplicationInfo.FLAG_SYSTEM) != 0) continue;

                // Get app name
                String appName = packageManager.getApplicationLabel(appInfo).toString();

                // Get last used time
                long lastTimeUsed = usageStats.getLastTimeUsed();

                // Get total time in foreground
                long totalTimeInForeground = usageStats.getTotalTimeInForeground();

                // Add to list
                userApps.add(new AppUsageInfo(appName, packageName, appInfo, lastTimeUsed, totalTimeInForeground));

                // Only get top 3 apps
                if (userApps.size() >= 3) break;
            } catch (Exception e) {
                Log.e("GameBooster", "Error processing package " + packageName + ": " + e.getMessage());
                // Skip this app if there's an error
            }
        }

        // If we didn't find enough running apps, add recently used apps
        if (userApps.size() < 3) {
            Log.d("GameBooster", "Not enough running apps found, adding recently used apps");
            for (Map.Entry<Long, UsageStats> entry : sortedMap.entrySet()) {
                UsageStats usageStats = entry.getValue();
                String packageName = usageStats.getPackageName();

                try {
                    // Skip our own app
                    if (packageName.equals(getPackageName())) continue;

                    // Skip apps we already added
                    boolean alreadyAdded = false;
                    for (AppUsageInfo app : userApps) {
                        if (app.packageName.equals(packageName)) {
                            alreadyAdded = true;
                            break;
                        }
                    }
                    if (alreadyAdded) continue;

                    // Get app info
                    ApplicationInfo appInfo = packageManager.getApplicationInfo(packageName, 0);

                    // Skip system apps
                    if ((appInfo.flags & ApplicationInfo.FLAG_SYSTEM) != 0) continue;

                    // Get app name
                    String appName = packageManager.getApplicationLabel(appInfo).toString();

                    // Get last used time
                    long lastTimeUsed = usageStats.getLastTimeUsed();

                    // Get total time in foreground
                    long totalTimeInForeground = usageStats.getTotalTimeInForeground();

                    // Add to list
                    userApps.add(new AppUsageInfo(appName, packageName, appInfo, lastTimeUsed, totalTimeInForeground));

                    // Only get top 3 apps
                    if (userApps.size() >= 3) break;
                } catch (Exception e) {
                    Log.e("GameBooster", "Error processing package " + packageName + ": " + e.getMessage());
                    // Skip this app if there's an error
                }
            }
        }

        Log.d("GameBooster", "Final list contains " + userApps.size() + " apps");

        // Convert AppUsageInfo to AppInfo for saving
        List<AppInfo> appInfoList = new ArrayList<>();
        for (AppUsageInfo app : userApps) {
            appInfoList.add(new AppInfo(app.name, app.packageName, app.appInfo, 0, 0));
        }

        // Save the top apps for later use
        saveTopRunningApps(appInfoList);

        // Add apps to the container
        for (AppUsageInfo app : userApps) {
            View appView = getLayoutInflater().inflate(R.layout.item_app, appsContainer, false);

            ImageView appIcon = appView.findViewById(R.id.app_icon);
            TextView appName = appView.findViewById(R.id.app_name);
            TextView appInfo = appView.findViewById(R.id.app_info);
            TextView appUsage = appView.findViewById(R.id.app_usage);
            ImageView closeIcon = appView.findViewById(R.id.close_icon);

            // Set app icon
            try {
                appIcon.setImageDrawable(packageManager.getApplicationIcon(app.packageName));
            } catch (Exception e) {
                // Use default icon if we can't get the app icon
                Log.e("GameBooster", "Error getting icon for " + app.packageName + ": " + e.getMessage());
            }

            // Set app name
            appName.setText(app.name);

            // Format last used time
            String lastUsed = formatTime(app.lastTimeUsed);
            appInfo.setText(getString(R.string.last_used, lastUsed));

            // Format usage time
            String usageTime = formatUsageTime(app.totalTimeInForeground);
            appUsage.setText(usageTime);

            // Set click listener for close button
            closeIcon.setOnClickListener(v -> {
                // Try to kill the app using multiple methods
                boolean success = forceStopApp(app.packageName);

                // Animate and remove from view
                appView.startAnimation(AnimationUtils.loadAnimation(this, android.R.anim.fade_out));
                appView.setVisibility(View.GONE);

                if (success) {
                    Toast.makeText(this, getString(R.string.app_closed, app.name), Toast.LENGTH_SHORT).show();
                } else {
                    Toast.makeText(this, getString(R.string.app_close_failed, app.name), Toast.LENGTH_SHORT).show();
                }
            });

            appsContainer.addView(appView);
        }

        // If no apps found, show a message
        if (userApps.isEmpty()) {
            TextView noAppsText = new TextView(this);
            noAppsText.setText(getString(R.string.no_apps_found));
            noAppsText.setTextColor(getResources().getColor(R.color.text_color_secondary));
            noAppsText.setPadding(16, 16, 16, 16);
            appsContainer.addView(noAppsText);
        }
    }

    /**
     * Force stop an app using multiple methods
     * @param packageName The package name of the app to stop
     * @return True if at least one method was successful
     */
    private boolean forceStopApp(String packageName) {
        boolean success = false;
        ActivityManager activityManager = (ActivityManager) getSystemService(Context.ACTIVITY_SERVICE);

        // Method 1: Use killBackgroundProcesses
        try {
            Log.d("GameBooster", "Trying to kill " + packageName + " with killBackgroundProcesses");
            activityManager.killBackgroundProcesses(packageName);
            success = true;
        } catch (Exception e) {
            Log.e("GameBooster", "Error killing " + packageName + " with killBackgroundProcesses: " + e.getMessage());
        }

        // Method 2: For Android 10+, try to use the app's force stop functionality via reflection
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            try {
                Log.d("GameBooster", "Trying to force stop " + packageName + " with reflection");
                // This is a more aggressive approach using reflection to access hidden APIs
                // Note: This might not work on all devices due to Android restrictions
                Class<?> activityManagerClass = Class.forName("android.app.ActivityManager");
                java.lang.reflect.Method forceStopPackage = activityManagerClass.getDeclaredMethod("forceStopPackage", String.class);
                forceStopPackage.setAccessible(true);
                forceStopPackage.invoke(activityManager, packageName);
                success = true;
            } catch (Exception e) {
                Log.e("GameBooster", "Error force stopping " + packageName + " with reflection: " + e.getMessage());
            }
        }

        return success;
    }

    private String formatTime(long timeInMillis) {
        // Format time as "Today 10:30 AM" or "Yesterday 3:45 PM" or "2023-04-22"
        java.util.Date date = new java.util.Date(timeInMillis);
        java.util.Date now = new java.util.Date();
        java.text.SimpleDateFormat timeFormat = new java.text.SimpleDateFormat("h:mm a", java.util.Locale.getDefault());
        java.text.SimpleDateFormat dateFormat = new java.text.SimpleDateFormat("yyyy-MM-dd", java.util.Locale.getDefault());

        // Check if it's today
        if (android.text.format.DateUtils.isToday(timeInMillis)) {
            return "Today " + timeFormat.format(date);
        }

        // Check if it's yesterday
        java.util.Calendar calendar = java.util.Calendar.getInstance();
        calendar.add(java.util.Calendar.DAY_OF_YEAR, -1);
        java.util.Date yesterday = calendar.getTime();
        if (dateFormat.format(yesterday).equals(dateFormat.format(date))) {
            return "Yesterday " + timeFormat.format(date);
        }

        // Otherwise, return the date
        return dateFormat.format(date);
    }

    private String formatUsageTime(long timeInMillis) {
        // Format time as "2h 30m" or "45m" or "30s"
        long seconds = timeInMillis / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;

        if (hours > 0) {
            return hours + "h " + (minutes % 60) + "m";
        } else if (minutes > 0) {
            return minutes + "m";
        } else {
            return seconds + "s";
        }
    }

    // Class to hold app usage information
    private static class AppUsageInfo {
        String name;
        String packageName;
        ApplicationInfo appInfo;
        long lastTimeUsed;
        long totalTimeInForeground;

        AppUsageInfo(String name, String packageName, ApplicationInfo appInfo, long lastTimeUsed, long totalTimeInForeground) {
            this.name = name;
            this.packageName = packageName;
            this.appInfo = appInfo;
            this.lastTimeUsed = lastTimeUsed;
            this.totalTimeInForeground = totalTimeInForeground;
        }
    }

    private void showOptimizationNeededNotification() {
        // Instead of showing a notification, show a popup dialog
        showOptimizationNeededPopup();
    }

    private void showCleaningCompleteNotification() {
        // Send a broadcast to show a notification about analysis completion
        Intent intent = new Intent(this, NotificationReceiver.class);
        intent.setAction("com.game.headshot.ACTION_ANALYSIS_COMPLETE");
        sendBroadcast(intent);

        Log.d("GameBooster", "Sent analysis complete broadcast");
    }

    private void showOptimizationExpiredNotification() {
        // Send a broadcast to show a notification
        Intent intent = new Intent(this, NotificationReceiver.class);
        intent.setAction("com.game.headshot.ACTION_CHECK_SYSTEM");
        sendBroadcast(intent);

        Log.d("GameBooster", "Sent optimization expired broadcast");
    }

    private void showOptimizationNeededPopup() {
        // Get the current activity as a context
        Context context = this;

        // Get system stats for the popup
        ActivityManager activityManager = (ActivityManager) getSystemService(Context.ACTIVITY_SERVICE);
        ActivityManager.MemoryInfo memoryInfo = new ActivityManager.MemoryInfo();
        activityManager.getMemoryInfo(memoryInfo);

        // Calculate memory usage percentage
        double totalMemoryGB = memoryInfo.totalMem / (1024.0 * 1024.0 * 1024.0);
        double usedMemoryGB = (memoryInfo.totalMem - memoryInfo.availMem) / (1024.0 * 1024.0 * 1024.0);
        int memoryUsagePercent = (int) ((usedMemoryGB / totalMemoryGB) * 100);

        // Get CPU usage (simulated)
        int cpuUsage = 65 + random.nextInt(20); // Random between 65-85%

        // Get running apps count
        int runningAppsCount = 0;
        if (hasUsageStatsPermission()) {
            UsageStatsManager usageStatsManager = (UsageStatsManager) getSystemService(Context.USAGE_STATS_SERVICE);
            long endTime = System.currentTimeMillis();
            long startTime = endTime - (24 * 60 * 60 * 1000); // 24 hours ago
            List<UsageStats> usageStatsList = usageStatsManager.queryUsageStats(
                    UsageStatsManager.INTERVAL_DAILY, startTime, endTime);

            // Count non-system apps
            PackageManager packageManager = getPackageManager();
            for (UsageStats usageStats : usageStatsList) {
                try {
                    ApplicationInfo appInfo = packageManager.getApplicationInfo(usageStats.getPackageName(), 0);
                    if ((appInfo.flags & ApplicationInfo.FLAG_SYSTEM) == 0) {
                        runningAppsCount++;
                    }
                } catch (Exception e) {
                    // Skip this app
                }
            }
        } else {
            // Fallback to a reasonable number
            runningAppsCount = 8 + random.nextInt(8); // Random between 8-15
        }

        // Create and show the dialog
        Dialog dialog = new Dialog(context);
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        dialog.setContentView(R.layout.popup_optimization_needed);
        dialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        dialog.getWindow().setLayout(WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.WRAP_CONTENT);

        // Set the values in the popup
        TextView cpuUsageText = dialog.findViewById(R.id.popup_cpu_usage);
        TextView memoryUsageText = dialog.findViewById(R.id.popup_memory_usage);
        TextView appsCountText = dialog.findViewById(R.id.popup_apps_count);

        cpuUsageText.setText(cpuUsage + "%");
        memoryUsageText.setText(memoryUsagePercent + "%");
        appsCountText.setText(String.valueOf(runningAppsCount));

        // Set button click listeners
        Button cancelButton = dialog.findViewById(R.id.popup_cancel_button);
        Button optimizeButton = dialog.findViewById(R.id.popup_optimize_button);

        cancelButton.setOnClickListener(v -> dialog.dismiss());

        optimizeButton.setOnClickListener(v -> {
            dialog.dismiss();
            showOptimizationProgressPopup();
        });

        dialog.show();
    }

    private Dialog progressDialog;
    private float optimizationProgress = 0;
    private Handler progressHandler = new Handler();
    private Runnable progressRunnable;

    private void showOptimizationProgressPopup() {
        // Create and show the progress dialog
        progressDialog = new Dialog(this);
        progressDialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        progressDialog.setContentView(R.layout.popup_optimization_progress);
        progressDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        progressDialog.getWindow().setLayout(WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.WRAP_CONTENT);
        progressDialog.setCancelable(false);

        // Get views from the dialog
        TextView statusText = progressDialog.findViewById(R.id.popup_progress_status);
        ProgressBar progressBar = progressDialog.findViewById(R.id.popup_progress_bar);
        TextView progressPercent = progressDialog.findViewById(R.id.popup_progress_percent);
        ImageView needleImage = progressDialog.findViewById(R.id.speedometer_needle);
        TextView speedValue = progressDialog.findViewById(R.id.speedometer_value);

        // Show the dialog
        progressDialog.show();

        // Reset progress
        optimizationProgress = 0;

        // Create a runnable to update the progress
        progressRunnable = new Runnable() {
            @Override
            public void run() {
                // Update progress with non-linear acceleration for more realistic movement
                if (optimizationProgress < 20) {
                    // Start slow
                    optimizationProgress += 0.5;
                } else if (optimizationProgress < 40) {
                    // Speed up
                    optimizationProgress += 0.8;
                } else if (optimizationProgress < 60) {
                    // Move faster in the middle
                    optimizationProgress += 1.2;
                } else if (optimizationProgress < 80) {
                    // Start slowing down
                    optimizationProgress += 0.8;
                } else if (optimizationProgress < 95) {
                    // Slow near the end
                    optimizationProgress += 0.5;
                } else {
                    // Very slow at the very end
                    optimizationProgress += 0.2;
                }

                // Ensure we don't exceed 100
                if (optimizationProgress > 100) optimizationProgress = 100;

                // Calculate integer progress for display
                int intProgress = (int) optimizationProgress;

                // Update UI
                progressBar.setProgress(intProgress);
                progressPercent.setText(intProgress + "%");

                // Update needle rotation with smooth easing (from -90 to 90 degrees)
                // Using a sine function for more natural movement
                float normalizedProgress = optimizationProgress / 100f;
                float easedProgress = (float) Math.sin(normalizedProgress * Math.PI / 2);
                float needleRotation = -90 + (180 * easedProgress);

                // Add a slight oscillation for a more dynamic feel
                float oscillation = (float) (Math.sin(optimizationProgress * 0.8) * (1.0 - normalizedProgress) * 3);
                needleImage.setRotation(needleRotation + oscillation);

                // Apply a subtle scale animation to the needle for emphasis
                float scale = 1.0f + (float) (Math.sin(normalizedProgress * Math.PI) * 0.05);
                needleImage.setScaleX(scale);
                needleImage.setScaleY(scale);

                // Update speed value with a slight lead for more dynamic feel
                int displaySpeed = Math.min(100, intProgress + 2);
                speedValue.setText(displaySpeed + "%");

                // Update status text based on progress
                if (optimizationProgress < 25) {
                    statusText.setText(getString(R.string.clearing_cache));
                } else if (optimizationProgress < 50) {
                    statusText.setText(getString(R.string.optimizing_cpu));
                } else if (optimizationProgress < 75) {
                    statusText.setText(getString(R.string.closing_apps));
                } else {
                    statusText.setText(getString(R.string.optimizing_ram));
                }

                // Continue updating if not done
                if (optimizationProgress < 100) {
                    // Vary the update rate for more natural movement
                    long updateDelay = optimizationProgress < 50 ? 30 : 20; // Faster updates as we progress
                    progressHandler.postDelayed(this, updateDelay);
                } else {
                    // Add a final "bounce" effect when reaching 100%
                    ObjectAnimator bounceAnim = ObjectAnimator.ofFloat(needleImage, "rotation", needleRotation, needleRotation + 10, needleRotation - 5, needleRotation + 2, needleRotation);
                    bounceAnim.setDuration(500);
                    bounceAnim.start();

                    // Optimization complete
                    progressHandler.postDelayed(() -> {
                        progressDialog.dismiss();
                        performActualOptimization();
                        showOptimizationCompletePopup();
                    }, 800); // Slightly longer delay to show the bounce effect
                }
            }
        };

        // Start the progress updates
        progressHandler.post(progressRunnable);
    }

    private void performActualOptimization() {
        // Actually perform the optimization
        clearCache();
        killBackgroundProcesses();
        closeBackgroundApps();
        clearMemory();

        // Update the UI
        isOptimized = true;
        btnBoost.setText(getString(R.string.optimized));
        statusText.setText(getString(R.string.optimization_complete));

        // Show notification that cleaning is complete
        showCleaningCompleteNotification();

        // Show toast that optimization is saved for 3 minutes
        Toast.makeText(this, getString(R.string.last_optimization_saved), Toast.LENGTH_SHORT).show();

        // Show rocket animation in the main UI
        rocketContainer.setVisibility(View.VISIBLE);
        showRocketAnimation();

        // Update UI to show analysis complete status
        cpuAfterText.setText(getString(R.string.analysis_disclaimer));
        cpuAfterProgress.setProgress(100);
        cpuAfterProgress.setProgressDrawable(ContextCompat.getDrawable(this, R.drawable.circular_progress_bar_green));

        // Update memory usage after analysis
        updateMemoryAfterOptimization();

        // Update RAM speed in the main UI
        int baseRamSpeed = 1000 + random.nextInt(500); // Random between 1000-1500 MB/s
        ramSpeedBefore.setText(baseRamSpeed + " MB/s");
        ramSpeedAfter.setText(getString(R.string.analysis_disclaimer));
        ramSpeedProgress.setProgress(100);
        ramSpeedProgress.setProgressDrawable(ContextCompat.getDrawable(this, R.drawable.circular_progress_bar_green));

        // Animate RAM speed progress bar
        ObjectAnimator ramSpeedAnim = ObjectAnimator.ofInt(ramSpeedProgress, "progress", 0, 100);
        ramSpeedAnim.setDuration(800);
        ramSpeedAnim.start();

        // Add a glow effect to the RAM speed text
        Animation glowPulse = AnimationUtils.loadAnimation(this, R.anim.glow_pulse);
        ramSpeedAfter.startAnimation(glowPulse);

        // Update high usage apps list
        if (hasUsageStatsPermission()) {
            updateHighUsageAppsWithUsageStats();
        } else {
            updateHighUsageApps();
        }

        // Save optimization state with timestamp
        SharedPreferences.Editor editor = preferences.edit();
        editor.putBoolean("isOptimized", true);
        editor.putLong("lastOptimizationTime", System.currentTimeMillis());
        editor.apply();

        // Schedule reset of optimization state after 2 minutes
        scheduleOptimizationReset();
    }

    private void showOptimizationCompletePopup() {
        // Show interstitial ad after optimization (only for non-premium users)
        if (!premiumManager.isPremium() && adManager != null) {
            adManager.showInterstitialAd(this, new AdManager.InterstitialAdCallback() {
                @Override
                public void onAdClosed() {
                    // Continue with showing the complete popup after ad is closed
                    displayOptimizationCompletePopup();
                }

                @Override
                public void onAdFailed() {
                    // Continue with showing the complete popup if ad fails to load
                    displayOptimizationCompletePopup();
                }
            });
        } else {
            // Premium user or ad manager not available, proceed directly
            displayOptimizationCompletePopup();
        }
    }

    private void displayOptimizationCompletePopup() {
        // Show the new gaming performance report instead of fake optimization results
        com.gfxtools.headshotsettingsgamebooster.utils.SystemAnalysisDialog.showGamingPerformanceReport(this);
    }
}
