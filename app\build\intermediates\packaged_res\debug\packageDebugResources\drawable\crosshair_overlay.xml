<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="200dp"
    android:height="200dp"
    android:viewportWidth="200"
    android:viewportHeight="200">

    <!-- Outer Circle -->
    <path
        android:strokeColor="#80FFFFFF"
        android:strokeWidth="1"
        android:strokeLineCap="round"
        android:pathData="M100,30 A70,70 0 1,1 99.9,30" />

    <!-- Inner Circle -->
    <path
        android:strokeColor="#80FFFFFF"
        android:strokeWidth="1"
        android:pathData="M100,70 A30,30 0 1,1 99.9,70" />

    <!-- Horizontal Line -->
    <path
        android:strokeColor="#80FFFFFF"
        android:strokeWidth="1"
        android:strokeLineCap="round"
        android:pathData="M30,100 L170,100" />

    <!-- Vertical Line -->
    <path
        android:strokeColor="#80FFFFFF"
        android:strokeWidth="1"
        android:strokeLineCap="round"
        android:pathData="M100,30 L100,170" />

    <!-- Diagonal Lines -->
    <path
        android:strokeColor="#4000FFFF"
        android:strokeWidth="0.5"
        android:strokeLineCap="round"
        android:pathData="M60,60 L140,140" />
    <path
        android:strokeColor="#4000FFFF"
        android:strokeWidth="0.5"
        android:strokeLineCap="round"
        android:pathData="M140,60 L60,140" />
</vector>
