package com.mahmoudffyt.gfxbooster.utils;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.google.android.gms.ads.MobileAds;
import com.google.android.gms.ads.initialization.InitializationStatus;
import com.google.android.gms.ads.initialization.OnInitializationCompleteListener;

/**
 * Helper class for optimized AdMob initialization
 */
public class MobileAdsInitializationHelper {
    private static final String TAG = "MobileAdsInitHelper";
    private static boolean isInitializing = false;
    private static boolean isInitialized = false;
    
    /**
     * Initialize AdMob with optimized settings
     * @param context Application context
     * @param listener Initialization complete listener
     */
    public static void initialize(Context context, final OnInitializationCompleteListener listener) {
        // If already initialized, call the listener immediately
        if (isInitialized) {
            Log.d(TAG, "AdMob already initialized, calling listener directly");
            if (listener != null) {
                new Handler(Looper.getMainLooper()).post(new Runnable() {
                    @Override
                    public void run() {
                        listener.onInitializationComplete(null);
                    }
                });
            }
            return;
        }
        
        // If initialization is in progress, wait for it to complete
        if (isInitializing) {
            Log.d(TAG, "AdMob initialization already in progress, waiting...");
            // Wait for initialization to complete with a timeout
            new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (isInitialized) {
                        if (listener != null) {
                            listener.onInitializationComplete(null);
                        }
                    } else {
                        // Try again with standard initialization
                        Log.d(TAG, "AdMob initialization timeout, using standard initialization");
                        MobileAds.initialize(context, listener);
                    }
                }
            }, 3000); // 3 second timeout
            return;
        }
        
        // Start initialization
        isInitializing = true;
        
        try {
            // Initialize AdMob with high priority
            Thread initThread = new Thread(new Runnable() {
                @Override
                public void run() {
                    try {
                        Log.d(TAG, "Starting optimized AdMob initialization");
                        MobileAds.initialize(context, new OnInitializationCompleteListener() {
                            @Override
                            public void onInitializationComplete(InitializationStatus initializationStatus) {
                                isInitialized = true;
                                isInitializing = false;
                                Log.d(TAG, "AdMob initialization completed successfully");
                                
                                if (listener != null) {
                                    listener.onInitializationComplete(initializationStatus);
                                }
                            }
                        });
                    } catch (Exception e) {
                        Log.e(TAG, "Error during optimized AdMob initialization: " + e.getMessage());
                        isInitializing = false;
                        
                        // Fall back to standard initialization
                        MobileAds.initialize(context, listener);
                    }
                }
            });
            
            // Set thread priority to maximum
            initThread.setPriority(Thread.MAX_PRIORITY);
            initThread.start();
            
            // Set a timeout for initialization
            new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (!isInitialized) {
                        Log.d(TAG, "AdMob initialization taking too long, continuing anyway");
                        isInitializing = false;
                        
                        if (listener != null) {
                            listener.onInitializationComplete(null);
                        }
                    }
                }
            }, 5000); // 5 second timeout
            
        } catch (Exception e) {
            Log.e(TAG, "Error starting AdMob initialization: " + e.getMessage());
            isInitializing = false;
            
            // Fall back to standard initialization
            MobileAds.initialize(context, listener);
        }
    }
}
