<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@android:color/transparent" />
        </shape>
    </item>
    <item>
        <rotate
            android:fromDegrees="45"
            android:pivotX="50%"
            android:pivotY="50%"
            android:toDegrees="45">
            <shape android:shape="line">
                <stroke
                    android:width="2dp"
                    android:color="@color/neon_blue"
                    android:dashGap="15dp"
                    android:dashWidth="30dp" />
            </shape>
        </rotate>
    </item>
    <item>
        <rotate
            android:fromDegrees="135"
            android:pivotX="50%"
            android:pivotY="50%"
            android:toDegrees="135">
            <shape android:shape="line">
                <stroke
                    android:width="1dp"
                    android:color="@color/neon_pink"
                    android:dashGap="20dp"
                    android:dashWidth="15dp" />
            </shape>
        </rotate>
    </item>
</layer-list>
