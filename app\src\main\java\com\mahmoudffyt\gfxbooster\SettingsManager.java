package com.mahmoudffyt.gfxbooster;

import android.content.Context;
import android.content.SharedPreferences;

import java.util.HashMap;
import java.util.Map;

/**
 * Class for managing sensitivity settings
 */
public class SettingsManager {
    private static final String PREFS_NAME = "HeadshotToolPrefs";
    private static final String KEY_GENERAL_SENSITIVITY = "generalSensitivity";
    private static final String KEY_RED_DOT_SENSITIVITY = "redDotSensitivity";
    private static final String KEY_SCOPE_2X_SENSITIVITY = "scope2xSensitivity";
    private static final String KEY_SCOPE_4X_SENSITIVITY = "scope4xSensitivity";
    private static final String KEY_SCOPE_AWM_SENSITIVITY = "scopeAwmSensitivity";
    private static final String KEY_BREATH_HOLD = "breathHold";
    private static final String KEY_DPI_SETTING = "dpiSetting";
    private static final String KEY_SELECTED_COLOR = "selectedColor";
    private static final String KEY_SMART_AIM_ENABLED = "smartAimEnabled";
    private static final String KEY_GFX_LEVEL = "gfxLevel";
    
    private Context context;
    private SharedPreferences preferences;
    private DeviceAnalyzer deviceAnalyzer;
    
    /**
     * Constructor for SettingsManager
     * 
     * @param context Application context
     */
    public SettingsManager(Context context) {
        this.context = context;
        this.preferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        this.deviceAnalyzer = new DeviceAnalyzer(context);
    }
    
    /**
     * Check if settings exist
     * 
     * @return true if settings exist, false otherwise
     */
    public boolean hasSettings() {
        return preferences.contains(KEY_GENERAL_SENSITIVITY);
    }
    
    /**
     * Save settings
     * 
     * @param generalSensitivity General sensitivity
     * @param redDotSensitivity Red dot sensitivity
     * @param scope2xSensitivity 2x scope sensitivity
     * @param scope4xSensitivity 4x scope sensitivity
     * @param scopeAwmSensitivity AWM scope sensitivity
     * @param breathHold Breath hold value
     * @param dpiSetting DPI setting
     * @param selectedColor Selected color
     * @param smartAimEnabled Smart aim enabled
     * @param gfxLevel GFX level
     */
    public void saveSettings(int generalSensitivity, int redDotSensitivity, int scope2xSensitivity,
                            int scope4xSensitivity, int scopeAwmSensitivity, int breathHold,
                            int dpiSetting, String selectedColor, boolean smartAimEnabled, String gfxLevel) {
        SharedPreferences.Editor editor = preferences.edit();
        editor.putInt(KEY_GENERAL_SENSITIVITY, generalSensitivity);
        editor.putInt(KEY_RED_DOT_SENSITIVITY, redDotSensitivity);
        editor.putInt(KEY_SCOPE_2X_SENSITIVITY, scope2xSensitivity);
        editor.putInt(KEY_SCOPE_4X_SENSITIVITY, scope4xSensitivity);
        editor.putInt(KEY_SCOPE_AWM_SENSITIVITY, scopeAwmSensitivity);
        editor.putInt(KEY_BREATH_HOLD, breathHold);
        editor.putInt(KEY_DPI_SETTING, dpiSetting);
        editor.putString(KEY_SELECTED_COLOR, selectedColor);
        editor.putBoolean(KEY_SMART_AIM_ENABLED, smartAimEnabled);
        editor.putString(KEY_GFX_LEVEL, gfxLevel);
        editor.apply();
    }
    
    /**
     * Load saved settings
     * 
     * @return Map of settings
     */
    public Map<String, Object> loadSettings() {
        Map<String, Object> settings = new HashMap<>();
        settings.put(KEY_GENERAL_SENSITIVITY, preferences.getInt(KEY_GENERAL_SENSITIVITY, 65));
        settings.put(KEY_RED_DOT_SENSITIVITY, preferences.getInt(KEY_RED_DOT_SENSITIVITY, 75));
        settings.put(KEY_SCOPE_2X_SENSITIVITY, preferences.getInt(KEY_SCOPE_2X_SENSITIVITY, 58));
        settings.put(KEY_SCOPE_4X_SENSITIVITY, preferences.getInt(KEY_SCOPE_4X_SENSITIVITY, 42));
        settings.put(KEY_SCOPE_AWM_SENSITIVITY, preferences.getInt(KEY_SCOPE_AWM_SENSITIVITY, 35));
        settings.put(KEY_BREATH_HOLD, preferences.getInt(KEY_BREATH_HOLD, 80));
        settings.put(KEY_DPI_SETTING, preferences.getInt(KEY_DPI_SETTING, 800));
        settings.put(KEY_SELECTED_COLOR, preferences.getString(KEY_SELECTED_COLOR, "#FF5252"));
        settings.put(KEY_SMART_AIM_ENABLED, preferences.getBoolean(KEY_SMART_AIM_ENABLED, false));
        settings.put(KEY_GFX_LEVEL, preferences.getString(KEY_GFX_LEVEL, "standard"));
        return settings;
    }
    
    /**
     * Reset settings
     */
    public void resetSettings() {
        SharedPreferences.Editor editor = preferences.edit();
        editor.clear();
        editor.apply();
    }
    
    /**
     * Apply Smart Aim optimization to settings
     * 
     * @param currentSettings Current settings
     * @return Optimized settings
     */
    public Map<String, Integer> applySmartAim(Map<String, Integer> currentSettings) {
        Map<String, Integer> optimizedSettings = new HashMap<>(currentSettings);
        
        // Get DPI setting
        int dpiSetting = currentSettings.get("dpiSetting");
        
        // Apply Smart Aim optimization based on DPI
        if (dpiSetting <= 400) {
            // For low DPI, increase sensitivity slightly
            optimizedSettings.put("generalSensitivity", Math.min(100, currentSettings.get("generalSensitivity") + 5));
            optimizedSettings.put("redDotSensitivity", Math.min(100, currentSettings.get("redDotSensitivity") + 8));
            optimizedSettings.put("scope2xSensitivity", Math.min(100, currentSettings.get("scope2xSensitivity") + 10));
            optimizedSettings.put("scope4xSensitivity", Math.min(100, currentSettings.get("scope4xSensitivity") + 12));
            optimizedSettings.put("scopeAwmSensitivity", Math.min(100, currentSettings.get("scopeAwmSensitivity") + 15));
        } else if (dpiSetting <= 800) {
            // For medium DPI, balanced optimization
            optimizedSettings.put("generalSensitivity", Math.min(100, currentSettings.get("generalSensitivity") + 3));
            optimizedSettings.put("redDotSensitivity", Math.min(100, currentSettings.get("redDotSensitivity") + 5));
            optimizedSettings.put("scope2xSensitivity", Math.min(100, currentSettings.get("scope2xSensitivity") + 7));
            optimizedSettings.put("scope4xSensitivity", Math.min(100, currentSettings.get("scope4xSensitivity") + 8));
            optimizedSettings.put("scopeAwmSensitivity", Math.min(100, currentSettings.get("scopeAwmSensitivity") + 10));
        } else {
            // For high DPI, decrease sensitivity slightly
            optimizedSettings.put("generalSensitivity", Math.max(1, currentSettings.get("generalSensitivity") - 2));
            optimizedSettings.put("redDotSensitivity", Math.max(1, currentSettings.get("redDotSensitivity") - 3));
            optimizedSettings.put("scope2xSensitivity", Math.max(1, currentSettings.get("scope2xSensitivity") - 4));
            optimizedSettings.put("scope4xSensitivity", Math.max(1, currentSettings.get("scope4xSensitivity") - 5));
            optimizedSettings.put("scopeAwmSensitivity", Math.max(1, currentSettings.get("scopeAwmSensitivity") - 6));
        }
        
        // Optimize breath hold
        optimizedSettings.put("breathHold", Math.min(100, currentSettings.get("breathHold") + 5));
        
        return optimizedSettings;
    }
    
    /**
     * Disable Smart Aim
     */
    public void disableSmartAim() {
        SharedPreferences.Editor editor = preferences.edit();
        editor.putBoolean(KEY_SMART_AIM_ENABLED, false);
        editor.apply();
    }
    
    /**
     * Get device analyzer
     * 
     * @return Device analyzer
     */
    public DeviceAnalyzer getDeviceAnalyzer() {
        return deviceAnalyzer;
    }
}
