<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="8dp">

    <!-- Pro Status Container -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="8dp">

        <!-- Pro Status Header with Glow Effect -->
        <TextView
            android:id="@+id/pro_status_header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/pro_status_active"
            android:fontFamily="@font/orbitron_bold"
            android:textColor="@color/cyber_neon_gold"
            android:textSize="14sp"
            android:textStyle="bold"
            android:gravity="center"
            android:shadowColor="@color/cyber_neon_gold_dim"
            android:shadowDx="0"
            android:shadowDy="0"
            android:shadowRadius="8"
            android:layout_marginBottom="8dp"/>

        <!-- Pro Status Card -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="4dp"
            app:cardBackgroundColor="@color/cyber_card_bg"
            app:cardCornerRadius="12dp"
            app:cardElevation="6dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="10dp"
                android:background="@drawable/premium_glow_bg">

                <!-- Pro Crown Icon -->
                <ImageView
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_gravity="center"
                    android:src="@drawable/ic_premium_crown"
                    android:layout_marginBottom="6dp"
                    android:contentDescription="@string/premium_crown" />

                <!-- Pro Status Message -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/currently_in_pro_mode"
                    android:fontFamily="@font/font_for_ar_en"
                    android:textColor="@color/cyber_neon_gold"
                    android:textSize="13sp"
                    android:textStyle="bold"
                    android:gravity="center"
                    android:layout_marginBottom="6dp"/>

                <!-- Pro Benefits Summary -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/pro_benefits_summary"
                    android:fontFamily="@font/font_for_ar_en"
                    android:textColor="@color/cyber_text_primary"
                    android:textSize="11sp"
                    android:gravity="center"
                    android:layout_marginBottom="8dp"/>

                <!-- Subscription Management Section -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:background="@drawable/cyber_border_gold"
                    android:padding="8dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/subscription_management"
                        android:fontFamily="@font/font_for_ar_en"
                        android:textColor="@color/cyber_neon_gold"
                        android:textSize="11sp"
                        android:textStyle="bold"
                        android:gravity="center"
                        android:layout_marginBottom="4dp"/>

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/cancel_subscription_info"
                        android:fontFamily="@font/font_for_ar_en"
                        android:textColor="@color/cyber_text_secondary"
                        android:textSize="9sp"
                        android:gravity="center"
                        android:layout_marginBottom="6dp"/>

                    <!-- Manage Subscription Button -->
                    <Button
                        android:id="@+id/btn_manage_subscription"
                        android:layout_width="match_parent"
                        android:layout_height="36dp"
                        android:text="@string/manage_subscription"
                        android:background="@drawable/neon_button_bg_gold"
                        android:textColor="@color/card_background"
                        android:fontFamily="@font/rajdhani_bold"
                        android:textSize="12sp"
                        android:layout_marginBottom="6dp"/>

                    <!-- Google Play Transparency Info -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/google_play_transparency"
                        android:fontFamily="@font/font_for_ar_en"
                        android:textColor="@color/cyber_text_secondary"
                        android:textSize="8sp"
                        android:gravity="center"
                        android:autoLink="web"/>

                </LinearLayout>
            </LinearLayout>
        </androidx.cardview.widget.CardView>
    </LinearLayout>
</FrameLayout>
