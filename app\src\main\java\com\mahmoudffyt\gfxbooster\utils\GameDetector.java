package com.mahmoudffyt.gfxbooster.utils;

import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.graphics.drawable.Drawable;
import android.util.Log;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * Utility class for detecting installed games and apps
 */
public class GameDetector {
    private static final String TAG = "GameDetector";
    
    /**
     * Game/App information model
     */
    public static class AppInfo {
        public String appName;
        public String packageName;
        public Drawable icon;
        public boolean isGame;
        
        public AppInfo(String appName, String packageName, Drawable icon, boolean isGame) {
            this.appName = appName;
            this.packageName = packageName;
            this.icon = icon;
            this.isGame = isGame;
        }
    }
    
    /**
     * Get list of installed games using multiple detection methods
     */
    public static List<AppInfo> getInstalledGames(Context context) {
        List<AppInfo> games = new ArrayList<>();

        try {
            PackageManager pm = context.getPackageManager();

            // Method 1: Get launchable apps and filter by game keywords
            games.addAll(getLaunchableGames(context, pm));

            // Method 2: Check all installed apps for game indicators
            games.addAll(getInstalledGameApps(context, pm));

            // Remove duplicates based on package name
            List<AppInfo> uniqueGames = removeDuplicates(games);

            // Sort games alphabetically
            Collections.sort(uniqueGames, new Comparator<AppInfo>() {
                @Override
                public int compare(AppInfo a1, AppInfo a2) {
                    return a1.appName.compareToIgnoreCase(a2.appName);
                }
            });

            Log.d(TAG, "Total games found: " + uniqueGames.size());
            return uniqueGames;

        } catch (Exception e) {
            Log.e(TAG, "Error getting installed games: " + e.getMessage());
        }

        return games;
    }

    /**
     * Method 1: Get launchable games using CATEGORY_LAUNCHER
     */
    private static List<AppInfo> getLaunchableGames(Context context, PackageManager pm) {
        List<AppInfo> games = new ArrayList<>();

        try {
            // Get all launchable apps
            Intent intent = new Intent(Intent.ACTION_MAIN, null);
            intent.addCategory(Intent.CATEGORY_LAUNCHER);

            List<ResolveInfo> allApps = pm.queryIntentActivities(intent, 0);

            for (ResolveInfo info : allApps) {
                try {
                    String appName = info.loadLabel(pm).toString();
                    String packageName = info.activityInfo.packageName;

                    // Skip system apps
                    ApplicationInfo appInfo = pm.getApplicationInfo(packageName, 0);
                    if ((appInfo.flags & ApplicationInfo.FLAG_SYSTEM) != 0) {
                        continue;
                    }

                    // Check if it's a game using keywords
                    if (isGameByKeywords(appName, packageName)) {
                        Drawable icon = info.loadIcon(pm);
                        games.add(new AppInfo(appName, packageName, icon, true));
                        Log.d(TAG, "Found launchable game: " + appName + " (" + packageName + ")");
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error processing launchable app: " + e.getMessage());
                }
            }

        } catch (Exception e) {
            Log.e(TAG, "Error getting launchable games: " + e.getMessage());
        }

        return games;
    }

    /**
     * Method 2: Check all installed apps for game indicators
     */
    private static List<AppInfo> getInstalledGameApps(Context context, PackageManager pm) {
        List<AppInfo> games = new ArrayList<>();

        try {
            List<ApplicationInfo> installedApps = pm.getInstalledApplications(PackageManager.GET_META_DATA);

            for (ApplicationInfo appInfo : installedApps) {
                try {
                    // Skip system apps
                    if ((appInfo.flags & ApplicationInfo.FLAG_SYSTEM) != 0) {
                        continue;
                    }

                    String appName = pm.getApplicationLabel(appInfo).toString();
                    String packageName = appInfo.packageName;

                    // Check if it's a game using advanced detection
                    if (isGameApp(context, appInfo, pm)) {
                        Drawable icon = pm.getApplicationIcon(appInfo);
                        games.add(new AppInfo(appName, packageName, icon, true));
                        Log.d(TAG, "Found installed game: " + appName + " (" + packageName + ")");
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error processing installed app: " + e.getMessage());
                }
            }

        } catch (Exception e) {
            Log.e(TAG, "Error getting installed game apps: " + e.getMessage());
        }

        return games;
    }

    /**
     * Remove duplicate games based on package name
     */
    private static List<AppInfo> removeDuplicates(List<AppInfo> games) {
        List<AppInfo> uniqueGames = new ArrayList<>();
        List<String> seenPackages = new ArrayList<>();

        for (AppInfo game : games) {
            if (!seenPackages.contains(game.packageName)) {
                uniqueGames.add(game);
                seenPackages.add(game.packageName);
            }
        }

        return uniqueGames;
    }

    /**
     * Check if app is a game using simple keyword matching
     */
    private static boolean isGameByKeywords(String appName, String packageName) {
        String lowerAppName = appName.toLowerCase();
        String lowerPackageName = packageName.toLowerCase();

        // Enhanced game keywords list
        String[] gameKeywords = {
            // Basic game terms
            "game", "games", "gaming", "play", "player", "battle", "arena", "fight",
            "fighting", "shoot", "shooting", "shooter", "war", "warfare", "combat",
            "action", "adventure", "puzzle", "racing", "race", "racer", "sport", "sports",
            "strategy", "simulation", "rpg", "mmo", "fps", "moba", "arcade",

            // Popular game names and franchises
            "pubg", "freefire", "freefireth", "garena", "clash", "candy", "angry",
            "temple", "subway", "hill", "plants", "zombies", "zombie", "cut", "fruit",
            "ninja", "bird", "run", "runner", "jump", "jumper", "dash", "rush", "escape",
            "minecraft", "roblox", "among", "fortnite", "callofduty", "cod", "mobile",

            // Game mechanics and genres
            "craft", "crafting", "build", "builder", "building", "farm", "farming",
            "city", "tycoon", "casino", "poker", "card", "cards", "chess", "board",
            "trivia", "word", "words", "match", "matching", "bubble", "jewel", "gem",
            "diamond", "treasure", "quest", "dungeon", "castle", "kingdom", "empire",
            "world", "universe", "galaxy", "space", "star", "planet", "alien", "robot",
            "mech", "tank", "plane", "car", "cars", "bike", "truck", "train", "ship",
            "boat", "helicopter", "vehicle",

            // Sports games
            "soccer", "football", "basketball", "tennis", "golf", "baseball", "hockey",
            "cricket", "volleyball", "badminton", "bowling", "pool", "snooker", "dart",
            "fifa", "pes", "nba", "nfl", "mlb", "nhl",

            // Action and RPG terms
            "hero", "heroes", "legend", "legends", "saga", "epic", "ultimate", "super",
            "mega", "ultra", "extreme", "master", "champion", "king", "queen", "lord",
            "god", "titan", "giant", "monster", "beast", "dragon", "magic", "spell",
            "wizard", "witch", "knight", "warrior", "soldier", "army", "legion",
            "fantasy", "medieval", "ancient", "future", "cyber", "mecha",

            // Package name specific keywords
            "studio", "entertainment", "interactive", "digital", "software", "apps",
            "mobile", "android", "ios", "free", "premium", "pro", "plus", "hd", "3d",
            "online", "multiplayer", "single", "offline", "casual", "hardcore",

            // Popular publishers and developers (including package variations)
            "tencent", "supercell", "king", "zynga", "gameloft", "ubisoft", "ea",
            "electronic", "arts", "activision", "blizzard", "riot", "valve", "epic",
            "unity", "unreal", "mihoyo", "netease", "nexon", "bandai", "namco",
            "square", "enix", "capcom", "konami", "sega", "nintendo", "sony", "microsoft",

            // Mobile game specific terms
            "tap", "touch", "swipe", "idle", "clicker", "endless", "infinite", "survival",
            "defense", "tower", "td", "rts", "turn", "based", "real", "time", "live",
            "pvp", "pve", "guild", "clan", "alliance", "team", "squad", "battle", "royale"
        };

        // Check if app name or package name contains any game keywords
        for (String keyword : gameKeywords) {
            if (lowerAppName.contains(keyword)) {
                Log.d(TAG, "Game detected by APP NAME: " + appName + " (keyword: " + keyword + ")");
                return true;
            }
            if (lowerPackageName.contains(keyword)) {
                Log.d(TAG, "Game detected by PACKAGE NAME: " + packageName + " (keyword: " + keyword + ")");
                return true;
            }
        }

        return false;
    }

    /**
     * Get list of all user-installed apps (excluding system apps)
     */
    public static List<AppInfo> getAllUserApps(Context context) {
        List<AppInfo> apps = new ArrayList<>();

        try {
            PackageManager pm = context.getPackageManager();

            // Method 1: Get all launchable apps
            Intent intent = new Intent(Intent.ACTION_MAIN, null);
            intent.addCategory(Intent.CATEGORY_LAUNCHER);
            List<ResolveInfo> launchableApps = pm.queryIntentActivities(intent, 0);

            for (ResolveInfo info : launchableApps) {
                try {
                    String appName = info.loadLabel(pm).toString();
                    String packageName = info.activityInfo.packageName;

                    // Skip system apps
                    ApplicationInfo appInfo = pm.getApplicationInfo(packageName, 0);
                    if ((appInfo.flags & ApplicationInfo.FLAG_SYSTEM) != 0) {
                        continue;
                    }

                    Drawable icon = info.loadIcon(pm);

                    // Check if it's a game using both methods
                    boolean isGame = isGameByKeywords(appName, packageName) ||
                                   isGameApp(context, appInfo, pm);

                    apps.add(new AppInfo(appName, packageName, icon, isGame));
                    Log.d(TAG, "Found launchable app: " + appName + " (" + packageName + ") - Game: " + isGame);

                } catch (Exception e) {
                    Log.e(TAG, "Error processing launchable app: " + e.getMessage());
                }
            }

            // Method 2: Add any missing apps from installed apps list
            List<ApplicationInfo> installedApps = pm.getInstalledApplications(PackageManager.GET_META_DATA);
            List<String> existingPackages = new ArrayList<>();

            // Get existing package names
            for (AppInfo app : apps) {
                existingPackages.add(app.packageName);
            }

            for (ApplicationInfo appInfo : installedApps) {
                try {
                    // Skip system apps and already added apps
                    if ((appInfo.flags & ApplicationInfo.FLAG_SYSTEM) != 0 ||
                        existingPackages.contains(appInfo.packageName)) {
                        continue;
                    }

                    String appName = pm.getApplicationLabel(appInfo).toString();
                    String packageName = appInfo.packageName;
                    Drawable icon = pm.getApplicationIcon(appInfo);

                    // Check if it's a game
                    boolean isGame = isGameByKeywords(appName, packageName) ||
                                   isGameApp(context, appInfo, pm);

                    apps.add(new AppInfo(appName, packageName, icon, isGame));
                    Log.d(TAG, "Found additional app: " + appName + " (" + packageName + ") - Game: " + isGame);

                } catch (Exception e) {
                    Log.e(TAG, "Error processing installed app: " + e.getMessage());
                }
            }

            // Sort apps alphabetically
            Collections.sort(apps, new Comparator<AppInfo>() {
                @Override
                public int compare(AppInfo a1, AppInfo a2) {
                    return a1.appName.compareToIgnoreCase(a2.appName);
                }
            });

            Log.d(TAG, "Total user apps found: " + apps.size());

        } catch (Exception e) {
            Log.e(TAG, "Error getting all user apps: " + e.getMessage());
        }

        return apps;
    }
    
    /**
     * Check if an app is a game using various detection methods
     */
    private static boolean isGameApp(Context context, ApplicationInfo appInfo, PackageManager pm) {
        try {
            String packageName = appInfo.packageName.toLowerCase();
            String appName = "";
            try {
                appName = pm.getApplicationLabel(appInfo).toString().toLowerCase();
            } catch (Exception e) {
                // If we can't get app name, just use package name
            }

            // Check for game-related keywords in package name or app name
            String[] gameKeywords = {
                "game", "play", "arcade", "action", "adventure", "puzzle", "racing",
                "sport", "strategy", "simulation", "rpg", "mmo", "fps", "battle",
                "war", "fight", "shoot", "gun", "weapon", "hero", "legend", "saga",
                "quest", "dungeon", "castle", "kingdom", "empire", "world", "craft",
                "build", "farm", "city", "tycoon", "casino", "poker", "card", "chess",
                "board", "trivia", "word", "match", "candy", "bubble", "jewel",
                "zombie", "dragon", "magic", "fantasy", "space", "star", "galaxy",
                "car", "bike", "truck", "train", "plane", "ship", "boat", "soccer",
                "football", "basketball", "tennis", "golf", "baseball", "hockey",
                "cricket", "volleyball", "badminton", "bowling", "pool", "snooker",
                // Popular game names
                "pubg", "fortnite", "minecraft", "roblox", "among", "clash", "candy",
                "angry", "temple", "subway", "hill", "plants", "zombies", "cut",
                "fruit", "ninja", "bird", "run", "jump", "dash", "rush", "escape",
                "free", "fire", "garena", "tencent", "supercell", "king", "zynga"
            };

            // Check if package name or app name contains any game keywords
            for (String keyword : gameKeywords) {
                if (packageName.contains(keyword) || appName.contains(keyword)) {
                    return true;
                }
            }

            // Additional check: if it's in Games category (Android 8.0+)
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                try {
                    if (appInfo.category == ApplicationInfo.CATEGORY_GAME) {
                        return true;
                    }
                } catch (Exception e) {
                    // Ignore if category is not available
                }
            }

            return false;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * Check if an app is installed
     */
    public static boolean isAppInstalled(Context context, String packageName) {
        try {
            PackageManager pm = context.getPackageManager();
            pm.getPackageInfo(packageName, PackageManager.GET_ACTIVITIES);
            return true;
        } catch (PackageManager.NameNotFoundException e) {
            return false;
        }
    }
    
    /**
     * Launch an app by package name
     */
    public static boolean launchApp(Context context, String packageName) {
        try {
            PackageManager pm = context.getPackageManager();
            Intent launchIntent = pm.getLaunchIntentForPackage(packageName);
            if (launchIntent != null) {
                context.startActivity(launchIntent);
                return true;
            }
        } catch (Exception e) {
            Log.e(TAG, "Error launching app: " + e.getMessage());
        }
        return false;
    }
}
