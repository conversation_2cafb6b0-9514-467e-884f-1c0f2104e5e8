package com.mahmoudffyt.gfxbooster.utils;

import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.graphics.drawable.Drawable;
import android.util.Log;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * Utility class for detecting installed games and apps
 */
public class GameDetector {
    private static final String TAG = "GameDetector";
    
    /**
     * Game/App information model
     */
    public static class AppInfo {
        public String appName;
        public String packageName;
        public Drawable icon;
        public boolean isGame;
        
        public AppInfo(String appName, String packageName, Drawable icon, boolean isGame) {
            this.appName = appName;
            this.packageName = packageName;
            this.icon = icon;
            this.isGame = isGame;
        }
    }
    
    /**
     * Get list of installed games using CATEGORY_GAME
     */
    public static List<AppInfo> getInstalledGames(Context context) {
        List<AppInfo> games = new ArrayList<>();
        
        try {
            PackageManager pm = context.getPackageManager();
            
            // Create intent with CATEGORY_GAME to find games
            Intent mainIntent = new Intent(Intent.ACTION_MAIN, null);
            mainIntent.addCategory(Intent.CATEGORY_GAME);
            
            List<ResolveInfo> gameApps = pm.queryIntentActivities(mainIntent, 0);
            
            for (ResolveInfo info : gameApps) {
                try {
                    String appName = info.loadLabel(pm).toString();
                    String packageName = info.activityInfo.packageName;
                    Drawable icon = info.loadIcon(pm);
                    
                    // Skip system apps
                    ApplicationInfo appInfo = pm.getApplicationInfo(packageName, 0);
                    if ((appInfo.flags & ApplicationInfo.FLAG_SYSTEM) == 0) {
                        games.add(new AppInfo(appName, packageName, icon, true));
                        Log.d(TAG, "Found game: " + appName + " (" + packageName + ")");
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error processing game: " + e.getMessage());
                }
            }
            
            // Sort games alphabetically
            Collections.sort(games, new Comparator<AppInfo>() {
                @Override
                public int compare(AppInfo a1, AppInfo a2) {
                    return a1.appName.compareToIgnoreCase(a2.appName);
                }
            });
            
        } catch (Exception e) {
            Log.e(TAG, "Error getting installed games: " + e.getMessage());
        }
        
        return games;
    }
    
    /**
     * Get list of all user-installed apps (excluding system apps)
     */
    public static List<AppInfo> getAllUserApps(Context context) {
        List<AppInfo> apps = new ArrayList<>();
        
        try {
            PackageManager pm = context.getPackageManager();
            List<ApplicationInfo> installedApps = pm.getInstalledApplications(PackageManager.GET_META_DATA);
            
            for (ApplicationInfo appInfo : installedApps) {
                try {
                    // Skip system apps
                    if ((appInfo.flags & ApplicationInfo.FLAG_SYSTEM) == 0) {
                        String appName = pm.getApplicationLabel(appInfo).toString();
                        String packageName = appInfo.packageName;
                        Drawable icon = pm.getApplicationIcon(appInfo);
                        
                        // Check if it's a game
                        boolean isGame = isGameApp(context, packageName);
                        
                        apps.add(new AppInfo(appName, packageName, icon, isGame));
                        Log.d(TAG, "Found app: " + appName + " (" + packageName + ") - Game: " + isGame);
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error processing app: " + e.getMessage());
                }
            }
            
            // Sort apps alphabetically
            Collections.sort(apps, new Comparator<AppInfo>() {
                @Override
                public int compare(AppInfo a1, AppInfo a2) {
                    return a1.appName.compareToIgnoreCase(a2.appName);
                }
            });
            
        } catch (Exception e) {
            Log.e(TAG, "Error getting all user apps: " + e.getMessage());
        }
        
        return apps;
    }
    
    /**
     * Check if an app is a game using CATEGORY_GAME
     */
    private static boolean isGameApp(Context context, String packageName) {
        try {
            PackageManager pm = context.getPackageManager();
            Intent mainIntent = new Intent(Intent.ACTION_MAIN, null);
            mainIntent.addCategory(Intent.CATEGORY_GAME);
            mainIntent.setPackage(packageName);
            
            List<ResolveInfo> gameApps = pm.queryIntentActivities(mainIntent, 0);
            return !gameApps.isEmpty();
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * Check if an app is installed
     */
    public static boolean isAppInstalled(Context context, String packageName) {
        try {
            PackageManager pm = context.getPackageManager();
            pm.getPackageInfo(packageName, PackageManager.GET_ACTIVITIES);
            return true;
        } catch (PackageManager.NameNotFoundException e) {
            return false;
        }
    }
    
    /**
     * Launch an app by package name
     */
    public static boolean launchApp(Context context, String packageName) {
        try {
            PackageManager pm = context.getPackageManager();
            Intent launchIntent = pm.getLaunchIntentForPackage(packageName);
            if (launchIntent != null) {
                context.startActivity(launchIntent);
                return true;
            }
        } catch (Exception e) {
            Log.e(TAG, "Error launching app: " + e.getMessage());
        }
        return false;
    }
}
