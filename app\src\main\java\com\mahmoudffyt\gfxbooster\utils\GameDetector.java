package com.mahmoudffyt.gfxbooster.utils;

import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.graphics.drawable.Drawable;
import android.util.Log;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * Utility class for detecting installed games and apps
 */
public class GameDetector {
    private static final String TAG = "GameDetector";
    
    /**
     * Game/App information model
     */
    public static class AppInfo {
        public String appName;
        public String packageName;
        public Drawable icon;
        public boolean isGame;
        
        public AppInfo(String appName, String packageName, Drawable icon, boolean isGame) {
            this.appName = appName;
            this.packageName = packageName;
            this.icon = icon;
            this.isGame = isGame;
        }
    }
    
    /**
     * Get list of installed games using game detection logic
     */
    public static List<AppInfo> getInstalledGames(Context context) {
        List<AppInfo> games = new ArrayList<>();

        try {
            PackageManager pm = context.getPackageManager();
            List<ApplicationInfo> installedApps = pm.getInstalledApplications(PackageManager.GET_META_DATA);

            for (ApplicationInfo appInfo : installedApps) {
                try {
                    // Check if it's a game
                    if (isGameApp(context, appInfo, pm)) {
                        String appName = pm.getApplicationLabel(appInfo).toString();
                        String packageName = appInfo.packageName;
                        Drawable icon = pm.getApplicationIcon(appInfo);

                        // Skip system apps
                        if ((appInfo.flags & ApplicationInfo.FLAG_SYSTEM) == 0) {
                            games.add(new AppInfo(appName, packageName, icon, true));
                            Log.d(TAG, "Found game: " + appName + " (" + packageName + ")");
                        }
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error processing app: " + e.getMessage());
                }
            }

            // Sort games alphabetically
            Collections.sort(games, new Comparator<AppInfo>() {
                @Override
                public int compare(AppInfo a1, AppInfo a2) {
                    return a1.appName.compareToIgnoreCase(a2.appName);
                }
            });

        } catch (Exception e) {
            Log.e(TAG, "Error getting installed games: " + e.getMessage());
        }

        return games;
    }
    
    /**
     * Get list of all user-installed apps (excluding system apps)
     */
    public static List<AppInfo> getAllUserApps(Context context) {
        List<AppInfo> apps = new ArrayList<>();
        
        try {
            PackageManager pm = context.getPackageManager();
            List<ApplicationInfo> installedApps = pm.getInstalledApplications(PackageManager.GET_META_DATA);
            
            for (ApplicationInfo appInfo : installedApps) {
                try {
                    // Skip system apps
                    if ((appInfo.flags & ApplicationInfo.FLAG_SYSTEM) == 0) {
                        String appName = pm.getApplicationLabel(appInfo).toString();
                        String packageName = appInfo.packageName;
                        Drawable icon = pm.getApplicationIcon(appInfo);
                        
                        // Check if it's a game
                        boolean isGame = isGameApp(context, appInfo, pm);
                        
                        apps.add(new AppInfo(appName, packageName, icon, isGame));
                        Log.d(TAG, "Found app: " + appName + " (" + packageName + ") - Game: " + isGame);
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error processing app: " + e.getMessage());
                }
            }
            
            // Sort apps alphabetically
            Collections.sort(apps, new Comparator<AppInfo>() {
                @Override
                public int compare(AppInfo a1, AppInfo a2) {
                    return a1.appName.compareToIgnoreCase(a2.appName);
                }
            });
            
        } catch (Exception e) {
            Log.e(TAG, "Error getting all user apps: " + e.getMessage());
        }
        
        return apps;
    }
    
    /**
     * Check if an app is a game using various detection methods
     */
    private static boolean isGameApp(Context context, ApplicationInfo appInfo, PackageManager pm) {
        try {
            String packageName = appInfo.packageName.toLowerCase();
            String appName = "";
            try {
                appName = pm.getApplicationLabel(appInfo).toString().toLowerCase();
            } catch (Exception e) {
                // If we can't get app name, just use package name
            }

            // Check for game-related keywords in package name or app name
            String[] gameKeywords = {
                "game", "play", "arcade", "action", "adventure", "puzzle", "racing",
                "sport", "strategy", "simulation", "rpg", "mmo", "fps", "battle",
                "war", "fight", "shoot", "gun", "weapon", "hero", "legend", "saga",
                "quest", "dungeon", "castle", "kingdom", "empire", "world", "craft",
                "build", "farm", "city", "tycoon", "casino", "poker", "card", "chess",
                "board", "trivia", "word", "match", "candy", "bubble", "jewel",
                "zombie", "dragon", "magic", "fantasy", "space", "star", "galaxy",
                "car", "bike", "truck", "train", "plane", "ship", "boat", "soccer",
                "football", "basketball", "tennis", "golf", "baseball", "hockey",
                "cricket", "volleyball", "badminton", "bowling", "pool", "snooker",
                // Popular game names
                "pubg", "fortnite", "minecraft", "roblox", "among", "clash", "candy",
                "angry", "temple", "subway", "hill", "plants", "zombies", "cut",
                "fruit", "ninja", "bird", "run", "jump", "dash", "rush", "escape",
                "free", "fire", "garena", "tencent", "supercell", "king", "zynga"
            };

            // Check if package name or app name contains any game keywords
            for (String keyword : gameKeywords) {
                if (packageName.contains(keyword) || appName.contains(keyword)) {
                    return true;
                }
            }

            // Additional check: if it's in Games category (Android 8.0+)
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                try {
                    if (appInfo.category == ApplicationInfo.CATEGORY_GAME) {
                        return true;
                    }
                } catch (Exception e) {
                    // Ignore if category is not available
                }
            }

            return false;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * Check if an app is installed
     */
    public static boolean isAppInstalled(Context context, String packageName) {
        try {
            PackageManager pm = context.getPackageManager();
            pm.getPackageInfo(packageName, PackageManager.GET_ACTIVITIES);
            return true;
        } catch (PackageManager.NameNotFoundException e) {
            return false;
        }
    }
    
    /**
     * Launch an app by package name
     */
    public static boolean launchApp(Context context, String packageName) {
        try {
            PackageManager pm = context.getPackageManager();
            Intent launchIntent = pm.getLaunchIntentForPackage(packageName);
            if (launchIntent != null) {
                context.startActivity(launchIntent);
                return true;
            }
        } catch (Exception e) {
            Log.e(TAG, "Error launching app: " + e.getMessage());
        }
        return false;
    }
}
