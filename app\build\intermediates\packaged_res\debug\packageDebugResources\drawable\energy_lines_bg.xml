<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@android:color/transparent" />
        </shape>
    </item>
    <item>
        <rotate
            android:fromDegrees="45"
            android:pivotX="50%"
            android:pivotY="50%"
            android:toDegrees="45">
            <shape android:shape="line">
                <stroke
                    android:width="1dp"
                    android:color="#2000FFFF"
                    android:dashGap="20dp"
                    android:dashWidth="40dp" />
            </shape>
        </rotate>
    </item>
    <item>
        <rotate
            android:fromDegrees="135"
            android:pivotX="50%"
            android:pivotY="50%"
            android:toDegrees="135">
            <shape android:shape="line">
                <stroke
                    android:width="1dp"
                    android:color="#1500FFFF"
                    android:dashGap="30dp"
                    android:dashWidth="20dp" />
            </shape>
        </rotate>
    </item>
    <item>
        <rotate
            android:fromDegrees="0"
            android:pivotX="50%"
            android:pivotY="50%"
            android:toDegrees="0">
            <shape android:shape="line">
                <stroke
                    android:width="1dp"
                    android:color="#1000FFFF"
                    android:dashGap="25dp"
                    android:dashWidth="15dp" />
            </shape>
        </rotate>
    </item>
</layer-list>
