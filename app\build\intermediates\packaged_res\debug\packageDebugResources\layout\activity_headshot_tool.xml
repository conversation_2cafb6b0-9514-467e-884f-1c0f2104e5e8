<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/cyber_neon_gradient_bg"
    tools:context=".HeadshotToolActivity">

    <!-- Back Button -->
    <ImageView
        android:id="@+id/btn_back"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_margin="16dp"
        android:padding="12dp"
        android:src="@drawable/ic_back"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:clickable="true"
        android:focusable="true"
        app:tint="#80FFFFFF"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Speed Lines Background Animation -->
    <ImageView
        android:id="@+id/energy_lines_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:src="@drawable/blue_speed_lines"
        android:alpha="0.4"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Empty space holder -->
    <View
        android:id="@+id/top_space"
        android:layout_width="match_parent"
        android:layout_height="8dp"
        android:layout_marginTop="56dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Main Content ScrollView -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="8dp"
        android:fillViewport="true"
        android:scrollbars="none"
        android:overScrollMode="never"
        app:layout_constraintTop_toBottomOf="@+id/top_space"
        app:layout_constraintBottom_toTopOf="@+id/apply_button_container"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingBottom="24dp">

            <!-- Header Section -->
            <LinearLayout
                android:id="@+id/header_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="16dp">
<!--
                <ImageView
                    android:id="@+id/headshot_logo"
                    android:layout_width="100dp"
                    android:layout_height="100dp"
                    android:src="@drawable/headshot_logo" />
-->
                <TextView
                    android:id="@+id/headshot_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/headshot_tool_title"
                    android:textSize="24sp"
                    android:textColor="@color/cyber_neon_cyan"
                    android:textStyle="bold"
                    android:fontFamily="@font/font_for_ar_en"
                    android:layout_marginTop="8dp"
                    android:shadowColor="#80000000"
                    android:shadowDx="2"
                    android:shadowDy="2"
                    android:shadowRadius="3" />

                <TextView
                    android:id="@+id/headshot_subtitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/free_fire_optimization"
                    android:textSize="12sp"
                    android:textColor="@color/cyber_text_secondary"
                    android:fontFamily="@font/cairo_regular"
                    android:layout_marginTop="4dp" />
            </LinearLayout>

            <!-- Device Info Section (Compact) -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginTop="16dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="8dp"
                app:cardBackgroundColor="@color/cyber_card_bg">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="12dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/device_analysis"
                            android:textSize="14sp"
                            android:textColor="@color/cyber_neon_cyan"
                            android:textStyle="bold"
                            android:fontFamily="@font/font_for_ar_en" />

                        <ProgressBar
                            android:id="@+id/analyze_progress_bar"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginStart="8dp"
                            android:indeterminateTint="@color/cyber_neon_cyan"
                            style="?android:attr/progressBarStyle"
                            android:indeterminate="true" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/device_info_text"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="@string/analyzing_device"
                        android:textSize="12sp"
                        android:textColor="@color/cyber_text_secondary"
                        android:fontFamily="@font/cairo_regular" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Ad Container for General Section -->
            <FrameLayout
                android:id="@+id/ad_container_general"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:minHeight="100dp" />

            <!-- General Settings Section -->
            <androidx.cardview.widget.CardView
                android:id="@+id/general_section"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginTop="16dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="8dp"
                app:cardBackgroundColor="@color/cyber_card_bg">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/general"
                        android:textSize="18sp"
                        android:textColor="@color/cyber_neon_cyan"
                        android:textStyle="bold"
                        android:fontFamily="@font/font_for_ar_en" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/cyber_card_stroke"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="16dp" />

                    <!-- Sensitivity Slider -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/general_sensitivity"
                        android:textSize="16sp"

                        android:textColor="@color/cyber_text_primary"
                        android:fontFamily="@font/cairo_regular" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginTop="8dp"
                        android:gravity="center_vertical">

                        <SeekBar
                            android:id="@+id/general_sensitivity_slider"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:max="200"
                            android:progress="101"
                            android:progressTint="@color/cyber_neon_cyan"
                            android:thumbTint="@color/cyber_neon_cyan" />

                        <TextView
                            android:id="@+id/general_sensitivity_value"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="65"
                            android:textSize="16sp"
                            android:textColor="@color/cyber_neon_cyan"
                            android:fontFamily="@font/cairo_regular"
                            android:layout_marginStart="16dp" />
                    </LinearLayout>

                    <!-- Graphics Settings -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginTop="16dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/graphics_level"
                            android:textSize="16sp"
                            android:textColor="@color/cyber_text_primary"
                            android:fontFamily="@font/cairo_regular" />

                        <Button
                            android:id="@+id/gfx_select_button"
                            android:layout_width="wrap_content"
                            android:layout_height="36dp"
                            android:text="@string/standard"
                            android:textColor="#FFFFFF"
                            android:textSize="12sp"
                            android:fontFamily="sans-serif-condensed-medium"
                            android:background="@drawable/neon_button_bg"
                            android:paddingStart="12dp"
                            android:paddingEnd="12dp" />
                    </LinearLayout>

                    <!-- DPI Setting -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/dpi_setting"
                        android:textSize="16sp"
                        android:textColor="@color/cyber_text_primary"
                        android:fontFamily="@font/cairo_regular"
                        android:layout_marginTop="16dp" />

                    <RadioGroup
                        android:id="@+id/dpi_radio_group"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginTop="8dp">

                        <RadioButton
                            android:id="@+id/dpi_400"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="400"
                            android:textColor="@color/cyber_text_primary"
                            android:buttonTint="@color/cyber_neon_cyan"
                            android:layout_weight="1" />

                        <RadioButton
                            android:id="@+id/dpi_800"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="800"
                            android:textColor="@color/cyber_text_primary"
                            android:buttonTint="@color/cyber_neon_cyan"
                            android:layout_weight="1"
                            android:checked="true" />

                        <RadioButton
                            android:id="@+id/dpi_1600"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="1600"
                            android:textColor="@color/cyber_text_primary"
                            android:buttonTint="@color/cyber_neon_cyan"
                            android:layout_weight="1" />
                    </RadioGroup>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Red Dot Section
            <androidx.cardview.widget.CardView
                android:id="@+id/red_dot_section"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginTop="16dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="8dp"
                app:cardBackgroundColor="@color/cyber_card_bg">-->
                <!--
                               <LinearLayout
                                   android:layout_width="match_parent"
                                   android:layout_height="wrap_content"
                                   android:orientation="vertical"
                                   android:padding="16dp">

                                   <TextView
                                       android:layout_width="match_parent"
                                       android:layout_height="wrap_content"
                                       android:text="@string/red_dot"
                                       android:textSize="18sp"
                                       android:textColor="@color/cyber_neon_cyan"
                                       android:textStyle="bold"
                                       android:fontFamily="@font/rajdhani_bold" />

                                   <View
                                       android:layout_width="match_parent"
                                       android:layout_height="1dp"
                                       android:background="@color/cyber_card_stroke"
                                       android:layout_marginTop="8dp"
                                       android:layout_marginBottom="16dp" />

                                   Red Dot Sensitivity
                                   <TextView
                                       android:layout_width="match_parent"
                                       android:layout_height="wrap_content"
                                       android:text="@string/red_dot_sensitivity"
                                       android:textSize="16sp"
                                       android:textColor="@color/cyber_text_primary"
                                       android:fontFamily="@font/cairo_regular" />

                                   <LinearLayout
                                       android:layout_width="match_parent"
                                       android:layout_height="wrap_content"
                                       android:orientation="horizontal"
                                       android:layout_marginTop="8dp"
                                       android:gravity="center_vertical">

                                       <SeekBar
                                           android:id="@+id/red_dot_sensitivity_slider"
                                           android:layout_width="0dp"
                                           android:layout_height="wrap_content"
                                           android:layout_weight="1"
                                           android:max="100"
                                           android:progress="75"
                                           android:progressTint="@color/cyber_neon_cyan"
                                           android:thumbTint="@color/cyber_neon_cyan" />

                                       <TextView
                                           android:id="@+id/red_dot_sensitivity_value"
                                           android:layout_width="wrap_content"
                                           android:layout_height="wrap_content"
                                           android:text="75"
                                           android:textSize="16sp"
                                           android:textColor="@color/cyber_neon_cyan"
                                           android:fontFamily="@font/cairo_regular"
                                           android:layout_marginStart="16dp" />
                                   </LinearLayout>


                               </LinearLayout>
            </androidx.cardview.widget.CardView>
-->
            <!-- 2x Scope Section -->
            <androidx.cardview.widget.CardView
                android:id="@+id/scope_2x_section"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginTop="16dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="8dp"
                app:cardBackgroundColor="@color/cyber_card_bg">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/scope_2x"
                        android:textSize="18sp"
                        android:textColor="@color/cyber_neon_cyan"
                        android:textStyle="bold"
                        android:fontFamily="@font/font_for_ar_en" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/cyber_card_stroke"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="16dp" />

                    <!-- 2x Scope Sensitivity -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/scope_2x_sensitivity"
                        android:textSize="16sp"
                        android:textColor="@color/cyber_text_primary"
                        android:fontFamily="@font/cairo_regular" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginTop="8dp"
                        android:gravity="center_vertical">

                        <SeekBar
                            android:id="@+id/scope_2x_sensitivity_slider"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:max="200"
                            android:progress="58"
                            android:progressTint="@color/cyber_neon_cyan"
                            android:thumbTint="@color/cyber_neon_cyan" />

                        <TextView
                            android:id="@+id/scope_2x_sensitivity_value"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="58"
                            android:textSize="16sp"
                            android:textColor="@color/cyber_neon_cyan"
                            android:fontFamily="@font/cairo_regular"
                            android:layout_marginStart="16dp" />
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- 4x Scope Section -->
            <androidx.cardview.widget.CardView
                android:id="@+id/scope_4x_section"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginTop="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/cyber_card_bg">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/scope_4x"
                        android:textSize="18sp"
                        android:textColor="@color/neon_blue"
                        android:textStyle="bold"
                        android:fontFamily="@font/font_for_ar_en" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#334FC3F7"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="16dp" />

                    <!-- 4x Scope Sensitivity -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/scope_4x_sensitivity"
                        android:textSize="16sp"
                        android:textColor="#FFFFFF"
                        android:fontFamily="@font/cairo_regular" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginTop="8dp"
                        android:gravity="center_vertical">

                        <SeekBar
                            android:id="@+id/scope_4x_sensitivity_slider"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:max="200"
                            android:progress="42"
                            android:progressTint="@color/neon_blue"
                            android:thumbTint="@color/neon_blue" />

                        <TextView
                            android:id="@+id/scope_4x_sensitivity_value"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="42"
                            android:textSize="16sp"
                            android:textColor="@color/neon_blue"
                            android:fontFamily="sans-serif-medium"
                            android:layout_marginStart="16dp" />
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- AWM Scope Section -->
            <androidx.cardview.widget.CardView
                android:id="@+id/scope_awm_section"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginTop="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/cyber_card_bg">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/awm_scope_title"
                        android:textSize="18sp"
                        android:textColor="@color/neon_blue"
                        android:textStyle="bold"
                        android:fontFamily="@font/font_for_ar_en" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#334FC3F7"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="16dp" />

                    <!-- AWM Scope Sensitivity -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/awm_scope_sensitivity_label"
                        android:textSize="16sp"
                        android:textColor="#FFFFFF"
                        android:fontFamily="@font/cairo_regular" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginTop="8dp"
                        android:gravity="center_vertical">

                        <SeekBar
                            android:id="@+id/scope_awm_sensitivity_slider"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:max="200"
                            android:progress="35"
                            android:progressTint="#4FC3F7"
                            android:thumbTint="#4FC3F7" />

                        <TextView
                            android:id="@+id/scope_awm_sensitivity_value"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="35"
                            android:textSize="16sp"
                            android:textColor="#4FC3F7"
                            android:fontFamily="sans-serif-medium"
                            android:layout_marginStart="16dp" />
                    </LinearLayout>


                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Ad Container for AWM SCOPE Section -->
            <FrameLayout
                android:id="@+id/ad_container_awm"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:minHeight="100dp" />

            <!-- Free Camera Button Section -->
            <androidx.cardview.widget.CardView
                android:id="@+id/free_camera_section"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginTop="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/cyber_card_bg">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/free_camera_button"
                        android:textSize="18sp"
                        android:textColor="@color/cyber_neon_cyan"
                        android:textStyle="bold"
                        android:fontFamily="@font/font_for_ar_en" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/cyber_card_stroke"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="16dp" />

                    <!-- Free Camera Button Sensitivity -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/free_camera_sensitivity"
                        android:textSize="16sp"
                        android:textColor="@color/cyber_text_primary"
                        android:fontFamily="@font/cairo_regular" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginTop="8dp"
                        android:gravity="center_vertical">

                        <SeekBar
                            android:id="@+id/free_camera_sensitivity_slider"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:max="200"
                            android:progress="50"
                            android:progressTint="@color/cyber_neon_cyan"
                            android:thumbTint="@color/cyber_neon_cyan" />

                        <TextView
                            android:id="@+id/free_camera_sensitivity_value"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="50"
                            android:textSize="16sp"
                            android:textColor="@color/cyber_neon_cyan"
                            android:fontFamily="@font/cairo_regular"
                            android:layout_marginStart="16dp" />
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>
        </LinearLayout>
    </ScrollView>

    <!-- Action Buttons Container (Compact) -->
    <LinearLayout
        android:id="@+id/apply_button_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/cyber_dark_start"
        android:padding="8dp"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- Main Row: Apply + Smart Aim -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/apply_settings_button"
                android:layout_width="0dp"
                android:layout_height="60dp"
                android:layout_weight="2"
                android:layout_marginEnd="4dp"
                android:text="@string/apply_settings_headshot"
                android:textColor="#FFFFFF"
                android:textSize="14sp"
                android:fontFamily="@font/font_for_ar_en"
                android:background="@drawable/neon_button_bg" />


        </LinearLayout>

        <!-- Secondary Row: Reset, Test, Share, Aim Overlay -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="4dp">



            <Button
                android:id="@+id/test_button"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:layout_marginEnd="4dp"
                android:text="@string/test_aim"
                android:textColor="#FFFFFF"
                android:textSize="12sp"
                android:fontFamily="@font/cairo_regular"
                android:background="@drawable/card_bg_translucent" />

            <Button
                android:id="@+id/share_button"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:layout_marginStart="4dp"
                android:text="@string/share"
                android:textColor="#FFFFFF"
                android:textSize="12sp"
                android:fontFamily="@font/cairo_regular"
                android:background="@drawable/card_bg_translucent" />
        </LinearLayout>

        <!-- Aim Overlay Button -->
        <Button
            android:id="@+id/aim_overlay_button"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_marginTop="4dp"
            android:text="@string/aim_crosshair"
            android:textColor="#FFFFFF"
            android:textSize="12sp"
            android:textStyle="bold"
            android:fontFamily="@font/font_for_ar_en"
            android:background="@drawable/neon_button_bg_premium"
            android:drawableStart="@drawable/ic_crosshair"
            android:paddingStart="12dp"
            android:paddingEnd="12dp"
            android:gravity="center" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
