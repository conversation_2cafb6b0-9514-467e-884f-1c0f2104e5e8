<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\assets"><file name="animations.css" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\assets\animations.css"/><file name="app_guide_new.html" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\assets\app_guide_new.html"/><file name="lightning_animation.json" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\assets\lightning_animation.json"/><file name="ottieAnimationView.json" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\assets\ottieAnimationView.json"/><file name="script.js" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\assets\script.js"/><file name="styles.css" path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\assets\styles.css"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\build\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>