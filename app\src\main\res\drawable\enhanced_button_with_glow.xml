<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Outer glow effect -->
    <item>
        <shape android:shape="rectangle">
            <corners android:radius="20dp" />
            <gradient
                android:angle="135"
                android:endColor="#7000ccff"
                android:centerColor="#9000ccff"
                android:startColor="#7000ccff"
                android:type="linear" />
            <padding
                android:bottom="8dp"
                android:left="8dp"
                android:right="8dp"
                android:top="8dp" />
        </shape>
    </item>

    <!-- Shadow -->
    <item>
        <shape android:shape="rectangle">
            <corners android:radius="16dp" />
            <padding
                android:bottom="3dp"
                android:left="1dp"
                android:right="3dp"
                android:top="1dp" />
            <solid android:color="@color/shadow_color" />
        </shape>
    </item>

    <!-- Button background with gradient -->
    <item>
        <shape android:shape="rectangle">
            <corners android:radius="16dp" />
            <gradient
                android:angle="135"
                android:endColor="@color/gradient_end"
                android:centerColor="@color/gradient_center"
                android:startColor="@color/gradient_start"
                android:type="linear" />
        </shape>
    </item>

    <!-- Inner area -->
    <item android:bottom="2dp" android:left="1dp" android:right="2dp" android:top="1dp">
        <shape android:shape="rectangle">
            <corners android:radius="14dp" />
            <gradient
                android:angle="135"
                android:endColor="#162c36"
                android:centerColor="#203a43"
                android:startColor="#0f2027"
                android:type="linear" />
        </shape>
    </item>

    <!-- Inner shadow to create depth -->
    <item android:bottom="2dp" android:left="1dp" android:right="2dp" android:top="1dp">
        <shape android:shape="rectangle">
            <corners android:radius="14dp" />
            <gradient
                android:angle="90"
                android:endColor="#00000000"
                android:startColor="#10000000"
                android:type="linear" />
        </shape>
    </item>

    <!-- Highlight at top -->
    <item android:top="2dp" android:left="2dp" android:right="2dp" android:bottom="30dp">
        <shape android:shape="rectangle">
            <corners android:topLeftRadius="12dp" android:topRightRadius="12dp" />
            <gradient
                android:angle="270"
                android:endColor="#00FFFFFF"
                android:startColor="#40FFFFFF"
                android:type="linear" />
        </shape>
    </item>
</layer-list>
