package com.mahmoudffyt.gfxbooster;

import android.content.Context;
import android.content.SharedPreferences;

import java.util.HashMap;
import java.util.Map;

/**
 * Helper class to manage GFX settings
 */
public class GfxSettingsHelper {
    private static final String PREFS_NAME = "GfxSettingsPrefs";
    private static final String KEY_GFX_LEVEL = "gfxLevel";

    private SharedPreferences preferences;

    public GfxSettingsHelper(Context context) {
        this.preferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
    }

    /**
     * Save GFX level setting
     *
     * @param gfxLevel GFX level (smooth, standard, high)
     */
    public void saveGfxLevel(String gfxLevel) {
        preferences.edit().putString(KEY_GFX_LEVEL, gfxLevel).apply();
    }

    /**
     * Get saved GFX level
     *
     * @return GFX level, defaults to "standard" if not set
     */
    public String getGfxLevel() {
        return preferences.getString(KEY_GFX_LEVEL, "standard");
    }

    /**
     * Save all GFX settings
     *
     * @param settings Map of settings to save
     */
    public void saveGfxSettings(Map<String, Object> settings) {
        SharedPreferences.Editor editor = preferences.edit();

        for (Map.Entry<String, Object> entry : settings.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            if (value instanceof String) {
                editor.putString(key, (String) value);
            } else if (value instanceof Integer) {
                editor.putInt(key, (Integer) value);
            } else if (value instanceof Boolean) {
                editor.putBoolean(key, (Boolean) value);
            } else if (value instanceof Float) {
                editor.putFloat(key, (Float) value);
            } else if (value instanceof Long) {
                editor.putLong(key, (Long) value);
            }
        }

        editor.apply();
    }

    /**
     * Get all GFX settings
     *
     * @return Map of all settings
     */
    public Map<String, Object> getGfxSettings() {
        Map<String, Object> settings = new HashMap<>();

        // Add default settings
        settings.put("resolution", preferences.getString("resolution", "1080p"));
        settings.put("fps", preferences.getInt("fps", 60));
        settings.put("graphicsQuality", preferences.getInt("graphicsQuality", 1));
        settings.put("hdrEnabled", preferences.getBoolean("hdrEnabled", false));
        settings.put("antiAliasingEnabled", preferences.getBoolean("antiAliasingEnabled", true));
        settings.put("gpuBoostEnabled", preferences.getBoolean("gpuBoostEnabled", false));
        settings.put(KEY_GFX_LEVEL, getGfxLevel());

        return settings;
    }

    /**
     * Get recommended DPI based on GFX level
     *
     * @return Recommended DPI value
     */
    public int getRecommendedDpi(String gfxLevel) {
        if (gfxLevel.equals("smooth")) {
            return 460; // Based on database for smooth
        } else if (gfxLevel.equals("high")) {
            return 600; // Based on database for high
        } else {
            return 550; // Based on database for standard
        }
    }

    /**
     * Get recommended sensitivity settings based on GFX level and device RAM
     *
     * @param gfxLevel GFX level
     * @param ramSizeGB RAM size in GB
     * @return Array of sensitivity values [general, redDot, scope2x, scope4x, scopeAwm]
     */
    public int[] getRecommendedSensitivitySettings(String gfxLevel, double ramSizeGB) {
        // Convert to int for comparison with thresholds
        int ramSizeInt = (int) Math.round(ramSizeGB);
        int[] settings = new int[5];

        if (gfxLevel.equals("smooth")) {
            if (ramSizeInt <= 2) {
                // 2GB RAM, smooth
                settings[0] = 200; // general
                settings[1] = 200; // redDot
                settings[2] = 180; // scope2x
                settings[3] = 180; // scope4x
                settings[4] = 140; // scopeAwm
            } else {
                // 3GB+ RAM, smooth
                settings[0] = 200; // general
                settings[1] = 200; // redDot
                settings[2] = 195; // scope2x
                settings[3] = 190; // scope4x
                settings[4] = 140; // scopeAwm
            }
        } else if (gfxLevel.equals("high")) {
            // high graphics
            settings[0] = 200; // general
            settings[1] = 200; // redDot
            settings[2] = 192; // scope2x
            settings[3] = 188; // scope4x
            settings[4] = 140; // scopeAwm
        } else {
            // standard graphics
            settings[0] = 200; // general
            settings[1] = 200; // redDot
            settings[2] = 190; // scope2x
            settings[3] = 180; // scope4x
            settings[4] = 130; // scopeAwm
        }

        return settings;
    }

    /**
     * Get RAM size in GB
     *
     * @param context Application context
     * @return RAM size in GB, minimum 1
     */
    public static double getDeviceRamSizeGB(Context context) {
        android.app.ActivityManager activityManager =
                (android.app.ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        android.app.ActivityManager.MemoryInfo memoryInfo = new android.app.ActivityManager.MemoryInfo();
        activityManager.getMemoryInfo(memoryInfo);

        long totalMemoryBytes = memoryInfo.totalMem;
        double ramSizeGB = totalMemoryBytes / (1024.0 * 1024.0 * 1024.0);

        // Apply correction factor to account for system reserved memory
        ramSizeGB = adjustRamSize(ramSizeGB);

        // Ensure minimum 1GB
        return Math.max(1.0, ramSizeGB);
    }

    /**
     * Adjusts the RAM size to account for system reserved memory
     * This helps show the marketed RAM size rather than available RAM
     *
     * @param detectedRamGB The detected RAM size in GB
     * @return The adjusted RAM size in GB
     */
    private static double adjustRamSize(double detectedRamGB) {
        // Apply correction factor based on detected RAM size
        // These adjustments are based on common discrepancies between
        // reported RAM and marketed RAM in Android devices

        if (detectedRamGB >= 5.5 && detectedRamGB < 6.0) {
            return 6.0; // Likely a 6GB device
        } else if (detectedRamGB >= 3.5 && detectedRamGB < 4.0) {
            return 4.0; // Likely a 4GB device
        } else if (detectedRamGB >= 2.7 && detectedRamGB < 3.0) {
            return 3.0; // Likely a 3GB device
        } else if (detectedRamGB >= 1.7 && detectedRamGB < 2.0) {
            return 2.0; // Likely a 2GB device
        } else if (detectedRamGB >= 7.5 && detectedRamGB < 8.0) {
            return 8.0; // Likely an 8GB device
        } else if (detectedRamGB >= 11.5 && detectedRamGB < 12.0) {
            return 12.0; // Likely a 12GB device
        } else if (detectedRamGB >= 15.5 && detectedRamGB < 16.0) {
            return 16.0; // Likely a 16GB device
        }

        // For values that don't match common thresholds, apply a general adjustment
        // Add approximately 10% to account for system reserved memory
        return Math.round(detectedRamGB * 1.1 * 10) / 10.0;
    }

    /**
     * Get formatted RAM size string
     *
     * @param context Application context
     * @return Formatted RAM size string (e.g., "6 GB" or "5.5 GB")
     */
    public static String getFormattedRamSize(Context context) {
        double ramSizeGB = getDeviceRamSizeGB(context);

        // Format RAM size with one decimal place
        String formattedRam;
        if (ramSizeGB % 1 < 0.1) {
            // If very close to a whole number, just show the whole number
            // Convert to int first to avoid format exception
            int roundedRam = (int)Math.round(ramSizeGB);
            formattedRam = String.valueOf(roundedRam);
        } else {
            // Otherwise show with one decimal place
            formattedRam = String.format("%.1f", ramSizeGB);
        }

        return formattedRam + " GB";
    }
}
