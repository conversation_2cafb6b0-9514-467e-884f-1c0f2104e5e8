[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\anim_fade_in.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\anim\\fade_in.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_custom_progress.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\custom_progress.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_circle_shape_green.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\circle_shape_green.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_neon_button_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\neon_button_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_mood_game.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_mood_game.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_android.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_android.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\mipmap-hdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\mipmap-hdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_gaming_tips_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\gaming_tips_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\layout_dialog_app_disclaimer.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\layout\\dialog_app_disclaimer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_network.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_network.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_preview_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\preview_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_card_dark_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\card_dark_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_gaming_score_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\gaming_score_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_cyber_button_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\cyber_button_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\anim_float_animation.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\anim\\float_animation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\font_tajawal_medium.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\font\\tajawal_medium.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_crosshair_lines.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\crosshair_lines.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_neon_button_active_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\neon_button_active_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_resolution.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_resolution.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_circle_shape_red.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\circle_shape_red.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_boost_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\boost_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_premium.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_premium.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_toast_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\toast_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_energy_lines_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\energy_lines_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\anim_subtle_scale.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\anim\\subtle_scale.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_enhanced_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\enhanced_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_apps.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_apps.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_game_mode_button_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\game_mode_button_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_rate.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_rate.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_neon_button_bg_premium.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\neon_button_bg_premium.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_crosshair_overlay.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\crosshair_overlay.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\layout_activity_app_guide.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\layout\\activity_app_guide.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_logo.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_logo.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_help.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_help.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\layout_item_selected_game.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\layout\\item_selected_game.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_gaming_trophy.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_gaming_trophy.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_speedometer_needle.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_speedometer_needle.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_neon_button_bg_gold.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\neon_button_bg_gold.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\anim_splash_logo_anim.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\anim\\splash_logo_anim.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\anim_slide_in_left.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\anim\\slide_in_left.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\layout_popup_optimization_needed.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\layout\\popup_optimization_needed.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\layout_activity_gfx_tools.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\layout\\activity_gfx_tools.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\anim_glow_pulse_repeat.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\anim\\glow_pulse_repeat.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\anim_slide_in_right.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\anim\\slide_in_right.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\layout_activity_game_mode.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\layout\\activity_game_mode.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_delete.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_delete.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_premium_header_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\premium_header_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_red_dot_shape.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\red_dot_shape.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\layout_activity_headshot_tool.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\layout\\activity_headshot_tool.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\anim_boost_success_animation.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\anim\\boost_success_animation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_circular_progress_bar.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\circular_progress_bar.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\anim_button_shake.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\anim\\button_shake.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\layout_activity_game_booster.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\layout\\activity_game_booster.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_recoil_pattern.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\recoil_pattern.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_smart_aim_button_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\smart_aim_button_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_fps.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_fps.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\anim_premium_feature_animation.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\anim\\premium_feature_animation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_crown.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_crown.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\anim_button_press.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\anim\\button_press.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_circle_button.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\circle_button.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\layout_nav_header.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\layout\\nav_header.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_neon_button_bg_cyan.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\neon_button_bg_cyan.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_cyber_neon_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\cyber_neon_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\font_cairo_regular.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\font\\cairo_regular.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_check_small.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_check_small.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_premium_crown.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_premium_crown.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\layout_dialog_premium_features.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\layout\\dialog_premium_features.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_cyber_border_gold.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\cyber_border_gold.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\anim_pulse.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\anim\\pulse.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_progress_bar_gradient.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\progress_bar_gradient.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\anim_glow_pulse.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\anim\\glow_pulse.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_game_booster.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_game_booster.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_rounded_button_dark.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\rounded_button_dark.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_gfx_tools.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_gfx_tools.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\anim_button_pulse.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\anim\\button_pulse.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_aim_target.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\aim_target.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_green_color_button.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\green_color_button.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_card_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\card_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\font_rajdhani_medium.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\font\\rajdhani_medium.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\anim_zoom_in.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\anim\\zoom_in.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\anim_slide_up.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\anim\\slide_up.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\layout_dialog_about.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\layout\\dialog_about.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_logo_drawable_android.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\logo_drawable_android.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\font_font.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\font\\font.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_premium_button_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\premium_button_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\font_orbitron_bold.otf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\font\\orbitron_bold.otf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_card_bg_translucent.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\card_bg_translucent.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_gfx_tools_gradient_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\gfx_tools_gradient_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\layout_activity_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\layout\\activity_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_cpu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_cpu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\anim_energy_grid_animation.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\anim\\energy_grid_animation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_logo_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\logo_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_aim_preview_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\aim_preview_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_premium_price_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\premium_price_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_circle_shape_yellow.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\circle_shape_yellow.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_neon_button_bg_secondary.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\neon_button_bg_secondary.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\anim_flame_animation.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\anim\\flame_animation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\anim_slide_down_bounce.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\anim\\slide_down_bounce.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_about.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_about.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_game_mode_button_custom.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\game_mode_button_custom.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\layout_item_app.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\layout\\item_app.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_disclaimer_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\disclaimer_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_red_color_button.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\red_color_button.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_auto_optimization.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_auto_optimization.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_target_icon.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\target_icon.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_spinner_shape.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\spinner_shape.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_memory.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_memory.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_headshot_logo_blue.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\headshot_logo_blue.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_info.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_info.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_headshot_tool_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\headshot_tool_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_game_booster_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\game_booster_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_crosshair_simple.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\crosshair_simple.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_wifi.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_wifi.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_cyber_neon_gradient_bg_gold.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\cyber_neon_gradient_bg_gold.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\font_tajawal_bold.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\font\\tajawal_bold.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_boost.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_boost.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\anim_icon_pulse.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\anim\\icon_pulse.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_energy_lines.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\energy_lines.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\anim_lightning_flash.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\anim\\lightning_flash.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_gaming_tag_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\gaming_tag_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\font_tajawal_regular.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\font\\tajawal_regular.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_gaming_controller.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_gaming_controller.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_gfx_logo.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_gfx_logo.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_neon_radio_selector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\neon_radio_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_ram.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_ram.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\font_cyberpunk_font.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\font\\cyberpunk_font.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_swipe.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_swipe.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_rounded_button_blue.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\rounded_button_blue.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\anim_slide_up_fade_in.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\anim\\slide_up_fade_in.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_game_mode_button_active.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\game_mode_button_active.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_speedometer_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_speedometer_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_gaming_recommendation_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\gaming_recommendation_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\anim_lines_animation.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\anim\\lines_animation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_rocket.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_rocket.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_blue_speed_lines.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\blue_speed_lines.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\anim_pulse_animation.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\anim\\pulse_animation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_play_game.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_play_game.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_storage.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_storage.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_gradient_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\gradient_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_gaming_fps.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_gaming_fps.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_recoil_tip.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_recoil_tip.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_energy_grid.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\energy_grid.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\layout_nav_footer_pro_status.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\layout\\nav_footer_pro_status.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\layout_activity_webview.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\layout\\activity_webview.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\layout_dialog_progress.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\layout\\dialog_progress.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_check_circle.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_check_circle.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\menu_drawer_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\menu\\drawer_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_dialog_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\dialog_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\font_fonts.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\font\\fonts.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_card_shadow.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\card_shadow.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_gaming_button_secondary.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\gaming_button_secondary.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_analytics.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_analytics.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\layout_activity_aim_overlay_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\layout\\activity_aim_overlay_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\anim_card_fade_in_slide_up.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\anim\\card_fade_in_slide_up.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_premium_glow_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\premium_glow_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_logo_glow.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\logo_glow.xml"}, {"merged": "com.mahmoudffyt.gfxbooster.app-debug-51:/layout_activity_game_mode.xml.flat", "source": "com.mahmoudffyt.gfxbooster.app-main-53:/layout/activity_game_mode.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_enhanced_button_with_glow.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\enhanced_button_with_glow.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_exit.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_exit.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\anim_speed_lines_animation.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\anim\\speed_lines_animation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_back.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_back.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_crosshair.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_crosshair.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\layout_dialog_premium_trial.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\layout\\dialog_premium_trial.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_rounded_button_green.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\rounded_button_green.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\layout_popup_system_analysis_report.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\layout\\popup_system_analysis_report.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\font_font_for_ar_en.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\font\\font_for_ar_en.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_games.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_games.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\font_premium_font.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\font\\premium_font.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_red_dot_tip.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_red_dot_tip.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\layout_gfx_selection_layout.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\layout\\gfx_selection_layout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_advanced.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_advanced.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_smart_aim.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_smart_aim.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_gaming_popup_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\gaming_popup_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_button_glow.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\button_glow.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_speed_lines_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\speed_lines_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\layout_popup_optimization_progress.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\layout\\popup_optimization_progress.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_circle_shape_blue.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\circle_shape_blue.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\anim_gold_pulse.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\anim\\gold_pulse.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\layout_dialog_aim_test.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\layout\\dialog_aim_test.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_smart_aim_button_glow.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\smart_aim_button_glow.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_headshot_logo.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\headshot_logo.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\layout_nav_footer_premium_new.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\layout\\nav_footer_premium_new.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_lightning_effect.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\lightning_effect.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_graphics.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_graphics.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_dpi_tip.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_dpi_tip.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\animator_button_state_animator.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\animator\\button_state_animator.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\layout_activity_splashscreen.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\layout\\activity_splashscreen.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_arrow_right.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_arrow_right.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_privacy.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_privacy.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_particle_effect.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\particle_effect.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_circular_progress_bar_green.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\circular_progress_bar_green.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_speed_lines_blue.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\speed_lines_blue.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_crosshair_precision.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\crosshair_precision.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_tap.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_tap.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_battery.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_battery.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_gaming_performance.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_gaming_performance.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\layout_aim_overlay.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\layout\\aim_overlay.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_edit_text_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\edit_text_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_cyber_neon_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\cyber_neon_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\layout_popup_optimization_complete.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\layout\\popup_optimization_complete.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\anim_rocket_animation.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\anim\\rocket_animation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_crosshair_dot.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\crosshair_dot.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\anim_staggered_fade_in.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\anim\\staggered_fade_in.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\mipmap-xxhdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\mipmap-xxhdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_power.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_power.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\anim_rotate_animation.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\anim\\rotate_animation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\font_rajdhani_bold.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\font\\rajdhani_bold.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_cyber_neon_gradient_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\cyber_neon_gradient_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_yellow_color_button.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\yellow_color_button.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\mipmap-xxxhdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\mipmap-xxxhdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable-anydpi-v24_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-pngs-46:\\drawable-anydpi-v24\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_temperature.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_temperature.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_aim_button.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_aim_button.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_sensitivity_tip.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_sensitivity_tip.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_game_mode_logo.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\game_mode_logo.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\anim_button_pulse_glow.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\anim\\button_pulse_glow.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\layout_premium_feature_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\layout\\premium_feature_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_blue_color_button.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\blue_color_button.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\anim_logo_pulse.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\anim\\logo_pulse.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_ic_headshot_tool.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\ic_headshot_tool.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\mipmap-xhdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\mipmap-xhdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\anim_logo_shrink.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\anim\\logo_shrink.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\mipmap-mdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\mipmap-mdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_animated_gradient_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\animated_gradient_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\anim_hover_effect.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\anim\\hover_effect.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_enhanced_button_square_blue.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\enhanced_button_square_blue.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_smartphone_frame.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\smartphone_frame.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-debug-51:\\drawable_button_cyan_gradient.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.gfxbooster.app-main-53:\\drawable\\button_cyan_gradient.xml"}]