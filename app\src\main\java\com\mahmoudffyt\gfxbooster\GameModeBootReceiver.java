package com.mahmoudffyt.gfxbooster;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Build;

public class GameModeBootReceiver extends BroadcastReceiver {
    @Override
    public void onReceive(Context context, Intent intent) {
        if (intent.getAction() != null && intent.getAction().equals(Intent.ACTION_BOOT_COMPLETED)) {
            // Check if Game Mode was active before device reboot
            SharedPreferences preferences = context.getSharedPreferences("GameModePrefs", Context.MODE_PRIVATE);
            boolean isGameModeActive = preferences.getBoolean("isGameModeActive", false);

            if (isGameModeActive) {
                // Start the Game Mode service
                Intent serviceIntent = new Intent(context, GameModeService.class);

                // For Android 12+, specify the foreground service type
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    serviceIntent.putExtra("foregroundServiceType", android.content.pm.ServiceInfo.FOREGROUND_SERVICE_TYPE_DATA_SYNC);
                    context.startForegroundService(serviceIntent);
                } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    // For Android 8.0+ to Android 11
                    context.startForegroundService(serviceIntent);
                } else {
                    // For older Android versions
                    context.startService(serviceIntent);
                }
            }
        }
    }
}
