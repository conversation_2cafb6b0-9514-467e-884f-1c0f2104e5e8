<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="@drawable/energy_grid">

    <!-- Title -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/app_disclaimer_title"
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="@color/cyan_accent"
        android:fontFamily="@font/orbitron_bold"
        android:gravity="center"
        android:layout_marginBottom="16dp" />

    <!-- Icon -->
    <ImageView
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:layout_gravity="center"
        android:src="@drawable/ic_info"
        android:tint="@color/cyan_accent"
        android:layout_marginBottom="16dp" />

    <!-- Main Message -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/app_disclaimer_message"
        android:textSize="14sp"
        android:textColor="@color/accent_color"
        android:fontFamily="@font/cairo_regular"
        android:lineSpacingExtra="4dp"
        android:layout_marginBottom="16dp" />

    <!-- Monitoring Notice -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/monitoring_tools_notice"
        android:textSize="12sp"
        android:textColor="@color/vibrant_red"
        android:fontFamily="@font/cairo_regular"
        android:lineSpacingExtra="2dp"
        android:layout_marginBottom="24dp" />

    <!-- Understand Button -->
    <Button
        android:id="@+id/btnUnderstand"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:text="@string/understand"
        android:textColor="@color/dark_background"
        android:textSize="16sp"
        android:textStyle="bold"
        android:fontFamily="@font/orbitron_bold"
        android:background="@drawable/button_cyan_gradient"
        android:elevation="4dp" />

</LinearLayout>
