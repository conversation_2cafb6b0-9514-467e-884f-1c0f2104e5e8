<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools">
    <color name="accent_color">#00ccff</color>
    <color name="accent_color_dim">#8000ccff</color>
    <color name="black">#FF000000</color>
    <color name="button_background">#162c36</color>
    <color name="button_glow">#3D00ccff</color>
    <color name="button_stroke">#00ccff</color>
    <color name="card_background">#121228</color>
    <color name="card_dark_blue">#162b35</color>
    <color name="card_shadow">#30000000</color>
    <color name="cyan_accent">#00FFFF</color>
    <color name="cyber_card_bg">#66000000</color>
    <color name="cyber_card_stroke">#3300FFFF</color>
    <color name="cyber_dark_center">#203a43</color>
    <color name="cyber_dark_end">#2c5364</color>
    <color name="cyber_dark_start">#0f2027</color>
    <color name="cyber_neon_blue">#0088FF</color>
    <color name="cyber_neon_cyan">#00FFFF</color>
    <color name="cyber_neon_gold">#FFD700</color>
    <color name="cyber_neon_gold_dim">#80FFD700</color>
    <color name="cyber_neon_magenta">#FF00FF</color>
    <color name="cyber_slider_active">#00FFFF</color>
    <color name="cyber_slider_inactive">#336688</color>
    <color name="cyber_text_primary">#FFFFFF</color>
    <color name="cyber_text_secondary">#AAAAAA</color>
    <color name="dark_background">#0f2027</color>
    <color name="dark_blue">#0D1B2A</color>
    <color name="dark_blue_end">#2c5364</color>
    <color name="dark_blue_mid">#203a43</color>
    <color name="dark_blue_start">#0f2027</color>
    <color name="drawer_background">#0a1a22</color>
    <color name="electric_blue">#0077FF</color>
    <color name="game_mode_background">#0A0A1A</color>
    <color name="gradient_center">#203a43</color>
    <color name="gradient_end">#2c5364</color>
    <color name="gradient_start">#0f2027</color>
    <color name="ic_launcher_background">#00020A</color>
    <color name="light_gray">#999999</color>
    <color name="main_background">#0f2027</color>
    <color name="main_card_background">#162c36</color>
    <color name="neon_blue">#00FFFF</color>
    <color name="neon_blue_dim">#8000FFFF</color>
    <color name="neon_blue_glow">#8000E5FF</color>
    <color name="neon_cyan">#00FFFF</color>
    <color name="neon_green">#00FF9D</color>
    <color name="neon_orange">#FF5722</color>
    <color name="neon_pink">#ff0099</color>
    <color name="neon_purple">#B026FF</color>
    <color name="neon_red">#FF073A</color>
    <color name="neon_yellow">#FFFF00</color>
    <color name="primary_color">#0f2027</color>
    <color name="primary_color_dim">#800f2027</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="secondary_color">#203a43</color>
    <color name="secondary_color_dim">#80203a43</color>
    <color name="shadow_color">#40000000</color>
    <color name="splash_background">#121212</color>
    <color name="switch_track_color">#4D00e5ff</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="text_color_2primary">#0f2027</color>
    <color name="text_color_2secondary">#203a43</color>
    <color name="text_color_primary">#FFFFFF</color>
    <color name="text_color_secondary">#CCCCCC</color>
    <color name="vibrant_blue">#00ccff</color>
    <color name="vibrant_gold">#FFD700</color>
    <color name="vibrant_green">#00ff9d</color>
    <color name="vibrant_orange">#ff9500</color>
    <color name="vibrant_purple">#d900ff</color>
    <color name="vibrant_red">#ff0050</color>
    <color name="white">#FFFFFFFF</color>
    <dimen name="activity_horizontal_margin">16dp</dimen>
    <dimen name="activity_vertical_margin">16dp</dimen>
    <dimen name="card_corner_radius">16dp</dimen>
    <dimen name="card_elevation">8dp</dimen>
    <dimen name="card_small_corner_radius">12dp</dimen>
    <dimen name="card_small_elevation">4dp</dimen>
    <dimen name="icon_size_large">64dp</dimen>
    <dimen name="icon_size_medium">32dp</dimen>
    <dimen name="icon_size_small">24dp</dimen>
    <dimen name="margin_large">24dp</dimen>
    <dimen name="margin_medium">16dp</dimen>
    <dimen name="margin_small">8dp</dimen>
    <dimen name="margin_tiny">4dp</dimen>
    <dimen name="padding_large">24dp</dimen>
    <dimen name="padding_medium">16dp</dimen>
    <dimen name="padding_small">8dp</dimen>
    <dimen name="padding_tiny">4dp</dimen>
    <dimen name="text_size_button">16sp</dimen>
    <dimen name="text_size_card_content">14sp</dimen>
    <dimen name="text_size_card_title">16sp</dimen>
    <dimen name="text_size_small">12sp</dimen>
    <dimen name="text_size_subtitle">14sp</dimen>
    <dimen name="text_size_title">28sp</dimen>
    <string name="_4x_scope">4X SCOPE</string>
    <string name="about">About</string>
    <string name="activate_game_mode">START</string>
    <string name="ad_closed">Ad closed</string>
    <string name="ad_closed_without_reward">Ad closed without reward. Proceeding anyway.</string>
    <string name="ad_failed_to_load">Ad failed to load</string>
    <string name="ad_load_failed">Ad load failed. Showing placeholder.</string>
    <string name="ad_loading">Ad is loading. Please try again in a moment.</string>
    <string name="ad_loading_short">Ad loading…</string>
    <string name="ad_reward_failed">Failed to receive reward. Please try again.</string>
    <string name="ad_space">Ad space</string>
    <string name="add_game">Add Game</string>
    <string name="advanced_aiming_controls">Advanced Aiming Controls</string>
    <string name="advanced_options">ADVANCED OPTIONS</string>
    <string name="advanced_options_gfx">ADVANCED OPTIONS</string>
    <string name="after">NOW</string>
    <string name="after_optimization">AFTER</string>
    <string name="aim_button">Aim Settings Pro</string>
    <string name="aim_button_premium_message">Aim Button is a premium feature. Please upgrade to access it.</string>
    <string name="aim_crosshair">Aim Crosshair</string>
    <string name="aim_overlay_color">Overlay Color</string>
    <string name="aim_overlay_disabled">Aim overlay disabled</string>
    <string name="aim_overlay_enabled">Aim overlay enabled</string>
    <string name="aim_overlay_permission_message">To show the aim overlay on screen, this app needs the \"Display over other apps\" permission. Please enable it in Settings.</string>
    <string name="aim_overlay_permission_required">Permission required for aim overlay</string>
    <string name="aim_overlay_premium_message">The Aim Overlay feature is only available in the premium version. Upgrade now to access this and other premium features!</string>
    <string name="aim_overlay_premium_title">Premium Feature: Aim Overlay</string>
    <string name="aim_overlay_settings">Aim Overlay Settings</string>
    <string name="aim_overlay_size">Overlay Size</string>
    <string name="aim_overlay_toggle">Toggle Overlay</string>
    <string name="aim_preview">AIM PREVIEW</string>
    <string name="aim_sensitivity">AIM SENSITIVITY</string>
    <string name="aim_test">AIM TEST</string>
    <string name="aim_tips_title">AIMING TIPS</string>
    <string name="alarm_permission_message">To schedule notifications and reminders, this app needs permission to set exact alarms. Please enable it in Settings.</string>
    <string name="alarm_permission_title">Alarm Permission Required</string>
    <string name="already_optimized">Device already scanned!</string>
    <string name="alternative_ad_loading">Trying alternative ad loading method…</string>
    <string name="analysis_disclaimer">Gaming Ready</string>
    <string name="analyzing_device">Analyzing device...</string>
    <string name="android">Android</string>
    <string name="android_label">Android</string>
    <string name="anti_aliasing">Anti-aliasing</string>
    <string name="anti_aliasing_gfx">Anti-aliasing</string>
    <string name="antialiasing_rec">• Anti-aliasing</string>
    <string name="app_close_failed">Could not get info for %1$s</string>
    <string name="app_closed">%1$s information viewed</string>
    <string name="app_disclaimer_message">This app provides monitoring and analysis tools for gaming performance. It does not modify system settings or guarantee performance improvements. Results may vary by device.</string>
    <string name="app_disclaimer_title">Important Notice</string>
    <string name="app_guide">Application guide</string>
    <string name="app_name">Game Booster Fix Lag - Gfx Tool</string>
    <string name="app_name_welcom_screen">GFX TOOLS</string>
    <string name="app_version">Version 1.0.0</string>
    <string name="apply">APPLY</string>
    <string name="apply_settings">APPLY SETTINGS</string>
    <string name="apply_settings_aim">APPLY SETTINGS</string>
    <string name="apply_settings_gfx">APPLY SETTINGS</string>
    <string name="apply_settings_headshot">SUGGEST BEST SETTINGS</string>
    <string name="apps_closed_count">%1$d background apps managed</string>
    <string name="apps_description">These apps are currently using system resources:</string>
    <string name="arabic">العربية</string>
    <string name="are_you_sure_you_want_to_reset_all_settings_to_default_values">Are you sure you want to reset all settings to default values?</string>
    <string name="asphalt_9">Asphalt 9 (detected for monitoring) </string>
    <string name="auto_optimization">Auto-Monitoring</string>
    <string name="auto_optimization_desc">Automatically tracks system performance when gaming apps are detected</string>
    <string name="awm_scope">AWM SCOPE</string>
    <string name="awm_scope_sensitivity">AWM Scope Sensitivity</string>
    <string name="awm_scope_sensitivity_label">AWM Scope Sensitivity</string>
    <string name="awm_scope_title">AWM SCOPE</string>
    <string name="back">Back</string>
    <string name="background_apps">BACKGROUND APPS</string>
    <string name="background_process">Background process</string>
    <string name="basic">Basic</string>
    <string name="battery_optimization">Battery Status</string>
    <string name="battery_optimization_desc">Tracks battery usage during gaming sessions</string>
    <string name="battery_optimization_title">Battery Monitor</string>
    <string name="battery_optimization_value">Monitoring</string>
    <string name="battery_temperature">Battery Temperature</string>
    <string name="before">CURRENT</string>
    <string name="before_optimization">BEFORE</string>
    <string name="best_settings_for_your_device">Best Settings For Your Device</string>
    <string name="boost_now">SCAN SYSTEM STATUS</string>
    <string name="breath_hold">Breath Hold</string>
    <string name="cache_cleared_size">%1$s of cache analyzed</string>
    <string name="call_of_duty">Call of Duty (detected for monitoring) </string>
    <string name="cancel">Cancel</string>
    <string name="cancel_3">Cancel</string>
    <string name="cancel_subscription_info">To cancel: Google Play Store > Subscriptions</string>
    <string name="check_webview">Checking WebView availability…</string>
    <string name="choose_the_graphics_level_you_use_in_free_fire">Choose the graphics level you use in Free Fire</string>
    <string name="clean_device">Clean Device</string>
    <string name="cleaning_complete_notification">Analysis complete! System status reviewed.</string>
    <string name="cleaning_device">Cleaning device...</string>
    <string name="clearing_cache">Reading cache information…</string>
    <string name="close">Close</string>
    <string name="closing_apps">Scanning background apps…</string>
    <string name="com.google.firebase.crashlytics.mapping_file_id" ns1:ignore="UnusedResources,TypographyDashes" translatable="false">00000000000000000000000000000000</string>
    <string name="compare_settings">COMPARE SETTINGS</string>
    <string name="comparing_settings">Comparing settings...</string>
    <string name="cpu">CPU</string>
    <string name="cpu_boost">CPU Status</string>
    <string name="cpu_boost_desc">Shows CPU usage information for better gaming awareness</string>
    <string name="cpu_boost_title">CPU Monitor</string>
    <string name="cpu_boost_value">Monitoring</string>
    <string name="cpu_power">CPU Power</string>
    <string name="cpu_status">CPU USAGE</string>
    <string name="crosshair_size">Crosshair Size</string>
    <string name="currently_in_pro_mode">You are currently in Pro Mode</string>
    <string name="daily_reminder_notification_1">Did you forget to boost your device today? It only takes a second!</string>
    <string name="daily_reminder_notification_2">Don\'t play without boosting your device for the best gaming speed! Boost now!</string>
    <string name="dark_mode">Dark Mode</string>
    <string name="days_remaining">Trial days remaining: %d</string>
    <string name="deactivate_game_mode">STOP</string>
    <string name="developed_by_mahmoud_ff_yt" translatable="false">Developed by: mahmoud ff yt</string>
    <string name="device_analysis">DEVICE ANALYSIS</string>
    <string name="device_cleaned">Device cleaned and optimized for gaming</string>
    <string name="device_gaming_analysis">Your device information for gaming session awareness</string>
    <string name="device_info">Device Info</string>
    <string name="device_info_gfx">DEVICE INFO</string>
    <string name="device_info_gfx_tools">DEVICE INFO</string>
    <string name="device_label">Device</string>
    <string name="device_optimized_description">Analysis complete. System status reviewed.</string>
    <string name="disabled">Disabled</string>
    <string name="done">ANALYSIS COMPLETE</string>
    <string name="dpi_info">DPI</string>
    <string name="dpi_set_to_1600">DPI set to 1600</string>
    <string name="dpi_set_to_400">DPI set to 400</string>
    <string name="dpi_set_to_800">DPI set to 800</string>
    <string name="dpi_setting">DPI Setting</string>
    <string name="dpi_settings">DPI SETTINGS</string>
    <string name="dpi_tip">Higher DPI = more precise mouse/touch movement</string>
    <string name="drag_to_move_crosshair">Drag the crosshair to move it anywhere on screen</string>
    <string name="email_contact"><EMAIL></string>
    <string name="enable_dark_theme_throughout_the_app">Enable dark theme throughout the app</string>
    <string name="enable_smart_aim">Enable Smart Aim</string>
    <string name="enabled">Enabled</string>
    <string name="english">English</string>
    <string name="error_message">Error: %1$s</string>
    <string name="error_opening_settings">Error opening settings. Please try manually.</string>
    <string name="error_opening_subscription_management">Unable to open subscription management</string>
    <string name="estimated_fps">Est. FPS</string>
    <string name="estimated_fps_info">Approximate FPS estimation</string>
    <string name="excellent">SUITABLE</string>
    <string name="excellent_gaming">Good for Gaming</string>
    <string name="exit">Exit</string>
    <string name="exit_confirmation">Are you sure you want to exit?</string>
    <string name="feature_coming_soon">" feature coming soon!"</string>
    <string name="feedback">Feedback</string>
    <string name="for_more_suggestions_and_solutions_contact_us_via_email">For more suggestions and solutions, contact us via email</string>
    <string name="fortnite">Fortnite (detected for monitoring) </string>
    <string name="fps">FPS</string>
    <string name="fps_30">30 FPS</string>
    <string name="fps_60">60 FPS</string>
    <string name="fps_90">90 FPS</string>
    <string name="fps_gaming">FPS Gaming Info</string>
    <string name="fps_rec">• FPS</string>
    <string name="fps_settings">FPS Settings</string>
    <string name="free_camera_button">FREE CAMERA BUTTON</string>
    <string name="free_camera_sensitivity">Free Camera Sensitivity</string>
    <string name="free_fire">Free Fire (detected for monitoring) </string>
    <string name="free_fire_launched">Launching Free Fire</string>
    <string name="free_fire_not_installed">Free Fire is not installed on this device</string>
    <string name="free_fire_optimization">GENERAL OPTIMIZATION SETTINGS FOR FREE FIRE</string>
    <string name="free_fire_settings">Free Fire Information</string>
    <string name="free_memory">AVAILABLE MEMORY</string>
    <string name="free_version_game_limit">Free version: 1 game only. Upgrade to Pro for unlimited games.</string>
    <string name="game_added_successfully">Game added successfully</string>
    <string name="game_booster">GAME BOOSTER</string>
    <string name="game_booster_description">Monitor your gaming sessions by tracking background apps and system resource usage for better gaming awareness.</string>
    <string name="game_booster_title">GAMING BOOSTER</string>
    <string name="game_enhancer">GAME ENHANCER</string>
    <string name="game_launched">Game launched successfully</string>
    <string name="game_limit_reached">Game limit reached. Upgrade to Pro for unlimited games.</string>
    <string name="game_mode_activated">Game Mode activated</string>
    <string name="game_mode_activated_for_one_hour">Game Mode activated for 1 hour!</string>
    <string name="game_mode_active">Game Mode Active</string>
    <string name="game_mode_description">Monitor your gaming sessions and track system performance for better gaming awareness and insights.</string>
    <string name="game_mode_features">GAMING MODE FEATURES</string>
    <string name="game_mode_notification_text">System monitoring is active for your gaming session</string>
    <string name="game_mode_notification_title">Gaming Session Dashboard Active</string>
    <string name="game_mode_service">Game Mode Service</string>
    <string name="game_mode_status_active">Gaming Session Dashboard Active – System monitoring enabled</string>
    <string name="game_mode_status_inactive">GAMING SESSION DASHBOARD INACTIVE</string>
    <string name="game_mode_title">GAMING MODE</string>
    <string name="game_not_installed">Game not installed</string>
    <string name="game_optimization">Game Optimization</string>
    <string name="game_removed">Game removed</string>
    <string name="gaming_performance_report">Gaming Session Report</string>
    <string name="gaming_ready">Gaming Compatible</string>
    <string name="gaming_recommendations">Gaming Information</string>
    <string name="gaming_score">Device Rating</string>
    <string name="gaming_tips">Gaming Tips</string>
    <string name="gaming_tips_content">• Monitor background apps during gaming\n• Use Gaming Focus Mode for awareness\n• Keep device cool during long sessions\n• Check system resources regularly</string>
    <string name="gb">GB</string>
    <string name="gcm_defaultSenderId" translatable="false">************</string>
    <string name="general">GENERAL</string>
    <string name="general_sensitivity">General Sensitivity</string>
    <string name="gfx_logo">GFX Logo</string>
    <string name="gfx_settings_applied">GFX settings applied successfully!</string>
    <string name="gfx_settings_applied_successfully">GFX settings applied successfully!</string>
    <string name="gfx_tools_description">Optimize your game graphics settings for the best balance of visual quality and performance.</string>
    <string name="gfx_tools_main">GFX Tools</string>
    <string name="gfx_tools_title">GFX TOOLS</string>
    <string name="go_to_settings">Go to Settings</string>
    <string name="google_api_key" translatable="false">AIzaSyASO9hE3c2UMYvbiQUGte1KVWz9OibzPJ0</string>
    <string name="google_app_id" translatable="false">1:************:android:44a220dfeeb874d848e7a5</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyASO9hE3c2UMYvbiQUGte1KVWz9OibzPJ0</string>
    <string name="google_play_transparency">Managed via Google Play Store. Cancel anytime in account settings.</string>
    <string name="google_storage_bucket" translatable="false">gfx-tool-f60ef.firebasestorage.app</string>
    <string name="got_it">Got It!</string>
    <string name="gpu_boost">GPU Boost</string>
    <string name="gpu_boost_gfx">GPU Boost</string>
    <string name="graphics_level">Graphics Level</string>
    <string name="graphics_level_set_to">"Graphics level set to "</string>
    <string name="graphics_optimization">GRAPHICS OPTIMIZATION</string>
    <string name="graphics_quality">GRAPHICS QUALITY</string>
    <string name="graphics_quality_gfxtool">"Graphics Quality: "</string>
    <string name="graphics_rec">• Graphics</string>
    <string name="hdr_mode">HDR Mode</string>
    <string name="hdr_mode_gfx">HDR Mode</string>
    <string name="hdr_rec">• HDR</string>
    <string name="headshot">HEADSHOT</string>
    <string name="headshot_game_enhancer_v1_0">Game Booster Fix Lag - Gfx Tool v1.0</string>
    <string name="headshot_tool">Headshot Tool</string>
    <string name="headshot_tool_title">HEADSHOT TOOL</string>
    <string name="hide_tips">HIDE TIPS</string>
    <string name="high">High</string>
    <string name="high_1440p">High (1440p)</string>
    <string name="high_gfx2">HIGH</string>
    <string name="high_graphics_recommended">Device supports high graphics settings</string>
    <string name="high_usage_apps">RESOURCE HEAVY APPS</string>
    <string name="high_usage_notification">Your device is under heavy load (CPU: %1$d%%, RAM: %2$d%%). Boost now for better gaming!</string>
    <string name="hold_to_fire">Hold to simulate continuous fire</string>
    <string name="horizontal_sensitivity">Horizontal Sensitivity</string>
    <string name="hours_remaining">Hours remaining: %d</string>
    <string name="keep_settings">Keep Settings</string>
    <string name="language">Language</string>
    <string name="language_change_message">The app will restart to apply the language change</string>
    <string name="language_settings">Language Settings</string>
    <string name="last_optimization_saved">Analysis saved for 3 minutes</string>
    <string name="last_used">Last used: %1$s</string>
    <string name="launch_error">An error occurred while trying to launch the game.</string>
    <string name="launch_free_fire">Launch Free Fire</string>
    <string name="launch_game">Launch Game</string>
    <string name="loading_price">5$</string>
    <string name="low">Low</string>
    <string name="low_720p">Low (720p)</string>
    <string name="manage_subscription">Manage Subscription</string>
    <string name="maybe_later">MAYBE LATER</string>
    <string name="medium">Medium</string>
    <string name="medium_1080p">Medium (1080p)</string>
    <string name="memory_status">MEMORY USAGE</string>
    <string name="minecraft">Minecraft (detected for monitoring) </string>
    <string name="minutes_remaining">Minutes remaining: %d</string>
    <string name="mobile_data">Mobile Data</string>
    <string name="monitoring_for_game_launches">Monitoring for game launches</string>
    <string name="monitoring_notice">This app monitors system performance and does not make actual changes to your device\'s core settings.</string>
    <string name="monitoring_tools_notice">These are monitoring and analysis tools designed to help you understand your device\'s performance during gaming.</string>
    <string name="mood_game">Mood Game</string>
    <string name="more_games_detected">...and many more games are automatically detected!</string>
    <string name="navigation_drawer_close">Close navigation drawer</string>
    <string name="navigation_drawer_open">Open navigation drawer</string>
    <string name="needs_optimization">High Usage</string>
    <string name="network_ping">Network Ping</string>
    <string name="network_priority">Network Status</string>
    <string name="network_priority_desc">Displays network usage information during gaming sessions</string>
    <string name="network_priority_title">Network Monitor</string>
    <string name="network_priority_value">Monitoring</string>
    <string name="network_type">Network Type</string>
    <string name="no">No</string>
    <string name="no_apps_found">No resource-heavy apps detected</string>
    <string name="no_connection">No Connection</string>
    <string name="no_games_selected">No games selected yet</string>
    <string name="ok">OK</string>
    <string name="open_settings">Open Settings</string>
    <string name="optimal">COMPATIBLE</string>
    <string name="optimization_complete">Scan complete. System status reviewed for gaming awareness.</string>
    <string name="optimization_expired">Scan session has expired</string>
    <string name="optimization_expired_notification">Analysis period has expired. Analyze your device again!</string>
    <string name="optimization_needed">Gaming session check available</string>
    <string name="optimization_needed_description">Check your current gaming session status. Tap to view.</string>
    <string name="optimization_success">Gaming Session Monitor scan complete!</string>
    <string name="optimize_settings">Optimize Settings</string>
    <string name="optimize_your_gaming_experience">Optimize your gaming experience</string>
    <string name="optimized">SCAN COMPLETE</string>
    <string name="optimized_items">Analyzed Items:</string>
    <string name="optimizing">Scanning your device status…</string>
    <string name="optimizing_cpu">Reading CPU usage...</string>
    <string name="optimizing_for">"Optimizing for "</string>
    <string name="optimizing_performance_for">"Optimizing performance for "</string>
    <string name="optimizing_progress">Scanning: %1$d%%</string>
    <string name="optimizing_ram">Reading memory information…</string>
    <string name="overlay_disabled">Overlay Disabled</string>
    <string name="overlay_enabled">Overlay Enabled</string>
    <string name="overlay_error_occurred">Error occurred while displaying crosshair: %1$s</string>
    <string name="overlay_failed_to_show">Failed to display crosshair. Please try again.</string>
    <string name="overlay_permission_denied">Permission denied. Crosshair overlay cannot be displayed without this permission.</string>
    <string name="overlay_permission_message">To display aim overlay and other features on screen, this app needs permission to draw over other apps. Please enable it in Settings.</string>
    <string name="overlay_permission_title">Overlay Permission Required</string>
    <string name="performance_notice">INFORMATION ONLY: This tool provides system monitoring and information display only. No modifications, optimizations, or performance enhancements are made to your device.</string>
    <string name="performance_stats">SYSTEM STATUS</string>
    <string name="performance_warning_message">The selected settings may be too demanding for your device. This could result in lower frame rates, overheating, or increased battery drain. Consider using lower settings for better performance.</string>
    <string name="performance_warning_title">Performance Warning</string>
    <string name="permission_required">Permission required</string>
    <string name="permissions_explanation">This app requires several permissions to function properly. Please grant all requested permissions for the best experience.</string>
    <string name="ping_excellent">Excellent</string>
    <string name="ping_fair">Fair</string>
    <string name="ping_good">Good</string>
    <string name="ping_poor">Poor</string>
    <string name="please_wait_while_we_optimize_your_game_settings">Please wait while we optimize your game settings</string>
    <string name="position_saved">Position saved</string>
    <string name="powerful">Powerful</string>
    <string name="powering_up_welcomescreen">POWERING UP...</string>
    <string name="premium">Premium</string>
    <string name="premium_crown">Premium Crown</string>
    <string name="premium_feature">Premium Feature</string>
    <string name="premium_feature_aim_button">Aim Button - Advanced aiming controls</string>
    <string name="premium_feature_aim_overlay">Aim Overlay - Customizable crosshair for precise aiming</string>
    <string name="premium_feature_exclusive_content">Exclusive Content - Access to pro settings</string>
    <string name="premium_feature_message">This feature is only available in the premium version. Upgrade now to access all premium features!</string>
    <string name="premium_feature_no_ads">No Ads - Enjoy ad-free experience</string>
    <string name="premium_feature_priority_updates">Priority Updates - Get new features first</string>
    <string name="premium_feature_smart_aim">Smart Aim - Professional headshot optimization</string>
    <string name="premium_feature_support">Premium Support - Direct support channel</string>
    <string name="premium_features">PREMIUM FEATURES</string>
    <string name="premium_features_description">Upgrade to Premium to unlock all these amazing features and enhance your gaming experience!</string>
    <string name="premium_features_unlocked">Premium features unlocked!</string>
    <string name="premium_message_aim_button">Unlock Aim Button for advanced aiming controls and customization!</string>
    <string name="premium_message_smart_aim">Unlock Smart Aim to get professional headshot optimization based on your device specifications!</string>
    <string name="premium_price_description">Monthly subscription for access to all premium features</string>
    <string name="premium_price_title">MONTHLY SUBSCRIPTION</string>
    <string name="premium_version">Premium Version (Ad-Free)</string>
    <string name="premium_version_benefits">Premium Version Benefits:</string>
    <string name="privacy_policy">Privacy Policy</string>
    <string name="pro_benefits_summary">Enjoy unlimited access to all premium features</string>
    <string name="pro_feature_aim_overlay">Advanced Aim Overlay - Customizable crosshair for precise aiming</string>
    <string name="pro_feature_exclusive_updates">Exclusive Updates - Get new features first</string>
    <string name="pro_feature_game_mode">Enhanced Game Mode - Maximum performance optimization</string>
    <string name="pro_feature_no_ads">Ad-Free Experience - No interruptions during gameplay</string>
    <string name="pro_feature_priority_support">Priority Support - Get help when you need it</string>
    <string name="pro_status_active">PRO MODE ACTIVE</string>
    <string name="pro_version">Pro version</string>
    <string name="pro_version_description">Unlock all premium features and enhance your gaming experience!</string>
    <string name="pro_version_price">$5/month</string>
    <string name="pro_version_title">Upgrade to Pro Version</string>
    <string name="pro_version_unlimited_games">Pro version: Unlimited games</string>
    <string name="processor_info">Processor</string>
    <string name="processor_label">Processor</string>
    <string name="product_not_available">This product is not available at the moment.</string>
    <string name="project_id" translatable="false">gfx-tool-f60ef</string>
    <string name="pubg_mobile">PUBG Mobile (detected for monitoring)</string>
    <string name="purchase_failed">Purchase failed. Please try again later.</string>
    <string name="ram">RAM</string>
    <string name="ram_info">RAM</string>
    <string name="ram_label">RAM</string>
    <string name="ram_optimization">RAM Status</string>
    <string name="ram_optimization_value">Monitoring</string>
    <string name="ram_optimized">RAM usage: %1$d%% (Current reading)</string>
    <string name="ram_speed">MEMORY INFO</string>
    <string name="rate_us">Rate Us</string>
    <string name="ready_to_optimize">Ready to scan your device status</string>
    <string name="realtime_monitoring">Real-time Monitoring</string>
    <string name="recoil_compensation">Recoil Compensation</string>
    <string name="recoil_control">RECOIL CONTROL</string>
    <string name="recoil_tip">Higher recoil compensation = less weapon kick when firing</string>
    <string name="recommended_settings">Recommended Settings</string>
    <string name="recommended_settings_applied">Suggested settings applied based on general device info.</string>
    <string name="red_dot">RED DOT</string>
    <string name="red_dot_color">Red Dot Color</string>
    <string name="red_dot_color_set_to_blue_2">Red dot color set to Blue</string>
    <string name="red_dot_color_set_to_blue_aim">Red dot color set to Blue</string>
    <string name="red_dot_color_set_to_green">Red dot color set to Green</string>
    <string name="red_dot_color_set_to_green_2">Red dot color set to Green</string>
    <string name="red_dot_color_set_to_red">Red dot color set to Red</string>
    <string name="red_dot_color_set_to_red_2">Red dot color set to Red</string>
    <string name="red_dot_color_set_to_yellow">Red dot color set to Yellow</string>
    <string name="red_dot_color_set_to_yellow_2">Red dot color set to Yellow</string>
    <string name="red_dot_optimization">RED DOT OPTIMIZATION</string>
    <string name="red_dot_sensitivity">Red Dot Sensitivity</string>
    <string name="red_dot_tip">Smaller red dot = better precision for distant targets</string>
    <string name="remove_ads">Remove Ads</string>
    <string name="remove_game">Remove Game</string>
    <string name="reset">RESET</string>
    <string name="reset_23">Reset</string>
    <string name="reset_settings">Reset Settings</string>
    <string name="reset_to_default">RESET TO DEFAULT</string>
    <string name="resolution">RESOLUTION</string>
    <string name="resolution_gfx">RESOLUTION</string>
    <string name="resolution_rec">• Resolution</string>
    <string name="restart">Restart</string>
    <string name="retry">Retry</string>
    <string name="retrying_ad_load">Retrying ad load…</string>
    <string name="reward_earned">Reward earned!</string>
    <string name="scope_2x">2X SCOPE</string>
    <string name="scope_2x_sensitivity">2x Scope Sensitivity</string>
    <string name="scope_4x">4X SCOPE</string>
    <string name="scope_4x_sensitivity">4x Scope Sensitivity</string>
    <string name="score_0">Score: 0</string>
    <string name="score_value">Score: %1$d</string>
    <string name="screen_info">Screen</string>
    <string name="screen_label">Screen</string>
    <string name="screen_large">Large</string>
    <string name="screen_medium">Medium</string>
    <string name="screen_size">Screen</string>
    <string name="screen_small">Small</string>
    <string name="select_game_from_list">Select a game from your installed apps</string>
    <string name="select_game_to_optimize">Select Game to Optimize</string>
    <string name="select_graphics_level">SELECT GRAPHICS LEVEL</string>
    <string name="select_language">Select Language</string>
    <string name="select_optimal_dpi">Select optimal DPI for your device</string>
    <string name="sensitivity_tip">Higher sensitivity = faster aim movement but less precision</string>
    <string name="settings">Settings</string>
    <string name="settings_amp_game_booster_welcome_screen"><![CDATA[ Headshot & Game Booster]]></string>
    <string name="settings_applied">Settings Applied</string>
    <string name="settings_optimized">Settings optimized for your device</string>
    <string name="settings_optimized_for_your_device">Settings optimized for your device</string>
    <string name="settings_reset">Settings Reset to Default</string>
    <string name="settings_reset_to_recommended_values">Settings reset to recommended values</string>
    <string name="settings_suggested_successfully">Optimal settings suggested for your device!</string>
    <string name="share">SHARE</string>
    <string name="share_settings_title">My Free Fire Headshot Tool Settings</string>
    <string name="share_settings_via">Share settings via</string>
    <string name="show_tips">SHOW TIPS</string>
    <string name="showing_aim_tips">Showing aim tips...</string>
    <string name="size_large">Large</string>
    <string name="size_medium">Medium</string>
    <string name="size_small">Small</string>
    <string name="smart_aim">SMART AIM</string>
    <string name="smart_aim_description">Helps adjust sensitivity settings based on your preferences.</string>
    <string name="smart_aim_disabled">Smart Aim disabled</string>
    <string name="smart_aim_disabled_33">Smart Aim disabled</string>
    <string name="smart_aim_enabled">Smart Aim enabled - Optimized for %1$s devices</string>
    <string name="smart_aim_premium_message">Smart Aim is a premium feature. Please upgrade to access it.</string>
    <string name="smooth">SMOOTH</string>
    <string name="smooth_fps_expected">Device capable of smooth gameplay</string>
    <string name="standard">STANDARD</string>
    <string name="standard_gfx_3">STANDARD</string>
    <string name="subscribe_now">SUBSCRIBE NOW</string>
    <string name="subscription_management">Subscription Management</string>
    <string name="suggest_best_settings">SUGGEST BEST SETTINGS</string>
    <string name="suggest_best_settings_description">Please wait while we analyze your device and suggest the best settings</string>
    <string name="supported_games">SUPPORTED GAMES</string>
    <string name="supported_games_desc">Game Mode automatically detects and monitors popular games including:</string>
    <string name="swipe_to_aim">Swipe to test aiming movement</string>
    <string name="system_analysis">Analyzing...</string>
    <string name="system_monitor_channel">Gaming Session Monitor</string>
    <string name="system_monitor_channel_description">Notifications about gaming session monitoring</string>
    <string name="tap_boost_button">Tap SCAN NOW to check current system status</string>
    <string name="tap_target">Tap on target to simulate shooting</string>
    <string name="tap_the_targets_as_quickly_as_possible">Tap the targets as quickly as possible</string>
    <string name="tap_to_test">Tap to test your aim settings</string>
    <string name="test_aim">TEST AIM</string>
    <string name="test_aim_button">TEST AIM</string>
    <string name="testing_aim_settings">Testing aim settings...</string>
    <string name="testing_with_default_sensitivity">Testing with default sensitivity</string>
    <string name="testing_with_sensitivity">Testing with: Sensitivity %1$d | DPI %2$d</string>
    <string name="time_0s">Time: 0s</string>
    <string name="time_seconds">Time: %1$d s</string>
    <string name="total_memory">TOTAL MEMORY</string>
    <string name="trial_activated">Trial activated! You can use this feature for 1 hour</string>
    <string name="trial_expired">Trial period has expired. Please upgrade to Pro for unlimited access.</string>
    <string name="trial_limit_reached">You\'ve reached your daily trial limit. Come back tomorrow or upgrade to Pro for unlimited access.</string>
    <string name="try_free">Try Free for 1 Hour</string>
    <string name="ultra_2160p">Ultra (2160p)</string>
    <string name="understand">I Understand</string>
    <string name="upgrade_now">UPGRADE NOW</string>
    <string name="usage_access_denied_message">Without usage access, some features may not work properly.</string>
    <string name="usage_access_message">To show the most accurate list of running apps, this app needs Usage Access permission. Please enable it in Settings.</string>
    <string name="usage_access_required">Usage Access Required</string>
    <string name="used_memory">USED MEMORY</string>
    <string name="vertical_sensitivity">Vertical Sensitivity</string>
    <string name="vibrant_green">#4CAF50</string>
    <string name="watch_ad_for_trial">Try this feature for 1 hour (once per day for 3 days)</string>
    <string name="webview_available">WebView is now available. Ads should work properly.</string>
    <string name="webview_not_available">WebView is not available on this device. Some features may not work properly.</string>
    <string name="webview_not_available_message">Your device is missing WebView, which is required for ads and some app features. Please install or update WebView from the Google Play Store.</string>
    <string name="webview_not_available_title">WebView Not Available</string>
    <string name="webview_still_unavailable">WebView is still unavailable. Please install or update WebView.</string>
    <string name="welcome_premium_button">UPGRADE TO PRO</string>
    <string name="welcome_premium_description">Enhance your gaming experience with our Pro features!</string>
    <string name="welcome_premium_later">CONTINUE WITH FREE VERSION</string>
    <string name="welcome_premium_lifetime">$99 one-time payment</string>
    <string name="welcome_premium_monthly">Unlimited Access</string>
    <string name="welcome_premium_title">UNLOCK PRO FEATURES</string>
    <string name="welcome_premium_trial">Premium Features</string>
    <string name="welcome_premium_yearly">$45/year (25% off)</string>
    <string name="wifi_connected">WiFi</string>
    <string name="yes">Yes</string>
    <style name="AlertDialogButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/cyber_neon_cyan</item>
    </style>
    <style name="AlertDialogTheme" parent="ThemeOverlay.MaterialComponents.Dialog.Alert">
        <item name="colorPrimary">@color/cyber_neon_cyan</item>
        <item name="colorAccent">@color/cyber_neon_cyan</item>
        <item name="android:background">@color/cyber_dark_start</item>
        <item name="android:textColorPrimary">@color/cyber_text_primary</item>
        <item name="android:textColor">@color/cyber_text_primary</item>
        <item name="buttonBarPositiveButtonStyle">@style/AlertDialogButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/AlertDialogButtonStyle</item>
    </style>
    <style name="ArabicTextViewBoldStyle">
        <item name="android:fontFamily">@font/font_for_ar_en</item>
        <item name="android:textColor">@color/cyber_text_primary</item>
    </style>
    <style name="ArabicTextViewMediumStyle">
        <item name="android:fontFamily">@font/font_for_ar_en</item>
        <item name="android:textColor">@color/cyber_text_primary</item>
    </style>
    <style name="ArabicTextViewStyle">
        <item name="android:fontFamily">@font/font_for_ar_en</item>
        <item name="android:textColor">@color/cyber_text_primary</item>
    </style>
    <style name="CyberNeonBodyTextStyle">
        <item name="android:fontFamily">@font/cairo_regular</item>
        <item name="android:textColor">@color/cyber_text_secondary</item>
        <item name="android:textSize">14sp</item>
    </style>
    <style name="CyberNeonButtonStyle">
        <item name="android:background">@drawable/neon_button_bg</item>
        <item name="android:fontFamily">@font/rajdhani_bold</item>
        <item name="android:textColor">@color/cyber_neon_cyan</item>
        <item name="android:textSize">16sp</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
        <item name="android:stateListAnimator">@animator/button_state_animator</item>
    </style>
    <style name="CyberNeonHeadingStyle">
        <item name="android:fontFamily">@font/orbitron_bold</item>
        <item name="android:textColor">@color/cyber_neon_cyan</item>
        <item name="android:textSize">22sp</item>
        <item name="android:shadowColor">@color/cyber_neon_cyan</item>
        <item name="android:shadowDx">0</item>
        <item name="android:shadowDy">0</item>
        <item name="android:shadowRadius">8</item>
    </style>
    <style name="CyberNeonSubheadingStyle">
        <item name="android:fontFamily">@font/rajdhani_bold</item>
        <item name="android:textColor">@color/cyber_text_primary</item>
        <item name="android:textSize">16sp</item>
    </style>
    <style name="PremiumButtonStyle" parent="Widget.MaterialComponents.Button">
        <item name="android:fontFamily">@font/font_for_ar_en</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:textSize">16sp</item>
        <item name="android:paddingTop">8dp</item>
        <item name="android:paddingBottom">8dp</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="backgroundTint">@color/cyber_neon_gold</item>
        <item name="cornerRadius">24dp</item>
        <item name="android:elevation">4dp</item>
    </style>
    <style name="PremiumFeatureStyle">
        <item name="android:fontFamily">@font/font_for_ar_en</item>
        <item name="android:textColor">@color/cyber_neon_gold</item>
        <item name="android:drawableTint">@color/cyber_neon_gold</item>
        <item name="android:drawablePadding">8dp</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:textSize">14sp</item>
        <item name="android:paddingTop">4dp</item>
        <item name="android:paddingBottom">4dp</item>
    </style>
    <style name="TextAppearance.App.Body1" parent="TextAppearance.MaterialComponents.Body1">
        <item name="fontFamily">@font/font_for_ar_en</item>
        <item name="android:fontFamily">@font/font_for_ar_en</item>
    </style>
    <style name="TextAppearance.App.Body2" parent="TextAppearance.MaterialComponents.Body2">
        <item name="fontFamily">@font/font_for_ar_en</item>
        <item name="android:fontFamily">@font/font_for_ar_en</item>
    </style>
    <style name="TextAppearance.App.Button" parent="TextAppearance.MaterialComponents.Button">
        <item name="fontFamily">@font/font_for_ar_en</item>
        <item name="android:fontFamily">@font/font_for_ar_en</item>
    </style>
    <style name="TextAppearance.App.Caption" parent="TextAppearance.MaterialComponents.Caption">
        <item name="fontFamily">@font/font_for_ar_en</item>
        <item name="android:fontFamily">@font/font_for_ar_en</item>
    </style>
    <style name="TextAppearance.App.Headline1" parent="TextAppearance.MaterialComponents.Headline1">
        <item name="fontFamily">@font/font_for_ar_en</item>
        <item name="android:fontFamily">@font/font_for_ar_en</item>
    </style>
    <style name="TextAppearance.App.Headline2" parent="TextAppearance.MaterialComponents.Headline2">
        <item name="fontFamily">@font/font_for_ar_en</item>
        <item name="android:fontFamily">@font/font_for_ar_en</item>
    </style>
    <style name="TextAppearance.App.Headline3" parent="TextAppearance.MaterialComponents.Headline3">
        <item name="fontFamily">@font/font_for_ar_en</item>
        <item name="android:fontFamily">@font/font_for_ar_en</item>
    </style>
    <style name="TextAppearance.App.Headline4" parent="TextAppearance.MaterialComponents.Headline4">
        <item name="fontFamily">@font/font_for_ar_en</item>
        <item name="android:fontFamily">@font/font_for_ar_en</item>
    </style>
    <style name="TextAppearance.App.Headline5" parent="TextAppearance.MaterialComponents.Headline5">
        <item name="fontFamily">@font/font_for_ar_en</item>
        <item name="android:fontFamily">@font/font_for_ar_en</item>
    </style>
    <style name="TextAppearance.App.Headline6" parent="TextAppearance.MaterialComponents.Headline6">
        <item name="fontFamily">@font/font_for_ar_en</item>
        <item name="android:fontFamily">@font/font_for_ar_en</item>
    </style>
    <style name="TextAppearance.App.NavigationDrawer.ListItem" parent="TextAppearance.MaterialComponents.Body1">
        <item name="fontFamily">@font/font_for_ar_en</item>
        <item name="android:fontFamily">@font/font_for_ar_en</item>
        <item name="android:textSize">16sp</item>
    </style>
    <style name="TextAppearance.App.NavigationDrawer.ListItemSmall" parent="TextAppearance.MaterialComponents.Body2">
        <item name="fontFamily">@font/font_for_ar_en</item>
        <item name="android:fontFamily">@font/font_for_ar_en</item>
        <item name="android:textSize">14sp</item>
    </style>
    <style name="TextAppearance.App.Overline" parent="TextAppearance.MaterialComponents.Overline">
        <item name="fontFamily">@font/font_for_ar_en</item>
        <item name="android:fontFamily">@font/font_for_ar_en</item>
    </style>
    <style name="TextAppearance.App.Subtitle1" parent="TextAppearance.MaterialComponents.Subtitle1">
        <item name="fontFamily">@font/font_for_ar_en</item>
        <item name="android:fontFamily">@font/font_for_ar_en</item>
    </style>
    <style name="TextAppearance.App.Subtitle2" parent="TextAppearance.MaterialComponents.Subtitle2">
        <item name="fontFamily">@font/font_for_ar_en</item>
        <item name="android:fontFamily">@font/font_for_ar_en</item>
    </style>
    <style name="Theme.HeadshotSettingsGameBooster" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        
    </style>
    <style name="ThemeOverlay.App.NavigationDrawer" parent="ThemeOverlay.MaterialComponents.Dark">
        
        <item name="textAppearanceListItem">@style/TextAppearance.App.NavigationDrawer.ListItem</item>
        <item name="textAppearanceListItemSmall">@style/TextAppearance.App.NavigationDrawer.ListItemSmall</item>
        <item name="textAppearanceButton">@style/TextAppearance.App.Button</item>

        
        <item name="android:fontFamily">@font/font_for_ar_en</item>
        <item name="fontFamily">@font/font_for_ar_en</item>

        
        <item name="materialButtonStyle">@style/Widget.App.Button.Gold</item>
    </style>
    <style name="Widget.App.Button.Gold" parent="Widget.MaterialComponents.Button">
        <item name="backgroundTint">@color/cyber_neon_gold</item>
        <item name="android:textColor">@color/black</item>
        <item name="cornerRadius">24dp</item>
        <item name="android:fontFamily">@font/font_for_ar_en</item>
        <item name="fontFamily">@font/font_for_ar_en</item>
    </style>
    <style name="Widget.App.Button.Outlined" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="strokeColor">@color/cyber_neon_gold</item>
        <item name="strokeWidth">1dp</item>
        <item name="android:textColor">@color/cyber_text_primary</item>
        <item name="backgroundTint">@color/cyber_dark_center</item>
        <item name="cornerRadius">24dp</item>
        <item name="android:fontFamily">@font/font_for_ar_en</item>
        <item name="fontFamily">@font/font_for_ar_en</item>
    </style>
    <style name="no_bar" parent="Theme.MaterialComponents.NoActionBar">
        
        <item name="colorPrimary">@color/electric_blue</item>
        <item name="colorPrimaryVariant">@color/neon_blue</item>
        <item name="colorOnPrimary">@color/black</item>
        
        <item name="colorSecondary">@color/neon_blue</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">@color/cyber_dark_start</item>
        
        <item name="android:windowBackground">@drawable/cyber_neon_gradient_bg</item>
        
        <item name="android:navigationBarColor">@color/cyber_dark_start</item>

        
        <item name="textAppearanceButton">@style/TextAppearance.App.Button</item>
        <item name="textAppearanceBody1">@style/TextAppearance.App.Body1</item>
        <item name="textAppearanceBody2">@style/TextAppearance.App.Body2</item>
        <item name="textAppearanceSubtitle1">@style/TextAppearance.App.Subtitle1</item>
        <item name="textAppearanceSubtitle2">@style/TextAppearance.App.Subtitle2</item>
        <item name="textAppearanceHeadline6">@style/TextAppearance.App.Headline6</item>
        <item name="textAppearanceHeadline5">@style/TextAppearance.App.Headline5</item>
        <item name="textAppearanceHeadline4">@style/TextAppearance.App.Headline4</item>
        <item name="textAppearanceHeadline3">@style/TextAppearance.App.Headline3</item>
        <item name="textAppearanceHeadline2">@style/TextAppearance.App.Headline2</item>
        <item name="textAppearanceHeadline1">@style/TextAppearance.App.Headline1</item>
        <item name="textAppearanceCaption">@style/TextAppearance.App.Caption</item>
        <item name="textAppearanceOverline">@style/TextAppearance.App.Overline</item>

        
        <item name="android:fontFamily">@font/font_for_ar_en</item>
        <item name="fontFamily">@font/font_for_ar_en</item>
    </style>
</resources>