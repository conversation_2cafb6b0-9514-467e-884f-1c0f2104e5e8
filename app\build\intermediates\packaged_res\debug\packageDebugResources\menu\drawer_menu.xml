<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <group android:checkableBehavior="single">
        <item
            android:id="@+id/nav_settings"
            android:icon="@drawable/ic_settings"
            android:title="@string/settings"
            app:fontFamily="@font/font_for_ar_en" />
        <item
            android:id="@+id/nav_rate"
            android:icon="@drawable/ic_rate"
            android:title="@string/rate_us"
            app:fontFamily="@font/font_for_ar_en" />
        <item
            android:id="@+id/nav_privacy"
            android:icon="@drawable/ic_privacy"
            android:title="@string/privacy_policy"
            app:fontFamily="@font/font_for_ar_en" />
        <item
            android:id="@+id/nav_about"
            android:icon="@drawable/ic_about"
            android:title="@string/about"
            app:fontFamily="@font/font_for_ar_en" />
        <item
            android:id="@+id/nav_exit"
            android:icon="@drawable/ic_exit"
            android:title="@string/exit"
            app:fontFamily="@font/font_for_ar_en" />
    </group>

    <!-- Premium features section removed from here as it will be shown in the nav_header.xml only -->
</menu>
