package com.mahmoudffyt.gfxbooster;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.ServiceInfo;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.util.Log;
import android.view.View;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.content.res.ColorStateList;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.cardview.widget.CardView;
import androidx.core.app.NotificationCompat;

import com.mahmoudffyt.gfxbooster.utils.AdManager;
import com.mahmoudffyt.gfxbooster.utils.PremiumManager;
import com.mahmoudffyt.gfxbooster.utils.RealTimeMonitor;
import com.mahmoudffyt.gfxbooster.models.SelectedGame;
import com.mahmoudffyt.gfxbooster.adapters.SelectedGamesAdapter;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.app.AlertDialog;
import android.util.Log;
import java.util.ArrayList;
import java.util.List;

import com.android.billingclient.api.ProductDetails;
import com.mahmoudffyt.gfxbooster.utils.BillingManager;

import java.util.List;

public class GameModeActivity extends AppCompatActivity {

    private static final String CHANNEL_ID = "game_mode_channel";
    private static final int NOTIFICATION_ID = 1002;

    private ImageView appLogo, logoGlow, buttonGlow, lightningEffect, energyLinesBg, energyGrid, btnBack;
    private TextView gameModeTitle, gameModeDescription, gameModeStatus;
    private TextView batteryTemperatureValue, networkPingValue, networkTypeValue, gameLimitText;

    private Button gameModeButton, addGameButton;
    private FrameLayout logoContainer, buttonContainer, adContainer;
    private RecyclerView selectedGamesRecycler;

    private boolean isGameModeActive = false;
    private SharedPreferences preferences;
    private Handler handler = new Handler();
    private BillingManager billingManager;

    // Ad and premium managers
    private AdManager adManager;
    private PremiumManager premiumManager;

    // Real-time monitoring
    private RealTimeMonitor realTimeMonitor;

    // Game selection
    private List<SelectedGame> selectedGames;
    private SelectedGamesAdapter gamesAdapter;
    private static final int MAX_FREE_GAMES = 1;
    private static final int REQUEST_CODE_GAME_SELECTION = 1001;

    // Trial tracking
    private static final int TRIAL_DAYS = 3;
    private static final String DIALOG_TYPE_TRIAL = "trial";
    private static final String DIALOG_TYPE_PREMIUM = "premium";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Set fullscreen
        getWindow().setFlags(
                WindowManager.LayoutParams.FLAG_FULLSCREEN,
                WindowManager.LayoutParams.FLAG_FULLSCREEN
        );

        setContentView(R.layout.activity_game_mode);

        // Initialize preferences
        preferences = getSharedPreferences("GameModePrefs", MODE_PRIVATE);
        isGameModeActive = preferences.getBoolean("isGameModeActive", false);

        // Initialize billing manager
        initializeBillingManager();

        // Initialize ad and premium managers
        adManager = AdManager.getInstance(this);
        premiumManager = new PremiumManager(this);

        // Initialize real-time monitor
        realTimeMonitor = new RealTimeMonitor(this);

        // Initialize selected games list
        selectedGames = new ArrayList<>();
        loadSelectedGames();

        // Initialize views
        initializeViews();

        // Load ads
        loadAds();

        // Create notification channel for Android 8.0+
        createNotificationChannel();

        // Start entrance animations
        startEntranceAnimations();

        // Set click listeners
        setupClickListeners();

        // Setup real-time monitoring
        setupRealTimeMonitoring();

        // Update UI based on current state
        updateUI();
    }

    private void initializeBillingManager() {
        billingManager = new BillingManager(this, new BillingManager.BillingUpdatesListener() {
            @Override
            public void onPurchaseComplete(boolean success) {
                if (success) {
                    Toast.makeText(GameModeActivity.this, getString(R.string.premium_features_unlocked), Toast.LENGTH_SHORT).show();
                    // Activate game mode now that user is premium
                    activateGameMode();
                }
            }

            @Override
            public void onProductDetailsRetrieved(List<ProductDetails> productDetailsList) {
                // Product details retrieved, ready for purchase
                Log.d("GameModeActivity", "Product details retrieved: " + productDetailsList.size());
            }
        });
    }

    // onResume method moved below

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == REQUEST_CODE_GAME_SELECTION && resultCode == RESULT_OK && data != null) {
            String gameName = data.getStringExtra("selected_game_name");
            String gamePackage = data.getStringExtra("selected_game_package");

            if (gameName != null && gamePackage != null) {
                // Create SelectedGame object and add it
                try {
                    PackageManager pm = getPackageManager();
                    ApplicationInfo appInfo = pm.getApplicationInfo(gamePackage, 0);
                    android.graphics.drawable.Drawable appIcon = pm.getApplicationIcon(appInfo);

                    SelectedGame game = new SelectedGame(gamePackage, gameName, appIcon);
                    addGame(game);
                } catch (PackageManager.NameNotFoundException e) {
                    // App not found, add with default icon
                    SelectedGame game = new SelectedGame(gamePackage, gameName,
                        getResources().getDrawable(R.drawable.ic_games), false);
                    addGame(game);
                }
            }
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        // Stop real-time monitoring
        if (realTimeMonitor != null) {
            realTimeMonitor.stopMonitoring();
        }

        // Clean up billing manager
        if (billingManager != null) {
            billingManager.destroy();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();

        // Check subscription status when activity comes to foreground
        if (billingManager != null) {
            billingManager.queryPurchases();
        }

        // Reload ads when activity resumes
        loadAds();
    }

    /**
     * Load ads in the activity
     */
    private void loadAds() {
        // Skip if premium user
        if (premiumManager != null && premiumManager.isPremium()) {
            if (adContainer != null) {
                adContainer.setVisibility(View.GONE);
            }
            return;
        }

        // Load banner ad at the bottom of the screen
        if (adContainer != null) {
            adContainer.setVisibility(View.VISIBLE);
            adManager.loadBannerAd(this, adContainer, AdManager.BANNER_AD_UNIT_ID);
        }

        // Preload interstitial ad for when Game Mode is deactivated
        adManager.preloadInterstitialAd(this);

        // Preload rewarded ad for premium trial
        adManager.preloadRewardedAd(this);
    }

    private void initializeViews() {
        // Logo and background elements
        appLogo = findViewById(R.id.app_logo);
        logoGlow = findViewById(R.id.logo_glow);
        energyLinesBg = findViewById(R.id.speed_lines_bg);
        energyGrid = findViewById(R.id.energy_grid);
        btnBack = findViewById(R.id.btn_back);
        adContainer = findViewById(R.id.ad_container);

        // Containers
        logoContainer = findViewById(R.id.logo_container);
        buttonContainer = findViewById(R.id.button_container);

        // Button and effects
        gameModeButton = findViewById(R.id.game_mode_button);
        buttonGlow = findViewById(R.id.button_glow);
        lightningEffect = findViewById(R.id.lightning_effect);

        // Text elements
        gameModeTitle = findViewById(R.id.game_mode_title);
        gameModeDescription = findViewById(R.id.game_mode_description);
        gameModeStatus = findViewById(R.id.game_mode_status);

        // Game selection elements
        addGameButton = findViewById(R.id.add_game_button);
        selectedGamesRecycler = findViewById(R.id.selected_games_recycler);
        gameLimitText = findViewById(R.id.game_limit_text);

        // Real-time monitoring elements
        batteryTemperatureValue = findViewById(R.id.battery_temperature_value);
        networkPingValue = findViewById(R.id.network_ping_value);
        networkTypeValue = findViewById(R.id.network_type_value);

        // Setup games RecyclerView
        setupGamesRecyclerView();

        // Update game limit text based on premium status
        updateGameLimitText();
    }

    private void startEntranceAnimations() {
        // Load animations
        Animation fadeIn = AnimationUtils.loadAnimation(this, R.anim.fade_in);
        Animation slideUp = AnimationUtils.loadAnimation(this, R.anim.card_fade_in_slide_up);
        Animation logoPulse = AnimationUtils.loadAnimation(this, R.anim.logo_pulse);
        Animation buttonPulse = AnimationUtils.loadAnimation(this, R.anim.button_pulse);

        // Animate speed lines background
        animateSpeedLines();

        // Apply animations with delays for sequence
        appLogo.startAnimation(fadeIn);
        logoGlow.startAnimation(logoPulse);

        // Delay the title and description animations
        handler.postDelayed(() -> {
            gameModeTitle.setVisibility(View.VISIBLE);
            gameModeTitle.startAnimation(fadeIn);
        }, 300);

        handler.postDelayed(() -> {
            gameModeDescription.setVisibility(View.VISIBLE);
            gameModeDescription.startAnimation(fadeIn);
        }, 500);

        // Delay the button animation
        handler.postDelayed(() -> {
            buttonContainer.setVisibility(View.VISIBLE);
            buttonContainer.startAnimation(slideUp);
            buttonGlow.startAnimation(buttonPulse);
        }, 700);



        // Delay the status text animation
        handler.postDelayed(() -> {
            gameModeStatus.setVisibility(View.VISIBLE);
            gameModeStatus.startAnimation(fadeIn);
        }, 900);

        // Shrink logo after all elements are visible
        handler.postDelayed(() -> {
            Animation logoShrink = AnimationUtils.loadAnimation(this, R.anim.logo_shrink);
            logoContainer.startAnimation(logoShrink);
        }, 1200);
    }

    private void setupClickListeners() {
        // Back button click listener
        btnBack.setOnClickListener(v -> finish());

        // Game mode button click listener
        gameModeButton.setOnClickListener(v -> {
            if (isGameModeActive) {
                deactivateGameMode();
            } else {
                activateGameMode();
            }
        });

        // Add game button click listener
        addGameButton.setOnClickListener(v -> {
            showGameSelectionDialog();
        });
    }

    private void activateGameMode() {
        // Check if user is premium
        if (!premiumManager.isPremium()) {
            // First time activation - record start date and show trial dialog
            if (premiumManager.isFirstActivation()) {
                premiumManager.recordFirstActivation();
                showDialog(DIALOG_TYPE_TRIAL);
                return;
            }

            // Check if trial period has expired
            if (premiumManager.isTrialExpired()) {
                // Trial expired - show premium features dialog
               // Toast.makeText(this, getString(R.string.trial_expired), Toast.LENGTH_SHORT).show();
                showDialog(DIALOG_TYPE_PREMIUM);
                return;
            }

            // Check if user can watch an ad today
            if (premiumManager.canWatchAdToday()) {
                // Can watch ad today - show trial dialog
                showDialog(DIALOG_TYPE_TRIAL);
                return;
            } else {
                // Already watched ad today - show premium features dialog
           //     Toast.makeText(this, getString(R.string.trial_limit_reached), Toast.LENGTH_SHORT).show();
                showDialog(DIALOG_TYPE_PREMIUM);
                return;
            }
        }

        // Premium user or after successful ad watch - activate game mode
        performActivation();
    }

    /**
     * Perform the actual game mode activation
     */
    private void performActivation() {
        // Update state
        isGameModeActive = true;
        preferences.edit().putBoolean("isGameModeActive", true).apply();

        // Show lightning effect
        lightningEffect.setVisibility(View.VISIBLE);
        Animation lightningFlash = AnimationUtils.loadAnimation(this, R.anim.lightning_flash);
        lightningEffect.startAnimation(lightningFlash);

        // Update button text and appearance
        gameModeButton.setText(getString(R.string.deactivate_game_mode));
        gameModeButton.setBackground(getResources().getDrawable(R.drawable.game_mode_button_active));
        gameModeButton.setTextColor(getResources().getColor(R.color.neon_orange));
        gameModeButton.setCompoundDrawableTintList(ColorStateList.valueOf(getResources().getColor(R.color.neon_orange)));

        // Update status text with shadow animation
        gameModeStatus.setText(getString(R.string.game_mode_status_active));
        gameModeStatus.setShadowLayer(10, 0, 0, getResources().getColor(R.color.neon_blue));



        // Show notification
        showGameModeNotification();

        // Start the game mode service to monitor for game launches
        startGameModeService();

        // Update UI
        updateUI();

        // Update launch buttons visibility
        updateLaunchButtonsVisibility();
    }

    private void deactivateGameMode() {
        // Show interstitial ad before deactivating (only for non-premium users)
        if (!premiumManager.isPremium() && adManager != null) {
            adManager.showInterstitialAd(this, new AdManager.InterstitialAdCallback() {
                @Override
                public void onAdClosed() {
                    // Continue with deactivation after ad is closed
                    performDeactivation();
                }

                @Override
                public void onAdFailed() {
                    // Continue with deactivation if ad fails to load
                    performDeactivation();
                }
            });
        } else {
            // Premium user or ad manager not available, proceed directly
            performDeactivation();
        }
    }

    /**
     * Perform the actual game mode deactivation
     */
    private void performDeactivation() {
        // Update state
        isGameModeActive = false;
        preferences.edit().putBoolean("isGameModeActive", false).apply();

        // Hide lightning effect
        lightningEffect.clearAnimation();
        lightningEffect.setVisibility(View.INVISIBLE);

        // Update button text and appearance
        gameModeButton.setText(getString(R.string.activate_game_mode));
        gameModeButton.setBackground(getResources().getDrawable(R.drawable.game_mode_button_custom));
        gameModeButton.setTextColor(getResources().getColor(R.color.neon_blue));
        gameModeButton.setCompoundDrawableTintList(ColorStateList.valueOf(getResources().getColor(R.color.neon_blue)));

        // Update status text
        gameModeStatus.setText(getString(R.string.game_mode_status_inactive));
        gameModeStatus.setShadowLayer(0, 0, 0, 0);



        // Cancel notification
        NotificationManager notificationManager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
        notificationManager.cancel(NOTIFICATION_ID);

        // Stop the game mode service
        stopGameModeService();

        // Update UI
        updateUI();

        // Update launch buttons visibility
        updateLaunchButtonsVisibility();
    }



    private void updateUI() {
        if (isGameModeActive) {
            // Update button text and appearance
            gameModeButton.setText(getString(R.string.deactivate_game_mode));
            gameModeButton.setBackground(getResources().getDrawable(R.drawable.game_mode_button_active));
            gameModeButton.setTextColor(getResources().getColor(R.color.neon_orange));
            gameModeButton.setCompoundDrawableTintList(ColorStateList.valueOf(getResources().getColor(R.color.neon_orange)));

            // Show lightning effect
            lightningEffect.setVisibility(View.VISIBLE);
            Animation lightningFlash = AnimationUtils.loadAnimation(this, R.anim.lightning_flash);
            lightningEffect.startAnimation(lightningFlash);

            // Update status text with shadow
            gameModeStatus.setText(getString(R.string.game_mode_status_active));
            gameModeStatus.setShadowLayer(10, 0, 0, getResources().getColor(R.color.neon_blue));
        } else {
            // Update button text and appearance
            gameModeButton.setText(getString(R.string.activate_game_mode));
            gameModeButton.setBackground(getResources().getDrawable(R.drawable.game_mode_button_custom));
            gameModeButton.setTextColor(getResources().getColor(R.color.cyber_neon_cyan));
            gameModeButton.setCompoundDrawableTintList(ColorStateList.valueOf(getResources().getColor(R.color.neon_blue)));

            // Hide lightning effect
            lightningEffect.clearAnimation();
            lightningEffect.setVisibility(View.INVISIBLE);

            // Update status text
            gameModeStatus.setText(getString(R.string.game_mode_status_inactive));
            gameModeStatus.setShadowLayer(0, 0, 0, 0);
        }
    }

    private void createNotificationChannel() {
        // Create the notification channel for Android 8.0+
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                    CHANNEL_ID,
                    "Game Mode Notifications",
                    NotificationManager.IMPORTANCE_DEFAULT);
            channel.setDescription("Notifications for Game Mode status");

            NotificationManager notificationManager = getSystemService(NotificationManager.class);
            notificationManager.createNotificationChannel(channel);
        }
    }

    private void showGameModeNotification() {
        // Create an intent to open the GameModeActivity
        Intent intent = new Intent(this, GameModeActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);

        // Create pending intent
        int flags = PendingIntent.FLAG_UPDATE_CURRENT;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            flags |= PendingIntent.FLAG_IMMUTABLE;
        }
        PendingIntent pendingIntent = PendingIntent.getActivity(this, 0, intent, flags);

        // Build the notification
        NotificationCompat.Builder builder = new NotificationCompat.Builder(this, CHANNEL_ID)
                .setSmallIcon(R.drawable.ic_power)
                .setContentTitle(getString(R.string.game_mode_notification_title))
                .setContentText(getString(R.string.game_mode_notification_text))
                .setPriority(NotificationCompat.PRIORITY_DEFAULT)
                .setContentIntent(pendingIntent)
                .setOngoing(true); // Make it persistent

        // Show the notification
        NotificationManager notificationManager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
        notificationManager.notify(NOTIFICATION_ID, builder.build());
    }

    private void startGameModeService() {
        // In a real app, you would start a service to monitor for game launches
        // For this example, we'll just simulate it

        // Create an intent for the service
        Intent serviceIntent = new Intent(this, GameModeService.class);

        // Start the service based on Android version
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            // For Android 12+ (API 31+), need to specify foreground service type
            serviceIntent.putExtra("foregroundServiceType", ServiceInfo.FOREGROUND_SERVICE_TYPE_DATA_SYNC);
            startForegroundService(serviceIntent);
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            // For Android 8.0+ (API 26+) to Android 11
            startForegroundService(serviceIntent);
        } else {
            // For older Android versions
            startService(serviceIntent);
        }
    }

    private void animateSpeedLines() {
        // Create infinite animation for speed lines
        ObjectAnimator translateX = ObjectAnimator.ofFloat(energyLinesBg, "translationX", -200f, 200f);
        translateX.setDuration(8000);
        translateX.setRepeatCount(ObjectAnimator.INFINITE);
        translateX.setRepeatMode(ObjectAnimator.REVERSE);

        ObjectAnimator translateY = ObjectAnimator.ofFloat(energyLinesBg, "translationY", -100f, 100f);
        translateY.setDuration(6000);
        translateY.setRepeatCount(ObjectAnimator.INFINITE);
        translateY.setRepeatMode(ObjectAnimator.REVERSE);

        AnimatorSet animatorSet = new AnimatorSet();
        animatorSet.playTogether(translateX, translateY);
        animatorSet.start();
    }

    private void stopGameModeService() {
        // Stop the game mode service
        Intent serviceIntent = new Intent(this, GameModeService.class);
        stopService(serviceIntent);
    }

    /**
     * Show the appropriate dialog based on type
     * @param dialogType DIALOG_TYPE_TRIAL or DIALOG_TYPE_PREMIUM
     */
    private void showDialog(String dialogType) {
        if (DIALOG_TYPE_TRIAL.equals(dialogType)) {
            showTrialDialog();
        } else {
            showPremiumFeaturesDialog();
        }
    }

    /**
     * Show the trial dialog with ad watching option
     */
    private void showTrialDialog() {
        // Create and show trial dialog
        androidx.appcompat.app.AlertDialog.Builder builder = new androidx.appcompat.app.AlertDialog.Builder(this);
        View dialogView = getLayoutInflater().inflate(R.layout.dialog_premium_trial, null);
        builder.setView(dialogView);

        // Get dialog elements
        Button watchAdButton = dialogView.findViewById(R.id.btn_watch_ad);
        Button upgradeButton = dialogView.findViewById(R.id.btn_upgrade);
        Button cancelButton = dialogView.findViewById(R.id.btn_cancel);
        TextView trialInfoText = dialogView.findViewById(R.id.trial_info_text);
        TextView trialDescription = dialogView.findViewById(R.id.trial_description);

        // Get days remaining in trial
        int daysRemaining = premiumManager.getDaysRemainingInTrial();

        // Update trial info text with days remaining
        trialInfoText.setText(getString(R.string.days_remaining, daysRemaining));

        // Update description text to explain the trial
        trialDescription.setText(getString(R.string.watch_ad_for_trial));

        // If already watched ad today, disable watch ad button
        if (premiumManager.hasWatchedAdToday()) {
            watchAdButton.setEnabled(false);
            watchAdButton.setAlpha(0.5f);
            trialInfoText.setText(getString(R.string.trial_limit_reached));
        }

        // Create dialog
        androidx.appcompat.app.AlertDialog dialog = builder.create();

        // Set button click listeners
        watchAdButton.setOnClickListener(v -> {
            // Show rewarded ad for trial activation
            if (adManager != null) {
                adManager.showRewardedAd(this, new AdManager.RewardedAdCallback() {
                    @Override
                    public void onRewarded(boolean success) {
                        if (success) {
                            // Record that user watched an ad today
                            premiumManager.recordAdWatched();

                            Toast.makeText(GameModeActivity.this, getString(R.string.trial_activated), Toast.LENGTH_SHORT).show();
                            dialog.dismiss();

                            // Activate game mode
                            performActivation();

                            // Schedule deactivation after 1 hour
                            scheduleDeactivation();
                        } else {
                            Toast.makeText(GameModeActivity.this, getString(R.string.ad_reward_failed), Toast.LENGTH_SHORT).show();
                        }
                    }

                    @Override
                    public void onAdClosed() {
                        // Ad was closed without reward
                        Toast.makeText(GameModeActivity.this, getString(R.string.ad_closed_without_reward), Toast.LENGTH_SHORT).show();
                    }

                    @Override
                    public void onAdFailed() {
                        // Ad failed to load
                        Toast.makeText(GameModeActivity.this, getString(R.string.ad_failed_to_load), Toast.LENGTH_SHORT).show();
                    }
                });
            } else {
                // Fallback if ad manager is not available (for testing)
                premiumManager.recordAdWatched();
                Toast.makeText(GameModeActivity.this, getString(R.string.trial_activated), Toast.LENGTH_SHORT).show();
                dialog.dismiss();

                // Activate game mode
                performActivation();

                // Schedule deactivation after 1 hour
                scheduleDeactivation();
            }
        });

        upgradeButton.setOnClickListener(v -> {
            // Launch Google Play Billing flow
            if (billingManager != null) {
                billingManager.purchaseMonthlySubscription(this);
            } else {
                // Initialize billing manager if needed
                initializeBillingManager();
                // Try again after initialization
                if (billingManager != null) {
                    billingManager.purchaseMonthlySubscription(this);
                } else {
                    Toast.makeText(this, getString(R.string.purchase_failed), Toast.LENGTH_SHORT).show();
                }
            }
            dialog.dismiss();
        });

        cancelButton.setOnClickListener(v -> {
            dialog.dismiss();
        });

        // Show dialog
        dialog.show();
    }

    /**
     * Schedule deactivation of Game Mode after 1 hour
     */
    private void scheduleDeactivation() {
        // Save activation time
        SharedPreferences trialPrefs = getSharedPreferences("TrialPrefs", MODE_PRIVATE);
        SharedPreferences.Editor editor = trialPrefs.edit();

        // Set expiration time (1 hour from now)
        long expirationTime = System.currentTimeMillis() + (60 * 60 * 1000);
        editor.putLong("gameModeExpirationTime", expirationTime);
        editor.apply();

        // Schedule deactivation after 1 hour
        handler.postDelayed(this::deactivateGameMode, 60 * 60 * 1000);

        // Show toast that game mode is activated for 1 hour
        Toast.makeText(this, getString(R.string.game_mode_activated_for_one_hour), Toast.LENGTH_SHORT).show();
    }

    private void activateGameModeForOneHour() {
        // This method is now replaced by performActivation() + scheduleDeactivation()
        // Keeping it for backward compatibility

        // Activate game mode
        performActivation();

        // Schedule deactivation after 1 hour
        scheduleDeactivation();
    }

    private void showPremiumFeaturesDialog() {
        // Create and show premium features dialog
        androidx.appcompat.app.AlertDialog.Builder builder = new androidx.appcompat.app.AlertDialog.Builder(this, R.style.AlertDialogTheme);
        View premiumView = getLayoutInflater().inflate(R.layout.dialog_premium_features, null);
        builder.setView(premiumView);

        // Create dialog
        androidx.appcompat.app.AlertDialog dialog = builder.create();

        // Setup buttons
        Button subscribeButton = premiumView.findViewById(R.id.btn_subscribe);
        Button laterButton = premiumView.findViewById(R.id.btn_maybe_later);

        // Get price TextView
        TextView priceTextView = premiumView.findViewById(R.id.premium_price);

        // Update price from Google Play Billing
        if (billingManager != null) {
            // Get product details and update price
            billingManager.getMonthlySubscriptionDetails(new com.mahmoudffyt.gfxbooster.utils.BillingManager.ProductDetailsCallback() {
                @Override
                public void onProductDetailsReceived(com.android.billingclient.api.ProductDetails productDetails) {
                    if (productDetails != null) {
                        // Get the price from product details
                        String formattedPrice = "";

                        // For subscription products
                        if (productDetails.getSubscriptionOfferDetails() != null &&
                            !productDetails.getSubscriptionOfferDetails().isEmpty()) {

                            com.android.billingclient.api.ProductDetails.SubscriptionOfferDetails offerDetails =
                                productDetails.getSubscriptionOfferDetails().get(0);

                            if (offerDetails.getPricingPhases() != null &&
                                offerDetails.getPricingPhases().getPricingPhaseList() != null &&
                                !offerDetails.getPricingPhases().getPricingPhaseList().isEmpty()) {

                                com.android.billingclient.api.ProductDetails.PricingPhase pricingPhase =
                                    offerDetails.getPricingPhases().getPricingPhaseList().get(0);

                                formattedPrice = pricingPhase.getFormattedPrice();
                            }
                        }

                        // Update UI on main thread
                        final String finalPrice = formattedPrice;
                        runOnUiThread(() -> {
                            if (priceTextView != null && !finalPrice.isEmpty()) {
                                priceTextView.setText(finalPrice);
                            }
                        });
                    }
                }
            });
        }

        // Set click listeners
        subscribeButton.setOnClickListener(v -> {
            // Launch Google Play Billing flow
            if (billingManager != null) {
                billingManager.purchaseMonthlySubscription(this);
            } else {
                // Initialize billing manager if needed
                initializeBillingManager();
                // Try again after initialization
                if (billingManager != null) {
                    billingManager.purchaseMonthlySubscription(this);
                } else {
                    Toast.makeText(this, getString(R.string.purchase_failed), Toast.LENGTH_SHORT).show();
                }
            }

            // Dismiss dialog
            if (dialog != null && dialog.isShowing()) {
                dialog.dismiss();
            }
        });

        laterButton.setOnClickListener(v -> {
            // Show interstitial ad when user clicks "Maybe Later" (up to 4 times per session)
            if (adManager != null && adManager.getInterstitialCount() < 4) {
                adManager.showInterstitialAd(this, new AdManager.InterstitialAdCallback() {
                    @Override
                    public void onAdClosed() {
                        // Dismiss dialog after ad is closed
                        if (dialog != null && dialog.isShowing()) {
                            dialog.dismiss();
                        }
                    }

                    @Override
                    public void onAdFailed() {
                        // Dismiss dialog if ad fails to load
                        if (dialog != null && dialog.isShowing()) {
                            dialog.dismiss();
                        }
                    }
                });
            } else {
                // Just dismiss the dialog if we've shown too many ads
                if (dialog != null && dialog.isShowing()) {
                    dialog.dismiss();
                }
            }
        });

        // Show dialog
        dialog.show();

        // Make sure dialog doesn't get cut off on smaller screens
        dialog.getWindow().setLayout(android.view.ViewGroup.LayoutParams.MATCH_PARENT, android.view.ViewGroup.LayoutParams.WRAP_CONTENT);

        // Schedule trial expiration
        scheduleTrialExpiration();
    }

    private void scheduleTrialExpiration() {
        // Schedule a handler to check trial expiration after 1 hour
        handler.postDelayed(() -> {
            // Check if trial has expired
            SharedPreferences trialPrefs = getSharedPreferences("TrialPrefs", MODE_PRIVATE);
            long expirationTime = trialPrefs.getLong("gameModeExpirationTime", 0);

            if (System.currentTimeMillis() > expirationTime && isGameModeActive) {
                // Trial has expired, deactivate game mode
                deactivateGameMode();

                // Show toast
                Toast.makeText(this, getString(R.string.trial_expired), Toast.LENGTH_SHORT).show();
            }
        }, 60 * 60 * 1000); // 1 hour
    }

    // ==================== GAME SELECTION METHODS ====================

    private void setupGamesRecyclerView() {
        gamesAdapter = new SelectedGamesAdapter(this, selectedGames);
        selectedGamesRecycler.setLayoutManager(new LinearLayoutManager(this));
        selectedGamesRecycler.setAdapter(gamesAdapter);

        gamesAdapter.setOnGameActionListener(new SelectedGamesAdapter.OnGameActionListener() {
            @Override
            public void onGameRemoved(SelectedGame game, int position) {
                removeGame(position);
            }

            @Override
            public void onGameLaunched(SelectedGame game) {
                launchGame(game);
            }
        });

        // Update RecyclerView visibility
        updateGamesRecyclerVisibility();
    }

    private void updateGamesRecyclerVisibility() {
        if (selectedGames.isEmpty()) {
            selectedGamesRecycler.setVisibility(View.GONE);
        } else {
            selectedGamesRecycler.setVisibility(View.VISIBLE);
        }

        // Update launch buttons visibility based on game mode status
        updateLaunchButtonsVisibility();
    }

    private void updateLaunchButtonsVisibility() {
        // Hide launch buttons if game mode is not active
        boolean gameModeActive = isGameModeActive;

        // Update adapter to show/hide launch buttons
        if (gamesAdapter != null) {
            gamesAdapter.setLaunchButtonsVisible(gameModeActive);
            gamesAdapter.notifyDataSetChanged();
        }
    }

    private void updateGameLimitText() {
        if (premiumManager.isPremium()) {
            gameLimitText.setText(getString(R.string.pro_version_unlimited_games));
            gameLimitText.setTextColor(getResources().getColor(R.color.neon_green));
        } else {
            gameLimitText.setText(getString(R.string.free_version_game_limit));
            gameLimitText.setTextColor(getResources().getColor(R.color.text_color_secondary));
        }
    }

    private void loadSelectedGames() {
        // Load selected games from SharedPreferences
        SharedPreferences gamePrefs = getSharedPreferences("SelectedGames", MODE_PRIVATE);
        String gamesJson = gamePrefs.getString("games", "");

        // For now, we'll keep it simple and just store package names
        if (!gamesJson.isEmpty()) {
            String[] packageNames = gamesJson.split(",");
            for (String packageName : packageNames) {
                if (!packageName.trim().isEmpty()) {
                    addGameFromPackageName(packageName.trim());
                }
            }
        }
    }

    private void saveSelectedGames() {
        // Save selected games to SharedPreferences
        SharedPreferences gamePrefs = getSharedPreferences("SelectedGames", MODE_PRIVATE);
        StringBuilder gamesJson = new StringBuilder();

        for (int i = 0; i < selectedGames.size(); i++) {
            if (i > 0) gamesJson.append(",");
            gamesJson.append(selectedGames.get(i).getPackageName());
        }

        gamePrefs.edit().putString("games", gamesJson.toString()).apply();
    }

    private void addGameFromPackageName(String packageName) {
        try {
            PackageManager pm = getPackageManager();
            ApplicationInfo appInfo = pm.getApplicationInfo(packageName, 0);
            String appName = pm.getApplicationLabel(appInfo).toString();
            android.graphics.drawable.Drawable appIcon = pm.getApplicationIcon(appInfo);

            SelectedGame game = new SelectedGame(packageName, appName, appIcon);
            selectedGames.add(game);
        } catch (PackageManager.NameNotFoundException e) {
            // App not installed, add with placeholder
            SelectedGame game = new SelectedGame(packageName, packageName,
                getResources().getDrawable(R.drawable.ic_games), false);
            selectedGames.add(game);
        }
    }

    private void showGameSelectionDialog() {
        // Check if user can add more games
        if (!premiumManager.isPremium() && selectedGames.size() >= MAX_FREE_GAMES) {
            Toast.makeText(this, getString(R.string.game_limit_reached), Toast.LENGTH_SHORT).show();
            showPremiumFeaturesDialog();
            return;
        }

        // Launch GameSelectionActivity
        Intent intent = new Intent(this, GameSelectionActivity.class);
        startActivityForResult(intent, REQUEST_CODE_GAME_SELECTION);
    }

    private void showAllAppsSelectionDialog() {
        // Get list of all user apps
        List<SelectedGame> availableApps = getAllUserApps();

        if (availableApps.isEmpty()) {
            Toast.makeText(this, "لم يتم العثور على تطبيقات", Toast.LENGTH_SHORT).show();
            return;
        }

        // Create dialog
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("اختر تطبيق");

        // Create adapter for apps list
        String[] appNames = new String[availableApps.size()];
        for (int i = 0; i < availableApps.size(); i++) {
            appNames[i] = availableApps.get(i).getAppName();
        }

        builder.setItems(appNames, (dialog, which) -> {
            SelectedGame selectedApp = availableApps.get(which);
            addGame(selectedApp);
        });

        builder.setNegativeButton(getString(R.string.cancel), null);
        builder.show();
    }

    private List<SelectedGame> getAllUserApps() {
        List<SelectedGame> apps = new ArrayList<>();
        PackageManager pm = getPackageManager();

        // Get list of installed applications
        List<ApplicationInfo> installedApps = pm.getInstalledApplications(PackageManager.GET_META_DATA);

        for (ApplicationInfo appInfo : installedApps) {
            // Check if it's a user app with launcher intent
            Intent launchIntent = pm.getLaunchIntentForPackage(appInfo.packageName);
            if (launchIntent == null) continue;

            // Skip system apps
            if ((appInfo.flags & ApplicationInfo.FLAG_SYSTEM) != 0) {
                continue;
            }

            try {
                String appName = pm.getApplicationLabel(appInfo).toString();
                android.graphics.drawable.Drawable appIcon = pm.getApplicationIcon(appInfo);

                SelectedGame app = new SelectedGame(appInfo.packageName, appName, appIcon);

                // Don't add if already selected
                if (!isGameAlreadySelected(app.getPackageName())) {
                    apps.add(app);
                }
            } catch (Exception e) {
                // Skip this app if there's an error
            }
        }

        // Sort apps alphabetically
        apps.sort((a, b) -> a.getAppName().compareToIgnoreCase(b.getAppName()));

        return apps;
    }

    private List<SelectedGame> getInstalledGames() {
        List<SelectedGame> games = new ArrayList<>();
        PackageManager pm = getPackageManager();

        // Get list of installed applications
        List<ApplicationInfo> installedApps = pm.getInstalledApplications(PackageManager.GET_META_DATA);
        Log.d("GameModeActivity", "Total installed apps: " + installedApps.size());

        int gameCount = 0;
        for (ApplicationInfo appInfo : installedApps) {
            // Check if it's a game (has CATEGORY_GAME or is in games category)
            if (isGameApp(appInfo, pm)) {
                gameCount++;
                Log.d("GameModeActivity", "Found game: " + appInfo.packageName);
                try {
                    String appName = pm.getApplicationLabel(appInfo).toString();
                    android.graphics.drawable.Drawable appIcon = pm.getApplicationIcon(appInfo);

                    SelectedGame game = new SelectedGame(appInfo.packageName, appName, appIcon);

                    // Don't add if already selected
                    if (!isGameAlreadySelected(game.getPackageName())) {
                        games.add(game);
                        Log.d("GameModeActivity", "Added game: " + appName);
                    } else {
                        Log.d("GameModeActivity", "Game already selected: " + appName);
                    }
                } catch (Exception e) {
                    Log.e("GameModeActivity", "Error processing game: " + appInfo.packageName, e);
                }
            }
        }

        Log.d("GameModeActivity", "Total games found: " + gameCount + ", Available to add: " + games.size());

        // Sort games alphabetically
        games.sort((a, b) -> a.getAppName().compareToIgnoreCase(b.getAppName()));

        return games;
    }

    private boolean isGameApp(ApplicationInfo appInfo, PackageManager pm) {
        // Check if app has a launcher intent (user app)
        Intent launchIntent = pm.getLaunchIntentForPackage(appInfo.packageName);
        if (launchIntent == null) return false;

        // Skip system apps but allow some popular games that might be pre-installed
        if ((appInfo.flags & ApplicationInfo.FLAG_SYSTEM) != 0) {
            String packageName = appInfo.packageName.toLowerCase();
            // Allow some popular pre-installed games
            if (!packageName.contains("game") && !packageName.contains("play") &&
                !packageName.contains("pubg") && !packageName.contains("freefire") &&
                !packageName.contains("callofduty") && !packageName.contains("minecraft")) {
                return false;
            }
        }

        String packageName = appInfo.packageName.toLowerCase();
        String appName = "";
        try {
            appName = pm.getApplicationLabel(appInfo).toString().toLowerCase();
        } catch (Exception e) {
            // If we can't get app name, just use package name
        }

        // Check for game-related keywords in package name or app name
        String[] gameKeywords = {
            "game", "play", "arcade", "action", "adventure", "puzzle", "racing",
            "sport", "strategy", "simulation", "rpg", "mmo", "fps", "battle",
            "war", "fight", "shoot", "gun", "weapon", "hero", "legend", "saga",
            "quest", "dungeon", "castle", "kingdom", "empire", "world", "craft",
            "build", "farm", "city", "tycoon", "casino", "poker", "card", "chess",
            "board", "trivia", "word", "match", "candy", "bubble", "jewel",
            "zombie", "dragon", "magic", "fantasy", "space", "star", "galaxy",
            "car", "bike", "truck", "train", "plane", "ship", "boat", "soccer",
            "football", "basketball", "tennis", "golf", "baseball", "hockey",
            "cricket", "volleyball", "badminton", "bowling", "pool", "snooker",
            // Popular game names
            "pubg", "fortnite", "minecraft", "roblox", "among", "clash", "candy",
            "angry", "temple", "subway", "hill", "plants", "zombies", "cut",
            "fruit", "ninja", "bird", "run", "jump", "dash", "rush", "escape",
            "adventure", "quest", "legend", "saga", "story", "tale", "epic",
            "super", "mega", "ultra", "pro", "master", "king", "queen", "prince",
            "princess", "knight", "warrior", "fighter", "hunter", "shooter",
            "sniper", "battle", "combat", "war", "army", "military", "soldier",
            "tank", "gun", "weapon", "bomb", "missile", "rocket", "laser",
            "fire", "flame", "ice", "water", "earth", "wind", "thunder", "storm",
            "free", "fire", "garena", "tencent", "supercell", "king", "zynga",
            "ea", "ubisoft", "activision", "blizzard", "valve", "steam", "epic"
        };

        // Check if package name or app name contains any game keywords
        for (String keyword : gameKeywords) {
            if (packageName.contains(keyword) || appName.contains(keyword)) {
                return true;
            }
        }

        // Additional check: if it's in Games category (Android 8.0+)
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            try {
                if (appInfo.category == ApplicationInfo.CATEGORY_GAME) {
                    return true;
                }
            } catch (Exception e) {
                // Ignore if category is not available
            }
        }

        return false;
    }

    private boolean isGameAlreadySelected(String packageName) {
        for (SelectedGame game : selectedGames) {
            if (game.getPackageName().equals(packageName)) {
                return true;
            }
        }
        return false;
    }

    private void addGame(SelectedGame game) {
        // Check if user can add more games
        if (!premiumManager.isPremium() && selectedGames.size() >= MAX_FREE_GAMES) {
            Toast.makeText(this, getString(R.string.game_limit_reached), Toast.LENGTH_SHORT).show();
            showPremiumFeaturesDialog();
            return;
        }

        // Add game to list
        selectedGames.add(game);
        gamesAdapter.notifyItemInserted(selectedGames.size() - 1);

        // Update UI
        updateGamesRecyclerVisibility();
        saveSelectedGames();

        Toast.makeText(this, getString(R.string.game_added_successfully), Toast.LENGTH_SHORT).show();
    }

    private void removeGame(int position) {
        if (position >= 0 && position < selectedGames.size()) {
            selectedGames.remove(position);
            gamesAdapter.notifyItemRemoved(position);
            gamesAdapter.notifyItemRangeChanged(position, selectedGames.size());

            // Update UI
            updateGamesRecyclerVisibility();
            saveSelectedGames();

            Toast.makeText(this, getString(R.string.game_removed), Toast.LENGTH_SHORT).show();
        }
    }

    private void launchGame(SelectedGame game) {
        try {
            Intent launchIntent = getPackageManager().getLaunchIntentForPackage(game.getPackageName());
            if (launchIntent != null) {
                startActivity(launchIntent);
                Toast.makeText(this, getString(R.string.game_launched), Toast.LENGTH_SHORT).show();
            } else {
                Toast.makeText(this, getString(R.string.game_not_installed), Toast.LENGTH_SHORT).show();
            }
        } catch (Exception e) {
            Toast.makeText(this, "Error launching game: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }

    // ==================== REAL-TIME MONITORING METHODS ====================

    private void setupRealTimeMonitoring() {
        realTimeMonitor.setMonitorListener(new RealTimeMonitor.MonitorListener() {
            @Override
            public void onBatteryTemperatureUpdate(float temperature) {
                updateBatteryTemperature(temperature);
            }

            @Override
            public void onNetworkPingUpdate(int ping) {
                updateNetworkPing(ping);
            }

            @Override
            public void onNetworkTypeUpdate(String networkType) {
                updateNetworkType(networkType);
            }
        });

        // Start monitoring
        realTimeMonitor.startMonitoring();
    }

    private void updateBatteryTemperature(float temperature) {
        if (temperature > 0) {
            batteryTemperatureValue.setText(String.format("%.1f°C", temperature));

            // Change color based on temperature
            if (temperature < 35) {
                batteryTemperatureValue.setTextColor(getResources().getColor(R.color.neon_blue));
            } else if (temperature < 40) {
                batteryTemperatureValue.setTextColor(getResources().getColor(R.color.neon_green));
            } else if (temperature < 45) {
                batteryTemperatureValue.setTextColor(getResources().getColor(R.color.neon_orange));
            } else {
                batteryTemperatureValue.setTextColor(getResources().getColor(R.color.neon_red));
            }
        } else {
            batteryTemperatureValue.setText("--°C");
            batteryTemperatureValue.setTextColor(getResources().getColor(R.color.text_color_secondary));
        }
    }

    private void updateNetworkPing(int ping) {
        if (ping > 0) {
            networkPingValue.setText(ping + " ms");

            // Change color based on ping quality
            if (ping < 50) {
                networkPingValue.setTextColor(getResources().getColor(R.color.neon_green));
            } else if (ping < 100) {
                networkPingValue.setTextColor(getResources().getColor(R.color.neon_blue));
            } else if (ping < 200) {
                networkPingValue.setTextColor(getResources().getColor(R.color.neon_orange));
            } else {
                networkPingValue.setTextColor(getResources().getColor(R.color.neon_red));
            }
        } else {
            networkPingValue.setText("-- ms");
            networkPingValue.setTextColor(getResources().getColor(R.color.text_color_secondary));
        }
    }

    private void updateNetworkType(String networkType) {
        networkTypeValue.setText(networkType);

        // Change color based on network type
        if ("واي فاي".equals(networkType) || "WiFi".equals(networkType)) {
            networkTypeValue.setTextColor(getResources().getColor(R.color.neon_green));
        } else if ("بيانات الهاتف".equals(networkType) || "Mobile Data".equals(networkType)) {
            networkTypeValue.setTextColor(getResources().getColor(R.color.neon_cyan));
        } else if ("غير متصل".equals(networkType) || "No Connection".equals(networkType)) {
            networkTypeValue.setTextColor(getResources().getColor(R.color.neon_red));
        } else {
            networkTypeValue.setTextColor(getResources().getColor(R.color.text_color_secondary));
        }
    }


}
