<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:padding="8dp"
    android:background="?attr/selectableItemBackground"
    android:layout_marginBottom="8dp">

    <ImageView
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:src="@drawable/ic_premium"
        app:tint="@color/cyber_neon_gold"
        android:layout_marginEnd="12dp" />

    <TextView
        android:id="@+id/feature_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Premium Feature"
        android:textColor="@color/cyber_text_primary"
        android:textSize="14sp"
        android:fontFamily="@font/cairo_regular" />
</LinearLayout>
