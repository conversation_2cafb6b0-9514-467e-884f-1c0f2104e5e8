<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/cyber_neon_gradient_bg"
    tools:context=".GameModeActivity">

    <!-- Back Button -->
    <ImageView
        android:id="@+id/btn_back"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_margin="16dp"
        android:padding="12dp"
        android:src="@drawable/ic_back"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:clickable="true"
        android:focusable="true"
        app:tint="@color/neon_blue"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Background Speed Lines -->
    <TextView
        android:id="@+id/title_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/font_for_ar_en"
        android:gravity="center"
        android:text="@string/game_mode_title"
        android:textColor="@color/neon_blue"
        android:textSize="28sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@+id/scrollView2"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/btn_back"
        app:layout_constraintTop_toTopOf="@+id/speed_lines_bg" />

    <ImageView
        android:id="@+id/speed_lines_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:src="@drawable/blue_speed_lines"
        android:alpha="0.4"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Energy Grid Background -->
    <ImageView
        android:id="@+id/energy_grid"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:src="@drawable/energy_grid"
        android:alpha="0.2"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />



    <!-- ScrollView for content -->
    <ScrollView
        android:id="@+id/scrollView2"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:fillViewport="true"
        android:overScrollMode="never"
        android:scrollbars="none"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/btn_back">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="24dp">

            <!-- App Logo Container -->
            <FrameLayout
                android:id="@+id/logo_container"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="48dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/app_logo"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:src="@drawable/game_mode_logo" />

                <ImageView
                    android:id="@+id/logo_glow"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:alpha="0"
                    android:src="@drawable/logo_glow" />
            </FrameLayout>

            <!-- Game Mode Title -->
            <TextView
                android:id="@+id/game_mode_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:fontFamily="@font/font_for_ar_en"
                android:shadowColor="@color/neon_blue_glow"
                android:shadowDx="0"
                android:shadowDy="0"
                android:shadowRadius="10"
                android:text="@string/game_mode_title"
                android:textColor="@color/neon_blue"
                android:textSize="28sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/logo_container" />

            <!-- Game Mode Description -->
            <TextView
                android:id="@+id/game_mode_description"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="32dp"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="32dp"
                android:fontFamily="@font/cairo_regular"
                android:gravity="center"
                android:text="@string/game_mode_description"
                android:textColor="@color/text_color_secondary"
                android:textSize="16sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/game_mode_title" />

            <!-- Game Mode Button Container -->
            <FrameLayout
                android:id="@+id/button_container"
                android:layout_width="200dp"
                android:layout_height="200dp"
                android:layout_marginTop="32dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/game_mode_description">

                <!-- Button Glow Effect -->
                <ImageView
                    android:id="@+id/button_glow"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:alpha="0.7"
                    android:src="@drawable/button_glow" />

                <!-- Lightning Effect (Initially Invisible) -->
                <ImageView
                    android:id="@+id/lightning_effect"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:src="@drawable/lightning_effect"
                    android:visibility="invisible" />

                <!-- Game Mode Button -->
                <Button
                    android:id="@+id/game_mode_button"
                    android:layout_width="180dp"
                    android:layout_height="180dp"
                    android:layout_gravity="center"
                    android:background="@drawable/game_mode_button_custom"
                    android:drawableTop="@drawable/ic_power"
                    android:drawablePadding="50dp"
                    android:drawableTint="@color/neon_blue"
                    android:fontFamily="@font/font_for_ar_en"
                    android:gravity="center"
                    android:text="@string/activate_game_mode"
                    android:textColor="@color/neon_blue"
                    android:textSize="22sp" />
            </FrameLayout>



            <!-- Game Mode Status -->
            <TextView
                android:id="@+id/game_mode_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:fontFamily="@font/font_for_ar_en"
                android:text="@string/game_mode_status_inactive"
                android:textColor="@color/text_color_secondary"
                android:textSize="16sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/button_container" />

            <!-- Game Features Section -->
            <TextView
                android:id="@+id/features_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="24dp"
                android:layout_marginTop="32dp"
                android:layout_marginEnd="24dp"
                android:fontFamily="@font/font_for_ar_en"
                android:text="@string/game_mode_features"
                android:textColor="@color/neon_blue"
                android:textSize="18sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/game_mode_status" />

            <!-- Features List -->
            <androidx.cardview.widget.CardView
                android:id="@+id/features_container"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="24dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="24dp"
                app:cardBackgroundColor="@color/card_background"
                app:cardCornerRadius="16dp"
                app:cardElevation="8dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/features_title">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <!-- Feature 1 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingTop="8dp"
                        android:paddingBottom="8dp">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:src="@drawable/ic_power"
                            app:tint="@color/neon_blue" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/cairo_regular"
                                android:text="@string/auto_optimization"
                                android:textColor="@color/neon_blue"
                                android:textSize="16sp" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/cairo_regular"
                                android:text="@string/auto_optimization_desc"
                                android:textColor="@color/text_color_secondary"
                                android:textSize="14sp" />
                        </LinearLayout>
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#33FFFFFF" />

                    <!-- Feature 2 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingTop="8dp"
                        android:paddingBottom="8dp">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:src="@drawable/ic_network"
                            app:tint="@color/neon_blue" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/cairo_regular"
                                android:text="@string/network_priority_title"
                                android:textColor="@color/neon_blue"
                                android:textSize="16sp" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/cairo_regular"
                                android:text="@string/network_priority_desc"
                                android:textColor="@color/text_color_secondary"
                                android:textSize="14sp" />
                        </LinearLayout>
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#33FFFFFF" />

                    <!-- Feature 3 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingTop="8dp"
                        android:paddingBottom="8dp">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:src="@drawable/ic_cpu"
                            app:tint="@color/neon_blue" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/cairo_regular"
                                android:text="@string/cpu_boost_title"
                                android:textColor="@color/neon_blue"
                                android:textSize="16sp" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/cairo_regular"
                                android:text="@string/cpu_boost_desc"
                                android:textColor="@color/text_color_secondary"
                                android:textSize="14sp" />
                        </LinearLayout>
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#33FFFFFF" />

                    <!-- Feature 4 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingTop="8dp"
                        android:paddingBottom="8dp">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:src="@drawable/ic_battery"
                            app:tint="@color/neon_orange" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/cairo_regular"
                                android:text="@string/battery_optimization_title"
                                android:textColor="@color/neon_orange"
                                android:textSize="16sp" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/cairo_regular"
                                android:text="@string/battery_optimization_desc"
                                android:textColor="@color/text_color_secondary"
                                android:textSize="14sp" />
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Supported Games Section -->
            <TextView
                android:id="@+id/games_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="24dp"
                android:layout_marginTop="32dp"
                android:layout_marginEnd="24dp"
                android:fontFamily="@font/font_for_ar_en"
                android:text="@string/supported_games"
                android:textColor="@color/neon_blue"
                android:textSize="18sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/features_container" />

            <!-- Games List -->
            <androidx.cardview.widget.CardView
                android:id="@+id/games_container"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="24dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="24dp"
                app:cardBackgroundColor="@color/card_background"
                app:cardCornerRadius="16dp"
                app:cardElevation="8dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/games_title">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="8dp">

                    <!-- Game 1: PUBG Mobile -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingVertical="8dp">

                        <View
                            android:layout_width="8dp"
                            android:layout_height="8dp"
                            android:background="@color/neon_green" />

                        <TextView
                            android:id="@+id/pubg_mobile"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/font_for_ar_en"
                            android:text="@string/pubg_mobile"
                            android:textColor="@color/text_color_primary"
                            android:textSize="14sp" />

                        <ImageView
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:src="@drawable/ic_crown"
                            android:contentDescription="@string/pro_version" />
                    </LinearLayout>

                    <!-- Game 2: Call of Duty -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingVertical="8dp">

                        <View
                            android:layout_width="8dp"
                            android:layout_height="8dp"
                            android:background="@color/neon_green" />

                        <TextView
                            android:id="@+id/call_of_duty"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/font_for_ar_en"
                            android:text="@string/call_of_duty"
                            android:textColor="@color/text_color_primary"
                            android:textSize="14sp" />

                        <ImageView
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:src="@drawable/ic_crown"
                            android:contentDescription="@string/pro_version" />
                    </LinearLayout>

                    <!-- Game 3: Minecraft -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingVertical="8dp">

                        <View
                            android:layout_width="8dp"
                            android:layout_height="8dp"
                            android:background="@color/neon_green" />

                        <TextView
                            android:id="@+id/minecraft"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/font_for_ar_en"
                            android:text="@string/minecraft"
                            android:textColor="@color/text_color_primary"
                            android:textSize="14sp" />

                        <ImageView
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:src="@drawable/ic_crown"
                            android:contentDescription="@string/pro_version" />
                    </LinearLayout>

                    <!-- Game 4: Fortnite -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingVertical="8dp">

                        <View
                            android:layout_width="8dp"
                            android:layout_height="8dp"
                            android:background="@color/neon_green" />

                        <TextView
                            android:id="@+id/fortnite"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/font_for_ar_en"
                            android:text="@string/fortnite"
                            android:textColor="@color/text_color_primary"
                            android:textSize="14sp" />

                        <ImageView
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:src="@drawable/ic_crown"
                            android:contentDescription="@string/pro_version" />
                    </LinearLayout>

                    <!-- Game 5: Asphalt 9 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingVertical="8dp">

                        <View
                            android:layout_width="8dp"
                            android:layout_height="8dp"
                            android:background="@color/neon_green" />

                        <TextView
                            android:id="@+id/asphalt_9"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/font_for_ar_en"
                            android:text="@string/asphalt_9"
                            android:textColor="@color/text_color_primary"
                            android:textSize="14sp" />

                        <ImageView
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:src="@drawable/ic_crown"
                            android:contentDescription="@string/pro_version" />
                    </LinearLayout>

                    <!-- Game 6: Free Fire (بدون تاج) -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingVertical="8dp">

                        <View
                            android:layout_width="8dp"
                            android:layout_height="8dp"
                            android:background="@color/neon_green" />

                        <TextView
                            android:id="@+id/free_fire"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:fontFamily="@font/font_for_ar_en"
                            android:text="@string/free_fire"
                            android:textColor="@color/text_color_primary"
                            android:textSize="14sp" />
                    </LinearLayout>

                </LinearLayout>
            </androidx.cardview.widget.CardView>


            <!-- Disclaimer Notice -->
<!--            <TextView-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:layout_marginStart="24dp"-->
<!--                android:layout_marginTop="24dp"-->
<!--                android:layout_marginEnd="24dp"-->
<!--                android:layout_marginBottom="16dp"-->
<!--                android:fontFamily="@font/cairo_regular"-->
<!--                android:gravity="center"-->
<!--                android:text="@string/game_mode_disclaimer"-->
<!--                android:textColor="@color/light_gray"-->
<!--                android:textSize="12sp"-->
<!--                android:alpha="0.8"-->
<!--                app:layout_constraintTop_toBottomOf="@id/games_container"-->
<!--                app:layout_constraintStart_toStartOf="parent"-->
<!--                app:layout_constraintEnd_toEndOf="parent" />-->

        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>

    <!-- Ad Container -->
    <FrameLayout
        android:id="@+id/ad_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:minHeight="50dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
