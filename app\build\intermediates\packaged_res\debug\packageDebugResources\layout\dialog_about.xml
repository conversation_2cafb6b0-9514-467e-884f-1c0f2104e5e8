<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardBackgroundColor="@color/cyber_card_bg"
    app:cardCornerRadius="16dp"
    app:cardElevation="8dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <ImageView
            android:id="@+id/app_logo"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="16dp"
            android:src="@drawable/headshot_logo" />

        <TextView
            android:id="@+id/app_name_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fontFamily="@font/font_for_ar_en"
            android:gravity="center"
            android:text="@string/app_name"
            android:textColor="@color/cyber_neon_cyan"
            android:textSize="24sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/app_version"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fontFamily="@font/font_for_ar_en"
            android:gravity="center"
            android:text="@string/app_version"
            android:textColor="@color/cyber_text_secondary"
            android:textSize="16sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="16dp"
            android:background="@color/cyber_neon_cyan" />

        <TextView
            android:id="@+id/developer_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fontFamily="@font/font_for_ar_en"
            android:gravity="center"
            android:text="@string/developed_by_mahmoud_ff_yt"
            android:textColor="@color/cyber_text_primary"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/contact_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:fontFamily="@font/font_for_ar_en"
            android:gravity="center"
            android:text="@string/for_more_suggestions_and_solutions_contact_us_via_email"
            android:textColor="@color/cyber_text_secondary"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/email_contact"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:fontFamily="@font/font_for_ar_en"
            android:gravity="center"
            android:text="@string/email_contact"
            android:textColor="@color/cyber_neon_cyan"
            android:textSize="14sp" />

        <Button
            android:id="@+id/close_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:background="@drawable/neon_button_bg"
            android:fontFamily="@font/font_for_ar_en"
            android:text="@string/close"
            android:textColor="@android:color/white"
            android:textSize="16sp" />

    </LinearLayout>
</androidx.cardview.widget.CardView>
