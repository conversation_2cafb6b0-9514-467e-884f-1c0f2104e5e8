<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="200dp"
    android:height="200dp"
    android:viewportWidth="200"
    android:viewportHeight="200">

    <!-- Background Circle -->
    <path
        android:fillColor="#0A1A22"
        android:pathData="M100,100m-100,0a100,100 0,1 1,200 0a100,100 0,1 1,-200 0" />

    <!-- Inner Circle -->
    <path
        android:fillColor="#142C39"
        android:pathData="M100,100m-90,0a90,90 0,1 1,180 0a90,90 0,1 1,-180 0" />

    <!-- Outer Ring Glow -->
    <path
        android:strokeColor="#4FC3F7"
        android:strokeWidth="2"
        android:pathData="M100,100m-95,0a95,95 0,1 1,190 0a95,95 0,1 1,-190 0" />

    <!-- Crosshair -->
    <path
        android:strokeColor="#4FC3F7"
        android:strokeWidth="3"
        android:pathData="M100,30 L100,60 M100,140 L100,170 M30,100 L60,100 M140,100 L170,100" />

    <!-- Crosshair Circle -->
    <path
        android:strokeColor="#4FC3F7"
        android:strokeWidth="2"
        android:fillColor="#00000000"
        android:pathData="M100,100m-25,0a25,25 0,1 1,50 0a25,25 0,1 1,-50 0" />

    <!-- Crosshair Center Dot -->
    <path
        android:fillColor="#4FC3F7"
        android:pathData="M100,100m-5,0a5,5 0,1 1,10 0a5,5 0,1 1,-10 0" />

    <!-- Diagonal Lines -->
    <path
        android:strokeColor="#4FC3F7"
        android:strokeWidth="1.5"
        android:pathData="M70,70 L85,85 M115,85 L130,70 M70,130 L85,115 M115,115 L130,130" />
</vector>
