package com.mahmoudffyt.gfxbooster.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

import java.util.Calendar;
import java.util.Date;

/**
 * Utility class to manage premium features
 */
public class PremiumManager {

    private static final String PREFS_NAME = "PremiumPrefs";
    private static final String KEY_IS_PREMIUM = "isPremium";
    private static final String KEY_LAST_DIALOG_SHOWN = "lastDialogShown";
    private static final String KEY_GAME_MODE_USAGE_DATE = "gameModeUsageDate";
    private static final String KEY_GAME_MODE_USAGE_COUNT = "gameModeUsageCount";
    private static final String KEY_GAME_MODE_LAST_USAGE = "gameModeLastUsage";
    private static final String KEY_GAME_MODE_TRIAL_DAYS = "gameModeTrialDays";
    private static final String KEY_GAME_MODE_TRIAL_START_DATE = "gameModeTrialStartDate";

    // Keys for new trial logic
    private static final String KEY_TRIAL_START_DATE = "trial_start_date";
    private static final String KEY_LAST_AD_WATCH_DATE = "last_ad_watch_date";
    private static final long ONE_HOUR_MILLIS = 60 * 60 * 1000; // 1 hour in milliseconds
    private static final long ONE_DAY_MILLIS = 24 * ONE_HOUR_MILLIS; // 1 day in milliseconds
    private static final int MAX_TRIAL_DAYS = 3; // Maximum trial days

    private Context context;
    private SharedPreferences preferences;

    public PremiumManager(Context context) {
        this.context = context;
        this.preferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
    }

    /**
     * Check if user has premium access
     */
    public boolean isPremium() {
        // For testing purposes, check the actual preference value
        // Change this to return true for testing Pro mode, false for testing free mode
        return preferences.getBoolean(KEY_IS_PREMIUM, false);
//         return true; // Uncomment this line to test Pro mode
    }

    /**
     * Set premium status (for testing purposes)
     */
    public void setPremium(boolean isPremium) {
        preferences.edit().putBoolean(KEY_IS_PREMIUM, isPremium).apply();
    }

    /**
     * Check if we should show the premium dialog
     * Only show it if it hasn't been shown in the last hour
     */
    public boolean shouldShowPremiumDialog() {
        if (isPremium()) {
            return false; // Never show to premium users
        }

        long lastShown = preferences.getLong(KEY_LAST_DIALOG_SHOWN, 0);
        long currentTime = System.currentTimeMillis();

        // If it's been more than an hour since we last showed the dialog
        return (currentTime - lastShown) > ONE_HOUR_MILLIS;
    }

    /**
     * Record that we showed the premium dialog
     */
    public void recordPremiumDialogShown() {
        preferences.edit().putLong(KEY_LAST_DIALOG_SHOWN, System.currentTimeMillis()).apply();
    }

    /**
     * Check if a specific feature is available in free version
     */
    public boolean isFeatureAvailable(String featureKey) {
        // If user is premium, all features are available
        if (isPremium()) {
            return true;
        }

        // Check specific feature availability in free version
        switch (featureKey) {
            case "smart_aim":
                return false; // Smart Aim is premium only
            case "aim_button":
                return false; // Aim Button is premium only
            case "aim_overlay":
                return false; // Aim Overlay is premium only
            case "game_booster":
                return true; // Game Booster is available in free version
            case "gfx_tools":
                return true; // GFX Tools is available in free version
            case "mood_game":
                return canUseGameMode(); // Check if user can use Game Mode (3 times per day)
            default:
                return true; // Default to available
        }
    }

    /**
     * Check if user can use Game Mode (limited to once per day for 3 days)
     */
    public boolean canUseGameMode() {
        if (isPremium()) {
            return true; // Premium users have unlimited access
        }

        // Get current date in format YYYYMMDD
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1; // Month is 0-based
        int day = calendar.get(Calendar.DAY_OF_MONTH);
        String currentDate = String.format("%04d%02d%02d", year, month, day);

        // Check if trial period has started
        String trialStartDate = preferences.getString(KEY_GAME_MODE_TRIAL_START_DATE, "");
        int trialDaysUsed = preferences.getInt(KEY_GAME_MODE_TRIAL_DAYS, 0);

        // If trial hasn't started yet, allow usage
        if (trialStartDate.isEmpty()) {
            return true;
        }

        // If trial days are used up, deny usage
        if (trialDaysUsed >= MAX_TRIAL_DAYS) {
            return false;
        }

        // Get stored date and usage count for today
        String storedDate = preferences.getString(KEY_GAME_MODE_USAGE_DATE, "");
        int usageCount = preferences.getInt(KEY_GAME_MODE_USAGE_COUNT, 0);

        // If it's a new day, reset the daily counter
        if (!currentDate.equals(storedDate)) {
            return true; // Allow usage for the new day
        }

        // Check if user has already used Game Mode today
        return usageCount < 1; // Only allow one usage per day
    }

    /**
     * Record usage of Game Mode
     * @return true if successfully recorded, false if limit exceeded
     */
    public boolean recordGameModeUsage() {
        if (isPremium()) {
            return true; // Premium users have unlimited access
        }

        // Get current date in format YYYYMMDD
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1; // Month is 0-based
        int day = calendar.get(Calendar.DAY_OF_MONTH);
        String currentDate = String.format("%04d%02d%02d", year, month, day);

        // Check if trial period has started
        String trialStartDate = preferences.getString(KEY_GAME_MODE_TRIAL_START_DATE, "");
        int trialDaysUsed = preferences.getInt(KEY_GAME_MODE_TRIAL_DAYS, 0);

        // If trial hasn't started yet, initialize it
        if (trialStartDate.isEmpty()) {
            preferences.edit()
                .putString(KEY_GAME_MODE_TRIAL_START_DATE, currentDate)
                .putInt(KEY_GAME_MODE_TRIAL_DAYS, 1) // First day of trial
                .putString(KEY_GAME_MODE_USAGE_DATE, currentDate)
                .putInt(KEY_GAME_MODE_USAGE_COUNT, 1) // First usage of the day
                .putLong(KEY_GAME_MODE_LAST_USAGE, System.currentTimeMillis())
                .apply();
            return true;
        }

        // If trial days are used up, deny usage
        if (trialDaysUsed >= MAX_TRIAL_DAYS) {
            return false;
        }

        // Get stored date and usage count for today
        String storedDate = preferences.getString(KEY_GAME_MODE_USAGE_DATE, "");
        int usageCount = preferences.getInt(KEY_GAME_MODE_USAGE_COUNT, 0);

        // If it's a new day
        if (!currentDate.equals(storedDate)) {
            preferences.edit()
                .putString(KEY_GAME_MODE_USAGE_DATE, currentDate)
                .putInt(KEY_GAME_MODE_USAGE_COUNT, 1) // First usage of the new day
                .putInt(KEY_GAME_MODE_TRIAL_DAYS, trialDaysUsed + 1) // Increment trial days used
                .putLong(KEY_GAME_MODE_LAST_USAGE, System.currentTimeMillis())
                .apply();
            return true;
        }

        // Check if user has already used Game Mode today
        if (usageCount < 1) {
            preferences.edit()
                .putInt(KEY_GAME_MODE_USAGE_COUNT, usageCount + 1)
                .putLong(KEY_GAME_MODE_LAST_USAGE, System.currentTimeMillis())
                .apply();
            return true;
        }

        return false; // Daily limit exceeded
    }

    /**
     * Get remaining trial days
     */
    public int getRemainingTrialDays() {
        if (isPremium()) {
            return Integer.MAX_VALUE; // Premium users have unlimited access
        }

        int trialDaysUsed = preferences.getInt(KEY_GAME_MODE_TRIAL_DAYS, 0);
        return Math.max(0, MAX_TRIAL_DAYS - trialDaysUsed);
    }

    /**
     * Check if user has used their trial for today
     */
    public boolean hasUsedTrialToday() {
        if (isPremium()) {
            return false; // Premium users have unlimited access
        }

        // Get current date in format YYYYMMDD
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1; // Month is 0-based
        int day = calendar.get(Calendar.DAY_OF_MONTH);
        String currentDate = String.format("%04d%02d%02d", year, month, day);

        // Get stored date and usage count
        String storedDate = preferences.getString(KEY_GAME_MODE_USAGE_DATE, "");
        int usageCount = preferences.getInt(KEY_GAME_MODE_USAGE_COUNT, 0);

        // If it's not today or count is 0, they haven't used it
        if (!currentDate.equals(storedDate) || usageCount == 0) {
            return false;
        }

        return true; // They've used it today
    }

    /**
     * Get premium features list
     */
    public String[] getPremiumFeatures() {
        return new String[] {
            context.getString(com.mahmoudffyt.gfxbooster.R.string.premium_feature_smart_aim),
            context.getString(com.mahmoudffyt.gfxbooster.R.string.premium_feature_aim_button),
            context.getString(com.mahmoudffyt.gfxbooster.R.string.premium_feature_aim_overlay),
            context.getString(com.mahmoudffyt.gfxbooster.R.string.premium_feature_no_ads),
            context.getString(com.mahmoudffyt.gfxbooster.R.string.premium_feature_priority_updates),
            context.getString(com.mahmoudffyt.gfxbooster.R.string.premium_feature_support)
        };
    }

    /**
     * New Trial Logic Methods
     */

    /**
     * Check if this is the first time activating Game Mode
     * @return true if this is the first activation
     */
    public boolean isFirstActivation() {
        return !preferences.contains(KEY_TRIAL_START_DATE);
    }

    /**
     * Record the first activation date
     */
    public void recordFirstActivation() {
        if (!preferences.contains(KEY_TRIAL_START_DATE)) {
            preferences.edit()
                .putLong(KEY_TRIAL_START_DATE, System.currentTimeMillis())
                .apply();
            Log.d("PremiumManager", "First activation recorded: " + new Date(System.currentTimeMillis()));
        }
    }

    /**
     * Check if the trial period has expired (3 days from first activation)
     * @return true if trial period has expired
     */
    public boolean isTrialExpired() {
        if (isPremium()) {
            return false; // Premium users don't have trial expiration
        }

        // If trial hasn't started yet, it's not expired
        if (!preferences.contains(KEY_TRIAL_START_DATE)) {
            return false;
        }

        long trialStartDate = preferences.getLong(KEY_TRIAL_START_DATE, 0);
        long currentTime = System.currentTimeMillis();

        // Check if 3 days have passed since first activation
        return (currentTime - trialStartDate) > (MAX_TRIAL_DAYS * ONE_DAY_MILLIS);
    }

    /**
     * Check if user has already watched an ad today
     * @return true if user has watched an ad today
     */
    public boolean hasWatchedAdToday() {
        if (isPremium()) {
            return false; // Premium users don't need to watch ads
        }

        long lastAdWatchDate = preferences.getLong(KEY_LAST_AD_WATCH_DATE, 0);

        // If never watched an ad, return false
        if (lastAdWatchDate == 0) {
            return false;
        }

        // Check if the last ad watch was today
        Calendar lastWatch = Calendar.getInstance();
        lastWatch.setTimeInMillis(lastAdWatchDate);

        Calendar today = Calendar.getInstance();

        return lastWatch.get(Calendar.YEAR) == today.get(Calendar.YEAR) &&
               lastWatch.get(Calendar.DAY_OF_YEAR) == today.get(Calendar.DAY_OF_YEAR);
    }

    /**
     * Record that user has watched an ad today
     */
    public void recordAdWatched() {
        preferences.edit()
            .putLong(KEY_LAST_AD_WATCH_DATE, System.currentTimeMillis())
            .apply();
        Log.d("PremiumManager", "Ad watched recorded: " + new Date(System.currentTimeMillis()));
    }

    /**
     * Get days remaining in trial period
     * @return number of days remaining (0-3)
     */
    public int getDaysRemainingInTrial() {
        if (isPremium()) {
            return MAX_TRIAL_DAYS; // Premium users always have max days
        }

        // If trial hasn't started yet, return max days
        if (!preferences.contains(KEY_TRIAL_START_DATE)) {
            return MAX_TRIAL_DAYS;
        }

        long trialStartDate = preferences.getLong(KEY_TRIAL_START_DATE, 0);
        long currentTime = System.currentTimeMillis();

        // Calculate days passed
        long daysPassed = (currentTime - trialStartDate) / ONE_DAY_MILLIS;

        // Return days remaining (capped at 0 and MAX_TRIAL_DAYS)
        return Math.max(0, Math.min(MAX_TRIAL_DAYS, MAX_TRIAL_DAYS - (int)daysPassed));
    }

    /**
     * Check if user can watch an ad today to activate Game Mode
     * @return true if user can watch an ad today
     */
    public boolean canWatchAdToday() {
        if (isPremium()) {
            return false; // Premium users don't need to watch ads
        }

        // If trial has expired, can't watch ad
        if (isTrialExpired()) {
            return false;
        }

        // If already watched an ad today, can't watch another
        return !hasWatchedAdToday();
    }
}
