<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24">
    
    <!-- Outer Circle Glow -->
    <path
        android:fillColor="#3000FFFF"
        android:pathData="M12,2C6.48,2 2,6.48 2,12s4.48,10 10,10 10,-4.48 10,-10S17.52,2 12,2zM12,20c-4.42,0 -8,-3.58 -8,-8s3.58,-8 8,-8 8,3.58 8,8 -3.58,8 -8,8z"/>
    
    <!-- Outer Circle -->
    <path
        android:strokeColor="#00FFFF"
        android:strokeWidth="1.5"
        android:strokeLineCap="round"
        android:pathData="M12,2C6.48,2 2,6.48 2,12s4.48,10 10,10 10,-4.48 10,-10S17.52,2 12,2zM12,20c-4.42,0 -8,-3.58 -8,-8s3.58,-8 8,-8 8,3.58 8,8 -3.58,8 -8,8z"/>
    
    <!-- Inner Circle -->
    <path
        android:strokeColor="#00FFFF"
        android:strokeWidth="1"
        android:pathData="M12,8c-2.21,0 -4,1.79 -4,4s1.79,4 4,4 4,-1.79 4,-4 -1.79,-4 -4,-4z"/>
    
    <!-- Crosshair Lines -->
    <path
        android:strokeColor="#FFFFFF"
        android:strokeWidth="1"
        android:strokeLineCap="round"
        android:pathData="M12,4L12,6M12,18L12,20M4,12L6,12M18,12L20,12"/>
    
    <!-- Center Dot -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M12,11.5c-0.28,0 -0.5,0.22 -0.5,0.5s0.22,0.5 0.5,0.5 0.5,-0.22 0.5,-0.5 -0.22,-0.5 -0.5,-0.5z"/>
</vector>
