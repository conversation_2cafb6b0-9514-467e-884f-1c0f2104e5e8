<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Horizontal line -->
    <item>
        <shape android:shape="line">
            <stroke
                android:width="2dp"
                android:color="@color/accent_color"
                android:dashGap="10dp"
                android:dashWidth="10dp" />
        </shape>
    </item>

    <!-- Vertical line -->
    <item>
        <rotate
            android:fromDegrees="90"
            android:pivotX="50%"
            android:pivotY="50%"
            android:toDegrees="90">
            <shape android:shape="line">
                <stroke
                    android:width="2dp"
                    android:color="@color/accent_color"
                    android:dashGap="10dp"
                    android:dashWidth="10dp" />
            </shape>
        </rotate>
    </item>

    <!-- Diagonal line (45 degrees) -->
    <item>
        <rotate
            android:fromDegrees="45"
            android:pivotX="50%"
            android:pivotY="50%"
            android:toDegrees="45">
            <shape android:shape="line">
                <stroke
                    android:width="2dp"
                    android:color="@color/accent_color"
                    android:dashGap="10dp"
                    android:dashWidth="10dp" />
            </shape>
        </rotate>
    </item>

    <!-- Diagonal line (135 degrees) -->
    <item>
        <rotate
            android:fromDegrees="135"
            android:pivotX="50%"
            android:pivotY="50%"
            android:toDegrees="135">
            <shape android:shape="line">
                <stroke
                    android:width="2dp"
                    android:color="@color/accent_color"
                    android:dashGap="10dp"
                    android:dashWidth="10dp" />
            </shape>
        </rotate>
    </item>
</layer-list>
