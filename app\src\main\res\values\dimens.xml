<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Default screen margins, per the Android Design guidelines -->
    <dimen name="activity_horizontal_margin">16dp</dimen>
    <dimen name="activity_vertical_margin">16dp</dimen>
    
    <!-- Text sizes -->
    <dimen name="text_size_title">28sp</dimen>
    <dimen name="text_size_subtitle">14sp</dimen>
    <dimen name="text_size_button">16sp</dimen>
    <dimen name="text_size_card_title">16sp</dimen>
    <dimen name="text_size_card_content">14sp</dimen>
    <dimen name="text_size_small">12sp</dimen>
    
    <!-- Icon sizes -->
    <dimen name="icon_size_large">64dp</dimen>
    <dimen name="icon_size_medium">32dp</dimen>
    <dimen name="icon_size_small">24dp</dimen>
    
    <!-- Margins and paddings -->
    <dimen name="margin_large">24dp</dimen>
    <dimen name="margin_medium">16dp</dimen>
    <dimen name="margin_small">8dp</dimen>
    <dimen name="margin_tiny">4dp</dimen>
    
    <dimen name="padding_large">24dp</dimen>
    <dimen name="padding_medium">16dp</dimen>
    <dimen name="padding_small">8dp</dimen>
    <dimen name="padding_tiny">4dp</dimen>
    
    <!-- Card dimensions -->
    <dimen name="card_corner_radius">16dp</dimen>
    <dimen name="card_elevation">8dp</dimen>
    <dimen name="card_small_corner_radius">12dp</dimen>
    <dimen name="card_small_elevation">4dp</dimen>
</resources>
