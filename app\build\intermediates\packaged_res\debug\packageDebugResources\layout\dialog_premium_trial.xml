<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/margin_medium"
    app:cardBackgroundColor="@color/cyber_card_bg"
    app:cardCornerRadius="@dimen/card_corner_radius"
    app:cardElevation="@dimen/card_elevation">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="@dimen/padding_medium">

        <TextView
            android:id="@+id/trial_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fontFamily="@font/font_for_ar_en"
            android:gravity="center"
            android:text="@string/pro_version_title"
            android:textColor="@color/cyber_neon_gold"
            android:textSize="@dimen/text_size_title"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/premium_icon"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/margin_medium"
            android:src="@drawable/ic_premium"
            android:tint="@color/cyber_neon_gold" />

        <TextView
            android:id="@+id/trial_description"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_medium"
            android:fontFamily="@font/cairo_regular"
            android:gravity="center"
            android:text="@string/watch_ad_for_trial"
            android:textColor="@color/cyber_text_primary"
            android:textSize="@dimen/text_size_subtitle" />

        <TextView
            android:id="@+id/trial_info_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_small"
            android:fontFamily="@font/cairo_regular"
            android:gravity="center"
            android:text="@string/days_remaining"
            android:textColor="@color/cyber_neon_cyan"
            android:textSize="@dimen/text_size_subtitle" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_medium"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_watch_ad"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/margin_small"
                android:layout_weight="1"
                android:backgroundTint="@color/cyber_neon_cyan"
                android:fontFamily="@font/font_for_ar_en"
                android:text="@string/try_free"
                android:textColor="@android:color/black" />

            <Button
                android:id="@+id/btn_upgrade"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_small"
                android:layout_weight="1"
                android:backgroundTint="@color/cyber_neon_gold"
                android:fontFamily="@font/font_for_ar_en"
                android:text="@string/subscribe_now"
                android:textColor="@android:color/black" />
        </LinearLayout>

        <Button
            android:id="@+id/btn_cancel"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_small"
            android:backgroundTint="@color/cyber_card_stroke"
            android:fontFamily="@font/font_for_ar_en"
            android:text="@string/cancel"
            android:textColor="@color/cyber_text_primary" />

    </LinearLayout>
</androidx.cardview.widget.CardView>
