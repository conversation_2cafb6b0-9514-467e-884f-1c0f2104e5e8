-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:2:1-124:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:2:1-124:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:2:1-124:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:2:1-124:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\464e6a7dcfefd0da870c4050aa381b0c\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d3bfba1f66ed76a002b3095ffa4a442\transformed\constraintlayout-2.2.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.airbnb.android:lottie:6.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa9f63409c886091ad2e87355e8b4fc6\transformed\lottie-6.0.0\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f827cf25f7862f0f03f625c0fff7beb\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a44ad6b726c152c4fe16ed54c5a8c02\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:2:1-38:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\52866cc66fe3b6210482b9ca75cda756\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-location:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd69874069156293e09f6fb5d1dbd5f0\transformed\play-services-location-19.0.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-ads:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ba171b74415ef9d04b1f3a055c632e8\transformed\play-services-ads-24.2.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ec64ee55e8b2d7caf0c3d5a0e05cb1a\transformed\play-services-appset-16.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-analytics-ktx:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ad6a65ba20f74a8e88da6b866e65a53\transformed\firebase-analytics-ktx-22.4.0\AndroidManifest.xml:2:1-17:12
MERGED from [com.google.firebase:firebase-analytics:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e22719ef4c1940e0e5a676613c52c3fc\transformed\firebase-analytics-22.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47ccc0e24a10258223a997dba2340add\transformed\play-services-measurement-sdk-22.4.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5be24e152e5ffa226393bdb33a3aef6\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef3205b01dd1e8d62ca95413fbcfcde0\transformed\play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-crashlytics-ktx:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5995498b5b81ac31250b4f1324a21132\transformed\firebase-crashlytics-ktx-19.4.3\AndroidManifest.xml:15:1-30:12
MERGED from [com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\046374a346d3cc3e0fb7692fa882950a\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:17:1-39:12
MERGED from [com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a526ba4e24480c7108925ec9a95f4804\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:15:1-35:12
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3849af013f032b744f299890f6883a64\transformed\firebase-installations-18.0.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9c854e55cb7e484c6bf93dacda33eca\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-installations-interop:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\13062518ca145c233df63973b32ae5b8\transformed\firebase-installations-interop-17.2.0\AndroidManifest.xml:15:1-19:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e33d99db0e60492889ee01a086fddfe\transformed\ads-adservices-java-1.1.0-beta11\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6a554b97c6fdafe7f9e475f6f094c65\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f8cdc29058077ea676ff1cf735bd0db8\transformed\core-ktx-1.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ade37d50a5352dfb90d4f64617e2343\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37e33928c78c0b5c37d0e47d6d95412c\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1f2f08d4ef22c9e7417d286fb78a6299\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\163d8f14852b3ec60e1695d54d0ed062\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f68eb8005ee4007900de772b7f50d75\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\747cc69e4f70cdd210d779e95fcb0e32\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b87f265a57c9ebd6cbcb7a6156c9346b\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc757cae52d77fef3639111cfbd70412\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:17:1-115:12
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46c7e1c2c774c76fe5e74081b097ef9a\transformed\browser-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.webkit:webkit:1.11.0-alpha02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b176558c57fa68e6eeead9fc01cf480\transformed\webkit-1.11.0-alpha02\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c3ed062c8fee57bce302ac727e5e91b\transformed\play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b02ba864689118a0780e08784428d782\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\366ffaf53b1d63f2f6c1766ab4a86dc1\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1f8a9f48954aed687e4952c2c964f46e\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb1a011bed87b14c31f4355f21972067\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\129333b0c8ecb55c4464ed63f4afcb5a\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3dd3f063a8dc2168576dc8a6639ef4\transformed\core-1.13.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c17220d42609b7099a13844d88bf065e\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97807501b603dce69153c0410a51d4c3\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4b43ee03a56918c2f91003aee95a932\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\921836e7511c8cb3e70450775705a043\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71c738af83ef2034b82eb1807c939ed2\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78bfd8071e7a42117a7d03d72d78a29d\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53ec625aa4dc40f4efee1b09f5c94cc1\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15e499373e61a4c01ca0a7c30c277831\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\33ea3dfa4708107f1567d296fb39fc05\transformed\datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\89de661cc71c663b0686fe72581f7040\transformed\datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae6f74430a5b6eb34228cb640ef82a38\transformed\datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\573224c9122c27a96e929c121b9417b2\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9359014a9bdd06751a5e83a9c8611bbf\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\091bd03a708318c916022e504c59ffe6\transformed\firebase-measurement-connector-20.0.1\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-places-placereport:17.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b1b8c5d28b044b6d333a968efb8036cd\transformed\play-services-places-placereport-17.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.ump:user-messaging-platform:3.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ecbf3415acea89f234e763b19d4c3b4\transformed\user-messaging-platform-3.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2adc3a67aa3e84044dbc438a9bf8596\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:17:1-30:12
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec40bfad44d7f333973d83b42e2f25ef\transformed\play-services-measurement-base-22.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aff62db08cff511cc4373673ada3b6cf\transformed\play-services-basement-18.5.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fea55175bd7351d2ad813189ca90fcf0\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a45f6f0a50623914568ad00c0c5c4f2\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7723f1b9af1a91aebee2949e3a188b7f\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b29808633b68d0d203290857d34666ac\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d025b3265c888cd3a1bdd8eb838d60d6\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0cfe4ee17482e1198f9ec6063ea0e3fa\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd704e1e1f90908cf1fe364124e6a036\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5bd95e0bd3a26435e895636f321ad3e0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c5a84dde060c252df413aba1e00d40d4\transformed\firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30632a7c8dd1b017955f6c4482cfc334\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:15:1-31:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf66c28488d56b085c276c14a4449049\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:15:1-35:12
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18adbb13687610ba2ec263dcbb46b5e9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:15:1-39:12
MERGED from [com.google.android.datatransport:transport-api:3.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5280e756f471b9c925fc23cc67b9f6a4\transformed\transport-api-3.2.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-config-interop:16.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e9c22cef630d4fa23a07868a158eb4e8\transformed\firebase-config-interop-16.0.1\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a14d85f449dbf562c7e2ff341e6c9c51\transformed\firebase-encoders-json-18.0.1\AndroidManifest.xml:15:1-22:12
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac9518aeda46c3b802798bcb4651bb33\transformed\room-runtime-2.2.5\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\03c82351977e359f20cc55c016b1719a\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\035ef458de483479aea24c20649833e6\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\12e642919c92d644a8984f92f12af086\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\235be14cca6992f97d93c356dcc26fa2\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\835172f1e3d6d36b53a4e96b3ba17b72\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fca988ea54d2aca4576982756a9da820\transformed\sqlite-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e92248e6039d264b4b6e160952af6e23\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\03f2731a82c9f519c26465db79a33215\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.SCHEDULE_EXACT_ALARM
ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:6:5-79
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:6:22-76
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:7:5-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:7:22-74
uses-permission#android.permission.KILL_BACKGROUND_PROCESSES
ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:8:5-84
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:8:22-81
uses-permission#android.permission.PACKAGE_USAGE_STATS
ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:9:5-10:47
	tools:ignore
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:10:9-44
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:9:22-75
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:11:5-77
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:11:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_DATA_SYNC
ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:12:5-87
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:12:22-84
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:13:5-81
REJECTED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:30:5-32:31
REJECTED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:27:5-81
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:13:22-78
uses-permission#android.permission.VIBRATE
ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:14:5-66
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:14:22-63
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:15:5-78
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:15:22-75
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:16:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5be24e152e5ffa226393bdb33a3aef6\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5be24e152e5ffa226393bdb33a3aef6\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\046374a346d3cc3e0fb7692fa882950a\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\046374a346d3cc3e0fb7692fa882950a\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3849af013f032b744f299890f6883a64\transformed\firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3849af013f032b744f299890f6883a64\transformed\firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2adc3a67aa3e84044dbc438a9bf8596\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2adc3a67aa3e84044dbc438a9bf8596\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf66c28488d56b085c276c14a4449049\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf66c28488d56b085c276c14a4449049\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:23:5-67
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:16:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:17:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5be24e152e5ffa226393bdb33a3aef6\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5be24e152e5ffa226393bdb33a3aef6\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3849af013f032b744f299890f6883a64\transformed\firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3849af013f032b744f299890f6883a64\transformed\firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:25:5-79
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:26:5-79
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2adc3a67aa3e84044dbc438a9bf8596\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2adc3a67aa3e84044dbc438a9bf8596\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf66c28488d56b085c276c14a4449049\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf66c28488d56b085c276c14a4449049\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18adbb13687610ba2ec263dcbb46b5e9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:20:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18adbb13687610ba2ec263dcbb46b5e9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:20:5-79
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:17:22-76
queries
ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:20:5-25:15
MERGED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:12:5-19:15
MERGED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:12:5-19:15
MERGED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:35:5-68:15
MERGED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:35:5-68:15
intent#action:name:android.intent.action.MAIN+category:name:android.intent.category.GAME
ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:21:9-24:18
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:22:13-65
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:22:21-62
category#android.intent.category.GAME
ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:23:13-69
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:23:23-66
application
ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:27:5-122:19
INJECTED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:27:5-122:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\464e6a7dcfefd0da870c4050aa381b0c\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\464e6a7dcfefd0da870c4050aa381b0c\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d3bfba1f66ed76a002b3095ffa4a442\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d3bfba1f66ed76a002b3095ffa4a442\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [com.airbnb.android:lottie:6.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa9f63409c886091ad2e87355e8b4fc6\transformed\lottie-6.0.0\AndroidManifest.xml:9:5-20
MERGED from [com.airbnb.android:lottie:6.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa9f63409c886091ad2e87355e8b4fc6\transformed\lottie-6.0.0\AndroidManifest.xml:9:5-20
MERGED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:21:5-36:19
MERGED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:21:5-36:19
MERGED from [com.google.android.gms:play-services-location:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd69874069156293e09f6fb5d1dbd5f0\transformed\play-services-location-19.0.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd69874069156293e09f6fb5d1dbd5f0\transformed\play-services-location-19.0.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ec64ee55e8b2d7caf0c3d5a0e05cb1a\transformed\play-services-appset-16.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ec64ee55e8b2d7caf0c3d5a0e05cb1a\transformed\play-services-appset-16.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics-ktx:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ad6a65ba20f74a8e88da6b866e65a53\transformed\firebase-analytics-ktx-22.4.0\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-analytics-ktx:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ad6a65ba20f74a8e88da6b866e65a53\transformed\firebase-analytics-ktx-22.4.0\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-analytics:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e22719ef4c1940e0e5a676613c52c3fc\transformed\firebase-analytics-22.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e22719ef4c1940e0e5a676613c52c3fc\transformed\firebase-analytics-22.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47ccc0e24a10258223a997dba2340add\transformed\play-services-measurement-sdk-22.4.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47ccc0e24a10258223a997dba2340add\transformed\play-services-measurement-sdk-22.4.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5be24e152e5ffa226393bdb33a3aef6\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5be24e152e5ffa226393bdb33a3aef6\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef3205b01dd1e8d62ca95413fbcfcde0\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef3205b01dd1e8d62ca95413fbcfcde0\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.firebase:firebase-crashlytics-ktx:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5995498b5b81ac31250b4f1324a21132\transformed\firebase-crashlytics-ktx-19.4.3\AndroidManifest.xml:20:5-28:19
MERGED from [com.google.firebase:firebase-crashlytics-ktx:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5995498b5b81ac31250b4f1324a21132\transformed\firebase-crashlytics-ktx-19.4.3\AndroidManifest.xml:20:5-28:19
MERGED from [com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\046374a346d3cc3e0fb7692fa882950a\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\046374a346d3cc3e0fb7692fa882950a\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:29:5-37:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:29:5-37:19
MERGED from [com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a526ba4e24480c7108925ec9a95f4804\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:21:5-33:19
MERGED from [com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a526ba4e24480c7108925ec9a95f4804\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:21:5-33:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3849af013f032b744f299890f6883a64\transformed\firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3849af013f032b744f299890f6883a64\transformed\firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9c854e55cb7e484c6bf93dacda33eca\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9c854e55cb7e484c6bf93dacda33eca\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6a554b97c6fdafe7f9e475f6f094c65\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6a554b97c6fdafe7f9e475f6f094c65\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37e33928c78c0b5c37d0e47d6d95412c\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37e33928c78c0b5c37d0e47d6d95412c\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:70:5-113:19
MERGED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:70:5-113:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c3ed062c8fee57bce302ac727e5e91b\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c3ed062c8fee57bce302ac727e5e91b\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:30:5-143:19
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:30:5-143:19
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3dd3f063a8dc2168576dc8a6639ef4\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3dd3f063a8dc2168576dc8a6639ef4\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\921836e7511c8cb3e70450775705a043\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\921836e7511c8cb3e70450775705a043\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\573224c9122c27a96e929c121b9417b2\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\573224c9122c27a96e929c121b9417b2\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9359014a9bdd06751a5e83a9c8611bbf\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9359014a9bdd06751a5e83a9c8611bbf\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\091bd03a708318c916022e504c59ffe6\transformed\firebase-measurement-connector-20.0.1\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\091bd03a708318c916022e504c59ffe6\transformed\firebase-measurement-connector-20.0.1\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-places-placereport:17.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b1b8c5d28b044b6d333a968efb8036cd\transformed\play-services-places-placereport-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-places-placereport:17.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b1b8c5d28b044b6d333a968efb8036cd\transformed\play-services-places-placereport-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec40bfad44d7f333973d83b42e2f25ef\transformed\play-services-measurement-base-22.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec40bfad44d7f333973d83b42e2f25ef\transformed\play-services-measurement-base-22.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aff62db08cff511cc4373673ada3b6cf\transformed\play-services-basement-18.5.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aff62db08cff511cc4373673ada3b6cf\transformed\play-services-basement-18.5.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d025b3265c888cd3a1bdd8eb838d60d6\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d025b3265c888cd3a1bdd8eb838d60d6\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5bd95e0bd3a26435e895636f321ad3e0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5bd95e0bd3a26435e895636f321ad3e0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30632a7c8dd1b017955f6c4482cfc334\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30632a7c8dd1b017955f6c4482cfc334\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf66c28488d56b085c276c14a4449049\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:25:5-33:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf66c28488d56b085c276c14a4449049\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:25:5-33:19
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18adbb13687610ba2ec263dcbb46b5e9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:23:5-37:19
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18adbb13687610ba2ec263dcbb46b5e9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:23:5-37:19
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac9518aeda46c3b802798bcb4651bb33\transformed\room-runtime-2.2.5\AndroidManifest.xml:24:5-29:19
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac9518aeda46c3b802798bcb4651bb33\transformed\room-runtime-2.2.5\AndroidManifest.xml:24:5-29:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3dd3f063a8dc2168576dc8a6639ef4\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:35:9-35
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:33:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:31:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:34:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:37:9-29
	android:icon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:32:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:29:9-35
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:36:9-65
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:30:9-65
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:28:9-44
meta-data#com.google.android.gms.ads.APPLICATION_ID
ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:40:9-42:70
	android:value
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:42:13-67
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:41:13-69
activity#com.mahmoudffyt.gfxbooster.GameBoosterActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:43:9-46:45
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:45:13-37
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:46:13-42
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:44:13-48
activity#com.mahmoudffyt.gfxbooster.MainActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:47:9-50:45
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:49:13-37
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:50:13-42
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:48:13-41
activity#com.mahmoudffyt.gfxbooster.splashscreen
ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:51:9-60:20
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:53:13-36
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:54:13-42
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:52:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:55:13-59:29
	tools:ignore
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:55:28-52
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:58:17-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:58:27-74
activity#com.mahmoudffyt.gfxbooster.SettingsActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:62:9-65:45
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:64:13-37
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:65:13-42
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:63:13-45
activity#com.mahmoudffyt.gfxbooster.GameModeActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:66:9-69:45
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:68:13-37
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:69:13-42
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:67:13-45
activity#com.mahmoudffyt.gfxbooster.HeadshotToolActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:70:9-73:45
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:72:13-37
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:73:13-42
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:71:13-49
activity#com.mahmoudffyt.gfxbooster.AimOverlaySettingsActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:78:9-81:45
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:80:13-37
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:81:13-42
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:79:13-55
activity#com.mahmoudffyt.gfxbooster.GfxToolsActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:82:9-85:45
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:84:13-37
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:85:13-42
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:83:13-45
activity#com.mahmoudffyt.gfxbooster.AppGuideActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:86:9-89:45
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:88:13-37
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:89:13-42
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:87:13-45
activity#com.mahmoudffyt.gfxbooster.WebViewActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:90:9-93:45
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:92:13-37
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:93:13-42
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:91:13-44
service#com.mahmoudffyt.gfxbooster.GameModeService
ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:96:9-100:56
	android:enabled
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:98:13-35
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:99:13-37
	android:foregroundServiceType
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:100:13-53
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:97:13-44
receiver#com.mahmoudffyt.gfxbooster.GameModeBootReceiver
ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:102:9-109:20
	android:enabled
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:104:13-35
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:105:13-36
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:103:13-49
intent-filter#action:name:android.intent.action.BOOT_COMPLETED
ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:106:13-108:29
action#android.intent.action.BOOT_COMPLETED
ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:107:17-79
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:107:25-76
receiver#com.mahmoudffyt.gfxbooster.NotificationReceiver
ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:111:9-121:20
	android:enabled
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:113:13-35
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:114:13-36
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:112:13-49
intent-filter#action:name:com.game.headshot.ACTION_CHECK_SYSTEM+action:name:com.game.headshot.ACTION_EVENING_REMINDER+action:name:com.game.headshot.ACTION_HIGH_USAGE+action:name:com.game.headshot.ACTION_NOON_REMINDER
ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:115:13-120:29
action#com.game.headshot.ACTION_NOON_REMINDER
ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:116:17-81
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:116:25-78
action#com.game.headshot.ACTION_EVENING_REMINDER
ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:117:17-84
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:117:25-81
action#com.game.headshot.ACTION_HIGH_USAGE
ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:118:17-78
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:118:25-75
action#com.game.headshot.ACTION_CHECK_SYSTEM
ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:119:17-80
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml:119:25-77
uses-sdk
INJECTED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\464e6a7dcfefd0da870c4050aa381b0c\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\464e6a7dcfefd0da870c4050aa381b0c\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d3bfba1f66ed76a002b3095ffa4a442\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d3bfba1f66ed76a002b3095ffa4a442\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie:6.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa9f63409c886091ad2e87355e8b4fc6\transformed\lottie-6.0.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.airbnb.android:lottie:6.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa9f63409c886091ad2e87355e8b4fc6\transformed\lottie-6.0.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f827cf25f7862f0f03f625c0fff7beb\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f827cf25f7862f0f03f625c0fff7beb\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a44ad6b726c152c4fe16ed54c5a8c02\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a44ad6b726c152c4fe16ed54c5a8c02\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:6:5-8:41
MERGED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:6:5-8:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\52866cc66fe3b6210482b9ca75cda756\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\52866cc66fe3b6210482b9ca75cda756\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-location:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd69874069156293e09f6fb5d1dbd5f0\transformed\play-services-location-19.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd69874069156293e09f6fb5d1dbd5f0\transformed\play-services-location-19.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-ads:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ba171b74415ef9d04b1f3a055c632e8\transformed\play-services-ads-24.2.0\AndroidManifest.xml:21:5-23:52
MERGED from [com.google.android.gms:play-services-ads:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ba171b74415ef9d04b1f3a055c632e8\transformed\play-services-ads-24.2.0\AndroidManifest.xml:21:5-23:52
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ec64ee55e8b2d7caf0c3d5a0e05cb1a\transformed\play-services-appset-16.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ec64ee55e8b2d7caf0c3d5a0e05cb1a\transformed\play-services-appset-16.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics-ktx:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ad6a65ba20f74a8e88da6b866e65a53\transformed\firebase-analytics-ktx-22.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics-ktx:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ad6a65ba20f74a8e88da6b866e65a53\transformed\firebase-analytics-ktx-22.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e22719ef4c1940e0e5a676613c52c3fc\transformed\firebase-analytics-22.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e22719ef4c1940e0e5a676613c52c3fc\transformed\firebase-analytics-22.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47ccc0e24a10258223a997dba2340add\transformed\play-services-measurement-sdk-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47ccc0e24a10258223a997dba2340add\transformed\play-services-measurement-sdk-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5be24e152e5ffa226393bdb33a3aef6\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5be24e152e5ffa226393bdb33a3aef6\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef3205b01dd1e8d62ca95413fbcfcde0\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef3205b01dd1e8d62ca95413fbcfcde0\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.firebase:firebase-crashlytics-ktx:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5995498b5b81ac31250b4f1324a21132\transformed\firebase-crashlytics-ktx-19.4.3\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-crashlytics-ktx:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5995498b5b81ac31250b4f1324a21132\transformed\firebase-crashlytics-ktx-19.4.3\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\046374a346d3cc3e0fb7692fa882950a\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\046374a346d3cc3e0fb7692fa882950a\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a526ba4e24480c7108925ec9a95f4804\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a526ba4e24480c7108925ec9a95f4804\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3849af013f032b744f299890f6883a64\transformed\firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3849af013f032b744f299890f6883a64\transformed\firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9c854e55cb7e484c6bf93dacda33eca\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9c854e55cb7e484c6bf93dacda33eca\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\13062518ca145c233df63973b32ae5b8\transformed\firebase-installations-interop-17.2.0\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\13062518ca145c233df63973b32ae5b8\transformed\firebase-installations-interop-17.2.0\AndroidManifest.xml:17:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e33d99db0e60492889ee01a086fddfe\transformed\ads-adservices-java-1.1.0-beta11\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e33d99db0e60492889ee01a086fddfe\transformed\ads-adservices-java-1.1.0-beta11\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6a554b97c6fdafe7f9e475f6f094c65\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6a554b97c6fdafe7f9e475f6f094c65\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f8cdc29058077ea676ff1cf735bd0db8\transformed\core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f8cdc29058077ea676ff1cf735bd0db8\transformed\core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ade37d50a5352dfb90d4f64617e2343\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ade37d50a5352dfb90d4f64617e2343\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37e33928c78c0b5c37d0e47d6d95412c\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37e33928c78c0b5c37d0e47d6d95412c\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1f2f08d4ef22c9e7417d286fb78a6299\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1f2f08d4ef22c9e7417d286fb78a6299\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\163d8f14852b3ec60e1695d54d0ed062\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\163d8f14852b3ec60e1695d54d0ed062\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f68eb8005ee4007900de772b7f50d75\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f68eb8005ee4007900de772b7f50d75\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\747cc69e4f70cdd210d779e95fcb0e32\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\747cc69e4f70cdd210d779e95fcb0e32\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b87f265a57c9ebd6cbcb7a6156c9346b\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b87f265a57c9ebd6cbcb7a6156c9346b\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc757cae52d77fef3639111cfbd70412\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc757cae52d77fef3639111cfbd70412\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46c7e1c2c774c76fe5e74081b097ef9a\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46c7e1c2c774c76fe5e74081b097ef9a\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.11.0-alpha02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b176558c57fa68e6eeead9fc01cf480\transformed\webkit-1.11.0-alpha02\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.11.0-alpha02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b176558c57fa68e6eeead9fc01cf480\transformed\webkit-1.11.0-alpha02\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c3ed062c8fee57bce302ac727e5e91b\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c3ed062c8fee57bce302ac727e5e91b\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b02ba864689118a0780e08784428d782\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b02ba864689118a0780e08784428d782\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\366ffaf53b1d63f2f6c1766ab4a86dc1\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\366ffaf53b1d63f2f6c1766ab4a86dc1\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1f8a9f48954aed687e4952c2c964f46e\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1f8a9f48954aed687e4952c2c964f46e\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb1a011bed87b14c31f4355f21972067\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb1a011bed87b14c31f4355f21972067\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\129333b0c8ecb55c4464ed63f4afcb5a\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\129333b0c8ecb55c4464ed63f4afcb5a\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3dd3f063a8dc2168576dc8a6639ef4\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3dd3f063a8dc2168576dc8a6639ef4\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c17220d42609b7099a13844d88bf065e\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c17220d42609b7099a13844d88bf065e\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97807501b603dce69153c0410a51d4c3\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97807501b603dce69153c0410a51d4c3\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4b43ee03a56918c2f91003aee95a932\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4b43ee03a56918c2f91003aee95a932\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\921836e7511c8cb3e70450775705a043\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\921836e7511c8cb3e70450775705a043\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71c738af83ef2034b82eb1807c939ed2\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71c738af83ef2034b82eb1807c939ed2\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78bfd8071e7a42117a7d03d72d78a29d\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78bfd8071e7a42117a7d03d72d78a29d\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53ec625aa4dc40f4efee1b09f5c94cc1\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53ec625aa4dc40f4efee1b09f5c94cc1\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15e499373e61a4c01ca0a7c30c277831\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15e499373e61a4c01ca0a7c30c277831\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\33ea3dfa4708107f1567d296fb39fc05\transformed\datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\33ea3dfa4708107f1567d296fb39fc05\transformed\datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\89de661cc71c663b0686fe72581f7040\transformed\datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\89de661cc71c663b0686fe72581f7040\transformed\datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae6f74430a5b6eb34228cb640ef82a38\transformed\datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae6f74430a5b6eb34228cb640ef82a38\transformed\datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\573224c9122c27a96e929c121b9417b2\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\573224c9122c27a96e929c121b9417b2\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9359014a9bdd06751a5e83a9c8611bbf\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9359014a9bdd06751a5e83a9c8611bbf\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\091bd03a708318c916022e504c59ffe6\transformed\firebase-measurement-connector-20.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\091bd03a708318c916022e504c59ffe6\transformed\firebase-measurement-connector-20.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-places-placereport:17.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b1b8c5d28b044b6d333a968efb8036cd\transformed\play-services-places-placereport-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-places-placereport:17.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b1b8c5d28b044b6d333a968efb8036cd\transformed\play-services-places-placereport-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.ump:user-messaging-platform:3.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ecbf3415acea89f234e763b19d4c3b4\transformed\user-messaging-platform-3.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.ump:user-messaging-platform:3.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ecbf3415acea89f234e763b19d4c3b4\transformed\user-messaging-platform-3.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2adc3a67aa3e84044dbc438a9bf8596\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2adc3a67aa3e84044dbc438a9bf8596\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec40bfad44d7f333973d83b42e2f25ef\transformed\play-services-measurement-base-22.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec40bfad44d7f333973d83b42e2f25ef\transformed\play-services-measurement-base-22.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aff62db08cff511cc4373673ada3b6cf\transformed\play-services-basement-18.5.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aff62db08cff511cc4373673ada3b6cf\transformed\play-services-basement-18.5.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fea55175bd7351d2ad813189ca90fcf0\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fea55175bd7351d2ad813189ca90fcf0\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a45f6f0a50623914568ad00c0c5c4f2\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a45f6f0a50623914568ad00c0c5c4f2\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7723f1b9af1a91aebee2949e3a188b7f\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7723f1b9af1a91aebee2949e3a188b7f\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b29808633b68d0d203290857d34666ac\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b29808633b68d0d203290857d34666ac\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d025b3265c888cd3a1bdd8eb838d60d6\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d025b3265c888cd3a1bdd8eb838d60d6\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0cfe4ee17482e1198f9ec6063ea0e3fa\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0cfe4ee17482e1198f9ec6063ea0e3fa\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd704e1e1f90908cf1fe364124e6a036\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd704e1e1f90908cf1fe364124e6a036\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5bd95e0bd3a26435e895636f321ad3e0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5bd95e0bd3a26435e895636f321ad3e0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c5a84dde060c252df413aba1e00d40d4\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c5a84dde060c252df413aba1e00d40d4\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30632a7c8dd1b017955f6c4482cfc334\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30632a7c8dd1b017955f6c4482cfc334\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf66c28488d56b085c276c14a4449049\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf66c28488d56b085c276c14a4449049\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18adbb13687610ba2ec263dcbb46b5e9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18adbb13687610ba2ec263dcbb46b5e9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5280e756f471b9c925fc23cc67b9f6a4\transformed\transport-api-3.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5280e756f471b9c925fc23cc67b9f6a4\transformed\transport-api-3.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-config-interop:16.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e9c22cef630d4fa23a07868a158eb4e8\transformed\firebase-config-interop-16.0.1\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-config-interop:16.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e9c22cef630d4fa23a07868a158eb4e8\transformed\firebase-config-interop-16.0.1\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a14d85f449dbf562c7e2ff341e6c9c51\transformed\firebase-encoders-json-18.0.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a14d85f449dbf562c7e2ff341e6c9c51\transformed\firebase-encoders-json-18.0.1\AndroidManifest.xml:18:5-20:41
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac9518aeda46c3b802798bcb4651bb33\transformed\room-runtime-2.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac9518aeda46c3b802798bcb4651bb33\transformed\room-runtime-2.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\03c82351977e359f20cc55c016b1719a\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\03c82351977e359f20cc55c016b1719a\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\035ef458de483479aea24c20649833e6\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\035ef458de483479aea24c20649833e6\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\12e642919c92d644a8984f92f12af086\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\12e642919c92d644a8984f92f12af086\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\235be14cca6992f97d93c356dcc26fa2\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\235be14cca6992f97d93c356dcc26fa2\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\835172f1e3d6d36b53a4e96b3ba17b72\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\835172f1e3d6d36b53a4e96b3ba17b72\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fca988ea54d2aca4576982756a9da820\transformed\sqlite-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fca988ea54d2aca4576982756a9da820\transformed\sqlite-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e92248e6039d264b4b6e160952af6e23\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e92248e6039d264b4b6e160952af6e23\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\03f2731a82c9f519c26465db79a33215\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\03f2731a82c9f519c26465db79a33215\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
	tools:overrideLibrary
		ADDED from [com.google.android.gms:play-services-ads:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ba171b74415ef9d04b1f3a055c632e8\transformed\play-services-ads-24.2.0\AndroidManifest.xml:23:9-49
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\HeadshotSettingsGameBooster\app\src\main\AndroidManifest.xml
uses-permission#com.android.vending.BILLING
ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:10:5-67
	android:name
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:10:22-64
intent#action:name:com.android.vending.billing.InAppBillingService.BIND
ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:13:9-15:18
action#com.android.vending.billing.InAppBillingService.BIND
ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:14:13-91
	android:name
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:14:21-88
intent#action:name:com.google.android.apps.play.billingtestcompanion.BillingOverrideService.BIND
ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:16:9-18:18
action#com.google.android.apps.play.billingtestcompanion.BillingOverrideService.BIND
ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:17:13-116
	android:name
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:17:21-113
meta-data#com.google.android.play.billingclient.version
ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:22:9-24:37
	android:value
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:24:13-34
	android:name
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:23:13-73
activity#com.android.billingclient.api.ProxyBillingActivity
ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:26:9-30:75
	android:exported
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:29:13-37
	android:configChanges
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:28:13-96
	android:theme
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:30:13-72
	android:name
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:27:13-78
activity#com.android.billingclient.api.ProxyBillingActivityV2
ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:31:9-35:75
	android:exported
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:34:13-37
	android:configChanges
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:33:13-96
	android:theme
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:35:13-72
	android:name
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df66cb2474837d018f10c8eb5a8680d6\transformed\billing-7.1.1\AndroidManifest.xml:32:13-80
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-analytics-ktx:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ad6a65ba20f74a8e88da6b866e65a53\transformed\firebase-analytics-ktx-22.4.0\AndroidManifest.xml:8:9-14:19
MERGED from [com.google.firebase:firebase-crashlytics-ktx:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5995498b5b81ac31250b4f1324a21132\transformed\firebase-crashlytics-ktx-19.4.3\AndroidManifest.xml:21:9-27:19
MERGED from [com.google.firebase:firebase-crashlytics-ktx:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5995498b5b81ac31250b4f1324a21132\transformed\firebase-crashlytics-ktx-19.4.3\AndroidManifest.xml:21:9-27:19
MERGED from [com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\046374a346d3cc3e0fb7692fa882950a\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\046374a346d3cc3e0fb7692fa882950a\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:30:9-36:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:30:9-36:19
MERGED from [com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a526ba4e24480c7108925ec9a95f4804\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:26:9-32:19
MERGED from [com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a526ba4e24480c7108925ec9a95f4804\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:26:9-32:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3849af013f032b744f299890f6883a64\transformed\firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3849af013f032b744f299890f6883a64\transformed\firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9c854e55cb7e484c6bf93dacda33eca\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9c854e55cb7e484c6bf93dacda33eca\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30632a7c8dd1b017955f6c4482cfc334\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30632a7c8dd1b017955f6c4482cfc334\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:22:9-28:19
	android:exported
		ADDED from [com.google.firebase:firebase-analytics-ktx:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ad6a65ba20f74a8e88da6b866e65a53\transformed\firebase-analytics-ktx-22.4.0\AndroidManifest.xml:10:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-analytics-ktx:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ad6a65ba20f74a8e88da6b866e65a53\transformed\firebase-analytics-ktx-22.4.0\AndroidManifest.xml:9:13-84
meta-data#com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar
ADDED from [com.google.firebase:firebase-analytics-ktx:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ad6a65ba20f74a8e88da6b866e65a53\transformed\firebase-analytics-ktx-22.4.0\AndroidManifest.xml:11:13-13:85
	android:value
		ADDED from [com.google.firebase:firebase-analytics-ktx:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ad6a65ba20f74a8e88da6b866e65a53\transformed\firebase-analytics-ktx-22.4.0\AndroidManifest.xml:13:17-82
	android:name
		ADDED from [com.google.firebase:firebase-analytics-ktx:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ad6a65ba20f74a8e88da6b866e65a53\transformed\firebase-analytics-ktx-22.4.0\AndroidManifest.xml:12:17-129
uses-permission#android.permission.WAKE_LOCK
ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5be24e152e5ffa226393bdb33a3aef6\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5be24e152e5ffa226393bdb33a3aef6\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:24:5-68
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2adc3a67aa3e84044dbc438a9bf8596\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2adc3a67aa3e84044dbc438a9bf8596\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:25:5-68
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:25:22-65
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5be24e152e5ffa226393bdb33a3aef6\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5be24e152e5ffa226393bdb33a3aef6\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:26:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\693b52147fa175de0f606cda2b20d804\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:40:13-87
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5be24e152e5ffa226393bdb33a3aef6\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9359014a9bdd06751a5e83a9c8611bbf\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9359014a9bdd06751a5e83a9c8611bbf\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2adc3a67aa3e84044dbc438a9bf8596\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2adc3a67aa3e84044dbc438a9bf8596\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5be24e152e5ffa226393bdb33a3aef6\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:27:22-76
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef3205b01dd1e8d62ca95413fbcfcde0\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef3205b01dd1e8d62ca95413fbcfcde0\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef3205b01dd1e8d62ca95413fbcfcde0\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef3205b01dd1e8d62ca95413fbcfcde0\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
meta-data#com.google.firebase.components:com.google.firebase.crashlytics.ktx.FirebaseCrashlyticsKtxRegistrar
ADDED from [com.google.firebase:firebase-crashlytics-ktx:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5995498b5b81ac31250b4f1324a21132\transformed\firebase-crashlytics-ktx-19.4.3\AndroidManifest.xml:24:13-26:85
	android:value
		ADDED from [com.google.firebase:firebase-crashlytics-ktx:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5995498b5b81ac31250b4f1324a21132\transformed\firebase-crashlytics-ktx-19.4.3\AndroidManifest.xml:26:17-82
	android:name
		ADDED from [com.google.firebase:firebase-crashlytics-ktx:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5995498b5b81ac31250b4f1324a21132\transformed\firebase-crashlytics-ktx-19.4.3\AndroidManifest.xml:25:17-130
meta-data#com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar
ADDED from [com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\046374a346d3cc3e0fb7692fa882950a\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\046374a346d3cc3e0fb7692fa882950a\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\046374a346d3cc3e0fb7692fa882950a\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:16:17-126
meta-data#com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar
ADDED from [com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\046374a346d3cc3e0fb7692fa882950a\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\046374a346d3cc3e0fb7692fa882950a\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\046374a346d3cc3e0fb7692fa882950a\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:19:17-115
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:26:5-88
MERGED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:28:5-88
MERGED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:28:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2adc3a67aa3e84044dbc438a9bf8596\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:27:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2adc3a67aa3e84044dbc438a9bf8596\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:26:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2adc3a67aa3e84044dbc438a9bf8596\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2adc3a67aa3e84044dbc438a9bf8596\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:28:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:27:22-79
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:33:13-35:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:35:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddd500c8987a68f88c609ce560548778\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:34:17-139
service#com.google.firebase.sessions.SessionLifecycleService
ADDED from [com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a526ba4e24480c7108925ec9a95f4804\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:22:9-25:40
	android:enabled
		ADDED from [com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a526ba4e24480c7108925ec9a95f4804\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:24:13-35
	android:exported
		ADDED from [com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a526ba4e24480c7108925ec9a95f4804\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a526ba4e24480c7108925ec9a95f4804\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:23:13-80
meta-data#com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar
ADDED from [com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a526ba4e24480c7108925ec9a95f4804\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:29:13-31:85
	android:value
		ADDED from [com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a526ba4e24480c7108925ec9a95f4804\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:31:17-82
	android:name
		ADDED from [com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a526ba4e24480c7108925ec9a95f4804\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:30:17-117
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3849af013f032b744f299890f6883a64\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3849af013f032b744f299890f6883a64\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3849af013f032b744f299890f6883a64\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3849af013f032b744f299890f6883a64\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3849af013f032b744f299890f6883a64\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3849af013f032b744f299890f6883a64\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9c854e55cb7e484c6bf93dacda33eca\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9c854e55cb7e484c6bf93dacda33eca\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9c854e55cb7e484c6bf93dacda33eca\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a39121bcd84095e7b891a4b919568a60\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6a554b97c6fdafe7f9e475f6f094c65\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6a554b97c6fdafe7f9e475f6f094c65\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6a554b97c6fdafe7f9e475f6f094c65\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37e33928c78c0b5c37d0e47d6d95412c\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:31:9-39:20
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:31:9-39:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\921836e7511c8cb3e70450775705a043\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\921836e7511c8cb3e70450775705a043\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d025b3265c888cd3a1bdd8eb838d60d6\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d025b3265c888cd3a1bdd8eb838d60d6\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37e33928c78c0b5c37d0e47d6d95412c\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37e33928c78c0b5c37d0e47d6d95412c\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37e33928c78c0b5c37d0e47d6d95412c\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37e33928c78c0b5c37d0e47d6d95412c\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37e33928c78c0b5c37d0e47d6d95412c\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37e33928c78c0b5c37d0e47d6d95412c\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37e33928c78c0b5c37d0e47d6d95412c\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
uses-permission#android.permission.ACCESS_ADSERVICES_TOPICS
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:29:5-83
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:29:22-80
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:38:9-44:18
action#android.intent.action.VIEW
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:39:13-65
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:39:21-62
category#android.intent.category.BROWSABLE
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:41:13-74
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:41:23-71
data
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:43:13-44
	android:scheme
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:43:19-41
intent#action:name:android.support.customtabs.action.CustomTabsService
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:47:9-49:18
action#android.support.customtabs.action.CustomTabsService
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:48:13-90
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:48:21-87
intent#action:name:android.intent.action.INSERT+data:mimeType:vnd.android.cursor.dir/event
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:52:9-56:18
action#android.intent.action.INSERT
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:53:13-67
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:53:21-64
intent#action:name:android.intent.action.VIEW+data:scheme:sms
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:57:9-61:18
intent#action:name:android.intent.action.DIAL+data:path:tel:
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:62:9-66:18
action#android.intent.action.DIAL
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:63:13-65
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:63:21-62
activity#com.google.android.gms.ads.AdActivity
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:73:9-78:43
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:76:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:78:13-40
	android:configChanges
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:75:13-122
	android:theme
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:77:13-61
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:74:13-65
provider#com.google.android.gms.ads.MobileAdsInitProvider
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:80:9-85:43
	android:authorities
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:82:13-73
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:83:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:85:13-40
	android:initOrder
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:84:13-36
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:81:13-76
service#com.google.android.gms.ads.AdService
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:87:9-91:43
	android:enabled
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:89:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:90:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:91:13-40
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:88:13-64
activity#com.google.android.gms.ads.OutOfContextTestingActivity
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:93:9-97:43
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:96:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:97:13-40
	android:configChanges
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:95:13-122
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:94:13-82
activity#com.google.android.gms.ads.NotificationHandlerActivity
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:98:9-105:43
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:100:13-46
	android:launchMode
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:102:13-44
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:101:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:105:13-40
	android:theme
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:104:13-72
	android:taskAffinity
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:103:13-36
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:99:13-82
meta-data#com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:107:9-109:36
	android:value
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:109:13-33
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:108:13-79
meta-data#com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:110:9-112:36
	android:value
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:112:13-33
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bbb8b47e04969157e585b36331eb40\transformed\play-services-ads-api-24.2.0\AndroidManifest.xml:111:13-83
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
	android:value
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:46:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:53:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42817097875fd23fefd6b360ff5530d\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3dd3f063a8dc2168576dc8a6639ef4\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3dd3f063a8dc2168576dc8a6639ef4\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3dd3f063a8dc2168576dc8a6639ef4\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
permission#com.mahmoudffyt.gfxbooster.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3dd3f063a8dc2168576dc8a6639ef4\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3dd3f063a8dc2168576dc8a6639ef4\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3dd3f063a8dc2168576dc8a6639ef4\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3dd3f063a8dc2168576dc8a6639ef4\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3dd3f063a8dc2168576dc8a6639ef4\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
uses-permission#com.mahmoudffyt.gfxbooster.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3dd3f063a8dc2168576dc8a6639ef4\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3dd3f063a8dc2168576dc8a6639ef4\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\921836e7511c8cb3e70450775705a043\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\921836e7511c8cb3e70450775705a043\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\921836e7511c8cb3e70450775705a043\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aff62db08cff511cc4373673ada3b6cf\transformed\play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aff62db08cff511cc4373673ada3b6cf\transformed\play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aff62db08cff511cc4373673ada3b6cf\transformed\play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da261f728de5c2f450c9ba6f94ed61e8\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30632a7c8dd1b017955f6c4482cfc334\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30632a7c8dd1b017955f6c4482cfc334\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30632a7c8dd1b017955f6c4482cfc334\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:26:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf66c28488d56b085c276c14a4449049\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:26:9-32:19
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18adbb13687610ba2ec263dcbb46b5e9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:34:9-36:40
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18adbb13687610ba2ec263dcbb46b5e9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:34:9-36:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf66c28488d56b085c276c14a4449049\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf66c28488d56b085c276c14a4449049\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:27:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf66c28488d56b085c276c14a4449049\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:29:13-31:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf66c28488d56b085c276c14a4449049\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:31:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf66c28488d56b085c276c14a4449049\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:30:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18adbb13687610ba2ec263dcbb46b5e9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:24:9-28:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18adbb13687610ba2ec263dcbb46b5e9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:26:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18adbb13687610ba2ec263dcbb46b5e9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:27:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18adbb13687610ba2ec263dcbb46b5e9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:25:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18adbb13687610ba2ec263dcbb46b5e9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:30:9-32:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18adbb13687610ba2ec263dcbb46b5e9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18adbb13687610ba2ec263dcbb46b5e9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:31:13-132
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac9518aeda46c3b802798bcb4651bb33\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
	android:exported
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac9518aeda46c3b802798bcb4651bb33\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac9518aeda46c3b802798bcb4651bb33\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac9518aeda46c3b802798bcb4651bb33\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
