package com.mahmoudffyt.gfxbooster;

import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.app.AlertDialog;
import android.content.Context;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.VibrationEffect;
import android.os.Vibrator;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.SeekBar;
import android.widget.TextView;
import android.widget.Toast;

import java.util.HashMap;
import java.util.Map;

import androidx.appcompat.app.AppCompatActivity;
import androidx.cardview.widget.CardView;

import com.mahmoudffyt.gfxbooster.utils.AdManager;
import com.mahmoudffyt.gfxbooster.utils.PremiumManager;
import com.google.android.material.switchmaterial.SwitchMaterial;

public class GfxToolsActivity extends AppCompatActivity {

    // UI Elements
    private ImageButton btnBack;
    private ImageView speedLinesBg, gfxLogo;
    private TextView titleText, deviceInfoText;
    private CardView deviceInfoCard, resolutionCard, fpsCard, graphicsCard, advancedOptionsCard;
    private RadioGroup resolutionRadioGroup, fpsRadioGroup;
    private RadioButton resolutionLow, resolutionMedium, resolutionHigh, resolutionUltra;
    private RadioButton fps30, fps60, fps90;
    private SeekBar graphicsSeekbar;
    private SwitchMaterial switchHdr, switchAntialiasing, switchGpuBoost;
    private Button applyButton, suggestSettingsButton;
    private FrameLayout adContainer;

    // Settings
    private String selectedResolution = "1080p";
    private int selectedFps = 60;
    private int selectedGraphicsQuality = 1; // 0=Low, 1=Medium, 2=High
    private boolean hdrEnabled = false;
    private boolean antiAliasingEnabled = true;
    private boolean gpuBoostEnabled = false;

    // Utility classes and state
    private GfxSettingsHelper gfxSettingsHelper;
    private Handler handler = new Handler();
    private AdManager adManager;
    private PremiumManager premiumManager;
    private boolean adLoadedInCurrentSession = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_gfx_tools);

        // Initialize utility classes
        gfxSettingsHelper = new GfxSettingsHelper(this);
        adManager = AdManager.getInstance(this);
        premiumManager = new PremiumManager(this);

        // Initialize UI elements
        initializeViews();

        // Set up listeners
        setupListeners();

        // Start entrance animations
        startEntranceAnimations();

        // Analyze device and update info
        analyzeDevice();

        // Load ads
        loadAds();
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        // حفظ حالة الإعدادات
        outState.putString("selectedResolution", selectedResolution);
        outState.putInt("selectedFps", selectedFps);
        outState.putInt("selectedGraphicsQuality", selectedGraphicsQuality);
        outState.putBoolean("hdrEnabled", hdrEnabled);
        outState.putBoolean("antiAliasingEnabled", antiAliasingEnabled);
        outState.putBoolean("gpuBoostEnabled", gpuBoostEnabled);
    }

    @Override
    protected void onRestoreInstanceState(Bundle savedInstanceState) {
        super.onRestoreInstanceState(savedInstanceState);
        if (savedInstanceState != null) {
            // استعادة حالة الإعدادات
            selectedResolution = savedInstanceState.getString("selectedResolution", "1080p");
            selectedFps = savedInstanceState.getInt("selectedFps", 60);
            selectedGraphicsQuality = savedInstanceState.getInt("selectedGraphicsQuality", 1);
            hdrEnabled = savedInstanceState.getBoolean("hdrEnabled", false);
            antiAliasingEnabled = savedInstanceState.getBoolean("antiAliasingEnabled", true);
            gpuBoostEnabled = savedInstanceState.getBoolean("gpuBoostEnabled", false);

            // تحديث واجهة المستخدم بالقيم المستعادة
            updateUIWithSettings();
        }
    }

    @Override
    protected void onStop() {
        super.onStop();
        // حفظ الحالة وتنظيف الموارد عند دخول التطبيق في الخلفية
        if (speedLinesAnimator != null) {
            speedLinesAnimator.pause();
        }
        saveCurrentSettings();
    }

    /**
     * Save current settings to preferences
     */
    private void saveCurrentSettings() {
        // Create settings map
        Map<String, Object> settings = new HashMap<>();
        settings.put("resolution", selectedResolution);
        settings.put("fps", selectedFps);
        settings.put("graphicsQuality", selectedGraphicsQuality);
        settings.put("hdrEnabled", hdrEnabled);
        settings.put("antiAliasingEnabled", antiAliasingEnabled);
        settings.put("gpuBoostEnabled", gpuBoostEnabled);

        // Save settings to preferences
        gfxSettingsHelper.saveGfxSettings(settings);
    }

    @Override
    protected void onRestart() {
        super.onRestart();
        // إعادة تشغيل الanimations إذا كانت متوقفة
        if (speedLinesAnimator != null) {
            speedLinesAnimator.resume();
        }
        // تحديث معلومات الجهاز
        analyzeDevice();
    }

    @Override
    protected void onResume() {
        super.onResume();

        // تحميل الإعلانات فقط إذا لم يتم تحميلها في الجلسة الحالية
        if (!adLoadedInCurrentSession) {
            loadAds();
            adLoadedInCurrentSession = true;
        }

        // إعادة تشغيل الanimations إذا كانت متوقفة
        if (speedLinesAnimator != null && speedLinesAnimator.isPaused()) {
            speedLinesAnimator.resume();
        }

        // تحديث واجهة المستخدم بآخر الإعدادات المحفوظة
        updateUIWithSettings();
    }

    /**
     * Load ads in the activity
     */
    private void loadAds() {
        // Load medium rectangle banner ad below FPS card
        if (adContainer != null) {
            adManager.loadMediumRectangleBannerAd(this, adContainer, AdManager.BANNER_AD_UNIT_ID);
        }

        // Preload interstitial ad for Apply Settings button
        adManager.preloadInterstitialAd(this);

        // Preload rewarded ad for Suggest Best Settings button
        adManager.preloadRewardedAd(this);
    }

    private void initializeViews() {
        // Basic UI elements
        btnBack = findViewById(R.id.btn_back);
        speedLinesBg = findViewById(R.id.speed_lines_bg);
        //gfxLogo = findViewById(R.id.gfx_logo);
        titleText = findViewById(R.id.title_text);
        deviceInfoText = findViewById(R.id.device_info_text);

        // Cards
        deviceInfoCard = findViewById(R.id.device_info_card);
        resolutionCard = findViewById(R.id.resolution_card);
        fpsCard = findViewById(R.id.fps_card);
        graphicsCard = findViewById(R.id.graphics_card);
        advancedOptionsCard = findViewById(R.id.advanced_options_card);

        // Ad container
        adContainer = findViewById(R.id.ad_container);

        // Resolution radio group
        resolutionRadioGroup = findViewById(R.id.resolution_radio_group);
        resolutionLow = findViewById(R.id.resolution_low);
        resolutionMedium = findViewById(R.id.resolution_medium);
        resolutionHigh = findViewById(R.id.resolution_high);
        resolutionUltra = findViewById(R.id.resolution_ultra);

        // FPS radio group
        fpsRadioGroup = findViewById(R.id.fps_radio_group);
        fps30 = findViewById(R.id.fps_30);
        fps60 = findViewById(R.id.fps_60);
        fps90 = findViewById(R.id.fps_90);

        // Graphics seekbar
        graphicsSeekbar = findViewById(R.id.graphics_seekbar);

        // Switches
        switchHdr = findViewById(R.id.switch_hdr);
        switchAntialiasing = findViewById(R.id.switch_antialiasing);
        switchGpuBoost = findViewById(R.id.switch_gpu_boost);

        // Suggest Settings button
        suggestSettingsButton = findViewById(R.id.suggest_settings_button);

        // Apply button
        applyButton = findViewById(R.id.apply_button);

        // Load saved settings if available
        loadSavedSettings();
    }

    private void setupListeners() {
        // Back button
        btnBack.setOnClickListener(v -> {
            vibrateDevice(20);
            finish();
        });

        // Resolution radio group
        resolutionRadioGroup.setOnCheckedChangeListener((group, checkedId) -> {
            vibrateDevice(20);
            if (checkedId == R.id.resolution_low) {
                selectedResolution = "720p";
            } else if (checkedId == R.id.resolution_medium) {
                selectedResolution = "1080p";
            } else if (checkedId == R.id.resolution_high) {
                selectedResolution = "1440p";
            } else if (checkedId == R.id.resolution_ultra) {
                selectedResolution = "2160p";
            }
        });

        // FPS radio group
        fpsRadioGroup.setOnCheckedChangeListener((group, checkedId) -> {
            vibrateDevice(20);
            if (checkedId == R.id.fps_30) {
                selectedFps = 30;
            } else if (checkedId == R.id.fps_60) {
                selectedFps = 60;
            } else if (checkedId == R.id.fps_90) {
                selectedFps = 90;
            }
        });

        // Graphics seekbar
        graphicsSeekbar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                selectedGraphicsQuality = progress;
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                vibrateDevice(20);
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                vibrateDevice(20);
                String qualityText;
                switch (selectedGraphicsQuality) {
                    case 0:
                        qualityText = "Low";
                        break;
                    case 2:
                        qualityText = "High";
                        break;
                    default:
                        qualityText = "Medium";
                        break;
                }
               // Toast.makeText(GfxToolsActivity.this, getString(R.string.graphics_quality_gfxtool) + qualityText, Toast.LENGTH_SHORT).show();
            }
        });

        // Switches
        switchHdr.setOnCheckedChangeListener((buttonView, isChecked) -> {
            vibrateDevice(20);
            hdrEnabled = isChecked;
            Toast.makeText(this, "HDR Mode: " + (isChecked ? getString(R.string.enabled)  : getString(R.string.disabled) ), Toast.LENGTH_SHORT).show();
        });

        switchAntialiasing.setOnCheckedChangeListener((buttonView, isChecked) -> {
            vibrateDevice(20);
            antiAliasingEnabled = isChecked;
            Toast.makeText(this, "Anti-aliasing: " + (isChecked ? getString(R.string.enabled)  : getString(R.string.disabled) ), Toast.LENGTH_SHORT).show();
        });

        switchGpuBoost.setOnCheckedChangeListener((buttonView, isChecked) -> {
            vibrateDevice(20);
            gpuBoostEnabled = isChecked;
            Toast.makeText(this, "GPU Boost: " + (isChecked ?getString(R.string.enabled)  : getString(R.string.disabled) ), Toast.LENGTH_SHORT).show();
        });

        // Suggest Best Settings button
        suggestSettingsButton.setOnClickListener(v -> {
            vibrateDevice(50);
            Animation buttonPress = AnimationUtils.loadAnimation(this, R.anim.pulse);
            v.startAnimation(buttonPress);

            // Show rewarded ad before suggesting best settings
            adManager.showRewardedAd(this, new AdManager.RewardedAdCallback() {
                @Override
                public void onRewarded(boolean success) {
                    // Proceed with suggesting best settings
                    suggestBestSettings();
                }

                @Override
                public void onAdClosed() {
                    // Ad was closed without reward, still proceed but with a message
                   // Toast.makeText(GfxToolsActivity.this, R.string.ad_closed_without_reward, Toast.LENGTH_SHORT).show();
                    suggestBestSettings();
                }

                @Override
                public void onAdFailed() {
                    // Ad failed to load, proceed anyway
                    suggestBestSettings();
                }
            });
        });

        // Apply button
        applyButton.setOnClickListener(v -> {
            vibrateDevice(50);
            Animation buttonPress = AnimationUtils.loadAnimation(this, R.anim.pulse);
            v.startAnimation(buttonPress);

            // Show interstitial ad before applying settings
            adManager.showInterstitialAd(this, new AdManager.InterstitialAdCallback() {
                @Override
                public void onAdClosed() {
                    // Apply settings after ad is closed
                    applySettings();
                }

                @Override
                public void onAdFailed() {
                    // Ad failed to load, apply settings anyway
                    applySettings();
                }
            });
        });
    }

    private void startEntranceAnimations() {
        // Animate speed lines background
        animateSpeedLines();

        // Animate title and logo
        Animation fadeIn = AnimationUtils.loadAnimation(this, R.anim.fade_in);
        titleText.startAnimation(fadeIn);
        //gfxLogo.startAnimation(fadeIn);

        // Animate cards with staggered delay
        Animation slideUp = AnimationUtils.loadAnimation(this, R.anim.slide_up);

        deviceInfoCard.setVisibility(View.INVISIBLE);
        resolutionCard.setVisibility(View.INVISIBLE);
        fpsCard.setVisibility(View.INVISIBLE);
        graphicsCard.setVisibility(View.INVISIBLE);
        advancedOptionsCard.setVisibility(View.INVISIBLE);
        suggestSettingsButton.setVisibility(View.INVISIBLE);
        applyButton.setVisibility(View.INVISIBLE);

        deviceInfoCard.postDelayed(() -> {
            deviceInfoCard.setVisibility(View.VISIBLE);
            deviceInfoCard.startAnimation(slideUp);
        }, 200);

        resolutionCard.postDelayed(() -> {
            resolutionCard.setVisibility(View.VISIBLE);
            resolutionCard.startAnimation(slideUp);
        }, 300);

        fpsCard.postDelayed(() -> {
            fpsCard.setVisibility(View.VISIBLE);
            fpsCard.startAnimation(slideUp);
        }, 400);

        graphicsCard.postDelayed(() -> {
            graphicsCard.setVisibility(View.VISIBLE);
            graphicsCard.startAnimation(slideUp);
        }, 500);

        advancedOptionsCard.postDelayed(() -> {
            advancedOptionsCard.setVisibility(View.VISIBLE);
            advancedOptionsCard.startAnimation(slideUp);
        }, 600);

        suggestSettingsButton.postDelayed(() -> {
            suggestSettingsButton.setVisibility(View.VISIBLE);
            suggestSettingsButton.startAnimation(slideUp);
        }, 700);

        applyButton.postDelayed(() -> {
            applyButton.setVisibility(View.VISIBLE);
            applyButton.startAnimation(slideUp);
        }, 800);
    }

    private AnimatorSet speedLinesAnimator;

    private void animateSpeedLines() {
        // Create infinite animation for speed lines
        ObjectAnimator translateX = ObjectAnimator.ofFloat(speedLinesBg, "translationX", -200f, 200f);
        translateX.setDuration(8000);
        translateX.setRepeatCount(ObjectAnimator.INFINITE);
        translateX.setRepeatMode(ObjectAnimator.REVERSE);

        ObjectAnimator translateY = ObjectAnimator.ofFloat(speedLinesBg, "translationY", -100f, 100f);
        translateY.setDuration(6000);
        translateY.setRepeatCount(ObjectAnimator.INFINITE);
        translateY.setRepeatMode(ObjectAnimator.REVERSE);

        speedLinesAnimator = new AnimatorSet();
        speedLinesAnimator.playTogether(translateX, translateY);
        speedLinesAnimator.start();
    }

    private void analyzeDevice() {
        // Get device specs
        double ramSizeGB = GfxSettingsHelper.getDeviceRamSizeGB(this);
        boolean isWeakCpu = isWeakCpu();
        String screenSizeCategory = getScreenSizeCategory();
        int estimatedFps = estimateAverageFps(ramSizeGB, isWeakCpu);

        // Format RAM size with one decimal place
        String formattedRam;
        if (ramSizeGB % 1 < 0.1) {
            // If very close to a whole number, just show the whole number
            // Convert to int first to avoid format exception
            int roundedRam = (int)Math.round(ramSizeGB);
            formattedRam = String.valueOf(roundedRam);
        } else {
            // Otherwise show with one decimal place
            formattedRam = String.format("%.1f", ramSizeGB);
        }

        // Update device info text
        StringBuilder sb = new StringBuilder();
        //هو ده مش سحر ولا شعوزة بس لو سالتنى اذاى هقولك معرفش
        // Get localized strings
        String ramInfo = getString(R.string.ram_info);
        String cpuPower = getString(R.string.cpu_power);
        String screenSize = getString(R.string.screen_size);
        String estFps = getString(R.string.estimated_fps);
        String recSettings = getString(R.string.recommended_settings);
        String resolutionRec = getString(R.string.resolution_rec);
        String fpsRec = getString(R.string.fps_rec);
        String graphicsRec = getString(R.string.graphics_rec);
        String hdrRec = getString(R.string.hdr_rec);
        String antiAliasingRec = getString(R.string.antialiasing_rec);
        String basicCpu = getString(R.string.basic);
        String powerfulCpu = getString(R.string.powerful);
        String enabled = getString(R.string.enabled);
        String disabled = getString(R.string.disabled);

        // Build the device info text with localized strings
        sb.append(ramInfo).append(": ").append(formattedRam).append(" ").append(getString(R.string.gb)).append("\n");
        sb.append(cpuPower).append(": ").append(isWeakCpu ? basicCpu : powerfulCpu).append("\n");
        sb.append(screenSize).append(": ").append(screenSizeCategory).append("\n");
        sb.append(estFps).append(": ").append(estimatedFps).append("\n");
        sb.append(recSettings).append(":\n");

        // Add recommended settings based on device specs
        if (ramSizeGB <= 2 || isWeakCpu) {
            sb.append(resolutionRec).append(": 720p\n");
            sb.append(fpsRec).append(": 30\n");
            sb.append(graphicsRec).append(": ").append(getString(R.string.low)).append("\n");
            sb.append(hdrRec).append(": ").append(disabled).append("\n");
            sb.append(antiAliasingRec).append(": ").append(disabled).append("\n");
        } else if (ramSizeGB <= 4) {
            sb.append(resolutionRec).append(": 1080p\n");
            sb.append(fpsRec).append(": 60\n");
            sb.append(graphicsRec).append(": ").append(getString(R.string.medium)).append("\n");
            sb.append(hdrRec).append(": ").append(disabled).append("\n");
            sb.append(antiAliasingRec).append(": ").append(enabled).append("\n");
        } else {
            sb.append(resolutionRec).append(": 1440p\n");
            sb.append(fpsRec).append(": 90\n");
            sb.append(graphicsRec).append(": ").append(getString(R.string.high)).append("\n");
            sb.append(hdrRec).append(": ").append(enabled).append("\n");
            sb.append(antiAliasingRec).append(": ").append(enabled).append("\n");
        }

        deviceInfoText.setText(sb.toString());
    }

    private boolean isWeakCpu() {
        int cpuCores = Runtime.getRuntime().availableProcessors();
        int sdkVersion = Build.VERSION.SDK_INT;

        // Simple heuristic: consider CPU weak if it has fewer than 4 cores or old Android version
        return cpuCores < 4 || sdkVersion < Build.VERSION_CODES.M;
    }

    private String getScreenSizeCategory() {
        android.view.WindowManager windowManager = (android.view.WindowManager) getSystemService(Context.WINDOW_SERVICE);
        android.util.DisplayMetrics metrics = new android.util.DisplayMetrics();
        windowManager.getDefaultDisplay().getMetrics(metrics);

        float density = metrics.density;
        float widthDp = metrics.widthPixels / density;
        float heightDp = metrics.heightPixels / density;
        float screenSizeDp = (float) Math.sqrt(widthDp * widthDp + heightDp * heightDp);

        if (screenSizeDp < 6.5) {
            return getString(R.string.screen_small);
        } else if (screenSizeDp < 7.5) {
            return getString(R.string.screen_medium);
        } else {
            return getString(R.string.screen_large);
        }
    }

    private int estimateAverageFps(double ramSizeGB, boolean isWeakCpu) {
        if (ramSizeGB <= 2 || isWeakCpu) {
            return 30; // Low FPS
        } else if (ramSizeGB <= 4) {
            return 60; // Medium FPS
        } else {
            return 90; // High FPS
        }
    }

    private void loadSavedSettings() {
        // Load settings from GfxSettingsHelper
        Map<String, Object> settings = gfxSettingsHelper.getGfxSettings();

        // Apply settings to UI
        if (settings != null) {
            // Resolution
            selectedResolution = (String) settings.getOrDefault("resolution", "1080p");
            switch (selectedResolution) {
                case "720p":
                    resolutionLow.setChecked(true);
                    break;
                case "1440p":
                    resolutionHigh.setChecked(true);
                    break;
                case "2160p":
                    resolutionUltra.setChecked(true);
                    break;
                default:
                    resolutionMedium.setChecked(true);
                    break;
            }

            // FPS
            selectedFps = (int) settings.getOrDefault("fps", 60);
            switch (selectedFps) {
                case 30:
                    fps30.setChecked(true);
                    break;
                case 90:
                    fps90.setChecked(true);
                    break;
                default:
                    fps60.setChecked(true);
                    break;
            }

            // Graphics Quality
            selectedGraphicsQuality = (int) settings.getOrDefault("graphicsQuality", 1);
            graphicsSeekbar.setProgress(selectedGraphicsQuality);

            // Switches
            hdrEnabled = (boolean) settings.getOrDefault("hdrEnabled", false);
            antiAliasingEnabled = (boolean) settings.getOrDefault("antiAliasingEnabled", true);
            gpuBoostEnabled = (boolean) settings.getOrDefault("gpuBoostEnabled", false);

            switchHdr.setChecked(hdrEnabled);
            switchAntialiasing.setChecked(antiAliasingEnabled);
            switchGpuBoost.setChecked(gpuBoostEnabled);
        }
    }

    private void applySettings() {
        // Show a progress dialog
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        View progressView = getLayoutInflater().inflate(R.layout.dialog_progress, null);
        builder.setView(progressView);
        builder.setCancelable(false);
        AlertDialog progressDialog = builder.create();
        progressDialog.show();

        // Create settings map
        Map<String, Object> settings = new HashMap<>();
        settings.put("resolution", selectedResolution);
        settings.put("fps", selectedFps);
        settings.put("graphicsQuality", selectedGraphicsQuality);
        settings.put("hdrEnabled", hdrEnabled);
        settings.put("antiAliasingEnabled", antiAliasingEnabled);
        settings.put("gpuBoostEnabled", gpuBoostEnabled);

        // Save settings with a delay for visual effect
        handler.postDelayed(() -> {
            // Save settings
            gfxSettingsHelper.saveGfxSettings(settings);

            // Dismiss progress dialog
            progressDialog.dismiss();

            // Show success message
            Toast.makeText(this, R.string.gfx_settings_applied , Toast.LENGTH_SHORT).show();

            // Show warning if high settings on low-end device
            double ramSizeGB = GfxSettingsHelper.getDeviceRamSizeGB(this);
            boolean isWeakCpu = isWeakCpu();

            if ((ramSizeGB <= 2 || isWeakCpu) &&
                (selectedFps > 30 || selectedGraphicsQuality > 0 || selectedResolution.equals("1440p") || selectedResolution.equals("2160p"))) {
                showPerformanceWarningDialog();
            }
        }, 1500);
    }

    private void showPerformanceWarningDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("Performance Warning");
        builder.setMessage("The selected settings may be too demanding for your device. This could result in lower frame rates, overheating, or increased battery drain. Consider using lower settings for better performance.");
        builder.setPositiveButton("Keep Settings", null);
        builder.setNegativeButton("Optimize Settings", (dialog, which) -> {
            // Apply recommended settings based on device specs
            double ramSizeGB = GfxSettingsHelper.getDeviceRamSizeGB(this);
            boolean isWeakCpu = isWeakCpu();

            if (ramSizeGB <= 2 || isWeakCpu) {
                selectedResolution = "720p";
                selectedFps = 30;
                selectedGraphicsQuality = 0;
                hdrEnabled = false;
                antiAliasingEnabled = false;
                gpuBoostEnabled = true;

                resolutionLow.setChecked(true);
                fps30.setChecked(true);
                graphicsSeekbar.setProgress(0);
                switchHdr.setChecked(false);
                switchAntialiasing.setChecked(false);
                switchGpuBoost.setChecked(true);
            } else if (ramSizeGB <= 4) {
                selectedResolution = "1080p";
                selectedFps = 60;
                selectedGraphicsQuality = 1;
                hdrEnabled = false;
                antiAliasingEnabled = true;
                gpuBoostEnabled = false;

                resolutionMedium.setChecked(true);
                fps60.setChecked(true);
                graphicsSeekbar.setProgress(1);
                switchHdr.setChecked(false);
                switchAntialiasing.setChecked(true);
                switchGpuBoost.setChecked(false);
            }

            Toast.makeText(this, R.string.settings_optimized_for_your_device , Toast.LENGTH_SHORT).show();
        });
        builder.show();
    }

    /**
     * Suggests the best settings based on device specifications
     * This method analyzes the device and suggests optimal settings
     */
    private void suggestBestSettings() {
        // Show a progress dialog
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        View progressView = getLayoutInflater().inflate(R.layout.dialog_progress, null);
        TextView progressText = progressView.findViewById(R.id.progress_text);
        TextView progressDescription = progressView.findViewById(R.id.progress_description);
        progressText.setText(R.string.best_settings_for_your_device);
        progressDescription.setText(R.string.suggest_best_settings_description);
        builder.setView(progressView);
        builder.setCancelable(false);
        AlertDialog progressDialog = builder.create();
        progressDialog.show();

        // Get device specs
        double ramSizeGB = GfxSettingsHelper.getDeviceRamSizeGB(this);
        boolean isWeakCpu = isWeakCpu();

        // Delay for visual effect
        handler.postDelayed(() -> {
            // Apply recommended settings based on device specs
            if (ramSizeGB <= 2 || isWeakCpu) {
                // Low-end device settings
                selectedResolution = "720p";
                selectedFps = 30;
                selectedGraphicsQuality = 0;
                hdrEnabled = false;
                antiAliasingEnabled = false;
                gpuBoostEnabled = true;

                // Update UI
                resolutionLow.setChecked(true);
                fps30.setChecked(true);
                graphicsSeekbar.setProgress(0);
                switchHdr.setChecked(false);
                switchAntialiasing.setChecked(false);
                switchGpuBoost.setChecked(true);
            } else if (ramSizeGB <= 4) {
                // Mid-range device settings
                selectedResolution = "1080p";
                selectedFps = 60;
                selectedGraphicsQuality = 1;
                hdrEnabled = false;
                antiAliasingEnabled = true;
                gpuBoostEnabled = false;

                // Update UI
                resolutionMedium.setChecked(true);
                fps60.setChecked(true);
                graphicsSeekbar.setProgress(1);
                switchHdr.setChecked(false);
                switchAntialiasing.setChecked(true);
                switchGpuBoost.setChecked(false);
            } else {
                // High-end device settings
                selectedResolution = "1440p";
                selectedFps = 90;
                selectedGraphicsQuality = 2;
                hdrEnabled = true;
                antiAliasingEnabled = true;
                gpuBoostEnabled = false;

                // Update UI
                resolutionHigh.setChecked(true);
                fps90.setChecked(true);
                graphicsSeekbar.setProgress(2);
                switchHdr.setChecked(true);
                switchAntialiasing.setChecked(true);
                switchGpuBoost.setChecked(false);
            }

            // Dismiss progress dialog
            progressDialog.dismiss();

            // Show success message with animation
            Toast.makeText(this, R.string.settings_suggested_successfully, Toast.LENGTH_LONG).show();

            // Apply subtle animations to the UI elements to highlight changes
            Animation pulse = AnimationUtils.loadAnimation(this, R.anim.pulse);
            resolutionRadioGroup.startAnimation(pulse);
            fpsRadioGroup.startAnimation(pulse);
            graphicsSeekbar.startAnimation(pulse);
            switchHdr.startAnimation(pulse);
            switchAntialiasing.startAnimation(pulse);
            switchGpuBoost.startAnimation(pulse);

        }, 1500); // 1.5 second delay for visual effect
    }

    private void vibrateDevice(long milliseconds) {
        try {
            Vibrator vibrator = (Vibrator) getSystemService(Context.VIBRATOR_SERVICE);
            if (vibrator != null && vibrator.hasVibrator()) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    vibrator.vibrate(VibrationEffect.createOneShot(milliseconds, VibrationEffect.DEFAULT_AMPLITUDE));
                } else {
                    // Deprecated in API 26
                    vibrator.vibrate(milliseconds);
                }
            }
        } catch (Exception e) {
            // Just continue without vibration
            e.printStackTrace();
        }
    }

    /**
     * Update UI with current settings
     */
    private void updateUIWithSettings() {
        // Update resolution radio buttons
        switch (selectedResolution) {
            case "720p":
                resolutionLow.setChecked(true);
                break;
            case "1080p":
                resolutionMedium.setChecked(true);
                break;
            case "1440p":
                resolutionHigh.setChecked(true);
                break;
            case "2160p":
                resolutionUltra.setChecked(true);
                break;
        }

        // Update FPS radio buttons
        switch (selectedFps) {
            case 30:
                fps30.setChecked(true);
                break;
            case 60:
                fps60.setChecked(true);
                break;
            case 90:
                fps90.setChecked(true);
                break;
        }

        // Update graphics seekbar
        graphicsSeekbar.setProgress(selectedGraphicsQuality);

        // Update switches
        switchHdr.setChecked(hdrEnabled);
        switchAntialiasing.setChecked(antiAliasingEnabled);
        switchGpuBoost.setChecked(gpuBoostEnabled);
    }
}
