package com.mahmoudffyt.gfxbooster.utils;

import android.app.Activity;
import android.content.Context;
import android.util.Log;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.android.billingclient.api.AcknowledgePurchaseParams;
import com.android.billingclient.api.AcknowledgePurchaseResponseListener;
import com.android.billingclient.api.BillingClient;
import com.android.billingclient.api.BillingClientStateListener;
import com.android.billingclient.api.BillingFlowParams;
import com.android.billingclient.api.BillingResult;
import com.android.billingclient.api.Purchase;
import com.android.billingclient.api.PurchasesResponseListener;
import com.android.billingclient.api.PurchasesUpdatedListener;
import com.android.billingclient.api.QueryProductDetailsParams;
import com.android.billingclient.api.QueryPurchasesParams;
import com.android.billingclient.api.ProductDetails;
import com.android.billingclient.api.ProductDetailsResponseListener;
import com.mahmoudffyt.gfxbooster.R;


import java.util.ArrayList;
import java.util.List;

/**
 * Handles all the Google Play Billing operations
 */
public class BillingManager implements PurchasesUpdatedListener {
    private static final String TAG = "BillingManager";

    // Product IDs - to be replaced with actual product IDs from Google Play Console
    public static final String SUBSCRIPTION_MONTHLY = "mo_gfx_app_fft"; //subscription-30-day
    //public static final String SUBSCRIPTION_YEARLY = "mood_game_pro_yearly";
    //public static final String SUBSCRIPTION_LIFETIME = "mood_game_pro_lifetime";

    private BillingClient billingClient;
    private Context context;
    private PremiumManager premiumManager;
    private ProductDetails monthlySubDetails;
    private ProductDetails yearlySubDetails;
    private ProductDetails lifetimeSubDetails;

    // Listener for purchase updates
    public interface BillingUpdatesListener {
        void onPurchaseComplete(boolean success);
        void onProductDetailsRetrieved(List<ProductDetails> productDetailsList);
    }

    /**
     * Callback for receiving product details
     */
    public interface ProductDetailsCallback {
        void onProductDetailsReceived(ProductDetails productDetails);
    }

    private BillingUpdatesListener billingUpdatesListener;

    public BillingManager(Context context, BillingUpdatesListener listener) {
        this.context = context;
        this.billingUpdatesListener = listener;
        this.premiumManager = new PremiumManager(context);

        // Initialize billing client
        billingClient = BillingClient.newBuilder(context)
                .setListener(this)
                .enablePendingPurchases()
                .build();

        // Connect to Google Play
        connectToGooglePlay();
    }

    private void connectToGooglePlay() {
        billingClient.startConnection(new BillingClientStateListener() {
            @Override
            public void onBillingSetupFinished(@NonNull BillingResult billingResult) {
                if (billingResult.getResponseCode() == BillingClient.BillingResponseCode.OK) {
                    // The billing client is ready. You can query purchases here.
                    Log.d(TAG, "Billing client connected successfully");

                    // Query available products
                    queryProductDetails();

                    // Query existing purchases
                    queryPurchases();
                } else {
                    Log.e(TAG, "Billing client connection failed with code: " + billingResult.getResponseCode());
                }
            }

            @Override
            public void onBillingServiceDisconnected() {
                // Try to restart the connection on the next request to
                // Google Play by calling the startConnection() method.
                Log.d(TAG, "Billing service disconnected");
            }
        });
    }

    private void queryProductDetails() {
        List<QueryProductDetailsParams.Product> productList = new ArrayList<>();

        // Add subscription products
        productList.add(QueryProductDetailsParams.Product.newBuilder()
                .setProductId(SUBSCRIPTION_MONTHLY)
                .setProductType(BillingClient.ProductType.SUBS)
                .build());

//        productList.add(QueryProductDetailsParams.Product.newBuilder()
//                .setProductId(SUBSCRIPTION_YEARLY)
//                .setProductType(BillingClient.ProductType.SUBS)
//                .build());
//
//        productList.add(QueryProductDetailsParams.Product.newBuilder()
//                .setProductId(SUBSCRIPTION_LIFETIME)
//                .setProductType(BillingClient.ProductType.SUBS)
//                .build());

        QueryProductDetailsParams params = QueryProductDetailsParams.newBuilder()
                .setProductList(productList)
                .build();

        billingClient.queryProductDetailsAsync(params, new ProductDetailsResponseListener() {
            @Override
            public void onProductDetailsResponse(@NonNull BillingResult billingResult, @NonNull List<ProductDetails> productDetailsList) {
                if (billingResult.getResponseCode() == BillingClient.BillingResponseCode.OK) {
                    // Process the result
                    for (ProductDetails productDetails : productDetailsList) {
                        String productId = productDetails.getProductId();
                        if (SUBSCRIPTION_MONTHLY.equals(productId)) {
                            monthlySubDetails = productDetails;
                        } //else if (SUBSCRIPTION_YEARLY.equals(productId)) {
//                            yearlySubDetails = productDetails;
//                        } else if (SUBSCRIPTION_LIFETIME.equals(productId)) {
//                            lifetimeSubDetails = productDetails;
//                        }
                    }

                    // Notify listener
                    if (billingUpdatesListener != null) {
                        billingUpdatesListener.onProductDetailsRetrieved(productDetailsList);
                    }
                } else {
                    Log.e(TAG, "Failed to query product details: " + billingResult.getDebugMessage());
                }
            }
        });
    }

    /**
     * Query for existing subscription purchases
     * This should be called at app startup and when the app comes to the foreground
     */
    public void queryPurchases() {
        // Query for existing subscription purchases
        QueryPurchasesParams params = QueryPurchasesParams.newBuilder()
                .setProductType(BillingClient.ProductType.SUBS)
                .build();

        billingClient.queryPurchasesAsync(params, new PurchasesResponseListener() {
            @Override
            public void onQueryPurchasesResponse(@NonNull BillingResult billingResult, @NonNull List<Purchase> list) {
                if (billingResult.getResponseCode() == BillingClient.BillingResponseCode.OK) {
                    if (list.isEmpty()) {
                        // No active subscriptions, make sure premium is disabled
                        premiumManager.setPremium(false);
                        Log.d(TAG, "No active subscriptions found");
                    } else {
                        processPurchases(list);
                    }
                } else {
                    Log.e(TAG, "Failed to query purchases: " + billingResult.getDebugMessage());
                }
            }
        });
    }

    private void processPurchases(List<Purchase> purchases) {
        for (Purchase purchase : purchases) {
            // Check purchase state
            if (purchase.getPurchaseState() == Purchase.PurchaseState.PURCHASED) {
                // Grant entitlement to the user
                handlePurchase(purchase);
            } else if (purchase.getPurchaseState() == Purchase.PurchaseState.PENDING) {
                // Here you can confirm to the user that they've started the pending
                // purchase, and to complete it, they should follow instructions that
                // are given to them. You can also choose to remind the user in the
                // future to complete the purchase if you detect that it is still
                // pending.
                Log.d(TAG, "Purchase is pending: " + purchase.getOrderId());
            }
        }
    }

    private void handlePurchase(Purchase purchase) {
        // Verify the purchase.
        // Ensure entitlement was not already granted for this purchase.
        // Grant entitlement to the user.

        // Check if purchase is acknowledged
        if (!purchase.isAcknowledged()) {
            AcknowledgePurchaseParams acknowledgePurchaseParams =
                    AcknowledgePurchaseParams.newBuilder()
                            .setPurchaseToken(purchase.getPurchaseToken())
                            .build();

            billingClient.acknowledgePurchase(acknowledgePurchaseParams, new AcknowledgePurchaseResponseListener() {
                @Override
                public void onAcknowledgePurchaseResponse(@NonNull BillingResult billingResult) {
                    if (billingResult.getResponseCode() == BillingClient.BillingResponseCode.OK) {
                        // Purchase acknowledged
                        Log.d(TAG, "Purchase acknowledged");

                        // Set premium status only after successful acknowledgment
                        premiumManager.setPremium(true);
                    }
                }
            });
        } else {
            // Purchase was already acknowledged, check if it's still valid
            if (purchase.getPurchaseState() == Purchase.PurchaseState.PURCHASED) {
                // Set premium status
                premiumManager.setPremium(true);
            }
        }
    }

    @Override
    public void onPurchasesUpdated(@NonNull BillingResult billingResult, @Nullable List<Purchase> purchases) {
        if (billingResult.getResponseCode() == BillingClient.BillingResponseCode.OK && purchases != null) {
            // Handle the purchases
            for (Purchase purchase : purchases) {
                handlePurchase(purchase);
            }

            // Notify listener
            if (billingUpdatesListener != null) {
                billingUpdatesListener.onPurchaseComplete(true);
            }

            // Show success message
            if (context != null) {
                Toast.makeText(context, context.getString(R.string.premium_features_unlocked), Toast.LENGTH_SHORT).show();
            }
        } else if (billingResult.getResponseCode() == BillingClient.BillingResponseCode.USER_CANCELED) {
            // Handle user canceled
            Log.d(TAG, "User canceled the purchase");

            // Notify listener
            if (billingUpdatesListener != null) {
                billingUpdatesListener.onPurchaseComplete(false);
            }
        } else {
            // Handle other error codes
            Log.e(TAG, "Purchase failed with code: " + billingResult.getResponseCode());

            // Notify listener
            if (billingUpdatesListener != null) {
                billingUpdatesListener.onPurchaseComplete(false);
            }

            // Show error message
            if (context != null) {
                Toast.makeText(context, context.getString(R.string.purchase_failed), Toast.LENGTH_SHORT).show();
            }
        }
    }

    /**
     * Get monthly subscription details
     *
     * @param callback Callback to receive the product details
     */
    public void getMonthlySubscriptionDetails(ProductDetailsCallback callback) {
        if (monthlySubDetails != null) {
            // If we already have the details, return them immediately
            callback.onProductDetailsReceived(monthlySubDetails);
        } else {
            // If we don't have the details yet, query them
            List<QueryProductDetailsParams.Product> productList = new ArrayList<>();

            // Add monthly subscription product
            productList.add(QueryProductDetailsParams.Product.newBuilder()
                    .setProductId(SUBSCRIPTION_MONTHLY)
                    .setProductType(BillingClient.ProductType.SUBS)
                    .build());

            QueryProductDetailsParams params = QueryProductDetailsParams.newBuilder()
                    .setProductList(productList)
                    .build();

            billingClient.queryProductDetailsAsync(params, new ProductDetailsResponseListener() {
                @Override
                public void onProductDetailsResponse(@NonNull BillingResult billingResult, @NonNull List<ProductDetails> productDetailsList) {
                    if (billingResult.getResponseCode() == BillingClient.BillingResponseCode.OK) {
                        // Find the monthly subscription details
                        for (ProductDetails productDetails : productDetailsList) {
                            if (SUBSCRIPTION_MONTHLY.equals(productDetails.getProductId())) {
                                monthlySubDetails = productDetails;
                                callback.onProductDetailsReceived(monthlySubDetails);
                                return;
                            }
                        }

                        // If we get here, we didn't find the product
                        callback.onProductDetailsReceived(null);
                    } else {
                        // Query failed
                        Log.e(TAG, "Failed to query product details: " + billingResult.getDebugMessage());
                        callback.onProductDetailsReceived(null);
                    }
                }
            });
        }
    }

    /**
     * Launch the purchase flow for monthly subscription
     */
    public void purchaseMonthlySubscription(Activity activity) {
        launchPurchaseFlow(activity, monthlySubDetails);
    }

    /**
     * Launch the purchase flow for yearly subscription
     */
    public void purchaseYearlySubscription(Activity activity) {
        launchPurchaseFlow(activity, yearlySubDetails);
    }

    /**
     * Launch the purchase flow for lifetime subscription
     */
    public void purchaseLifetimeSubscription(Activity activity) {
        launchPurchaseFlow(activity, lifetimeSubDetails);
    }

    private void launchPurchaseFlow(Activity activity, ProductDetails productDetails) {
        if (productDetails == null) {
            Log.e(TAG, "Product details not available");
            Toast.makeText(context, context.getString(R.string.product_not_available), Toast.LENGTH_SHORT).show();
            return;
        }

        // Get the offers
        List<ProductDetails.SubscriptionOfferDetails> offerDetails = productDetails.getSubscriptionOfferDetails();
        if (offerDetails == null || offerDetails.isEmpty()) {
            Log.e(TAG, "No subscription offers available");
            Toast.makeText(context, context.getString(R.string.product_not_available), Toast.LENGTH_SHORT).show();
            return;
        }

        // Get the first offer (you can implement logic to select a specific offer)
        String offerToken = offerDetails.get(0).getOfferToken();

        // Create the billing flow params
        List<BillingFlowParams.ProductDetailsParams> productDetailsParamsList = new ArrayList<>();
        productDetailsParamsList.add(
                BillingFlowParams.ProductDetailsParams.newBuilder()
                        .setProductDetails(productDetails)
                        .setOfferToken(offerToken)
                        .build()
        );

        BillingFlowParams billingFlowParams = BillingFlowParams.newBuilder()
                .setProductDetailsParamsList(productDetailsParamsList)
                .build();

        // Launch the billing flow
        BillingResult billingResult = billingClient.launchBillingFlow(activity, billingFlowParams);

        if (billingResult.getResponseCode() != BillingClient.BillingResponseCode.OK) {
            Log.e(TAG, "Failed to launch billing flow: " + billingResult.getDebugMessage());
            Toast.makeText(context, context.getString(R.string.purchase_failed), Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * Clean up the connection to prevent memory leaks
     */
    public void destroy() {
        if (billingClient != null && billingClient.isReady()) {
            billingClient.endConnection();
            billingClient = null;
        }
    }
}
