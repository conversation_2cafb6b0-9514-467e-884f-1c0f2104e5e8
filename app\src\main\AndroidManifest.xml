<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Permissions -->
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
    <uses-permission android:name="android.permission.PACKAGE_USAGE_STATS"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <!-- Declare foreground service types -->
    <queries>
        <intent>
            <action android:name="android.intent.action.MAIN" />
            <category android:name="android.intent.category.GAME" />
        </intent>
    </queries>

    <application
        android:name=".HeadshotApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.HeadshotSettingsGameBooster"
        tools:targetApi="31">

        <!-- AdMob App ID -->
        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="ca-app-pub-4043804233099568~1048346990" />
        <activity
            android:name=".GameBoosterActivity"
            android:exported="false"
            android:theme="@style/no_bar" />
        <activity
            android:name=".MainActivity"
            android:exported="false"
            android:theme="@style/no_bar" />
        <activity
            android:name=".splashscreen"
            android:exported="true"
            android:theme="@style/no_bar">
            <intent-filter tools:ignore="ExtraText">
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name=".SettingsActivity"
            android:exported="false"
            android:theme="@style/no_bar" />
        <activity
            android:name=".GameModeActivity"
            android:exported="false"
            android:theme="@style/no_bar" />
        <activity
            android:name=".HeadshotToolActivity"
            android:exported="false"
            android:theme="@style/no_bar" />
<!--        <activity-->
<!--            android:name=".AimButtonActivity"-->
<!--            android:exported="false"-->
<!--            android:theme="@style/no_bar" />-->
        <activity
            android:name=".AimOverlaySettingsActivity"
            android:exported="false"
            android:theme="@style/no_bar" />
        <activity
            android:name=".GfxToolsActivity"
            android:exported="false"
            android:theme="@style/no_bar" />
        <activity
            android:name=".AppGuideActivity"
            android:exported="false"
            android:theme="@style/no_bar" />
        <activity
            android:name=".WebViewActivity"
            android:exported="false"
            android:theme="@style/no_bar" />


        <service
            android:name=".GameModeService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="dataSync" />

        <receiver
            android:name=".GameModeBootReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
            </intent-filter>
        </receiver>

        <receiver
            android:name=".NotificationReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="com.game.headshot.ACTION_NOON_REMINDER" />
                <action android:name="com.game.headshot.ACTION_EVENING_REMINDER" />
                <action android:name="com.game.headshot.ACTION_HIGH_USAGE" />
                <action android:name="com.game.headshot.ACTION_CHECK_SYSTEM" />
            </intent-filter>
        </receiver>
    </application>

</manifest>