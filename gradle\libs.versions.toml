[versions]
agp = "8.9.2"
billing = "7.1.1"
firebaseBom = "33.13.0"
firebaseCrashlyticsPlugin = "3.0.3"
firebaseGoogleServicesPlugin = "4.4.2"
junit = "4.13.2"
junitVersion = "1.2.1"
espressoCore = "3.6.1"
appcompat = "1.7.0"
lottie = "6.0.0"
material = "1.12.0"
activity = "1.10.1"
constraintlayout = "2.2.1"
materialVersion = "1.12.0"
admob = "24.2.0"

[libraries]
billing = { module = "com.android.billingclient:billing", version.ref = "billing" }
firebase-analytics = { module = "com.google.firebase:firebase-analytics" }
firebase-analytics-ktx = { module = "com.google.firebase:firebase-analytics-ktx" }
firebase-bom = { module = "com.google.firebase:firebase-bom", version.ref = "firebaseBom" }
firebase-crashlytics = { module = "com.google.firebase:firebase-crashlytics" }
firebase-crashlytics-ktx = { module = "com.google.firebase:firebase-crashlytics-ktx" }
firebase-crashlytics-gradle = { module = "com.google.firebase:firebase-crashlytics-gradle", version.ref = "firebaseCrashlyticsPlugin" }
junit = { group = "junit", name = "junit", version.ref = "junit" }
ext-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }
appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" }
lottie = { module = "com.airbnb.android:lottie", version.ref = "lottie" }
#noinspection SimilarGradleDependency
material = { group = "com.google.android.material", name = "material", version.ref = "material" }
activity = { group = "androidx.activity", name = "activity", version.ref = "activity" }
constraintlayout = { group = "androidx.constraintlayout", name = "constraintlayout", version.ref = "constraintlayout" }
#noinspection SimilarGradleDependency
material-v100 = { module = "com.google.android.material:material", version.ref = "materialVersion" }
admob = { module = "com.google.android.gms:play-services-ads", version.ref = "admob" }

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
firebase-crashlytics = { id = "com.google.firebase.crashlytics", version.ref = "firebaseCrashlyticsPlugin" }
google-services = { id = "com.google.gms.google-services", version.ref = "firebaseGoogleServicesPlugin" }
