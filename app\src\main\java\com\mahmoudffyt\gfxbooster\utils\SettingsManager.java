package com.mahmoudffyt.gfxbooster.utils;

import android.content.Context;
import android.content.SharedPreferences;

import java.util.HashMap;
import java.util.Map;

/**
 * Utility class to manage Headshot Tool settings
 */
public class SettingsManager {

    private static final String PREFS_NAME = "HeadshotToolPrefs";
    private static final String KEY_GENERAL_SENSITIVITY = "generalSensitivity";
    private static final String KEY_RED_DOT_SENSITIVITY = "redDotSensitivity";
    private static final String KEY_SCOPE_2X_SENSITIVITY = "scope2xSensitivity";
    private static final String KEY_SCOPE_4X_SENSITIVITY = "scope4xSensitivity";
    private static final String KEY_SCOPE_AWM_SENSITIVITY = "scopeAwmSensitivity";
    private static final String KEY_DPI_SETTING = "dpiSetting";
    private static final String KEY_SMART_AIM_ENABLED = "smartAimEnabled";
    private static final String KEY_SELECTED_COLOR = "selectedColor";
    private static final String KEY_BREATH_HOLD = "breathHold";
    private static final String KEY_FREE_CAMERA_BUTTON = "freeCameraButton";

    private Context context;
    private SharedPreferences preferences;

    public SettingsManager(Context context) {
        this.context = context;
        this.preferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
    }

    /**
     * Save sensitivity settings
     */
    public void saveSettings(Map<String, Integer> settings) {
        SharedPreferences.Editor editor = preferences.edit();

        // Save each setting
        for (Map.Entry<String, Integer> entry : settings.entrySet()) {
            editor.putInt(entry.getKey(), entry.getValue());
        }

        editor.apply();
    }

    /**
     * Save a single setting
     */
    public void saveSetting(String key, int value) {
        preferences.edit().putInt(key, value).apply();
    }

    /**
     * Save a string setting
     */
    public void saveStringSetting(String key, String value) {
        preferences.edit().putString(key, value).apply();
    }

    /**
     * Save a boolean setting
     */
    public void saveBooleanSetting(String key, boolean value) {
        preferences.edit().putBoolean(key, value).apply();
    }

    /**
     * Get all sensitivity settings
     */
    public Map<String, Integer> getAllSettings() {
        Map<String, Integer> settings = new HashMap<>();

        // Get each setting with default values if not found
        settings.put(KEY_GENERAL_SENSITIVITY, preferences.getInt(KEY_GENERAL_SENSITIVITY, 50));
        settings.put(KEY_RED_DOT_SENSITIVITY, preferences.getInt(KEY_RED_DOT_SENSITIVITY, 75));
        settings.put(KEY_SCOPE_2X_SENSITIVITY, preferences.getInt(KEY_SCOPE_2X_SENSITIVITY, 58));
        settings.put(KEY_SCOPE_4X_SENSITIVITY, preferences.getInt(KEY_SCOPE_4X_SENSITIVITY, 42));
        settings.put(KEY_SCOPE_AWM_SENSITIVITY, preferences.getInt(KEY_SCOPE_AWM_SENSITIVITY, 35));
        settings.put(KEY_DPI_SETTING, preferences.getInt(KEY_DPI_SETTING, 800));
        settings.put(KEY_BREATH_HOLD, preferences.getInt(KEY_BREATH_HOLD, 80));
        settings.put(KEY_FREE_CAMERA_BUTTON, preferences.getInt(KEY_FREE_CAMERA_BUTTON, 50));

        return settings;
    }

    /**
     * Get a single setting
     */
    public int getSetting(String key, int defaultValue) {
        return preferences.getInt(key, defaultValue);
    }

    /**
     * Get a string setting
     */
    public String getStringSetting(String key, String defaultValue) {
        return preferences.getString(key, defaultValue);
    }

    /**
     * Get a boolean setting
     */
    public boolean getBooleanSetting(String key, boolean defaultValue) {
        return preferences.getBoolean(key, defaultValue);
    }

    /**
     * Check if settings have been saved before
     */
    public boolean hasSettings() {
        return preferences.contains(KEY_GENERAL_SENSITIVITY);
    }

    /**
     * Reset all settings to default values
     */
    public void resetSettings() {
        SharedPreferences.Editor editor = preferences.edit();
        editor.clear();
        editor.apply();
    }

    /**
     * Apply Smart Aim optimization
     * Adjusts settings for better headshot accuracy
     */
    public Map<String, Integer> applySmartAim(Map<String, Integer> currentSettings) {
        Map<String, Integer> optimizedSettings = new HashMap<>(currentSettings);

        // Optimize general sensitivity (slightly lower for better control)
        int generalSensitivity = currentSettings.get(KEY_GENERAL_SENSITIVITY);
        optimizedSettings.put(KEY_GENERAL_SENSITIVITY, Math.max(10, generalSensitivity - 5));

        // Optimize red dot sensitivity (slightly higher for quicker target acquisition)
        int redDotSensitivity = currentSettings.get(KEY_RED_DOT_SENSITIVITY);
        optimizedSettings.put(KEY_RED_DOT_SENSITIVITY, Math.min(100, redDotSensitivity + 5));

        // Optimize scope sensitivities for better headshot accuracy
        int scope2xSensitivity = currentSettings.get(KEY_SCOPE_2X_SENSITIVITY);
        optimizedSettings.put(KEY_SCOPE_2X_SENSITIVITY, calculateOptimizedScopeSensitivity(scope2xSensitivity));

        int scope4xSensitivity = currentSettings.get(KEY_SCOPE_4X_SENSITIVITY);
        optimizedSettings.put(KEY_SCOPE_4X_SENSITIVITY, calculateOptimizedScopeSensitivity(scope4xSensitivity));

        int scopeAwmSensitivity = currentSettings.get(KEY_SCOPE_AWM_SENSITIVITY);
        optimizedSettings.put(KEY_SCOPE_AWM_SENSITIVITY, calculateOptimizedScopeSensitivity(scopeAwmSensitivity));

        // Increase breath hold duration for better stability
        int breathHold = currentSettings.get(KEY_BREATH_HOLD);
        optimizedSettings.put(KEY_BREATH_HOLD, Math.min(100, breathHold + 10));

        // Save the optimized settings
        saveSettings(optimizedSettings);

        // Save Smart Aim state
        saveBooleanSetting(KEY_SMART_AIM_ENABLED, true);

        return optimizedSettings;
    }

    /**
     * Calculate optimized scope sensitivity for better headshot accuracy
     */
    private int calculateOptimizedScopeSensitivity(int currentSensitivity) {
        // For scopes, we want to reduce sensitivity slightly for better precision
        // but not too much to maintain responsiveness
        int optimizedSensitivity = (int) (currentSensitivity * 0.9f);
        return Math.max(10, optimizedSensitivity);
    }

    /**
     * Check if Smart Aim is enabled
     */
    public boolean isSmartAimEnabled() {
        return preferences.getBoolean(KEY_SMART_AIM_ENABLED, false);
    }

    /**
     * Disable Smart Aim
     */
    public void disableSmartAim() {
        preferences.edit().putBoolean(KEY_SMART_AIM_ENABLED, false).apply();
    }

    // Getter methods for specific settings
    public int getGeneralSensitivity() {
        return preferences.getInt(KEY_GENERAL_SENSITIVITY, 50);
    }

    public int getRedDotSensitivity() {
        return preferences.getInt(KEY_RED_DOT_SENSITIVITY, 75);
    }

    public int getScope2xSensitivity() {
        return preferences.getInt(KEY_SCOPE_2X_SENSITIVITY, 58);
    }

    public int getScope4xSensitivity() {
        return preferences.getInt(KEY_SCOPE_4X_SENSITIVITY, 42);
    }

    public int getScopeAwmSensitivity() {
        return preferences.getInt(KEY_SCOPE_AWM_SENSITIVITY, 35);
    }

    public int getDpiSetting() {
        return preferences.getInt(KEY_DPI_SETTING, 800);
    }

    public int getBreathHold() {
        return preferences.getInt(KEY_BREATH_HOLD, 80);
    }

    public String getSelectedColor() {
        return preferences.getString(KEY_SELECTED_COLOR, "#FF5252");
    }

    public int getFreeCameraButton() {
        return preferences.getInt(KEY_FREE_CAMERA_BUTTON, 50);
    }
}
