package com.mahmoudffyt.gfxbooster.utils;

import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.graphics.drawable.Drawable;
import android.util.Log;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * Game detector optimized for Android 14 with package visibility restrictions
 */
public class GameDetectorAndroid14 {
    private static final String TAG = "GameDetectorAndroid14";
    
    /**
     * Popular games package names for Android 14 compatibility
     */
    private static final String[] POPULAR_GAMES = {
        "com.tencent.ig",                           // PUBG Mobile
        "com.dts.freefireth",                       // Free Fire
        "com.supercell.clashofclans",               // Clash of Clans
        "com.supercell.clashroyale",                // Clash Royale
        "com.king.candycrushsaga",                  // Candy Crush Saga
        "com.gameloft.android.ANMP.GloftA8HM",      // Asphalt 8
        "com.mobile.legends",                       // Mobile Legends
        "com.garena.game.codm",                     // Call of Duty Mobile
        "com.mojang.minecraftpe",                   // Minecraft
        "com.roblox.client",                        // Roblox
        "com.innersloth.spacemafia",                // Among Us
        "com.ea.gp.fifamobile",                     // FIFA Mobile
        "com.ea.gp.apexlegendsmobilefps",           // Apex Legends Mobile
        "com.miHoYo.GenshinImpact",                 // Genshin Impact
        "com.activision.callofduty.shooter",        // Call of Duty
        "com.epicgames.fortnite",                   // Fortnite
        "com.netease.lztgglobal",                   // LifeAfter
        "com.netease.mrzhna",                       // Rules of Survival
        "com.zynga.words3",                         // Words With Friends
        "com.outfit7.mytalkingtom2",                // My Talking Tom 2
        "com.supercell.boombeach",                  // Boom Beach
        "com.supercell.hayday",                     // Hay Day
        "com.king.candycrushsodasaga",              // Candy Crush Soda
        "com.king.farmheroessaga",                  // Farm Heroes Saga
        "com.playgendary.kickthebuddy",             // Kick the Buddy
        "com.halfbrick.fruitninja",                 // Fruit Ninja
        "com.kiloo.subwaysurf",                     // Subway Surfers
        "com.imangi.templerun2",                    // Temple Run 2
        "com.rovio.angrybirdstransformers",         // Angry Birds
        "com.nianticlabs.pokemongo",                // Pokemon GO
        "com.playrix.gardenscapes",                 // Gardenscapes
        "com.playrix.homescapes",                   // Homescapes
        "com.facebook.games.wordscapes",            // Wordscapes
        "com.sgn.cookiejam.gp",                     // Cookie Jam
        "com.ea.games.pvz2_row",                    // Plants vs Zombies 2
        "com.gameloft.android.ANMP.GloftDMKP",      // Disney Magic Kingdoms
        "com.miniclip.eightballpool",               // 8 Ball Pool
        "com.miniclip.agar.io",                     // Agar.io
        "com.voodoo.paper.io",                      // Paper.io 2
        "com.hypah.io.slither"                      // Slither.io
    };
    
    public static class AppInfo {
        public String appName;
        public String packageName;
        public Drawable icon;
        public boolean isGame;
        public boolean isInstalled;
        
        public AppInfo(String appName, String packageName, Drawable icon, boolean isGame, boolean isInstalled) {
            this.appName = appName;
            this.packageName = packageName;
            this.icon = icon;
            this.isGame = isGame;
            this.isInstalled = isInstalled;
        }
    }
    
    /**
     * Get installed games optimized for Android 14
     */
    public static List<AppInfo> getInstalledGames(Context context) {
        List<AppInfo> games = new ArrayList<>();
        PackageManager pm = context.getPackageManager();
        
        Log.d(TAG, "Starting game detection for Android 14...");
        
        // Method 1: Check popular games directly
        games.addAll(checkPopularGames(context, pm));
        
        // Method 2: Get launchable apps and filter
        games.addAll(getLaunchableGames(context, pm));
        
        // Remove duplicates and sort
        List<AppInfo> uniqueGames = removeDuplicates(games);
        Collections.sort(uniqueGames, new Comparator<AppInfo>() {
            @Override
            public int compare(AppInfo a1, AppInfo a2) {
                return a1.appName.compareToIgnoreCase(a2.appName);
            }
        });
        
        Log.d(TAG, "Total games found: " + uniqueGames.size());
        return uniqueGames;
    }
    
    /**
     * Check popular games that are declared in manifest
     */
    private static List<AppInfo> checkPopularGames(Context context, PackageManager pm) {
        List<AppInfo> games = new ArrayList<>();
        
        for (String packageName : POPULAR_GAMES) {
            try {
                ApplicationInfo appInfo = pm.getApplicationInfo(packageName, 0);
                String appName = pm.getApplicationLabel(appInfo).toString();
                Drawable icon = pm.getApplicationIcon(appInfo);
                
                games.add(new AppInfo(appName, packageName, icon, true, true));
                Log.d(TAG, "Found popular game: " + appName + " (" + packageName + ")");
                
            } catch (PackageManager.NameNotFoundException e) {
                // Game not installed, skip
            }
        }
        
        return games;
    }
    
    /**
     * Get launchable apps and filter for games
     */
    private static List<AppInfo> getLaunchableGames(Context context, PackageManager pm) {
        List<AppInfo> games = new ArrayList<>();
        
        try {
            Intent intent = new Intent(Intent.ACTION_MAIN, null);
            intent.addCategory(Intent.CATEGORY_LAUNCHER);
            
            List<ResolveInfo> allApps = pm.queryIntentActivities(intent, 0);
            Log.d(TAG, "Found " + allApps.size() + " launchable apps");
            
            for (ResolveInfo info : allApps) {
                try {
                    String appName = info.loadLabel(pm).toString();
                    String packageName = info.activityInfo.packageName;
                    
                    // Skip system apps
                    ApplicationInfo appInfo = pm.getApplicationInfo(packageName, 0);
                    if ((appInfo.flags & ApplicationInfo.FLAG_SYSTEM) != 0) {
                        continue;
                    }
                    
                    // Check if it's a game using keywords
                    if (isGameByKeywords(appName, packageName)) {
                        Drawable icon = info.loadIcon(pm);
                        games.add(new AppInfo(appName, packageName, icon, true, true));
                        Log.d(TAG, "Found launchable game: " + appName + " (" + packageName + ")");
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error processing launchable app: " + e.getMessage());
                }
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error getting launchable games: " + e.getMessage());
        }
        
        return games;
    }
    
    /**
     * Check if app is a game using keywords
     */
    private static boolean isGameByKeywords(String appName, String packageName) {
        String lowerAppName = appName.toLowerCase();
        String lowerPackageName = packageName.toLowerCase();
        
        String[] gameKeywords = {
            "game", "games", "gaming", "play", "player", "battle", "arena", "fight", 
            "fighting", "shoot", "shooting", "shooter", "war", "warfare", "combat", 
            "action", "adventure", "puzzle", "racing", "race", "racer", "sport", "sports", 
            "strategy", "simulation", "rpg", "mmo", "fps", "moba", "arcade", "casual",
            
            // Popular game names
            "pubg", "freefire", "freefireth", "garena", "clash", "candy", "angry", 
            "temple", "subway", "hill", "plants", "zombies", "zombie", "cut", "fruit", 
            "ninja", "bird", "run", "runner", "jump", "jumper", "dash", "rush", "escape",
            "minecraft", "roblox", "among", "fortnite", "callofduty", "cod", "mobile",
            
            // Publishers
            "tencent", "supercell", "king", "zynga", "gameloft", "ubisoft", "ea", 
            "activision", "blizzard", "riot", "valve", "epic", "mihoyo", "netease"
        };
        
        for (String keyword : gameKeywords) {
            if (lowerAppName.contains(keyword) || lowerPackageName.contains(keyword)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Remove duplicate games based on package name
     */
    private static List<AppInfo> removeDuplicates(List<AppInfo> games) {
        List<AppInfo> uniqueGames = new ArrayList<>();
        List<String> seenPackages = new ArrayList<>();
        
        for (AppInfo game : games) {
            if (!seenPackages.contains(game.packageName)) {
                uniqueGames.add(game);
                seenPackages.add(game.packageName);
            }
        }
        
        return uniqueGames;
    }
    
    /**
     * Get all user apps (for fallback)
     */
    public static List<AppInfo> getAllUserApps(Context context) {
        List<AppInfo> apps = new ArrayList<>();
        PackageManager pm = context.getPackageManager();
        
        try {
            Intent intent = new Intent(Intent.ACTION_MAIN, null);
            intent.addCategory(Intent.CATEGORY_LAUNCHER);
            List<ResolveInfo> launchableApps = pm.queryIntentActivities(intent, 0);
            
            for (ResolveInfo info : launchableApps) {
                try {
                    String appName = info.loadLabel(pm).toString();
                    String packageName = info.activityInfo.packageName;
                    
                    // Skip system apps
                    ApplicationInfo appInfo = pm.getApplicationInfo(packageName, 0);
                    if ((appInfo.flags & ApplicationInfo.FLAG_SYSTEM) != 0) {
                        continue;
                    }
                    
                    Drawable icon = info.loadIcon(pm);
                    boolean isGame = isGameByKeywords(appName, packageName);
                    
                    apps.add(new AppInfo(appName, packageName, icon, isGame, true));
                    
                } catch (Exception e) {
                    Log.e(TAG, "Error processing app: " + e.getMessage());
                }
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error getting all user apps: " + e.getMessage());
        }
        
        return apps;
    }
}
