package com.mahmoudffyt.gfxbooster.models;

import android.graphics.drawable.Drawable;

/**
 * Model class for selected games in Game Mode
 */
public class SelectedGame {
    private String packageName;
    private String appName;
    private Drawable appIcon;
    private boolean isInstalled;

    public SelectedGame(String packageName, String appName, Drawable appIcon) {
        this.packageName = packageName;
        this.appName = appName;
        this.appIcon = appIcon;
        this.isInstalled = true;
    }

    public SelectedGame(String packageName, String appName, Drawable appIcon, boolean isInstalled) {
        this.packageName = packageName;
        this.appName = appName;
        this.appIcon = appIcon;
        this.isInstalled = isInstalled;
    }

    // Getters and Setters
    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public Drawable getAppIcon() {
        return appIcon;
    }

    public void setAppIcon(Drawable appIcon) {
        this.appIcon = appIcon;
    }

    public boolean isInstalled() {
        return isInstalled;
    }

    public void setInstalled(boolean installed) {
        isInstalled = installed;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        SelectedGame that = (SelectedGame) obj;
        return packageName != null ? packageName.equals(that.packageName) : that.packageName == null;
    }

    @Override
    public int hashCode() {
        return packageName != null ? packageName.hashCode() : 0;
    }
}
